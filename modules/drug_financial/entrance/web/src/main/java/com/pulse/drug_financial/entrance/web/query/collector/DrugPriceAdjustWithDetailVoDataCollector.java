package com.pulse.drug_financial.entrance.web.query.collector;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_financial.entrance.web.converter.DrugPriceAdjustWithDetailVoConverter;
import com.pulse.drug_financial.entrance.web.query.assembler.DrugPriceAdjustWithDetailVoDataAssembler.DrugPriceAdjustWithDetailVoDataHolder;
import com.pulse.drug_financial.entrance.web.vo.DrugPriceAdjustWithDetailVo;
import com.pulse.drug_financial.manager.dto.DrugPriceAdjustBaseDto;
import com.pulse.drug_financial.manager.dto.DrugPriceAdjustDetailBaseDto;
import com.pulse.drug_financial.manager.facade.drug_dictionary.DrugOriginBaseDtoServiceInDrugFinancialRpcAdapter;
import com.pulse.drug_financial.manager.facade.drug_dictionary.DrugOriginSpecificationBaseDtoServiceInDrugFinancialRpcAdapter;
import com.pulse.drug_financial.manager.facade.drug_dictionary.DrugProducerDictionaryBaseDtoServiceInDrugFinancialRpcAdapter;
import com.pulse.drug_financial.service.DrugPriceAdjustBaseDtoService;
import com.pulse.drug_financial.service.DrugPriceAdjustDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装DrugPriceAdjustWithDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "f31fc2aa-905a-38f9-8cfe-647127915d0a")
public class DrugPriceAdjustWithDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseDtoServiceInDrugFinancialRpcAdapter
            drugOriginBaseDtoServiceInDrugFinancialRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoServiceInDrugFinancialRpcAdapter
            drugOriginSpecificationBaseDtoServiceInDrugFinancialRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPriceAdjustBaseDtoService drugPriceAdjustBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPriceAdjustDetailBaseDtoService drugPriceAdjustDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPriceAdjustWithDetailVoConverter drugPriceAdjustWithDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPriceAdjustWithDetailVoDataCollector drugPriceAdjustWithDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugProducerDictionaryBaseDtoServiceInDrugFinancialRpcAdapter
            drugProducerDictionaryBaseDtoServiceInDrugFinancialRpcAdapter;

    /** 获取DrugPriceAdjustBaseDto数据填充DrugPriceAdjustWithDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "11acb07b-7785-3f1b-bbf2-30bfcd440890")
    public void collectDataWithDtoData(
            List<DrugPriceAdjustBaseDto> dtoList,
            DrugPriceAdjustWithDetailVoDataHolder dataHolder) {

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "24b7b8c9-47ad-389c-97f8-fcc4c6ff9b5e")
    private void fillDataWhenNecessary(DrugPriceAdjustWithDetailVoDataHolder dataHolder) {
        List<DrugPriceAdjustBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.drugPriceAdjustDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugPriceAdjustBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugPriceAdjustDetailBaseDto> baseDtoList =
                    drugPriceAdjustDetailBaseDtoService
                            .getByAdjustIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugPriceAdjustDetailBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugPriceAdjustDetailBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugPriceAdjustDetailBaseDto::getAdjustId));
            Map<DrugPriceAdjustDetailBaseDto, DrugPriceAdjustWithDetailVo.DrugPriceAdjustDetailVo>
                    dtoVoMap =
                            drugPriceAdjustWithDetailVoConverter
                                    .convertToDrugPriceAdjustDetailVoMap(baseDtoList);
            Map<DrugPriceAdjustDetailBaseDto, DrugPriceAdjustWithDetailVo.DrugPriceAdjustDetailVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugPriceAdjustDetailList =
                    rootDtoList.stream()
                            .map(DrugPriceAdjustBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugPriceAdjustDetailList2DrugOriginCode == null) {
            Set<String> ids =
                    dataHolder.drugPriceAdjustDetailList.keySet().stream()
                            .map(DrugPriceAdjustDetailBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginBaseDto> baseDtoList =
                    drugOriginBaseDtoServiceInDrugFinancialRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginBaseDto::getDrugOriginCode))
                            .collect(Collectors.toList());
            Map<String, DrugOriginBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginBaseDto::getDrugOriginCode,
                                            Function.identity()));
            Map<DrugOriginBaseDto, DrugPriceAdjustWithDetailVo.DrugOriginVo> dtoVoMap =
                    drugPriceAdjustWithDetailVoConverter.convertToDrugOriginVoMap(baseDtoList);
            Map<DrugOriginBaseDto, DrugPriceAdjustWithDetailVo.DrugOriginVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugPriceAdjustDetailList2DrugOriginCode =
                    dataHolder.drugPriceAdjustDetailList.keySet().stream()
                            .map(DrugPriceAdjustDetailBaseDto::getDrugOriginCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugPriceAdjustDetailList2DrugProducer == null) {
            Set<String> ids =
                    dataHolder.drugPriceAdjustDetailList.keySet().stream()
                            .map(DrugPriceAdjustDetailBaseDto::getDrugProducerId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugProducerDictionaryBaseDto> baseDtoList =
                    drugProducerDictionaryBaseDtoServiceInDrugFinancialRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugProducerDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugProducerDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugProducerDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<DrugProducerDictionaryBaseDto, DrugPriceAdjustWithDetailVo.DrugProducerDictionaryVo>
                    dtoVoMap =
                            drugPriceAdjustWithDetailVoConverter
                                    .convertToDrugProducerDictionaryVoMap(baseDtoList);
            Map<DrugProducerDictionaryBaseDto, DrugPriceAdjustWithDetailVo.DrugProducerDictionaryVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugPriceAdjustDetailList2DrugProducer =
                    dataHolder.drugPriceAdjustDetailList.keySet().stream()
                            .map(DrugPriceAdjustDetailBaseDto::getDrugProducerId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugPriceAdjustDetailList2DrugOriginSpecification == null) {
            Set<String> ids =
                    dataHolder.drugPriceAdjustDetailList.keySet().stream()
                            .map(DrugPriceAdjustDetailBaseDto::getDrugOriginSpecificationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginSpecificationBaseDto> baseDtoList =
                    drugOriginSpecificationBaseDtoServiceInDrugFinancialRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginSpecificationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugOriginSpecificationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginSpecificationBaseDto::getId,
                                            Function.identity()));
            Map<
                            DrugOriginSpecificationBaseDto,
                            DrugPriceAdjustWithDetailVo.DrugOriginSpecificationVo>
                    dtoVoMap =
                            drugPriceAdjustWithDetailVoConverter
                                    .convertToDrugOriginSpecificationVoMap(baseDtoList);
            Map<
                            DrugOriginSpecificationBaseDto,
                            DrugPriceAdjustWithDetailVo.DrugOriginSpecificationVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugPriceAdjustDetailList2DrugOriginSpecification =
                    dataHolder.drugPriceAdjustDetailList.keySet().stream()
                            .map(DrugPriceAdjustDetailBaseDto::getDrugOriginSpecificationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "471d2fc4-65cc-336d-918b-f9fac0a126d5")
    public void collectDataDefault(DrugPriceAdjustWithDetailVoDataHolder dataHolder) {
        drugPriceAdjustWithDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
