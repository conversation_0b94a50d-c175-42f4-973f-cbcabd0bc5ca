package com.pulse.drug_financial.service;

import com.pulse.drug_financial.manager.bo.*;
import com.pulse.drug_financial.service.base.BaseDrugPriceAdjustProfitBOService;
import com.pulse.drug_financial.service.bto.CreateDrugPriceAdjustProfitBto;
import com.vs.bo.AddedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "c19bb173-9a4b-41e0-91d5-796f13680f56|BO|SERVICE")
public class DrugPriceAdjustProfitBOService extends BaseDrugPriceAdjustProfitBOService {

    /** 创建调价盈亏 */
    @PublicInterface(
            id = "9de4dd76-ec99-418a-873a-7e800aa8038b",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1747291162520")
    @Transactional
    @AutoGenerated(locked = false, uuid = "80fe99c8-d9ca-4fd4-bddb-ebb2b09a24c1")
    public String createDrugPriceAdjustProfit(
            @Valid @NotNull CreateDrugPriceAdjustProfitBto createDrugPriceAdjustProfitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateDrugPriceAdjustProfitBoResult boResult =
                super.createDrugPriceAdjustProfitBase(createDrugPriceAdjustProfitBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateDrugPriceAdjustProfitBto */
        {
            CreateDrugPriceAdjustProfitBto bto =
                    boResult
                            .<CreateDrugPriceAdjustProfitBto>getBtoOfType(
                                    CreateDrugPriceAdjustProfitBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateDrugPriceAdjustProfitBto, DrugPriceAdjustProfitBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                DrugPriceAdjustProfitBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
