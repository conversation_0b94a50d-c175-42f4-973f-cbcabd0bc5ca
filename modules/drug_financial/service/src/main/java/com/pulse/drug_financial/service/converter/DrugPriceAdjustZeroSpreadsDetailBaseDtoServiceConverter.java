package com.pulse.drug_financial.service.converter;

import com.pulse.drug_financial.manager.dto.DrugPriceAdjustZeroSpreadsDetailBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "5bde819f-7e51-35bc-ac8a-1788a4eed1fa")
public class DrugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto>
            DrugPriceAdjustZeroSpreadsDetailBaseDtoConverter(
                    List<DrugPriceAdjustZeroSpreadsDetailBaseDto>
                            drugPriceAdjustZeroSpreadsDetailBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugPriceAdjustZeroSpreadsDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
