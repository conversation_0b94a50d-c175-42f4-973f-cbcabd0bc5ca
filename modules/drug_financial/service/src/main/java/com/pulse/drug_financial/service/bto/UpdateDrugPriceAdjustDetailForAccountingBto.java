package com.pulse.drug_financial.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugPriceAdjust
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "f4351276-34c5-41ba-9170-fce1e437099f|BTO|DEFINITION")
public class UpdateDrugPriceAdjustDetailForAccountingBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "fe19721c-de7b-4a68-850e-4e7c6058741f")
    private List<UpdateDrugPriceAdjustDetailForAccountingBto.DrugPriceAdjustDetailBto>
            drugPriceAdjustDetailBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "af29af2f-c8fe-4d96-9084-9d4748f1e4a4")
    private String id;

    @AutoGenerated(locked = true)
    public void setDrugPriceAdjustDetailBtoList(
            List<UpdateDrugPriceAdjustDetailForAccountingBto.DrugPriceAdjustDetailBto>
                    drugPriceAdjustDetailBtoList) {
        this.__$validPropertySet.add("drugPriceAdjustDetailBtoList");
        this.drugPriceAdjustDetailBtoList = drugPriceAdjustDetailBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> DrugPriceAdjustDetail
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class DrugPriceAdjustDetailBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "8e007212-ff90-4bc8-8c47-72c0b7d3d9d7")
        private String id;

        /** 已调价标志 */
        @AutoGenerated(locked = true, uuid = "5b5d642a-79f7-4ab9-a05e-43ff0e0ef360")
        private Boolean adjustedFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setAdjustedFlag(Boolean adjustedFlag) {
            this.__$validPropertySet.add("adjustedFlag");
            this.adjustedFlag = adjustedFlag;
        }
    }
}
