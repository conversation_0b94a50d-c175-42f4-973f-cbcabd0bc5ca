package com.pulse.drug_financial.service.converter;

import com.pulse.drug_financial.manager.dto.DrugPriceAdjustProfitBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "08aa8809-4357-3a14-8855-abf9e0cc6b07")
public class DrugPriceAdjustProfitBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugPriceAdjustProfitBaseDto> DrugPriceAdjustProfitBaseDtoConverter(
            List<DrugPriceAdjustProfitBaseDto> drugPriceAdjustProfitBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugPriceAdjustProfitBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
