package com.pulse.drug_financial.service.converter;

import com.pulse.drug_financial.manager.dto.DrugExportImportSettleBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "de23080b-c13e-33c1-ab85-001785523821")
public class DrugExportImportSettleBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugExportImportSettleBaseDto> DrugExportImportSettleBaseDtoConverter(
            List<DrugExportImportSettleBaseDto> drugExportImportSettleBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugExportImportSettleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
