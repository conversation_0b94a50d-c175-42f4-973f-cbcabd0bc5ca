package com.pulse.drug_financial.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_financial.manager.DrugPriceAdjustZeroSpreadsDetailBaseDtoManager;
import com.pulse.drug_financial.manager.dto.DrugPriceAdjustZeroSpreadsDetailBaseDto;
import com.pulse.drug_financial.service.converter.DrugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "56e74ada-fec8-456a-b950-e8728a79b395|DTO|SERVICE")
public class DrugPriceAdjustZeroSpreadsDetailBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPriceAdjustZeroSpreadsDetailBaseDtoManager
            drugPriceAdjustZeroSpreadsDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter
            drugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter;

    @PublicInterface(id = "11076163-5b77-44b1-a18c-258e3038d4d2", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "0349446e-e328-38f2-9251-27118090c980")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getBySupplierId(
            @NotNull(message = "供应商编码不能为空") String supplierId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySupplierIds(Arrays.asList(supplierId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c707772d-5f55-4697-9def-c427ba497e4c", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "05b0fe51-cf0e-3b44-a068-9aaa33825526")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByOriginCodes(
            @Valid @NotNull(message = "药品产地编码不能为空") List<String> originCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        originCode = new ArrayList<>(new HashSet<>(originCode));
        List<DrugPriceAdjustZeroSpreadsDetailBaseDto> drugPriceAdjustZeroSpreadsDetailBaseDtoList =
                drugPriceAdjustZeroSpreadsDetailBaseDtoManager.getByOriginCodes(originCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter
                .DrugPriceAdjustZeroSpreadsDetailBaseDtoConverter(
                        drugPriceAdjustZeroSpreadsDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "d8973c66-bedf-48fd-b975-d3bda4bcf383", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "15e857d7-cc26-3ba2-a7ac-cd1319369a80")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByZeroSpreadsAdjustId(
            @NotNull(message = "零差价调价记录id不能为空") String zeroSpreadsAdjustId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByZeroSpreadsAdjustIds(Arrays.asList(zeroSpreadsAdjustId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "a91115b3-7bf4-44f2-a771-ce353de7f7e5", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "5a06d327-d5dd-361c-937d-36deafecb20f")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugPriceAdjustZeroSpreadsDetailBaseDto> drugPriceAdjustZeroSpreadsDetailBaseDtoList =
                drugPriceAdjustZeroSpreadsDetailBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter
                .DrugPriceAdjustZeroSpreadsDetailBaseDtoConverter(
                        drugPriceAdjustZeroSpreadsDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "36411cd3-3fa9-4cad-aaf3-7e7aa58aa534", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "5de711e3-872d-3fda-9929-14dec1caa917")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByZeroSpreadsAdjustIds(
            @Valid @NotNull(message = "零差价调价记录id不能为空") List<String> zeroSpreadsAdjustId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        zeroSpreadsAdjustId = new ArrayList<>(new HashSet<>(zeroSpreadsAdjustId));
        List<DrugPriceAdjustZeroSpreadsDetailBaseDto> drugPriceAdjustZeroSpreadsDetailBaseDtoList =
                drugPriceAdjustZeroSpreadsDetailBaseDtoManager.getByZeroSpreadsAdjustIds(
                        zeroSpreadsAdjustId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter
                .DrugPriceAdjustZeroSpreadsDetailBaseDtoConverter(
                        drugPriceAdjustZeroSpreadsDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0a0996b3-5a3b-4d21-98f1-e6eaddc6f28a", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "803e135f-fc96-37bd-8789-72fba294ebc7")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByDrugOriginSpecificationId(
            @NotNull(message = "药品产地规格id不能为空") String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "73526f06-7e19-4c8b-b700-72331dd47ee0", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "827c8121-1fa2-369f-aa0f-ce0a9e9bf9a4")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByDrugOriginSpecificationIds(
            @Valid @NotNull(message = "药品产地规格id不能为空") List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginSpecificationId = new ArrayList<>(new HashSet<>(drugOriginSpecificationId));
        List<DrugPriceAdjustZeroSpreadsDetailBaseDto> drugPriceAdjustZeroSpreadsDetailBaseDtoList =
                drugPriceAdjustZeroSpreadsDetailBaseDtoManager.getByDrugOriginSpecificationIds(
                        drugOriginSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter
                .DrugPriceAdjustZeroSpreadsDetailBaseDtoConverter(
                        drugPriceAdjustZeroSpreadsDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "a5293251-d12f-4312-818f-a9dc37dfc691", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "e1ec5238-d7cf-3924-90a0-62031be2e64b")
    public DrugPriceAdjustZeroSpreadsDetailBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPriceAdjustZeroSpreadsDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "e8a3c1f9-a0b2-4b75-9b29-a5a0ef5c481b", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "f484ea8d-7226-39c9-86e1-4c08595504fb")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getByOriginCode(
            @NotNull(message = "药品产地编码不能为空") String originCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOriginCodes(Arrays.asList(originCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "91a90c22-e62a-4814-b5d0-74b2a3a04c24", module = "drug_financial")
    @AutoGenerated(locked = false, uuid = "fb27ef85-4377-3e3f-ae9b-f1701df908a7")
    public List<DrugPriceAdjustZeroSpreadsDetailBaseDto> getBySupplierIds(
            @Valid @NotNull(message = "供应商编码不能为空") List<String> supplierId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        supplierId = new ArrayList<>(new HashSet<>(supplierId));
        List<DrugPriceAdjustZeroSpreadsDetailBaseDto> drugPriceAdjustZeroSpreadsDetailBaseDtoList =
                drugPriceAdjustZeroSpreadsDetailBaseDtoManager.getBySupplierIds(supplierId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPriceAdjustZeroSpreadsDetailBaseDtoServiceConverter
                .DrugPriceAdjustZeroSpreadsDetailBaseDtoConverter(
                        drugPriceAdjustZeroSpreadsDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
