package com.pulse.drug_inventory.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "ad66d78a-ef82-4d10-85aa-fd3c71a31425|DTO|DEFINITION")
public class DrugOriginBatchInventoryBaseDto {
    /** 数量 库存数量，按最小规格单位存储 */
    @AutoGenerated(locked = true, uuid = "d85e2d72-0ac9-4dd1-985f-e834ac6b8886")
    private BigDecimal amount;

    /** 批次id */
    @AutoGenerated(locked = true, uuid = "6a52e278-3c96-4066-a9c7-3274ba9c1536")
    private String batchId;

    /** 批号 */
    @AutoGenerated(locked = true, uuid = "070f353e-e8c8-4fcb-ab8f-70f20633771f")
    private String batchNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "aa127bf9-bb03-49f5-9911-043cca734e3e")
    private Date createdAt;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "bac93804-a100-4314-91f7-3ae0c465adc1")
    private Long deletedAt;

    /** 药品产地编码 冗余存 */
    @AutoGenerated(locked = true, uuid = "129df797-a794-44c7-be8f-b9144aa8ef59")
    private String drugOriginCode;

    /** 药品产地规格id */
    @AutoGenerated(locked = true, uuid = "12fc9dc3-5a80-4769-9aa7-3667c25ea6da")
    private String drugOriginSpecificationId;

    /** 有效期 批次效期 */
    @AutoGenerated(locked = true, uuid = "85f4e697-87f0-428f-862d-591ba95a04de")
    private Date expirationDate;

    /** gcp编码 gcp药品一物一码，开单发药时也按编码开单发药 */
    @AutoGenerated(locked = true, uuid = "4bb59be6-2b70-42a8-a6a5-489a407d9b9f")
    private String gcpCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "63cc65f9-cb50-4db5-8bde-c34066f9a37f")
    private String id;

    /** 进货日期 */
    @AutoGenerated(locked = true, uuid = "cb618f1c-bac3-48e3-95b5-46e78e86bd38")
    private Date importDateTime;

    /** 库存ID */
    @AutoGenerated(locked = true, uuid = "a4168683-bed4-4f22-8664-51729e9ab1fc")
    private String inventoryId;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "591a56bd-5a52-4791-806e-9ae40b22a603")
    private BigDecimal purchasePrice;

    /** 零售价 */
    @AutoGenerated(locked = true, uuid = "5394e410-d7f7-4a37-8727-5b75cbfb60c2")
    private BigDecimal retailPrice;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "a72b84cc-e77a-4ebb-93f9-a403d9c6dfb5")
    private String storageCode;

    /** 可供标识id */
    @AutoGenerated(locked = true, uuid = "d3b41e06-48b1-4de4-8c11-10da405bbdbe")
    private String supplyId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "54e4c6b6-8691-488f-97cc-354d14e5f7ed")
    private Date updatedAt;

    /**
     * 虚库存 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
     * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
     * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
     */
    @AutoGenerated(locked = true, uuid = "794c8294-b90a-477c-b837-3760d224a9bc")
    private BigDecimal virtualAmount;
}
