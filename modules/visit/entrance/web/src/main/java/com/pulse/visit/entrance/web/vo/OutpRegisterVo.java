package com.pulse.visit.entrance.web.vo;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "f7ffa0b1-f73c-4a59-93c0-35a8ed2c45b0|VO|DEFINITION")
public class OutpRegisterVo {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "17286f1b-56f7-4a73-9f89-0497eb740b4f")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "8594aa87-fdc8-4096-9ba8-e7d0d72614d4")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "7c4ccd22-29cb-416c-b031-2d963f5b2e41")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "0761f840-a292-4bfe-98a2-6bfdcbc5a621")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "a2281e0d-0ac7-4dec-83e3-77d5d13f828b")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "67ed65b0-fd3b-4cdc-a5da-99e697950dbf")
    private String chargeTypeCode;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "59e0dc65-0b76-42ce-98b1-6aed3f2e66ba")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "8de006b9-cf54-4035-9f39-792223841daf")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "4bb7d161-f61b-42d1-beea-d552cfd46e71")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "8c90427c-2b5f-4c23-8661-0dd7266c8074")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "5dd2d380-1ae8-4a0d-b337-01f9ea346c00")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b6cf95ec-82ee-442e-8708-2d2fb7523581")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "391ad346-5ea1-4193-aa84-54142126b0bf")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "69b79224-fbe4-46c0-9d8c-58fae7f9f2e9")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "d0c7f0e8-3a29-416a-8ab8-4862992c9ff3")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "cbf7e310-ad8f-49a1-b77f-bd3e9a9be06a")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "8cbd23f6-d32a-42b3-9b1d-014761d2f7bf")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "a17865c6-88c5-41cb-8f7a-b88385d42fa1")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "a5723782-f5cf-4f60-a2ab-1a28206c737e")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "20122793-ee73-49eb-9ab7-be7d78b3e01e")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "abde8f23-7812-4f58-8b35-c73748c30cd1")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "24598b43-7363-4018-90d1-526ca2603db3")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "7975c10c-729e-4499-8474-2860aa98feb1")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "54658978-d12f-4933-932b-455a96530ec7")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "613b6a42-9fd5-43fe-8a76-89198916621f")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "e3ccab2c-f968-462b-9d7a-39de1a1b07de")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "80bf9758-cdaa-4fdc-98c6-319cd2170d5c")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "937772a6-f757-46c8-a141-f30ba542d396")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "4e553f24-ee01-405d-9a9d-0a8f8696c5ae")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "004abed0-7420-4815-af6b-2e360b9ec481")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "a6480955-9f21-4c86-984f-7f08cbe34b7b")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "f7570ba0-8067-4da7-b909-af7ff5b82bec")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "8aa03c14-6abf-4321-b908-9dc3cfd96abb")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "6467796d-d059-42b7-93a7-a4c7be7b94de")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "efa61adb-f74f-41dc-8fa0-4b31984d14cb")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "89c2095c-ecbd-4430-9f8c-afdf2a0d0c5d")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "3d92adfc-e4e7-4f32-8789-911e8ac5cbb0")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "f29c2eef-2c57-4ec6-9f94-dc19b2623553")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "c2377df3-2793-4efc-acdf-bfdac1a84b1b")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "2ef0efa8-b20b-4b47-a88e-b3059dbdda1e")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "de7ecf85-2449-4f12-9ed6-8666a56637c8")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "0f95de62-981e-4f26-8517-d1a668fdc706")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "dd027ef9-0a89-4b5a-886d-916195af702f")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "fa90b727-5028-4b5e-bc88-8f96afb4ab4c")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "901af1a9-cc4c-4991-ab51-b927fa50cdb0")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "52befda9-cd35-466f-898b-b74b00d88749")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "c2ef650b-c0e3-456a-a333-8a05f881b783")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "8d8543bf-38e8-4db6-8694-02f6f5f13668")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "f9d748d6-43b9-43f7-9adf-595ab9d975ab")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "d6b419bb-b85d-48a1-b668-ca60d391c35d")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "2e34b96c-9b8e-48af-aff1-b72b0e0613b3")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "936fcd76-8f69-4be6-bd39-a23e66c2d244")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "df6201d6-33f1-4896-86cf-acbd68960acf")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "de8432f8-f16a-4d7b-9fb7-0fbf7457f4f0")
    private TimeEo waitingStartTime;
}
