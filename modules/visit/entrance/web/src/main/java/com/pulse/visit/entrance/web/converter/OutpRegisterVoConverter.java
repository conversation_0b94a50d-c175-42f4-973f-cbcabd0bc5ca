package com.pulse.visit.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.visit.entrance.web.query.assembler.OutpRegisterVoDataAssembler;
import com.pulse.visit.entrance.web.vo.OutpRegisterVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OutpRegisterVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "f7ffa0b1-f73c-4a59-93c0-35a8ed2c45b0|VO|CONVERTER")
public class OutpRegisterVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterVoDataAssembler outpRegisterVoDataAssembler;

    /** 使用默认方式组装OutpRegisterVo数据 */
    @AutoGenerated(locked = true, uuid = "09551a1d-3e97-3788-8e75-5c0ab230f808")
    public OutpRegisterVo convertAndAssembleData(OutpRegisterBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OutpRegisterBaseDto转换成OutpRegisterVo */
    @AutoGenerated(locked = true, uuid = "21253976-67fb-3148-b778-874ffdd0d384")
    public OutpRegisterVo convertToOutpRegisterVo(OutpRegisterBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOutpRegisterVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OutpRegisterVo列表数据 */
    @AutoGenerated(locked = true, uuid = "30342159-fb26-3c3f-b228-8495c0d44e73")
    public List<OutpRegisterVo> convertAndAssembleDataList(List<OutpRegisterBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, OutpRegisterVo> voMap =
                convertToOutpRegisterVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        outpRegisterVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OutpRegisterBaseDto转换成OutpRegisterVo */
    @AutoGenerated(locked = false, uuid = "f7ffa0b1-f73c-4a59-93c0-35a8ed2c45b0-converter-Map")
    public Map<OutpRegisterBaseDto, OutpRegisterVo> convertToOutpRegisterVoMap(
            List<OutpRegisterBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<OutpRegisterBaseDto, OutpRegisterVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OutpRegisterVo vo = new OutpRegisterVo();
                                            vo.setId(dto.getId());
                                            vo.setPatientId(dto.getPatientId());
                                            vo.setVisitCardId(dto.getVisitCardId());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            vo.setSourceAppId(dto.getSourceAppId());
                                            vo.setSourceCampusId(dto.getSourceCampusId());
                                            vo.setTallyStatus(dto.getTallyStatus());
                                            vo.setRegisterNumber(dto.getRegisterNumber());
                                            vo.setPatientName(dto.getPatientName());
                                            vo.setAge(dto.getAge());
                                            vo.setIdentityType(dto.getIdentityType());
                                            vo.setInsuranceType(dto.getInsuranceType());
                                            vo.setInsuranceNumber(dto.getInsuranceNumber());
                                            vo.setRegisterStatus(dto.getRegisterStatus());
                                            vo.setOperatorId(dto.getOperatorId());
                                            vo.setRegisterDate(dto.getRegisterDate());
                                            vo.setSettleNumber(dto.getSettleNumber());
                                            vo.setCancellationInitiatorId(
                                                    dto.getCancellationInitiatorId());
                                            vo.setCancellationInitiationTime(
                                                    dto.getCancellationInitiationTime());
                                            vo.setReviewerId(dto.getReviewerId());
                                            vo.setReviewTime(dto.getReviewTime());
                                            vo.setTotalCost(dto.getTotalCost());
                                            vo.setTotalCharge(dto.getTotalCharge());
                                            vo.setReturnOperatorId(dto.getReturnOperatorId());
                                            vo.setReturnDate(dto.getReturnDate());
                                            vo.setReturnSettleNumber(dto.getReturnSettleNumber());
                                            vo.setReturnReason(dto.getReturnReason());
                                            vo.setChargePriceScheduleId(
                                                    dto.getChargePriceScheduleId());
                                            vo.setDiscountCategory(dto.getDiscountCategory());
                                            vo.setTallyReceiptNumber(dto.getTallyReceiptNumber());
                                            vo.setChargeTypeCode(dto.getChargeTypeCode());
                                            vo.setIdentityCode(dto.getIdentityCode());
                                            vo.setTakeAppointOperatorId(
                                                    dto.getTakeAppointOperatorId());
                                            vo.setTakeAppointDate(dto.getTakeAppointDate());
                                            vo.setRegistrationDoctorId(
                                                    dto.getRegistrationDoctorId());
                                            vo.setRegistrationDepartmentId(
                                                    dto.getRegistrationDepartmentId());
                                            vo.setOutpatientRegistrationCategory(
                                                    dto.getOutpatientRegistrationCategory());
                                            vo.setSpecifiedDiseaseFlag(
                                                    dto.getSpecifiedDiseaseFlag());
                                            vo.setSpecifiedDiseaseCode(
                                                    dto.getSpecifiedDiseaseCode());
                                            vo.setWaitingStartTime(dto.getWaitingStartTime());
                                            vo.setWaitingEndTime(dto.getWaitingEndTime());
                                            vo.setRegisterReviewStatus(
                                                    dto.getRegisterReviewStatus());
                                            vo.setReviewReason(dto.getReviewReason());
                                            vo.setReviewExplain(dto.getReviewExplain());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setMdtFeeType(dto.getMdtFeeType());
                                            vo.setOutpAppointId(dto.getOutpAppointId());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setGender(dto.getGender());
                                            vo.setChargeStatus(dto.getChargeStatus());
                                            vo.setHealthOfficialsFee(dto.getHealthOfficialsFee());
                                            vo.setInternationalPatientFee(
                                                    dto.getInternationalPatientFee());
                                            vo.setOperatorDepartmentId(
                                                    dto.getOperatorDepartmentId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OutpRegisterBaseDto转换成OutpRegisterVo */
    @AutoGenerated(locked = true, uuid = "f7ffa0b1-f73c-4a59-93c0-35a8ed2c45b0-converter-list")
    public List<OutpRegisterVo> convertToOutpRegisterVoList(List<OutpRegisterBaseDto> dtoList) {
        return new ArrayList<>(convertToOutpRegisterVoMap(dtoList).values());
    }
}
