package com.pulse.visit.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.dto.OutpRegisterAppointDto;
import com.pulse.visit.entrance.web.query.assembler.OutpRegisterAppointVoDataAssembler;
import com.pulse.visit.entrance.web.query.assembler.OutpRegisterAppointVoDataAssembler.OutpRegisterAppointVoDataHolder;
import com.pulse.visit.entrance.web.query.collector.OutpRegisterAppointVoDataCollector;
import com.pulse.visit.entrance.web.vo.OutpRegisterAppointVo;
import com.pulse.visit.manager.facade.appointment_booking.OutpRegisterBaseDtoServiceInVisitRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OutpRegisterAppointVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "6511009a-e4ed-490c-a42a-8b64d135aaf8|VO|CONVERTER")
public class OutpRegisterAppointVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterAppointVoDataAssembler outpRegisterAppointVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterAppointVoDataCollector outpRegisterAppointVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterBaseDtoServiceInVisitRpcAdapter outpRegisterBaseDtoServiceInVisitRpcAdapter;

    /** 把OutpRegisterAppointDto转换成OutpRegisterAppointVo */
    @AutoGenerated(locked = true, uuid = "39a5f46e-5641-314b-98c9-9f4b9a32c493")
    public OutpRegisterAppointVo convertToOutpRegisterAppointVo(OutpRegisterAppointDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOutpRegisterAppointVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OutpRegisterAppointVo列表数据 */
    @AutoGenerated(locked = true, uuid = "4ad4a642-2986-3277-b1ef-5312810920fe")
    public List<OutpRegisterAppointVo> convertAndAssembleDataList(
            List<OutpRegisterAppointDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OutpRegisterAppointVoDataHolder dataHolder = new OutpRegisterAppointVoDataHolder();
        dataHolder.setRootBaseDtoList(
                outpRegisterBaseDtoServiceInVisitRpcAdapter.getByIds(
                        dtoList.stream()
                                .map(OutpRegisterAppointDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OutpRegisterAppointVo> voMap =
                convertToOutpRegisterAppointVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        outpRegisterAppointVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        outpRegisterAppointVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OutpRegisterAppointDto转换成OutpRegisterAppointVo */
    @AutoGenerated(locked = false, uuid = "6511009a-e4ed-490c-a42a-8b64d135aaf8-converter-Map")
    public Map<OutpRegisterAppointDto, OutpRegisterAppointVo> convertToOutpRegisterAppointVoMap(
            List<OutpRegisterAppointDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<OutpRegisterAppointDto, OutpRegisterAppointVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OutpRegisterAppointVo vo = new OutpRegisterAppointVo();
                                            vo.setId(dto.getId());
                                            vo.setPatientId(dto.getPatientId());
                                            vo.setVisitCardId(dto.getVisitCardId());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            vo.setSourceAppId(dto.getSourceAppId());
                                            vo.setSourceCampusId(dto.getSourceCampusId());
                                            vo.setTallyStatus(dto.getTallyStatus());
                                            vo.setRegisterNumber(dto.getRegisterNumber());
                                            vo.setPatientName(dto.getPatientName());
                                            vo.setAge(dto.getAge());
                                            vo.setIdentityType(dto.getIdentityType());
                                            vo.setInsuranceType(dto.getInsuranceType());
                                            vo.setInsuranceNumber(dto.getInsuranceNumber());
                                            vo.setRegisterStatus(dto.getRegisterStatus());
                                            vo.setOperatorId(dto.getOperatorId());
                                            vo.setRegisterDate(dto.getRegisterDate());
                                            vo.setSettleNumber(dto.getSettleNumber());
                                            vo.setCancellationInitiatorId(
                                                    dto.getCancellationInitiatorId());
                                            vo.setCancellationInitiationTime(
                                                    dto.getCancellationInitiationTime());
                                            vo.setReviewerId(dto.getReviewerId());
                                            vo.setReviewTime(dto.getReviewTime());
                                            vo.setTotalCost(dto.getTotalCost());
                                            vo.setTotalCharge(dto.getTotalCharge());
                                            vo.setReturnOperatorId(dto.getReturnOperatorId());
                                            vo.setReturnDate(dto.getReturnDate());
                                            vo.setReturnSettleNumber(dto.getReturnSettleNumber());
                                            vo.setReturnReason(dto.getReturnReason());
                                            vo.setChargePriceScheduleId(
                                                    dto.getChargePriceScheduleId());
                                            vo.setDiscountCategory(dto.getDiscountCategory());
                                            vo.setTallyReceiptNumber(dto.getTallyReceiptNumber());
                                            vo.setChargeTypeCode(dto.getChargeTypeCode());
                                            vo.setIdentityCode(dto.getIdentityCode());
                                            vo.setTakeAppointOperatorId(
                                                    dto.getTakeAppointOperatorId());
                                            vo.setTakeAppointDate(dto.getTakeAppointDate());
                                            vo.setRegistrationDoctorId(
                                                    dto.getRegistrationDoctorId());
                                            vo.setRegistrationDepartmentId(
                                                    dto.getRegistrationDepartmentId());
                                            vo.setOutpatientRegistrationCategory(
                                                    dto.getOutpatientRegistrationCategory());
                                            vo.setSpecifiedDiseaseFlag(
                                                    dto.getSpecifiedDiseaseFlag());
                                            vo.setSpecifiedDiseaseCode(
                                                    dto.getSpecifiedDiseaseCode());
                                            vo.setWaitingStartTime(dto.getWaitingStartTime());
                                            vo.setWaitingEndTime(dto.getWaitingEndTime());
                                            vo.setRegisterReviewStatus(
                                                    dto.getRegisterReviewStatus());
                                            vo.setReviewReason(dto.getReviewReason());
                                            vo.setReviewExplain(dto.getReviewExplain());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setMdtFeeType(dto.getMdtFeeType());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setGender(dto.getGender());
                                            vo.setChargeStatus(dto.getChargeStatus());
                                            vo.setHealthOfficialsFee(dto.getHealthOfficialsFee());
                                            vo.setInternationalPatientFee(
                                                    dto.getInternationalPatientFee());
                                            vo.setOperatorDepartmentId(
                                                    dto.getOperatorDepartmentId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OutpRegisterAppointDto转换成OutpRegisterAppointVo */
    @AutoGenerated(locked = true, uuid = "6511009a-e4ed-490c-a42a-8b64d135aaf8-converter-list")
    public List<OutpRegisterAppointVo> convertToOutpRegisterAppointVoList(
            List<OutpRegisterAppointDto> dtoList) {
        return new ArrayList<>(convertToOutpRegisterAppointVoMap(dtoList).values());
    }

    /** 使用默认方式组装OutpRegisterAppointVo数据 */
    @AutoGenerated(locked = true, uuid = "bdd37504-4065-35d3-98d3-81aed7002ffe")
    public OutpRegisterAppointVo convertAndAssembleData(OutpRegisterAppointDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
