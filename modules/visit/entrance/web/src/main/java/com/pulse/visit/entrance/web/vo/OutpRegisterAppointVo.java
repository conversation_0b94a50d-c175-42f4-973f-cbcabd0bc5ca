package com.pulse.visit.entrance.web.vo;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "6511009a-e4ed-490c-a42a-8b64d135aaf8|VO|DEFINITION")
public class OutpRegisterAppointVo {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a69f4791-c995-4ff4-8efd-399b84b066c8")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "df1af860-b952-4b2a-9121-78660c09e12e")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "97a94358-0e6a-45ce-a118-8f217df151ec")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "369a0e5c-c6ce-4e43-b7f6-71b4732baac1")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "de763836-6da2-4798-9e01-ded086ef29d7")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "c33f211a-c1f2-44dd-b545-658f18f71df4")
    private String chargeTypeCode;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "dfc899f1-2b8b-4d0a-b4fd-1a4a24a27718")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "77f4106c-e3ab-4a7a-a857-2a23634e157c")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "291ecced-8481-4f49-8b23-a95b963ef8db")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "42e6c3db-d53a-49c9-b9c2-5bf9e152b2c8")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "d7ed5360-bdeb-4487-923c-9337cb740926")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "1233303f-92c2-45ef-8ddd-ffd1330d981e")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "d833c784-c94e-4cb8-8437-be629e918617")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "9b74b437-d450-4388-8ae5-85ca3a4767ce")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "914aa406-57dd-4cc6-b724-b5cbd620c9d4")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "84c53bb9-f473-4960-8a55-867ee17e533d")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "a21f1140-c33e-4962-8be2-c93aedbf7656")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "44dba18b-94bf-414b-93a8-0a159f30a8b6")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "fffc2904-0312-4776-bf31-343a7f836ddf")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "358ceaf6-d99e-406c-9de4-f2a0bbec7463")
    private String operatorId;

    /** 预约ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "b4217e0d-8e00-4e04-a8fa-35621547bd95")
    private OutpAppointBaseVo ouptAppoint;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "468ce217-ef46-4e29-86ad-7a3e3e9fcd39")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "5057991c-e651-4336-92b7-069a4c2062dc")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "8fac9650-098e-49fb-9279-3ebe6441e36d")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "04594bf1-f314-4784-9a98-68c49a2e5c9a")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "753df4ce-1cc2-457c-9529-e4bc7ba937e8")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "fef8bfc1-9424-4cad-8a4e-67f2e5859c6a")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "e272e0da-944c-4786-88c0-ae921a251941")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "65171888-803d-4479-813f-be7a4126bd1a")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "d382d52d-42a7-48aa-b8a7-c20161e3ab1d")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "aeade8c5-4cc6-4b75-8367-89cc1800f2dd")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "e7f96665-8a09-4502-b74c-a3be23648780")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "c6bd3148-bf63-4e33-a4b7-11dd691a5329")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "e41b7f59-f3e2-4635-a09a-0cab09e94998")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "2adcafdd-8f82-4417-a450-b537e888ec45")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "cd8f7e8e-6da1-479c-9897-179bdf13bc24")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "cf90281c-bf5d-4779-9bf5-930f32523bcf")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "d358e041-59d1-45e7-a60b-fce523be4d13")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "e1e04a3f-858b-4cdb-83e4-8f11a03685ec")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "1a4ac70f-0022-4e8e-bc4e-1722aaa582fe")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "dd52a42f-aa1e-4be7-a39a-4d00a6301e9f")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "6977703b-fc07-4087-b4f4-cc50e6a72285")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "de9627cf-96e6-42a2-ad45-3ab5433c0117")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "9892e787-001c-4dca-8435-0eb91f20bb7d")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "85ce38a7-dd16-499e-942f-7a0dcecedb94")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "64948bd4-f127-467c-87a6-87fe365fb97f")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "2d9c8518-fda0-4131-afd5-3de4b603cc95")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "5ad3795c-8577-4edc-8ab4-ed36a74720ac")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "6c40d9ac-3f3d-4ba7-9d58-d7516b6d2078")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "82f1137f-1f02-4f74-befb-759e0c13c8cb")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "5b5f849b-d3a0-4e64-aaa8-053adc444958")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "7208788b-ee28-4b6c-a57b-808603c3e14d")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f68a6c29-1e1d-4609-b3a7-e3709a6dbbb3")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0aa72c06-dea3-4159-9b14-fdb7df5fa654")
    private TimeEo waitingStartTime;
}
