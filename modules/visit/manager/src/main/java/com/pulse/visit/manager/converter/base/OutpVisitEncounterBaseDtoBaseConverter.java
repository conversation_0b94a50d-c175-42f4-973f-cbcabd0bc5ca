package com.pulse.visit.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.visit.manager.dto.OutpVisitEncounterBaseDto;
import com.pulse.visit.persist.dos.OutpVisitEncounter;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "277feae8-243b-4101-b7ca-9f39db51a407|DTO|BASE_CONVERTER")
public class OutpVisitEncounterBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OutpVisitEncounterBaseDto convertFromOutpVisitEncounterToOutpVisitEncounterBaseDto(
            OutpVisitEncounter outpVisitEncounter) {
        return convertFromOutpVisitEncounterToOutpVisitEncounterBaseDto(List.of(outpVisitEncounter))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OutpVisitEncounterBaseDto> convertFromOutpVisitEncounterToOutpVisitEncounterBaseDto(
            List<OutpVisitEncounter> outpVisitEncounterList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpVisitEncounterList)) {
            return new ArrayList<>();
        }
        List<OutpVisitEncounterBaseDto> outpVisitEncounterBaseDtoList = new ArrayList<>();
        for (OutpVisitEncounter outpVisitEncounter : outpVisitEncounterList) {
            if (outpVisitEncounter == null) {
                continue;
            }
            OutpVisitEncounterBaseDto outpVisitEncounterBaseDto = new OutpVisitEncounterBaseDto();
            outpVisitEncounterBaseDto.setId(outpVisitEncounter.getId());
            outpVisitEncounterBaseDto.setOutpVisitId(outpVisitEncounter.getOutpVisitId());
            outpVisitEncounterBaseDto.setPatientId(outpVisitEncounter.getPatientId());
            outpVisitEncounterBaseDto.setOutpVisitEncounterId(
                    outpVisitEncounter.getOutpVisitEncounterId());
            outpVisitEncounterBaseDto.setEncounterTimes(outpVisitEncounter.getEncounterTimes());
            outpVisitEncounterBaseDto.setVisitDepartmentId(
                    outpVisitEncounter.getVisitDepartmentId());
            outpVisitEncounterBaseDto.setVisitStartDate(outpVisitEncounter.getVisitStartDate());
            outpVisitEncounterBaseDto.setVisitEndDate(outpVisitEncounter.getVisitEndDate());
            outpVisitEncounterBaseDto.setEncounterDoctorId(
                    outpVisitEncounter.getEncounterDoctorId());
            outpVisitEncounterBaseDto.setClinicEncounterStatus(
                    outpVisitEncounter.getClinicEncounterStatus());
            outpVisitEncounterBaseDto.setGroupCode(outpVisitEncounter.getGroupCode());
            outpVisitEncounterBaseDto.setDoctorTitle(outpVisitEncounter.getDoctorTitle());
            outpVisitEncounterBaseDto.setCreatedAt(outpVisitEncounter.getCreatedAt());
            outpVisitEncounterBaseDto.setUpdatedAt(outpVisitEncounter.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            outpVisitEncounterBaseDtoList.add(outpVisitEncounterBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return outpVisitEncounterBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
