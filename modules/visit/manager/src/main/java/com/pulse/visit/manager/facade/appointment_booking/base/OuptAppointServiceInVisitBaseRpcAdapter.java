package com.pulse.visit.manager.facade.appointment_booking.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.appointment_booking.service.bto.CreateOuptAppointBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "3850b4a9-b28e-30ed-a02e-2f4872882174")
public class OuptAppointServiceInVisitBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    /** 保存oupt_appoint预约表信息 */
    @AutoGenerated(locked = true, uuid = "5bc453fa-fceb-49eb-a27c-79308c0d1041|RPC|BASE_ADAPTER")
    public Long saveOuptAppoint(CreateOuptAppointBto createOuptAppointBto) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("create_oupt_appoint_bto", createOuptAppointBto);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("create_oupt_appoint_bto", CreateOuptAppointBto.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/5bc453fa-fceb-49eb-a27c-79308c0d1041/OuptAppointService-saveOuptAppoint",
                        "com.pulse.appointment_booking.service.OuptAppointService",
                        "saveOuptAppoint",
                        paramMap,
                        paramTypeMap,
                        "20a9027a-a071-49bb-93db-29c8446b432c",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }
}
