package com.pulse.visit.manager.facade.appointment_booking;

import com.pulse.appointment_booking.manager.dto.OutpRegisterAppointDto;
import com.pulse.visit.manager.facade.appointment_booking.base.OutpRegisterAppointDtoServiceInVisitBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "20a9027a-a071-49bb-93db-29c8446b432c")
@AutoGenerated(locked = false, uuid = "c79f7776-1f4f-341e-8e49-b7734f3e05aa")
public class OutpRegisterAppointDtoServiceInVisitRpcAdapter
        extends OutpRegisterAppointDtoServiceInVisitBaseRpcAdapter {

    @RpcRefer(id = "4b7f1588-861f-4b2a-bdb0-ca67c2f70906", version = "1749000741474")
    @AutoGenerated(locked = false, uuid = "4b7f1588-861f-4b2a-bdb0-ca67c2f70906|RPC|ADAPTER")
    public OutpRegisterAppointDto getById(String id) {
        return super.getById(id);
    }

    @RpcRefer(id = "b3d98e91-e93f-44aa-acac-7a91e36c72e6", version = "1749000741485")
    @AutoGenerated(locked = false, uuid = "b3d98e91-e93f-44aa-acac-7a91e36c72e6|RPC|ADAPTER")
    public List<OutpRegisterAppointDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
