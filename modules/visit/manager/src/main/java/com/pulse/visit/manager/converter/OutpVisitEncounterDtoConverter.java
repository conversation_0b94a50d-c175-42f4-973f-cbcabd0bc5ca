package com.pulse.visit.manager.converter;

import com.pulse.visit.manager.converter.base.OutpVisitEncounterDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "642b3e57-8d19-46c9-be16-dade9db7d71e|DTO|CONVERTER")
public class OutpVisitEncounterDtoConverter extends OutpVisitEncounterDtoBaseConverter {}
