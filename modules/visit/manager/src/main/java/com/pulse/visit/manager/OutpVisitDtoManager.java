package com.pulse.visit.manager;

import com.pulse.visit.manager.dto.OutpVisitDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "d5689e12-5788-40ad-94c0-5bb3a3b3dacc|DTO|MANAGER")
public interface OutpVisitDtoManager {

    @AutoGenerated(locked = true, uuid = "19d2e6e1-b7eb-330b-9855-1280c54951e8")
    OutpVisitDto getById(String id);

    @AutoGenerated(locked = true, uuid = "652ea294-2e81-3504-af95-634b9c323457")
    List<OutpVisitDto> getByPatientIds(List<String> patientId);

    @AutoGenerated(locked = true, uuid = "9bd4de06-aa4d-364c-b959-0ab296fa8155")
    List<OutpVisitDto> getByOutpRegisterIds(List<String> outpRegisterId);

    @AutoGenerated(locked = true, uuid = "9bd59a32-1a71-3265-95d7-90517ebc02aa")
    List<OutpVisitDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "c83eb67c-3749-3df4-a709-53f986902c34")
    List<OutpVisitDto> getByPatientId(String patientId);

    @AutoGenerated(locked = true, uuid = "fcd30fa8-43ba-38e9-9a2b-299c4a36c262")
    List<OutpVisitDto> getByOutpRegisterId(String outpRegisterId);
}
