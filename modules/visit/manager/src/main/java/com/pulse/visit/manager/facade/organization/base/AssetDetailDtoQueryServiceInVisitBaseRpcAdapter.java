package com.pulse.visit.manager.facade.organization.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.organization.manager.dto.AssetDetailDto;
import com.pulse.organization.persist.qto.SearchAssetQto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "b0d11baa-3241-3b0b-9c52-752672b94f39")
public class AssetDetailDtoQueryServiceInVisitBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "9f791ae9-552a-4f30-bf0f-fb983ffed122|RPC|BASE_ADAPTER")
    public List<AssetDetailDto> searchAsset(SearchAssetQto qto) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("qto", qto);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("qto", SearchAssetQto.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/organization/9f791ae9-552a-4f30-bf0f-fb983ffed122/AssetDetailDtoQueryService-searchAsset",
                        "com.pulse.organization.service.query.AssetDetailDtoQueryService",
                        "searchAsset",
                        paramMap,
                        paramTypeMap,
                        "20a9027a-a071-49bb-93db-29c8446b432c",
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c"),
                new TypeReference<>() {});
    }
}
