package com.pulse.visit.manager.facade.appointment_booking;

import com.pulse.appointment_booking.manager.dto.OutpRegisterVisitDto;
import com.pulse.visit.manager.facade.appointment_booking.base.OutpRegisterVisitDtoServiceInVisitBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "20a9027a-a071-49bb-93db-29c8446b432c")
@AutoGenerated(locked = false, uuid = "7941067a-de30-3424-a2be-15900b5adcfb")
public class OutpRegisterVisitDtoServiceInVisitRpcAdapter
        extends OutpRegisterVisitDtoServiceInVisitBaseRpcAdapter {

    @RpcRefer(id = "2b918744-9791-4da2-8e3d-83af6b532a36", version = "1749000741592")
    @AutoGenerated(locked = false, uuid = "2b918744-9791-4da2-8e3d-83af6b532a36|RPC|ADAPTER")
    public OutpRegisterVisitDto getById(String id) {
        return super.getById(id);
    }

    @RpcRefer(id = "e789c8ca-329f-476c-8de7-23d5c00104a3", version = "1749000741611")
    @AutoGenerated(locked = false, uuid = "e789c8ca-329f-476c-8de7-23d5c00104a3|RPC|ADAPTER")
    public List<OutpRegisterVisitDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
