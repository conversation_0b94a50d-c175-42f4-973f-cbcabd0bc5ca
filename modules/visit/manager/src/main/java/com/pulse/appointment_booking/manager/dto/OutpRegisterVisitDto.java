package com.pulse.appointment_booking.manager.dto;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.pulse.visit.manager.dto.OutpVisitEncounterDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "c3813169-1d4b-49e4-8f2b-d96491e257c2|DTO|DEFINITION")
public class OutpRegisterVisitDto {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d2bb50d9-f58a-4645-a36b-837a575fe0e7")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "3180ffd7-675c-4375-9b7b-dffaf02317da")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "7a160556-c826-4794-8f3e-6e29db75c65d")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "c98195c2-5237-4cb8-a6c8-8ab87065e737")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "b458c7db-1975-4359-bb2b-e463f1aff4fb")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "a22146d5-83d5-407a-bec8-9a75723021d8")
    private String chargeTypeCode;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "474ad2d2-dd80-44e1-98d4-ecb5c45646c9")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "8de685d3-3b39-4952-90a9-ca1392a462d4")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "d984c7b1-514d-41d8-a63d-d03b29a388bf")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "65f830aa-057f-4221-ba33-3ea1ef346807")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "36eb0569-f599-4e97-b097-182a59007032")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9787437b-4149-41d6-8186-bd052d05df02")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "03dec0a2-**************-7fb72b86d967")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "49a38045-f2fe-4702-8ee5-96d4e239e2ca")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "028350ab-7e35-4ae7-a8e6-7111c34a70cd")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "634975e2-58b1-44e1-975a-ff4480b59f8f")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "27c34cbf-475c-463f-a7a4-8860e24bb263")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "e9c2555f-fb24-475d-a69e-7938a39dd5a1")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "8d7eb011-ed6e-454c-aecc-c274c5bda79a")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "87217477-5eb4-4699-a9c9-38605c3f17d9")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "a87497b0-9b25-4d02-962d-d22569eacceb")
    private String outpAppointId;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2f964027-5fc7-4a49-8019-dff6cd0baec5")
    @NotNull(message = "主键不能为空")
    private List<OutpVisitEncounterDto> outpVisitEncounterList;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "ce04ba46-feb6-4281-a7eb-2adfa5dd7a37")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "538f81ca-ece0-4808-b0f0-a316f9c8732c")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "94d79f03-87fd-4842-914f-9cfb5d6e810b")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "88c3c410-aaf5-450c-b73a-78a76cdb2350")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "10efff5e-c8ae-45e1-b9f1-fd0a2b29e8d0")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "02873fa7-1e1a-4855-a902-fd5cbf27e06f")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "b07bb217-49f9-4387-b14a-a5cdda813d23")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "551398b7-d98d-45bd-9644-19602c59bfc5")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "af795c71-184b-493a-9847-6263973f2d2e")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "12a29250-faf0-41eb-ace8-8abcccd8fb43")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "9dc8d838-a6e2-4716-a47c-a0e49272a495")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "879d7ad2-234c-413f-ab28-9daafb7c43a0")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "8bf095bd-8e11-4b12-984e-dd7ff2adb8f2")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "0199f994-b87c-4b4e-8cf1-7e257846290f")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "5d8a33db-a2d4-4155-b5e3-642cba8ef8b1")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "6339752b-a964-405d-95b4-9a210bac73ea")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "8e7033c4-cdca-4a56-ad3d-3bdead6bfc98")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "3d233666-8491-4705-a125-f0e240c70534")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "bcb2094b-cc05-4b0e-8202-f5f493bd1a9d")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "e72dfb26-f03d-4877-812c-bfb2b9f99593")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "aa8cedab-1077-4517-991f-17e34be92839")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "51c3bde4-23f6-4369-9a20-46ec772ec3af")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "3fab8db2-4544-4975-8d11-321cead7ea7c")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "*************-431f-b769-3c3cabafaa32")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "07018004-0a29-481b-9dea-bc6e52db12a8")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "725a9209-c7af-49b1-b74e-88e28d33a921")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "52f41766-b2ce-4f77-aee8-11099616c274")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "2b3dec08-da88-4dd2-9a25-1c9a148f9939")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "a909d32a-1d58-49a6-b26b-410538d0d1b8")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "c061af72-5c79-4c94-9bb4-6ec8135cc015")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "184a1404-d5c5-4da3-a3dd-d9ca71bcc0e8")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "16572d21-74ff-409e-a3ab-447c58da586c")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "5fe87219-38fd-46b7-a414-510203ba8ac4")
    private TimeEo waitingStartTime;
}
