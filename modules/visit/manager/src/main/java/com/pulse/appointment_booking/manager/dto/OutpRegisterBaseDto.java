package com.pulse.appointment_booking.manager.dto;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "0ffd3ab4-3d96-455b-af1a-511e22420b0f|DTO|DEFINITION")
public class OutpRegisterBaseDto {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2dcf816a-358e-49cf-ac02-67963ab8493c")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "14277aa0-4739-45d6-8385-696688dfc1c2")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "36e49468-986d-4069-9309-aaf23cfc59be")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "6ceeb460-5a4b-4317-80dd-e42c91b86f48")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "0a95b1aa-8027-49b7-a657-d4658ab51d55")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "b2cca418-47cf-41db-8d40-92100d988e76")
    private String chargeTypeCode;

    /** 挂号类别ID */
    @AutoGenerated(locked = true, uuid = "0a2bf83e-ef0a-4c69-bf40-c467b0b7010a")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "475904d8-7ad5-485a-9996-e61abaa1597f")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "8068a034-deb6-406c-93bb-bed854e39c56")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "40fb36d3-eb4f-45af-8e47-844596a97daa")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "6f887636-a6a7-4995-9dc2-fb2d75d910ce")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "fba387c9-dc80-4729-a085-5006f0190fcd")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "b15a93bc-a510-4750-840c-e77a36a9b49a")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "b02aa0ef-0c98-4cfe-9f91-54eb4a1c61f4")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "c44a1921-b82b-4b15-b65a-1f70fa60930b")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "0a9bfae9-a4e7-492c-af8d-c765659b41f9")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "30a65aaa-0e17-44eb-9d53-b2a361336f9c")
    private String internationalPatientFee;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "30b6be62-865b-43ab-ba7f-53485d33eee2")
    private Long lockVersion;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "6d011b58-4c4d-44fe-a96b-a8434c3b519e")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "8061d6b3-6667-444f-a64b-e98938395f63")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "85a1138d-1e59-47f2-a2fb-bc06000007b5")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "25c9f1e8-953b-4743-a762-0db3f5e948e1")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "19eb708e-140e-4e96-95f6-c395df0e40bf")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "bf9fe86b-3d17-452f-927f-525097f53c5c")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "85ff8bb9-a2de-432d-b8f2-2652e74e9dda")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "9d3ecdce-7feb-47a3-8fe1-c4a3a894082b")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "b884dcb8-92c4-49c9-a48a-b6e1e1e7b902")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "66f4aa1d-38a5-4641-a22d-55c6d0efc5f0")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "14f01f49-60d3-480d-b813-9d8510e6f161")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "a353d779-e39d-4899-84a8-9bf1aedcb62a")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "2fb812fd-13b8-4212-89e4-240a63cb67ca")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "9ff81dd9-1df8-4dc1-816d-73b4aeb647d8")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "*************-4882-8d9c-259146dda8fd")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "e72a284a-f74a-423f-b940-436d062e960f")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "71e6b39d-fa39-400d-82b5-3f0ce9a0876d")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "723b6d1c-b8fe-470a-bfdd-69f1d7bf703d")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "27f8ba5f-9c60-49dd-bd3b-35363f3974a7")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "a070c89e-165c-4d13-a77c-d6de0d46bd16")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "91d854d5-916c-41f6-9575-70e517d5f5a6")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "189627a1-8660-47ff-8267-1ebd330660a5")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "6eb8c212-b34a-4a2d-aed3-c0361410886a")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "26120de1-aff5-4420-a421-14e9af1c95a6")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "4671ddff-a3de-4bef-9db8-1c50d12aa7d1")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "b3379139-ee99-47ce-8429-356871d227e0")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "f368c4c1-2536-4c65-87f1-49ab192ab309")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "d81c1e50-b54c-4d75-98ed-3f80e836b2a5")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "61e58458-eaed-452f-b801-f586c8e87f11")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "84652204-9c6f-4cb8-b12f-7c483f94071c")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "03b8c9ff-cb0c-4dd0-a66f-b69bc15b2152")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "975082db-13aa-406e-8a12-bc1ab89e1a1d")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "35c55a23-0e28-4449-bb1c-95b961fc86d9")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "9a239981-b4e9-4ec2-8777-a9aca96f78e9")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "0787b4be-b885-4901-85ad-260b5c29131f")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4e2961b7-ca1f-4597-83e4-c92fc32a3425")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "93ca40c2-8933-41a0-b505-32b7732498b8")
    private TimeEo waitingStartTime;
}
