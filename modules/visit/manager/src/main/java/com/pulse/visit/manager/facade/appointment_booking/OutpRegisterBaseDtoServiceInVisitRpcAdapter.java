package com.pulse.visit.manager.facade.appointment_booking;

import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.visit.manager.facade.appointment_booking.base.OutpRegisterBaseDtoServiceInVisitBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "20a9027a-a071-49bb-93db-29c8446b432c")
@AutoGenerated(locked = false, uuid = "b61d2bdb-9f0a-3fe1-86e8-f7c4143de805")
public class OutpRegisterBaseDtoServiceInVisitRpcAdapter
        extends OutpRegisterBaseDtoServiceInVisitBaseRpcAdapter {

    @RpcRefer(id = "3db0cabd-e2b5-4046-a19a-e2ab0d813ee6", version = "1749000741522")
    @AutoGenerated(locked = false, uuid = "3db0cabd-e2b5-4046-a19a-e2ab0d813ee6|RPC|ADAPTER")
    public List<OutpRegisterBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }

    @RpcRefer(id = "71dff654-93d0-4af0-8af7-73945db50fc9", version = "1749000741513")
    @AutoGenerated(locked = false, uuid = "71dff654-93d0-4af0-8af7-73945db50fc9|RPC|ADAPTER")
    public OutpRegisterBaseDto getById(String id) {
        return super.getById(id);
    }
}
