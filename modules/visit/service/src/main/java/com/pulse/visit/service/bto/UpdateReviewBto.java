package com.pulse.visit.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> OutpVisit
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "98db24cf-7de1-41bc-8dcb-27b563b1481b|BTO|DEFINITION")
public class UpdateReviewBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 费用分类 */
    @AutoGenerated(locked = true, uuid = "4d45ecdd-4573-4d0b-b8b3-ffaedab48e62")
    private String costCategories;

    /** 费用性质 */
    @AutoGenerated(locked = true, uuid = "cbd4832b-d654-4b72-ba5f-650b07b8243f")
    private String costNature;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9e152483-6e42-4e31-b97f-54a1d62a438f")
    private String id;

    /** 挂号ID */
    @AutoGenerated(locked = true, uuid = "28a6c4a1-7c43-4673-90c4-604de4fa300a")
    private String outpRegisterId;

    /** 就诊科室ID */
    @AutoGenerated(locked = true, uuid = "5e2a3ad2-bfa1-433b-9380-14c334b03b8e")
    private String visitDepartmentId;

    /** 就诊医生ID */
    @AutoGenerated(locked = true, uuid = "7badb89e-cf9a-4682-bf31-f7f8cbd290e0")
    private String visitDoctorId;

    @AutoGenerated(locked = true)
    public void setCostCategories(String costCategories) {
        this.__$validPropertySet.add("costCategories");
        this.costCategories = costCategories;
    }

    @AutoGenerated(locked = true)
    public void setCostNature(String costNature) {
        this.__$validPropertySet.add("costNature");
        this.costNature = costNature;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setOutpRegisterId(String outpRegisterId) {
        this.__$validPropertySet.add("outpRegisterId");
        this.outpRegisterId = outpRegisterId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDepartmentId(String visitDepartmentId) {
        this.__$validPropertySet.add("visitDepartmentId");
        this.visitDepartmentId = visitDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDoctorId(String visitDoctorId) {
        this.__$validPropertySet.add("visitDoctorId");
        this.visitDoctorId = visitDoctorId;
    }
}
