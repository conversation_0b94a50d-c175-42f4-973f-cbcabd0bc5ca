package com.pulse.visit.service.converter;

import com.pulse.visit.manager.dto.OutpVisitRegisterAppointDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "23e68d7a-d455-31e6-9dba-13d8db8bd3da")
public class OutpVisitRegisterAppointDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OutpVisitRegisterAppointDto> OutpVisitRegisterAppointDtoConverter(
            List<OutpVisitRegisterAppointDto> outpVisitRegisterAppointDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return outpVisitRegisterAppointDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
