package com.pulse.visit.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.visit.persist.qto.SearchPatientVisitQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "67e1f2b5-143b-4042-b4cd-a2aab9d36e58|QTO|DAO")
public class SearchPatientVisitQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询病人就诊列表 */
    @AutoGenerated(locked = false, uuid = "67e1f2b5-143b-4042-b4cd-a2aab9d36e58-count")
    public Integer count(SearchPatientVisitQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(outp_visit.id) FROM outp_visit LEFT JOIN outp_register"
                    + " \"outpRegister\" on outp_visit.outp_register_id = \"outpRegister\".id LEFT"
                    + " JOIN outp_appoint \"outpRegister_ouptAppoint\" on"
                    + " \"outpRegister\".outp_appoint_id = \"outpRegister_ouptAppoint\".id WHERE"
                    + " outp_visit.visit_status = #visitStatusIs AND"
                    + " \"outpRegister\".time_description in #timeDescriptionIn AND"
                    + " \"outpRegister_ouptAppoint\".appointment_schedule_id in"
                    + " #appointmentScheduleIdIn AND"
                    + " \"outpRegister\".outpatient_registration_category = #registrationCategoryIs"
                    + " AND \"outpRegister\".registration_doctor_id = #doctorIdIs AND"
                    + " \"outpRegister\".register_status = 'REGISTERED' AND"
                    + " \"outpRegister\".registration_department_id = #departmentIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleIdIn())) {
            conditionToRemove.add("#appointmentScheduleIdIn");
        }
        if (qto.getRegistrationCategoryIs() == null) {
            conditionToRemove.add("#registrationCategoryIs");
        }
        if (CollectionUtil.isEmpty(qto.getTimeDescriptionIn())) {
            conditionToRemove.add("#timeDescriptionIn");
        }
        if (qto.getDepartmentIdIs() == null) {
            conditionToRemove.add("#departmentIdIs");
        }
        if (qto.getVisitStatusIs() == null) {
            conditionToRemove.add("#visitStatusIs");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#appointmentScheduleIdIn",
                                CollectionUtil.isEmpty(qto.getAppointmentScheduleIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleIdIn().size()))
                        .replace("#registrationCategoryIs", "?")
                        .replace(
                                "#timeDescriptionIn",
                                CollectionUtil.isEmpty(qto.getTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTimeDescriptionIn().size()))
                        .replace("#departmentIdIs", "?")
                        .replace("#visitStatusIs", "?")
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleIdIn")) {
                sqlParams.addAll(qto.getAppointmentScheduleIdIn());
            } else if (paramName.equalsIgnoreCase("#registrationCategoryIs")) {
                sqlParams.add(qto.getRegistrationCategoryIs());
            } else if (paramName.equalsIgnoreCase("#timeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#departmentIdIs")) {
                sqlParams.add(qto.getDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#visitStatusIs")) {
                sqlParams.add(qto.getVisitStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询病人就诊列表 */
    @AutoGenerated(locked = false, uuid = "67e1f2b5-143b-4042-b4cd-a2aab9d36e58-query-all")
    public List<String> query(SearchPatientVisitQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT outp_visit.id FROM outp_visit LEFT JOIN outp_register \"outpRegister\" on"
                    + " outp_visit.outp_register_id = \"outpRegister\".id LEFT JOIN outp_appoint"
                    + " \"outpRegister_ouptAppoint\" on \"outpRegister\".outp_appoint_id ="
                    + " \"outpRegister_ouptAppoint\".id WHERE outp_visit.visit_status ="
                    + " #visitStatusIs AND \"outpRegister\".time_description in #timeDescriptionIn"
                    + " AND \"outpRegister_ouptAppoint\".appointment_schedule_id in"
                    + " #appointmentScheduleIdIn AND"
                    + " \"outpRegister\".outpatient_registration_category = #registrationCategoryIs"
                    + " AND \"outpRegister\".registration_doctor_id = #doctorIdIs AND"
                    + " \"outpRegister\".register_status = 'REGISTERED' AND"
                    + " \"outpRegister\".registration_department_id = #departmentIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleIdIn())) {
            conditionToRemove.add("#appointmentScheduleIdIn");
        }
        if (qto.getRegistrationCategoryIs() == null) {
            conditionToRemove.add("#registrationCategoryIs");
        }
        if (CollectionUtil.isEmpty(qto.getTimeDescriptionIn())) {
            conditionToRemove.add("#timeDescriptionIn");
        }
        if (qto.getDepartmentIdIs() == null) {
            conditionToRemove.add("#departmentIdIs");
        }
        if (qto.getVisitStatusIs() == null) {
            conditionToRemove.add("#visitStatusIs");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#appointmentScheduleIdIn",
                                CollectionUtil.isEmpty(qto.getAppointmentScheduleIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleIdIn().size()))
                        .replace("#registrationCategoryIs", "?")
                        .replace(
                                "#timeDescriptionIn",
                                CollectionUtil.isEmpty(qto.getTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTimeDescriptionIn().size()))
                        .replace("#departmentIdIs", "?")
                        .replace("#visitStatusIs", "?")
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleIdIn")) {
                sqlParams.addAll(qto.getAppointmentScheduleIdIn());
            } else if (paramName.equalsIgnoreCase("#registrationCategoryIs")) {
                sqlParams.add(qto.getRegistrationCategoryIs());
            } else if (paramName.equalsIgnoreCase("#timeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#departmentIdIs")) {
                sqlParams.add(qto.getDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#visitStatusIs")) {
                sqlParams.add(qto.getVisitStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  \"outpRegister\".register_number asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
