package com.pulse.appointment_schedule.persist.mapper;

import com.pulse.appointment_schedule.persist.qto.SearchRegisterFeeQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "8c24e4b0-1356-4644-9103-4dcda2b71d59|QTO|DAO")
public class SearchRegisterFeeQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询挂号费 */
    @AutoGenerated(locked = false, uuid = "8c24e4b0-1356-4644-9103-4dcda2b71d59-count")
    public Integer count(SearchRegisterFeeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(clinic_register_type.id) FROM clinic_register_type WHERE"
                    + " clinic_register_type.cancel_flag = #cancelFlagIs AND"
                    + " clinic_register_type.clinic_register_type_id = #clinicRegisterTypeIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getClinicRegisterTypeIdIs() == null) {
            conditionToRemove.add("#clinicRegisterTypeIdIs");
        }
        if (qto.getCancelFlagIs() == null) {
            conditionToRemove.add("#cancelFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#clinicRegisterTypeIdIs", "?").replace("#cancelFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIs")) {
                sqlParams.add(qto.getClinicRegisterTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#cancelFlagIs")) {
                sqlParams.add(qto.getCancelFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询挂号费 */
    @AutoGenerated(locked = false, uuid = "8c24e4b0-1356-4644-9103-4dcda2b71d59-query-all")
    public List<String> query(SearchRegisterFeeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT clinic_register_type.id FROM clinic_register_type WHERE"
                    + " clinic_register_type.cancel_flag = #cancelFlagIs AND"
                    + " clinic_register_type.clinic_register_type_id = #clinicRegisterTypeIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getClinicRegisterTypeIdIs() == null) {
            conditionToRemove.add("#clinicRegisterTypeIdIs");
        }
        if (qto.getCancelFlagIs() == null) {
            conditionToRemove.add("#cancelFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#clinicRegisterTypeIdIs", "?").replace("#cancelFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIs")) {
                sqlParams.add(qto.getClinicRegisterTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#cancelFlagIs")) {
                sqlParams.add(qto.getCancelFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  clinic_register_type.created_at asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
