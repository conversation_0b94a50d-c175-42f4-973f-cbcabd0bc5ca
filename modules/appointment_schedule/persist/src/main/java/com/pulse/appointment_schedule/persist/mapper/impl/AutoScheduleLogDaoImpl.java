package com.pulse.appointment_schedule.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.appointment_schedule.persist.dos.AutoScheduleLog;
import com.pulse.appointment_schedule.persist.mapper.AutoScheduleLogDao;
import com.pulse.appointment_schedule.persist.mapper.mybatis.AutoScheduleLogMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "11cea3e3-10e0-4ac3-9ea1-8f7fb604c39b|ENTITY|DAO")
public class AutoScheduleLogDaoImpl implements AutoScheduleLogDao {
    @AutoGenerated(locked = true)
    @Resource
    private AutoScheduleLogMapper autoScheduleLogMapper;

    @AutoGenerated(locked = true, uuid = "4efb30c5-8019-3d08-8286-5d940efed508")
    @Override
    public AutoScheduleLog getById(Long id) {
        QueryWrapper<AutoScheduleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return autoScheduleLogMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "fdc07537-e940-3c5f-933b-5cd157432933")
    @Override
    public List<AutoScheduleLog> getByIds(List<Long> id) {
        QueryWrapper<AutoScheduleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return autoScheduleLogMapper.selectList(queryWrapper);
    }
}
