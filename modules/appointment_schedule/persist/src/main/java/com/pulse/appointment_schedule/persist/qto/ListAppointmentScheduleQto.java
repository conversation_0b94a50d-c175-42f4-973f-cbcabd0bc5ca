package com.pulse.appointment_schedule.persist.qto;

import com.pulse.appointment_schedule.common.enums.SchedulingstatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "e82ead49-cec4-4062-8c8d-68164784045e|QTO|DEFINITION")
public class ListAppointmentScheduleQto {
    /** 院区id appointment_schedule.branch_institution_id */
    @AutoGenerated(locked = true, uuid = "97c063c5-3a5f-47bf-8823-4bb19e4983d0")
    private String branchInstitutionIdIs;

    /** 挂号类别id appointment_schedule.clinic_register_type_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "a063bdc9-90cf-4737-b262-2ef7ff3fcd39")
    private List<String> clinicRegisterTypeIdIn;

    /** 星期 appointment_schedule.day_of_week */
    @Valid
    @AutoGenerated(locked = true, uuid = "4cbbe08c-debc-489f-a5db-02c006cef022")
    private List<String> dayOfWeekIn;

    /** 科室id appointment_schedule.department_id */
    @AutoGenerated(locked = true, uuid = "b8a90729-3d2c-47b9-95a3-4c484467187a")
    private String departmentIdIs;

    /** 医生id appointment_schedule.doctor_id */
    @AutoGenerated(locked = true, uuid = "11445073-7969-48ac-a30d-72ec4155d535")
    private String doctorIdIs;

    @AutoGenerated(locked = true, uuid = "bfced547-04d0-444c-a8e2-d993ee5c6841")
    private Integer from;

    /** 排班日期 appointment_schedule.register_time */
    @AutoGenerated(locked = true, uuid = "509a5a70-2844-41ee-a5d1-ab69b7c95826")
    private Date registerTimeBiggerThanEqual;

    /** 排班日期 appointment_schedule.register_time */
    @AutoGenerated(locked = true, uuid = "*************-4bd6-ba9d-06457b825e2b")
    private Date registerTimeLessThanEqual;

    /** 审核标志 appointment_schedule.review_flag */
    @AutoGenerated(locked = true, uuid = "f8f5ab7e-e804-44a0-b111-b551c1ef71a8")
    private Boolean reviewFlagIs;

    @AutoGenerated(locked = true, uuid = "93720d09-100e-49af-86bc-e00e1311fe11")
    private String scrollId;

    @AutoGenerated(locked = true, uuid = "58de9e9d-70d2-4e56-86b9-ebd911774212")
    private Integer size;

    /** 状态 appointment_schedule.status */
    @Valid
    @AutoGenerated(locked = true, uuid = "8c7bc225-e8e2-417c-8858-6352a310338e")
    private List<SchedulingstatusEnum> statusIn;

    /** 午别 appointment_schedule.time_description */
    @AutoGenerated(locked = true, uuid = "ac180078-2f65-463a-88e3-46004b13f132")
    private TimeDescriptionEnum timeDescriptionIs;
}
