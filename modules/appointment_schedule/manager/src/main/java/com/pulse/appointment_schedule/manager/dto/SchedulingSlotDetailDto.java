package com.pulse.appointment_schedule.manager.dto;

import com.pulse.appointment_schedule.common.enums.AppointmentStatusEnum;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "ff7c03a1-ee07-40d6-b1b5-776d57873908|DTO|DEFINITION")
public class SchedulingSlotDetailDto {
    /** 预约渠道ID */
    @AutoGenerated(locked = true, uuid = "731adb49-1584-485e-91de-d9175ddf6f67")
    private String appointmentCategoryId;

    /** 排班ID */
    @AutoGenerated(locked = true, uuid = "2b160c79-f8de-4a4c-9108-95e2259e0157")
    private String appointmentScheduleId;

    /** 号源状态 */
    @AutoGenerated(locked = true, uuid = "b5cc2419-46c8-4a57-99ee-99eae7ffb645")
    private AppointmentStatusEnum appointmentStatus;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b3fe7358-2bd6-4879-8847-cd2ae5134fea")
    private TimeEo endTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "e3a3760e-4382-478d-9c84-f401de601ad7")
    private String id;

    /** 计划标志 */
    @AutoGenerated(locked = true, uuid = "6adf6d80-a0e8-45a8-886f-a6db9196719d")
    private Boolean planFlag;

    /** 排班日期 */
    @AutoGenerated(locked = true, uuid = "67b29e66-d049-4279-b4c6-76df3aa6b8fe")
    private Date registerTime;

    /** 排班计划ID */
    @AutoGenerated(locked = true, uuid = "a2b3e14e-0836-43db-a873-782f6e672553")
    private String schedulePlanId;

    /** 号序 */
    @AutoGenerated(locked = true, uuid = "af7ea350-6379-4c7e-95de-2ac1b27cccd6")
    private Long scheduleSlotNumber;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a067f4f9-e7d3-413c-9e0e-cbced13bbc01")
    private TimeEo startTime;
}
