package com.pulse.appointment_schedule.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.SchedulePlanBaseDtoManager;
import com.pulse.appointment_schedule.manager.converter.SchedulePlanBaseDtoConverter;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanBaseDto;
import com.pulse.appointment_schedule.persist.dos.SchedulePlan;
import com.pulse.appointment_schedule.persist.mapper.SchedulePlanDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "f70412fb-41a8-469a-95d3-3ae520e06a71|DTO|BASE_MANAGER_IMPL")
public abstract class SchedulePlanBaseDtoManagerBaseImpl implements SchedulePlanBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private SchedulePlanBaseDtoConverter schedulePlanBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private SchedulePlanDao schedulePlanDao;

    @AutoGenerated(locked = true, uuid = "059c9667-8f6f-33d6-90af-ed2118d1bc35")
    @Override
    public List<SchedulePlanBaseDto> getByRoomId(String roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SchedulePlanBaseDto> schedulePlanBaseDtoList = getByRoomIds(Arrays.asList(roomId));
        return schedulePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "25cfb971-3e60-34cf-9286-998b2907daee")
    @Override
    public List<SchedulePlanBaseDto> getByRoomIds(List<String> roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(roomId)) {
            return Collections.emptyList();
        }

        List<SchedulePlan> schedulePlanList = schedulePlanDao.getByRoomIds(roomId);
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2aa04ec8-5a8c-3dab-ac02-f1e21bd95a27")
    public List<SchedulePlanBaseDto> doConvertFromSchedulePlanToSchedulePlanBaseDto(
            List<SchedulePlan> schedulePlanList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        Map<String, SchedulePlanBaseDto> dtoMap =
                schedulePlanBaseDtoConverter
                        .convertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        SchedulePlanBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<SchedulePlanBaseDto> schedulePlanBaseDtoList = new ArrayList<>();
        for (SchedulePlan i : schedulePlanList) {
            SchedulePlanBaseDto schedulePlanBaseDto = dtoMap.get(i.getId());
            if (schedulePlanBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            schedulePlanBaseDtoList.add(schedulePlanBaseDto);
        }
        return schedulePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "431f09c1-304e-3b21-8189-8fa2263865f6")
    @Override
    public List<SchedulePlanBaseDto> getByDepartmentId(String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SchedulePlanBaseDto> schedulePlanBaseDtoList =
                getByDepartmentIds(Arrays.asList(departmentId));
        return schedulePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "58376b8d-edc6-34a6-b1ce-d1629a483475")
    @Override
    public List<SchedulePlanBaseDto> getByDoctorId(String doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SchedulePlanBaseDto> schedulePlanBaseDtoList = getByDoctorIds(Arrays.asList(doctorId));
        return schedulePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "*************-337f-8102-499e971c7f35")
    @Override
    public List<SchedulePlanBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<SchedulePlan> schedulePlanList = schedulePlanDao.getByIds(id);
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, SchedulePlan> schedulePlanMap =
                schedulePlanList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        schedulePlanList =
                id.stream()
                        .map(i -> schedulePlanMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "940ac8ea-5768-357c-8791-b7b3000dde29")
    @Override
    public List<SchedulePlanBaseDto> getByClinicRegisterTypeId(String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SchedulePlanBaseDto> schedulePlanBaseDtoList =
                getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        return schedulePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a90502f9-0ab5-328f-833e-6e191bb66944")
    @Override
    public List<SchedulePlanBaseDto> getByDepartmentIds(List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(departmentId)) {
            return Collections.emptyList();
        }

        List<SchedulePlan> schedulePlanList = schedulePlanDao.getByDepartmentIds(departmentId);
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b49757e0-08a1-3e96-b894-efcc513ebc9e")
    @Override
    public List<SchedulePlanBaseDto> getBySchedulingTemplateId(String schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SchedulePlanBaseDto> schedulePlanBaseDtoList =
                getBySchedulingTemplateIds(Arrays.asList(schedulingTemplateId));
        return schedulePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b6a4d782-9a61-3fbf-a7a9-b4f5f257d680")
    @Override
    public List<SchedulePlanBaseDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicRegisterTypeId)) {
            return Collections.emptyList();
        }

        List<SchedulePlan> schedulePlanList =
                schedulePlanDao.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d277e959-043c-380a-b3eb-538deb19c35b")
    @Override
    public List<SchedulePlanBaseDto> getByDoctorIds(List<String> doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(doctorId)) {
            return Collections.emptyList();
        }

        List<SchedulePlan> schedulePlanList = schedulePlanDao.getByDoctorIds(doctorId);
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f0ce4bd9-78f9-3f35-a1ba-6ffa724e186e")
    @Override
    public SchedulePlanBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SchedulePlanBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        SchedulePlanBaseDto schedulePlanBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return schedulePlanBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ff4e8694-b894-3788-889e-25de272f6f09")
    @Override
    public List<SchedulePlanBaseDto> getBySchedulingTemplateIds(List<String> schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulingTemplateId)) {
            return Collections.emptyList();
        }

        List<SchedulePlan> schedulePlanList =
                schedulePlanDao.getBySchedulingTemplateIds(schedulingTemplateId);
        if (CollectionUtil.isEmpty(schedulePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromSchedulePlanToSchedulePlanBaseDto(schedulePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
