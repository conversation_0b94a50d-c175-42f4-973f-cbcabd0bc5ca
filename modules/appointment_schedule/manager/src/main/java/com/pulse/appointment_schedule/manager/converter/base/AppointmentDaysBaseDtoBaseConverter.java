package com.pulse.appointment_schedule.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.dto.AppointmentDaysBaseDto;
import com.pulse.appointment_schedule.persist.dos.AppointmentDays;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "540857fa-f0b8-4287-97c2-5d3af6e18619|DTO|BASE_CONVERTER")
public class AppointmentDaysBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public AppointmentDaysBaseDto convertFromAppointmentDaysToAppointmentDaysBaseDto(
            AppointmentDays appointmentDays) {
        return convertFromAppointmentDaysToAppointmentDaysBaseDto(List.of(appointmentDays)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<AppointmentDaysBaseDto> convertFromAppointmentDaysToAppointmentDaysBaseDto(
            List<AppointmentDays> appointmentDaysList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(appointmentDaysList)) {
            return new ArrayList<>();
        }
        List<AppointmentDaysBaseDto> appointmentDaysBaseDtoList = new ArrayList<>();
        for (AppointmentDays appointmentDays : appointmentDaysList) {
            if (appointmentDays == null) {
                continue;
            }
            AppointmentDaysBaseDto appointmentDaysBaseDto = new AppointmentDaysBaseDto();
            appointmentDaysBaseDto.setId(appointmentDays.getId());
            appointmentDaysBaseDto.setAppointmentChannelId(
                    appointmentDays.getAppointmentChannelId());
            appointmentDaysBaseDto.setPlatformId(appointmentDays.getPlatformId());
            appointmentDaysBaseDto.setOpenDays(appointmentDays.getOpenDays());
            appointmentDaysBaseDto.setOpenTime(appointmentDays.getOpenTime());
            appointmentDaysBaseDto.setCreatedAt(appointmentDays.getCreatedAt());
            appointmentDaysBaseDto.setUpdatedAt(appointmentDays.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            appointmentDaysBaseDtoList.add(appointmentDaysBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return appointmentDaysBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
