package com.pulse.appointment_schedule.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.dto.BlacklistBaseDto;
import com.pulse.appointment_schedule.persist.dos.Blacklist;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "881537b1-c020-46b7-9c2b-524ca890816f|DTO|BASE_CONVERTER")
public class BlacklistBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public BlacklistBaseDto convertFromBlacklistToBlacklistBaseDto(Blacklist blacklist) {
        return convertFromBlacklistToBlacklistBaseDto(List.of(blacklist)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<BlacklistBaseDto> convertFromBlacklistToBlacklistBaseDto(
            List<Blacklist> blacklistList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(blacklistList)) {
            return new ArrayList<>();
        }
        List<BlacklistBaseDto> blacklistBaseDtoList = new ArrayList<>();
        for (Blacklist blacklist : blacklistList) {
            if (blacklist == null) {
                continue;
            }
            BlacklistBaseDto blacklistBaseDto = new BlacklistBaseDto();
            blacklistBaseDto.setId(blacklist.getId());
            blacklistBaseDto.setPatientId(blacklist.getPatientId());
            blacklistBaseDto.setPatientTag(blacklist.getPatientTag());
            blacklistBaseDto.setLimitTime(blacklist.getLimitTime());
            blacklistBaseDto.setLimitReason(blacklist.getLimitReason());
            blacklistBaseDto.setPersonType(blacklist.getPersonType());
            blacklistBaseDto.setBlacklistSource(blacklist.getBlacklistSource());
            blacklistBaseDto.setLockVersion(blacklist.getLockVersion());
            blacklistBaseDto.setCreatedAt(blacklist.getCreatedAt());
            blacklistBaseDto.setUpdatedAt(blacklist.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            blacklistBaseDtoList.add(blacklistBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return blacklistBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
