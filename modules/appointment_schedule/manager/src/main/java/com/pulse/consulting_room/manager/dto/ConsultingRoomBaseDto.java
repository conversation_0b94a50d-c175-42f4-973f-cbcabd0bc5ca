package com.pulse.consulting_room.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "3d84e795-2f80-46c7-87f8-d5ddfcdecd73|DTO|DEFINITION")
public class ConsultingRoomBaseDto {
    /** 诊室区域 */
    @AutoGenerated(locked = true, uuid = "09ef12a3-7226-4b11-a978-4c47644e93a2")
    private String area;

    /** 诊室区域英文名称 */
    @AutoGenerated(locked = true, uuid = "1890d5b0-bb39-4efd-9518-5b046e0f0fae")
    private String areaEnglishName;

    /** 院区组织id */
    @AutoGenerated(locked = true, uuid = "35e7f2eb-316e-4df6-8c22-839dee8b35cb")
    private String campusOrganizationId;

    /** 诊室门牌号 */
    @AutoGenerated(locked = true, uuid = "82cf2997-4694-43d0-b1e7-be63fb59ca5c")
    private String clinicRoomNumber;

    /** 诊室代码 */
    @AutoGenerated(locked = true, uuid = "509cdc03-287b-482b-a116-26cd53ffdf54")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "fb9eb8ef-b6ee-4819-88ac-a06a049665f6")
    private Date createdAt;

    /** 创建人id */
    @AutoGenerated(locked = true, uuid = "54547b8b-6f6d-445d-be2a-d9c80b274c64")
    private String createdBy;

    /** 诊室名称(患者) */
    @AutoGenerated(locked = true, uuid = "d9b5eb66-5d51-45ed-84df-1d4bf0ba4993")
    private String displayName;

    /** 楼层单元 */
    @AutoGenerated(locked = true, uuid = "ecf6d806-53cc-403b-8449-e754dbb77fb8")
    private String floorUnit;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "6044f446-db76-4775-886a-c83b6616c808")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "21be24c2-9187-43df-8ad0-c2d6e5b82054")
    private InputCodeEo inputCode;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "11ad3f03-2b0a-4b2c-942e-dfa8cdbe1347")
    private Boolean invalidFlag;

    /** 作废原因 */
    @AutoGenerated(locked = true, uuid = "14d402db-0f14-4e13-a767-82198b638aed")
    private String invalidReason;

    /** IP地址 */
    @AutoGenerated(locked = true, uuid = "19b9170a-7bf3-45d0-a64f-702b70a91209")
    private String ip;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "9b93c26c-e336-4b7f-953d-deaae873d529")
    private Long lockVersion;

    /** 诊室名称（排班） */
    @AutoGenerated(locked = true, uuid = "564572bf-ef92-435c-9a93-ea88015bd94c")
    private String name;

    /** 性质 */
    @AutoGenerated(locked = true, uuid = "3003fe45-8cd2-4106-94d5-cc7b839d8987")
    private String property;

    /** 简码 */
    @AutoGenerated(locked = true, uuid = "28f7eb0a-faf8-4b57-a68d-3edd61e99132")
    private String shortCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "a2cc4a3a-1f08-4f37-aa2f-a6c50dad33f1")
    private Date updatedAt;

    /** 修改者id */
    @AutoGenerated(locked = true, uuid = "3ffde5f2-1b14-45ac-80bd-7e9af3f0451e")
    private String updatedBy;
}
