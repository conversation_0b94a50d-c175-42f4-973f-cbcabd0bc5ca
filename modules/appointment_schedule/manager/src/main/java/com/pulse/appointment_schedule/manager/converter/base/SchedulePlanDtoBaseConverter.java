package com.pulse.appointment_schedule.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.SchedulePlanBaseDtoManager;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanBaseDto;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "ac64310e-be68-4b67-a8e6-6452b9992c59|DTO|BASE_CONVERTER")
public class SchedulePlanDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanBaseDtoManager schedulePlanBaseDtoManager;

    @AutoGenerated(locked = true)
    public SchedulePlanDto convertFromSchedulePlanBaseDtoToSchedulePlanDto(
            SchedulePlanBaseDto schedulePlanBaseDto) {
        return convertFromSchedulePlanBaseDtoToSchedulePlanDto(List.of(schedulePlanBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<SchedulePlanDto> convertFromSchedulePlanBaseDtoToSchedulePlanDto(
            List<SchedulePlanBaseDto> schedulePlanBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulePlanBaseDtoList)) {
            return new ArrayList<>();
        }
        List<SchedulePlanDto> schedulePlanDtoList = new ArrayList<>();
        for (SchedulePlanBaseDto schedulePlanBaseDto : schedulePlanBaseDtoList) {
            if (schedulePlanBaseDto == null) {
                continue;
            }
            SchedulePlanDto schedulePlanDto = new SchedulePlanDto();
            schedulePlanDto.setId(schedulePlanBaseDto.getId());
            schedulePlanDto.setDayOfWeek(schedulePlanBaseDto.getDayOfWeek());
            schedulePlanDto.setBranchInstitutionId(schedulePlanBaseDto.getBranchInstitutionId());
            schedulePlanDto.setDepartmentId(schedulePlanBaseDto.getDepartmentId());
            schedulePlanDto.setTimeDescription(schedulePlanBaseDto.getTimeDescription());
            schedulePlanDto.setClinicRegisterTypeId(schedulePlanBaseDto.getClinicRegisterTypeId());
            schedulePlanDto.setDoctorId(schedulePlanBaseDto.getDoctorId());
            schedulePlanDto.setRoomId(schedulePlanBaseDto.getRoomId());
            schedulePlanDto.setActivationDate(schedulePlanBaseDto.getActivationDate());
            schedulePlanDto.setLimitNumber(schedulePlanBaseDto.getLimitNumber());
            schedulePlanDto.setAddNumber(schedulePlanBaseDto.getAddNumber());
            schedulePlanDto.setVisitEveryFewWeek(schedulePlanBaseDto.getVisitEveryFewWeek());
            schedulePlanDto.setRemark(schedulePlanBaseDto.getRemark());
            schedulePlanDto.setCancelFlag(schedulePlanBaseDto.getCancelFlag());
            schedulePlanDto.setUpdatedBy(schedulePlanBaseDto.getUpdatedBy());
            schedulePlanDto.setCreatedBy(schedulePlanBaseDto.getCreatedBy());
            schedulePlanDto.setCreatedAt(schedulePlanBaseDto.getCreatedAt());
            schedulePlanDto.setUpdatedAt(schedulePlanBaseDto.getUpdatedAt());
            schedulePlanDto.setStatus(schedulePlanBaseDto.getStatus());
            schedulePlanDto.setSchedulingTemplateId(schedulePlanBaseDto.getSchedulingTemplateId());
            schedulePlanDto.setPlanBusinessType(schedulePlanBaseDto.getPlanBusinessType());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            schedulePlanDtoList.add(schedulePlanDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return schedulePlanDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public SchedulePlanBaseDto convertFromSchedulePlanDtoToSchedulePlanBaseDto(
            SchedulePlanDto schedulePlanDto) {
        return convertFromSchedulePlanDtoToSchedulePlanBaseDto(List.of(schedulePlanDto)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<SchedulePlanBaseDto> convertFromSchedulePlanDtoToSchedulePlanBaseDto(
            List<SchedulePlanDto> schedulePlanDtoList) {
        if (CollectionUtil.isEmpty(schedulePlanDtoList)) {
            return new ArrayList<>();
        }
        return schedulePlanBaseDtoManager.getByIds(
                schedulePlanDtoList.stream()
                        .map(SchedulePlanDto::getId)
                        .collect(Collectors.toList()));
    }
}
