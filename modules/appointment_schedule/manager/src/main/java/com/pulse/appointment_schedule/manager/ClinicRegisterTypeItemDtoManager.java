package com.pulse.appointment_schedule.manager;

import com.pulse.appointment_schedule.manager.dto.ClinicRegisterTypeItemDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "eb9f5cf1-d8f3-48cd-aa4c-3d5640af0dfd|DTO|MANAGER")
public interface ClinicRegisterTypeItemDtoManager {

    @AutoGenerated(locked = true, uuid = "12186f97-aaa7-31c9-a48d-e9937f066188")
    List<ClinicRegisterTypeItemDto> getByClinicRegisterTypeId(String clinicRegisterTypeId);

    @AutoGenerated(locked = true, uuid = "368c5b17-4aeb-30a1-9df3-736699d9c70a")
    ClinicRegisterTypeItemDto getById(String id);

    @AutoGenerated(locked = true, uuid = "9e917b3f-de1f-36f1-a9d4-c6bb81edabe5")
    List<ClinicRegisterTypeItemDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "f1f3b90d-0564-3021-b80e-c31c83c6480f")
    List<ClinicRegisterTypeItemDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId);
}
