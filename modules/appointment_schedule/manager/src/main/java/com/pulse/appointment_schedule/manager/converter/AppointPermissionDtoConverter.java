package com.pulse.appointment_schedule.manager.converter;

import com.pulse.appointment_schedule.manager.converter.base.AppointPermissionDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "344fa429-f1f0-4ac9-a961-3fae5ef2d792|DTO|CONVERTER")
public class AppointPermissionDtoConverter extends AppointPermissionDtoBaseConverter {}
