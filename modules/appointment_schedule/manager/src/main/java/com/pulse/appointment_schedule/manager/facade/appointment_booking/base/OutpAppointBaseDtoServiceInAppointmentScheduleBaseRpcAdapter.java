package com.pulse.appointment_schedule.manager.facade.appointment_booking.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.appointment_booking.manager.dto.OutpAppointBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "e508d8d7-84dc-3682-bb16-eccbae04a9d1")
public class OutpAppointBaseDtoServiceInAppointmentScheduleBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "04f9c22d-d614-4fd1-b219-bf1596cb9a41|RPC|BASE_ADAPTER")
    public List<OutpAppointBaseDto> getByAppointmentScheduleId(String appointmentScheduleId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("appointment_schedule_id", appointmentScheduleId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("appointment_schedule_id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/04f9c22d-d614-4fd1-b219-bf1596cb9a41/OutpAppointBaseDtoService-getByAppointmentScheduleId",
                        "com.pulse.appointment_booking.service.OutpAppointBaseDtoService",
                        "getByAppointmentScheduleId",
                        paramMap,
                        paramTypeMap,
                        "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "1e05ea2b-74e1-4ea2-8130-9706c0464883|RPC|BASE_ADAPTER")
    public List<OutpAppointBaseDto> getByAppointmentScheduleIds(
            List<String> appointmentScheduleId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("appointment_schedule_id", appointmentScheduleId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("appointment_schedule_id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/1e05ea2b-74e1-4ea2-8130-9706c0464883/OutpAppointBaseDtoService-getByAppointmentScheduleIds",
                        "com.pulse.appointment_booking.service.OutpAppointBaseDtoService",
                        "getByAppointmentScheduleIds",
                        paramMap,
                        paramTypeMap,
                        "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "59e3ed41-6de6-4012-b0d6-28537915acbf|RPC|BASE_ADAPTER")
    public List<OutpAppointBaseDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("clinic_register_type_id", clinicRegisterTypeId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("clinic_register_type_id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/59e3ed41-6de6-4012-b0d6-28537915acbf/OutpAppointBaseDtoService-getByClinicRegisterTypeIds",
                        "com.pulse.appointment_booking.service.OutpAppointBaseDtoService",
                        "getByClinicRegisterTypeIds",
                        paramMap,
                        paramTypeMap,
                        "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "d1b1c1c7-2bb2-4f21-9346-f51cea5e93d0|RPC|BASE_ADAPTER")
    public List<OutpAppointBaseDto> getByClinicRegisterTypeId(String clinicRegisterTypeId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("clinic_register_type_id", clinicRegisterTypeId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("clinic_register_type_id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/d1b1c1c7-2bb2-4f21-9346-f51cea5e93d0/OutpAppointBaseDtoService-getByClinicRegisterTypeId",
                        "com.pulse.appointment_booking.service.OutpAppointBaseDtoService",
                        "getByClinicRegisterTypeId",
                        paramMap,
                        paramTypeMap,
                        "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }
}
