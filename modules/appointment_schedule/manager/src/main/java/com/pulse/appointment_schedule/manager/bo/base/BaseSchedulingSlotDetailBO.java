package com.pulse.appointment_schedule.manager.bo.base;

import com.pulse.appointment_schedule.common.enums.AppointmentStatusEnum;
import com.pulse.appointment_schedule.manager.bo.SchedulingSlotDetailBO;
import com.pulse.appointment_schedule.persist.dos.SchedulingSlotDetail;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "scheduling_slot_detail")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "1f08f1e0-44fd-3e0b-9bc8-22103b850f93")
public abstract class BaseSchedulingSlotDetailBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 预约渠道ID */
    @Column(name = "appointment_category_id")
    @AutoGenerated(locked = true, uuid = "dab86403-2759-4129-bd4d-76c62f8ea004")
    private String appointmentCategoryId;

    /** 排班ID */
    @Column(name = "appointment_schedule_id")
    @AutoGenerated(locked = true, uuid = "2435a2f6-4a78-4b0a-af12-d4d3030ac0b9")
    private String appointmentScheduleId;

    /** 号源状态 */
    @Column(name = "appointment_status")
    @AutoGenerated(locked = true, uuid = "95af1de1-e1ef-467e-b0b6-f4e2199933b0")
    @Enumerated(EnumType.STRING)
    private AppointmentStatusEnum appointmentStatus;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "06214bb4-92ec-5a4b-9a85-5350fa327c33")
    private Date createdAt;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "8e66f0ea-f9d1-5d45-98ab-b5b9c4e89337")
    private Long deletedAt = 0L;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "7bef3a80-4b63-43d1-8807-c36baf539d2f")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "5bbd7648-b577-43ee-8e21-e001975c0a5d")
    @Version
    private Long lockVersion;

    /** 计划标志 */
    @Column(name = "plan_flag")
    @AutoGenerated(locked = true, uuid = "89295773-8f11-4b17-a5cd-ab6beab39f34")
    private Boolean planFlag;

    /** 排班计划ID */
    @Column(name = "schedule_plan_id")
    @AutoGenerated(locked = true, uuid = "9b9d3ee7-7552-433e-9dd8-0572d86b0eab")
    private String schedulePlanId;

    /** 号序 */
    @Column(name = "schedule_slot_number")
    @AutoGenerated(locked = true, uuid = "61b7061e-3faf-46f0-bae1-37912877634f")
    private Long scheduleSlotNumber;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "23188a54-0d39-50fd-8bda-2de2d9e95a3c")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public SchedulingSlotDetail convertToSchedulingSlotDetail() {
        SchedulingSlotDetail entity = new SchedulingSlotDetail();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "appointmentScheduleId",
                "schedulePlanId",
                "appointmentCategoryId",
                "scheduleSlotNumber",
                "appointmentStatus",
                "planFlag",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public String getAppointmentCategoryId() {
        return this.appointmentCategoryId;
    }

    @AutoGenerated(locked = true)
    public String getAppointmentScheduleId() {
        return this.appointmentScheduleId;
    }

    @AutoGenerated(locked = true)
    public AppointmentStatusEnum getAppointmentStatus() {
        return this.appointmentStatus;
    }

    @AutoGenerated(locked = true)
    public static SchedulingSlotDetailBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        SchedulingSlotDetailBO schedulingSlotDetail =
                (SchedulingSlotDetailBO)
                        session.createQuery("from SchedulingSlotDetailBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return schedulingSlotDetail;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public Boolean getPlanFlag() {
        return this.planFlag;
    }

    @AutoGenerated(locked = true)
    public String getSchedulePlanId() {
        return this.schedulePlanId;
    }

    @AutoGenerated(locked = true)
    public Long getScheduleSlotNumber() {
        return this.scheduleSlotNumber;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setAppointmentCategoryId(String appointmentCategoryId) {
        this.appointmentCategoryId = appointmentCategoryId;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setAppointmentScheduleId(String appointmentScheduleId) {
        this.appointmentScheduleId = appointmentScheduleId;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setAppointmentStatus(AppointmentStatusEnum appointmentStatus) {
        this.appointmentStatus = appointmentStatus;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setId(String id) {
        this.id = id;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setPlanFlag(Boolean planFlag) {
        this.planFlag = planFlag;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setSchedulePlanId(String schedulePlanId) {
        this.schedulePlanId = schedulePlanId;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setScheduleSlotNumber(Long scheduleSlotNumber) {
        this.scheduleSlotNumber = scheduleSlotNumber;
        return (SchedulingSlotDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public SchedulingSlotDetailBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (SchedulingSlotDetailBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
