package com.pulse.appointment_schedule.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.CancellationRecordBaseDtoManager;
import com.pulse.appointment_schedule.manager.converter.CancellationRecordBaseDtoConverter;
import com.pulse.appointment_schedule.manager.dto.CancellationRecordBaseDto;
import com.pulse.appointment_schedule.persist.dos.CancellationRecord;
import com.pulse.appointment_schedule.persist.mapper.CancellationRecordDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "6ee22887-0f71-4676-9766-ee847787e14f|DTO|BASE_MANAGER_IMPL")
public abstract class CancellationRecordBaseDtoManagerBaseImpl
        implements CancellationRecordBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private CancellationRecordBaseDtoConverter cancellationRecordBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private CancellationRecordDao cancellationRecordDao;

    @AutoGenerated(locked = true, uuid = "3238799d-0179-3cb0-b1e5-74ccd217bb14")
    @Override
    public List<CancellationRecordBaseDto> getByScheduleIds(List<String> scheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(scheduleId)) {
            return Collections.emptyList();
        }

        List<CancellationRecord> cancellationRecordList =
                cancellationRecordDao.getByScheduleIds(scheduleId);
        if (CollectionUtil.isEmpty(cancellationRecordList)) {
            return Collections.emptyList();
        }

        return doConvertFromCancellationRecordToCancellationRecordBaseDto(cancellationRecordList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "be498093-e9d2-3f92-80e9-69b1f876264a")
    @Override
    public CancellationRecordBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CancellationRecordBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        CancellationRecordBaseDto cancellationRecordBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return cancellationRecordBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "be7079c5-a584-3b2b-b053-579f279aec2e")
    public List<CancellationRecordBaseDto>
            doConvertFromCancellationRecordToCancellationRecordBaseDto(
                    List<CancellationRecord> cancellationRecordList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(cancellationRecordList)) {
            return Collections.emptyList();
        }

        Map<String, CancellationRecordBaseDto> dtoMap =
                cancellationRecordBaseDtoConverter
                        .convertFromCancellationRecordToCancellationRecordBaseDto(
                                cancellationRecordList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        CancellationRecordBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<CancellationRecordBaseDto> cancellationRecordBaseDtoList = new ArrayList<>();
        for (CancellationRecord i : cancellationRecordList) {
            CancellationRecordBaseDto cancellationRecordBaseDto = dtoMap.get(i.getId());
            if (cancellationRecordBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            cancellationRecordBaseDtoList.add(cancellationRecordBaseDto);
        }
        return cancellationRecordBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "cc76cdbf-f773-3375-954b-da145ddb8ff4")
    @Override
    public List<CancellationRecordBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<CancellationRecord> cancellationRecordList = cancellationRecordDao.getByIds(id);
        if (CollectionUtil.isEmpty(cancellationRecordList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, CancellationRecord> cancellationRecordMap =
                cancellationRecordList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        cancellationRecordList =
                id.stream()
                        .map(i -> cancellationRecordMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromCancellationRecordToCancellationRecordBaseDto(cancellationRecordList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d95bcf54-2582-3838-a745-e1d8073e6acc")
    @Override
    public List<CancellationRecordBaseDto> getByScheduleId(String scheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CancellationRecordBaseDto> cancellationRecordBaseDtoList =
                getByScheduleIds(Arrays.asList(scheduleId));
        return cancellationRecordBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
