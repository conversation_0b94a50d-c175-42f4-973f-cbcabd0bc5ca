package com.pulse.appointment_schedule.manager;

import com.pulse.appointment_schedule.manager.dto.GetAppointmentScheduleDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "b738323e-2579-4630-a3e2-e8b6e26416f6|DTO|MANAGER")
public interface GetAppointmentScheduleDtoManager {

    @AutoGenerated(locked = true, uuid = "12a48b52-6a25-31be-a65f-ec7171938d9e")
    List<GetAppointmentScheduleDto> getByRoomId(String roomId);

    @AutoGenerated(locked = true, uuid = "1921b715-d65a-3afb-94ca-407e0049e403")
    List<GetAppointmentScheduleDto> getByDepartmentIds(List<String> departmentId);

    @AutoGenerated(locked = true, uuid = "202d9f6c-ad09-3b20-b35e-7420c937ae94")
    List<GetAppointmentScheduleDto> getBySchedulingTemplateIds(List<String> schedulingTemplateId);

    @AutoGenerated(locked = true, uuid = "39af04ab-d8f9-3620-a73f-96af6839f17f")
    List<GetAppointmentScheduleDto> getBySchedulePlanIds(List<String> schedulePlanId);

    @AutoGenerated(locked = true, uuid = "5b950d38-cf1d-31a1-b427-574369a8b7f1")
    List<GetAppointmentScheduleDto> getByDoctorIds(List<String> doctorId);

    @AutoGenerated(locked = true, uuid = "64037321-e4b7-3c45-8dda-5945b2aeeb2f")
    GetAppointmentScheduleDto getById(String id);

    @AutoGenerated(locked = true, uuid = "764ae196-19eb-34cd-b756-5752419996f5")
    List<GetAppointmentScheduleDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId);

    @AutoGenerated(locked = true, uuid = "7decae62-cc6c-3192-b666-59d7f1132c3d")
    List<GetAppointmentScheduleDto> getByRoomIds(List<String> roomId);

    @AutoGenerated(locked = true, uuid = "84e6fd1b-b5c2-3e9f-ae82-1b1a91a0bc19")
    List<GetAppointmentScheduleDto> getBySchedulingTemplateId(String schedulingTemplateId);

    @AutoGenerated(locked = true, uuid = "8c14f265-24f3-368a-b6c3-9928a4a873e1")
    List<GetAppointmentScheduleDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "9645eb6a-c8cc-3b68-89f2-ed91bbb40651")
    List<GetAppointmentScheduleDto> getByDepartmentId(String departmentId);

    @AutoGenerated(locked = true, uuid = "9849190d-b6be-3ef8-a3b2-53b208360a9e")
    List<GetAppointmentScheduleDto> getByDoctorId(String doctorId);

    @AutoGenerated(locked = true, uuid = "9e895d43-f535-3f9f-9fb1-6c2e66dd6b14")
    List<GetAppointmentScheduleDto> getBySchedulePlanId(String schedulePlanId);

    @AutoGenerated(locked = true, uuid = "d7ea13ad-fb29-33c6-9f63-d2f9e7add11e")
    List<GetAppointmentScheduleDto> getByClinicRegisterTypeId(String clinicRegisterTypeId);
}
