package com.pulse.appointment_schedule.manager.facade.appointment_booking.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.appointment_booking.manager.dto.OutpAppointDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "4ad79960-4696-35cc-a23f-205535f3e87c")
public class OutpAppointDtoServiceInAppointmentScheduleBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "a2129fb8-a180-4487-8d30-4701506f32db|RPC|BASE_ADAPTER")
    public List<OutpAppointDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("clinic_register_type_id", clinicRegisterTypeId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("clinic_register_type_id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/a2129fb8-a180-4487-8d30-4701506f32db/OutpAppointDtoService-getByClinicRegisterTypeIds",
                        "com.pulse.appointment_booking.service.OutpAppointDtoService",
                        "getByClinicRegisterTypeIds",
                        paramMap,
                        paramTypeMap,
                        "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "f588f31c-ad2d-409a-956b-88a79362d5c1|RPC|BASE_ADAPTER")
    public List<OutpAppointDto> getByAppointmentScheduleId(String appointmentScheduleId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("appointment_schedule_id", appointmentScheduleId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("appointment_schedule_id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/appointment_booking/f588f31c-ad2d-409a-956b-88a79362d5c1/OutpAppointDtoService-getByAppointmentScheduleId",
                        "com.pulse.appointment_booking.service.OutpAppointDtoService",
                        "getByAppointmentScheduleId",
                        paramMap,
                        paramTypeMap,
                        "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96"),
                new TypeReference<>() {});
    }
}
