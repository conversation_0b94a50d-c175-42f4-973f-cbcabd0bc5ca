package com.pulse.appointment_schedule.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.dto.ScheduleTemplateDetailBaseDto;
import com.pulse.appointment_schedule.persist.dos.ScheduleTemplateDetail;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "67cc739e-7d78-45b5-9f6c-3bc2b8646463|DTO|BASE_CONVERTER")
public class ScheduleTemplateDetailBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ScheduleTemplateDetailBaseDto
            convertFromScheduleTemplateDetailToScheduleTemplateDetailBaseDto(
                    ScheduleTemplateDetail scheduleTemplateDetail) {
        return convertFromScheduleTemplateDetailToScheduleTemplateDetailBaseDto(
                        List.of(scheduleTemplateDetail))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ScheduleTemplateDetailBaseDto>
            convertFromScheduleTemplateDetailToScheduleTemplateDetailBaseDto(
                    List<ScheduleTemplateDetail> scheduleTemplateDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(scheduleTemplateDetailList)) {
            return new ArrayList<>();
        }
        List<ScheduleTemplateDetailBaseDto> scheduleTemplateDetailBaseDtoList = new ArrayList<>();
        for (ScheduleTemplateDetail scheduleTemplateDetail : scheduleTemplateDetailList) {
            if (scheduleTemplateDetail == null) {
                continue;
            }
            ScheduleTemplateDetailBaseDto scheduleTemplateDetailBaseDto =
                    new ScheduleTemplateDetailBaseDto();
            scheduleTemplateDetailBaseDto.setId(scheduleTemplateDetail.getId());
            scheduleTemplateDetailBaseDto.setScheduleTemplateId(
                    scheduleTemplateDetail.getScheduleTemplateId());
            scheduleTemplateDetailBaseDto.setAppointmentTypeId(
                    scheduleTemplateDetail.getAppointmentTypeId());
            scheduleTemplateDetailBaseDto.setScheduleSlotNumber(
                    scheduleTemplateDetail.getScheduleSlotNumber());
            scheduleTemplateDetailBaseDto.setCreatedAt(scheduleTemplateDetail.getCreatedAt());
            scheduleTemplateDetailBaseDto.setUpdatedAt(scheduleTemplateDetail.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            scheduleTemplateDetailBaseDtoList.add(scheduleTemplateDetailBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return scheduleTemplateDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
