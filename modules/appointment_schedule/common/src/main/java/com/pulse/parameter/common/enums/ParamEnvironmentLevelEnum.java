package com.pulse.parameter.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "3524586b-c712-4e3a-a384-7b7b32204570|ENUM|DEFINITION")
public enum ParamEnvironmentLevelEnum {

    /** 全局级别 */
    GLOBAL(1 * 1L),

    /** 院区级别 */
    CAMPUS(2 * 1L),

    /** 科室级别 */
    DEPARTMENT(3 * 1L),

    /** 病区级别 */
    WARD(4 * 1L),

    /** 应用级别 */
    APPLICATION(5 * 1L),

    /** 用户级别 */
    USER(6 * 1L),

    /** 本地级别 */
    LOCAL(7 * 1L);

    /** 顺序/优先级 */
    @AutoGenerated(locked = true, uuid = "37a77acd-7673-47bd-affa-74b5db670539")
    private Long sortNumber;

    @AutoGenerated(locked = true)
    ParamEnvironmentLevelEnum(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public Long getSortNumber() {
        return this.sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }
}
