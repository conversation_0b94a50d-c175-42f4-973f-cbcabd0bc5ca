package com.pulse.appointment_schedule.entrance.web.query.executor;

import com.pulse.appointment_schedule.entrance.web.converter.ClinicRegisterPriceDetailVoConverter;
import com.pulse.appointment_schedule.entrance.web.query.assembler.ClinicRegisterPriceDetailVoDataAssembler;
import com.pulse.appointment_schedule.entrance.web.query.assembler.ClinicRegisterPriceDetailVoDataAssembler.ClinicRegisterPriceDetailVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.query.collector.ClinicRegisterPriceDetailVoDataCollector;
import com.pulse.appointment_schedule.entrance.web.vo.ClinicRegisterPriceDetailVo;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterPriceBaseDto;
import com.pulse.appointment_schedule.persist.qto.GetClinicRegisterPriceByTypeQto;
import com.pulse.appointment_schedule.service.ClinicRegisterPriceBaseDtoService;
import com.pulse.appointment_schedule.service.index.entity.GetClinicRegisterPriceByTypeQtoService;
import com.vs.code.AutoGenerated;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** ClinicRegisterPriceDetailVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "3d6729d7-2a74-31ac-9232-f90faa5392cd")
public class ClinicRegisterPriceDetailVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterPriceBaseDtoService clinicRegisterPriceBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterPriceDetailVoConverter clinicRegisterPriceDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterPriceDetailVoDataAssembler clinicRegisterPriceDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterPriceDetailVoDataCollector clinicRegisterPriceDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private GetClinicRegisterPriceByTypeQtoService getClinicRegisterPriceByTypeQtoService;

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "4b115226-e46f-3e54-8ac5-d61eeb0ee92d")
    private Map<String, ClinicRegisterPriceDetailVo> toIdVoMap(
            List<String> ids, ClinicRegisterPriceDetailVoDataHolder dataHolder) {
        List<ClinicRegisterPriceBaseDto> rootBaseDtoList =
                clinicRegisterPriceBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(rootBaseDtoList);
        Map<String, ClinicRegisterPriceBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        ClinicRegisterPriceBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<ClinicRegisterPriceBaseDto, ClinicRegisterPriceDetailVo> voMap =
                clinicRegisterPriceDetailVoConverter.convertToClinicRegisterPriceDetailVoMap(
                        new ArrayList<>(baseDtoMap.values()));
        Map<String, ClinicRegisterPriceDetailVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        ClinicRegisterPriceBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }

    /** 根据GetClinicRegisterPriceByTypeQto查询ClinicRegisterPriceDetailVo列表,上限100 */
    @AutoGenerated(locked = false, uuid = "6d66df38-f7e2-36dd-8857-2e73c4c2c7f2")
    public List<ClinicRegisterPriceDetailVo> getClinicRegisterPriceByType(
            @NotNull GetClinicRegisterPriceByTypeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = getClinicRegisterPriceByTypeQtoService.query(qto);
        ClinicRegisterPriceDetailVoDataHolder dataHolder =
                new ClinicRegisterPriceDetailVoDataHolder();
        Map<String, ClinicRegisterPriceDetailVo> idVoMap = toIdVoMap(ids, dataHolder);
        clinicRegisterPriceDetailVoDataCollector.collectDataDefault(dataHolder);
        clinicRegisterPriceDetailVoDataAssembler.assembleData(idVoMap, dataHolder);
        List<ClinicRegisterPriceDetailVo> result =
                ids.stream()
                        .map(id -> idVoMap.get(id))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据GetClinicRegisterPriceByTypeQto查询ClinicRegisterPriceDetailVo列表,瀑布流 */
    @AutoGenerated(locked = false, uuid = "72a959f7-33d0-30ef-a93d-e8e4153c0b1b")
    public VSQueryResult<ClinicRegisterPriceDetailVo> getClinicRegisterPriceByTypeWaterfall(
            @NotNull GetClinicRegisterPriceByTypeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = getClinicRegisterPriceByTypeQtoService.queryWaterfall(qto);
        ClinicRegisterPriceDetailVoDataHolder dataHolder =
                new ClinicRegisterPriceDetailVoDataHolder();
        Map<String, ClinicRegisterPriceDetailVo> idVoMap = toIdVoMap(ids, dataHolder);
        clinicRegisterPriceDetailVoDataCollector.collectDataDefault(dataHolder);
        clinicRegisterPriceDetailVoDataAssembler.assembleData(idVoMap, dataHolder);
        List<ClinicRegisterPriceDetailVo> voList = new ArrayList<>(idVoMap.values());
        VSQueryResult result = new VSQueryResult();
        result.setResult(voList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(getClinicRegisterPriceByTypeQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据GetClinicRegisterPriceByTypeQto查询数量 */
    @AutoGenerated(locked = false, uuid = "ba0520af-1a6d-38d8-bafc-6d37f14bb596")
    public Integer getClinicRegisterPriceByTypeCount(@NotNull GetClinicRegisterPriceByTypeQto qto) {
        return getClinicRegisterPriceByTypeQtoService.count(qto);
    }
}
