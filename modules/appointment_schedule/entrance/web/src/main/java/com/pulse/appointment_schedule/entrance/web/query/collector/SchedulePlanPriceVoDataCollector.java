package com.pulse.appointment_schedule.entrance.web.query.collector;

import com.pulse.appointment_schedule.entrance.web.converter.DepartmentManageVoConverter;
import com.pulse.appointment_schedule.entrance.web.converter.OrganizationDepartmentsVoConverter;
import com.pulse.appointment_schedule.entrance.web.converter.SchedulePlanPriceVoConverter;
import com.pulse.appointment_schedule.entrance.web.query.assembler.SchedulePlanPriceVoDataAssembler.SchedulePlanPriceVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.vo.DepartmentManageVo;
import com.pulse.appointment_schedule.entrance.web.vo.OrganizationDepartmentsVo;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanBaseDto;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanPriceDto;
import com.pulse.appointment_schedule.manager.facade.organization.DepartmentBaseDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.manager.facade.organization.DepartmentDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.manager.facade.organization.OrganizationBaseDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.manager.facade.organization.OrganizationDepartmentDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.service.SchedulePlanBaseDtoService;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装SchedulePlanPriceVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "34783dfd-36a5-3651-a0f5-777734be40ea")
public class SchedulePlanPriceVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentBaseDtoServiceInAppointmentScheduleRpcAdapter
            departmentBaseDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentDtoServiceInAppointmentScheduleRpcAdapter
            departmentDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentManageVoConverter departmentManageVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoServiceInAppointmentScheduleRpcAdapter
            organizationBaseDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentDtoServiceInAppointmentScheduleRpcAdapter
            organizationDepartmentDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentsVoConverter organizationDepartmentsVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanBaseDtoService schedulePlanBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanPriceVoConverter schedulePlanPriceVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanPriceVoDataCollector schedulePlanPriceVoDataCollector;

    /** 获取SchedulePlanPriceDto数据填充SchedulePlanPriceVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "73f70278-d826-3e94-a652-7e18f3bdb924")
    public void collectDataWithDtoData(
            List<SchedulePlanPriceDto> dtoList, SchedulePlanPriceVoDataHolder dataHolder) {
        Map<OrganizationBaseDto, OrganizationDepartmentDto> departmentBaseDtoDtoMap =
                new LinkedHashMap<>();
        Map<DepartmentBaseDto, DepartmentDto> department2DepartmentBaseDtoDtoMap =
                new LinkedHashMap<>();

        for (SchedulePlanPriceDto rootDto : dtoList) {
            OrganizationDepartmentDto departmentDto = rootDto.getDepartment();
            if (departmentDto != null) {
                OrganizationBaseDto departmentBaseDto =
                        organizationBaseDtoServiceInAppointmentScheduleRpcAdapter
                                .getByIds(List.of(departmentDto.getId()))
                                .stream()
                                .findAny()
                                .get();
                departmentBaseDtoDtoMap.put(departmentBaseDto, departmentDto);
                DepartmentDto department2DepartmentDto = departmentDto.getDepartment();
                if (department2DepartmentDto != null) {
                    DepartmentBaseDto department2DepartmentBaseDto =
                            departmentBaseDtoServiceInAppointmentScheduleRpcAdapter
                                    .getByIds(List.of(department2DepartmentDto.getId()))
                                    .stream()
                                    .findAny()
                                    .get();
                    department2DepartmentBaseDto.setOrganizationId(departmentDto.getId());
                    department2DepartmentBaseDtoDtoMap.put(
                            department2DepartmentBaseDto, department2DepartmentDto);
                }
            }
        }

        // access department
        Map<OrganizationDepartmentDto, OrganizationDepartmentsVo> departmentVoMap =
                organizationDepartmentsVoConverter.convertToOrganizationDepartmentsVoMap(
                        new ArrayList<>(departmentBaseDtoDtoMap.values()));
        dataHolder.department =
                departmentBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                departmentVoMap.get(
                                                        departmentBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access department2Department
        Map<DepartmentDto, DepartmentManageVo> department2DepartmentVoMap =
                departmentManageVoConverter.convertToDepartmentManageVoMap(
                        new ArrayList<>(department2DepartmentBaseDtoDtoMap.values()));
        dataHolder.department2Department =
                department2DepartmentBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                department2DepartmentVoMap.get(
                                                        department2DepartmentBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "7feef7a7-94b8-3d16-9735-565de3e9479f")
    private void fillDataWhenNecessary(SchedulePlanPriceVoDataHolder dataHolder) {
        List<SchedulePlanBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.department == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(SchedulePlanBaseDto::getDepartmentId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoServiceInAppointmentScheduleRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<String, OrganizationDepartmentDto> organizationDepartmentDtoMap =
                    organizationDepartmentDtoServiceInAppointmentScheduleRpcAdapter
                            .getByIds(
                                    baseDtoList.stream()
                                            .map(OrganizationBaseDto::getId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationDepartmentDto::getId, Function.identity()));
            Map<OrganizationDepartmentDto, OrganizationDepartmentsVo> dtoVoMap =
                    organizationDepartmentsVoConverter.convertToOrganizationDepartmentsVoMap(
                            new ArrayList<>(organizationDepartmentDtoMap.values()));
            Map<OrganizationBaseDto, OrganizationDepartmentsVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            organizationDepartmentDtoMap.containsKey(
                                                    baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            organizationDepartmentDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department =
                    rootDtoList.stream()
                            .map(SchedulePlanBaseDto::getDepartmentId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.department2Department == null) {
            Set<String> ids =
                    dataHolder.department.keySet().stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DepartmentBaseDto> baseDtoList =
                    departmentBaseDtoServiceInAppointmentScheduleRpcAdapter
                            .getByOrganizationIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DepartmentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DepartmentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DepartmentBaseDto::getOrganizationId));
            Map<String, DepartmentDto> departmentDtoMap =
                    departmentDtoServiceInAppointmentScheduleRpcAdapter
                            .getByOrganizationIds(
                                    baseDtoList.stream()
                                            .map(DepartmentBaseDto::getOrganizationId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
            Map<DepartmentDto, DepartmentManageVo> dtoVoMap =
                    departmentManageVoConverter.convertToDepartmentManageVoMap(
                            new ArrayList<>(departmentDtoMap.values()));
            Map<DepartmentBaseDto, DepartmentManageVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> departmentDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            departmentDtoMap.get(baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department2Department =
                    dataHolder.department.keySet().stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "d3b3314c-d007-3ac1-9a40-9aef761602dc")
    public void collectDataDefault(SchedulePlanPriceVoDataHolder dataHolder) {
        schedulePlanPriceVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
