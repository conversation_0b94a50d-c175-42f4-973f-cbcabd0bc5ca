package com.pulse.appointment_schedule.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.entrance.web.query.assembler.SchedulePlanVoDataAssembler;
import com.pulse.appointment_schedule.entrance.web.query.assembler.SchedulePlanVoDataAssembler.SchedulePlanVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.query.collector.SchedulePlanVoDataCollector;
import com.pulse.appointment_schedule.entrance.web.vo.ClinicWaitRegisterTimeVo;
import com.pulse.appointment_schedule.entrance.web.vo.SchedulePlanVo;
import com.pulse.appointment_schedule.entrance.web.vo.SchedulingNumberDetailVo;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanDto;
import com.pulse.appointment_schedule.manager.dto.SchedulingSlotDetailBaseDto;
import com.pulse.appointment_schedule.manager.dto.WaitTimeConfigBaseDto;
import com.pulse.appointment_schedule.service.SchedulePlanBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到SchedulePlanVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "356648f6-9f75-49f4-aa7b-872d15e70494|VO|CONVERTER")
public class SchedulePlanVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicWaitRegisterTimeVoConverter clinicWaitRegisterTimeVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanBaseDtoService schedulePlanBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanVoDataAssembler schedulePlanVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanVoDataCollector schedulePlanVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulingNumberDetailVoConverter schedulingNumberDetailVoConverter;

    /** 使用默认方式组装SchedulePlanVo列表数据 */
    @AutoGenerated(locked = true, uuid = "0073d065-bb5c-38a0-9eec-703cf8eeda2b")
    public List<SchedulePlanVo> convertAndAssembleDataList(List<SchedulePlanDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        SchedulePlanVoDataHolder dataHolder = new SchedulePlanVoDataHolder();
        dataHolder.setRootBaseDtoList(
                schedulePlanBaseDtoService.getByIds(
                        dtoList.stream().map(SchedulePlanDto::getId).collect(Collectors.toList())));
        Map<String, SchedulePlanVo> voMap =
                convertToSchedulePlanVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        schedulePlanVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        schedulePlanVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把SchedulePlanDto转换成SchedulePlanVo */
    @AutoGenerated(locked = false, uuid = "356648f6-9f75-49f4-aa7b-872d15e70494-converter-Map")
    public Map<SchedulePlanDto, SchedulePlanVo> convertToSchedulePlanVoMap(
            List<SchedulePlanDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<WaitTimeConfigBaseDto, ClinicWaitRegisterTimeVo> waitTimeConfigListMap =
                clinicWaitRegisterTimeVoConverter.convertToClinicWaitRegisterTimeVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getWaitTimeConfigList()))
                                .flatMap(dto -> dto.getWaitTimeConfigList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<SchedulingSlotDetailBaseDto, SchedulingNumberDetailVo> schedulingSlotDetailListMap =
                schedulingNumberDetailVoConverter.convertToSchedulingNumberDetailVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getSchedulingSlotDetailList()))
                                .flatMap(dto -> dto.getSchedulingSlotDetailList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<SchedulePlanDto, SchedulePlanVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            SchedulePlanVo vo = new SchedulePlanVo();
                                            vo.setId(dto.getId());
                                            vo.setDayOfWeek(dto.getDayOfWeek());
                                            vo.setBranchInstitutionId(dto.getBranchInstitutionId());
                                            vo.setDepartmentId(dto.getDepartmentId());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            vo.setDoctorId(dto.getDoctorId());
                                            vo.setRoomId(dto.getRoomId());
                                            vo.setActivationDate(dto.getActivationDate());
                                            vo.setLimitNumber(dto.getLimitNumber());
                                            vo.setAddNumber(dto.getAddNumber());
                                            vo.setVisitEveryFewWeek(dto.getVisitEveryFewWeek());
                                            vo.setRemark(dto.getRemark());
                                            vo.setCancelFlag(dto.getCancelFlag());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setStatus(dto.getStatus());
                                            vo.setWaitTimeConfigList(
                                                    dto.getWaitTimeConfigList() == null
                                                            ? null
                                                            : dto.getWaitTimeConfigList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    waitTimeConfigListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setSchedulingTemplateId(
                                                    dto.getSchedulingTemplateId());
                                            vo.setPlanBusinessType(dto.getPlanBusinessType());
                                            vo.setSchedulingSlotDetailList(
                                                    dto.getSchedulingSlotDetailList() == null
                                                            ? null
                                                            : dto
                                                                    .getSchedulingSlotDetailList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    schedulingSlotDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把SchedulePlanDto转换成SchedulePlanVo */
    @AutoGenerated(locked = true, uuid = "356648f6-9f75-49f4-aa7b-872d15e70494-converter-list")
    public List<SchedulePlanVo> convertToSchedulePlanVoList(List<SchedulePlanDto> dtoList) {
        return new ArrayList<>(convertToSchedulePlanVoMap(dtoList).values());
    }

    /** 把SchedulePlanDto转换成SchedulePlanVo */
    @AutoGenerated(locked = true, uuid = "48c57dff-d6a3-384a-a196-31f5efa095a6")
    public SchedulePlanVo convertToSchedulePlanVo(SchedulePlanDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToSchedulePlanVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装SchedulePlanVo数据 */
    @AutoGenerated(locked = true, uuid = "9c35afcd-3821-3942-a12d-828fad1907d7")
    public SchedulePlanVo convertAndAssembleData(SchedulePlanDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
