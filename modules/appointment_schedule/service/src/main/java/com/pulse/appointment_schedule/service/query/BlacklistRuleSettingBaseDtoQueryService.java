package com.pulse.appointment_schedule.service.query;

import com.pulse.appointment_schedule.manager.dto.BlacklistRuleSettingBaseDto;
import com.pulse.appointment_schedule.persist.qto.BlacklistRuleSettingQueryQto;
import com.pulse.appointment_schedule.service.BlacklistRuleSettingBaseDtoService;
import com.pulse.appointment_schedule.service.index.entity.BlacklistRuleSettingQueryQtoService;
import com.pulse.appointment_schedule.service.query.assembler.BlacklistRuleSettingBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** BlacklistRuleSettingBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "b6508282-f910-3b1e-9249-0fdb6d0f67a6")
public class BlacklistRuleSettingBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private BlacklistRuleSettingBaseDtoDataAssembler blacklistRuleSettingBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private BlacklistRuleSettingBaseDtoService blacklistRuleSettingBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private BlacklistRuleSettingQueryQtoService blacklistRuleSettingQueryQtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "189181f0-6778-3ef6-b08d-59a5d24cb49f")
    private List<BlacklistRuleSettingBaseDto> toDtoList(List<String> ids) {
        List<BlacklistRuleSettingBaseDto> baseDtoList =
                blacklistRuleSettingBaseDtoService.getByIds(ids);
        Map<String, BlacklistRuleSettingBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        BlacklistRuleSettingBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据BlacklistRuleSettingQueryQto查询BlacklistRuleSettingBaseDto列表,上限500 */
    @PublicInterface(id = "766c6bf8-8d9e-4688-9c3a-1fd56de6fdff", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "31ab91ae-026c-3b0b-a8cc-2ddc438a2339")
    public List<BlacklistRuleSettingBaseDto> queryByBlacklistRuleSettingQuery(
            @Valid @NotNull BlacklistRuleSettingQueryQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = blacklistRuleSettingQueryQtoService.query(qto);
        List<BlacklistRuleSettingBaseDto> result = toDtoList(ids);
        blacklistRuleSettingBaseDtoDataAssembler.assembleData(result);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据BlacklistRuleSettingQueryQto查询数量 */
    @PublicInterface(id = "b3a56a0f-e0a0-4c6f-b724-7f2e3ae7f5e5", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "d737d5ad-f7ac-3273-ac63-0beb1d58f7be")
    public Integer queryByBlacklistRuleSettingQueryCount(
            @Valid @NotNull BlacklistRuleSettingQueryQto qto) {
        return blacklistRuleSettingQueryQtoService.count(qto);
    }
}
