package com.pulse.appointment_schedule.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.NumberSourceConfigBaseDtoManager;
import com.pulse.appointment_schedule.manager.dto.NumberSourceConfigBaseDto;
import com.pulse.appointment_schedule.service.converter.NumberSourceConfigBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "d7a45688-166b-491c-bd22-d8c4d3950ee2|DTO|SERVICE")
public class NumberSourceConfigBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private NumberSourceConfigBaseDtoManager numberSourceConfigBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private NumberSourceConfigBaseDtoServiceConverter numberSourceConfigBaseDtoServiceConverter;

    @PublicInterface(id = "a8fad142-59f9-40fa-86c9-62241aa5162a", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "2ce687f7-5600-3ab5-b509-360f0fe4851d")
    public NumberSourceConfigBaseDto getById(@NotNull(message = "主键不能为空") Long id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<NumberSourceConfigBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "32b14074-bbac-4ea5-8897-c99711a09152", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "a3c648da-6822-3a89-bbb0-f85387945687")
    public List<NumberSourceConfigBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<Long> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<NumberSourceConfigBaseDto> numberSourceConfigBaseDtoList =
                numberSourceConfigBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return numberSourceConfigBaseDtoServiceConverter.NumberSourceConfigBaseDtoConverter(
                numberSourceConfigBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
