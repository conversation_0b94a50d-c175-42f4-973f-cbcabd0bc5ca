package com.pulse.appointment_schedule.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.appointment_schedule.manager.bo.*;
import com.pulse.appointment_schedule.manager.bo.BlacklistBO;
import com.pulse.appointment_schedule.persist.dos.Blacklist;
import com.pulse.appointment_schedule.service.base.BaseBlacklistBOService.CreateBlacklistBoResult;
import com.pulse.appointment_schedule.service.base.BaseBlacklistBOService.DeleteBlacklistBoResult;
import com.pulse.appointment_schedule.service.base.BaseBlacklistBOService.UpdateBlacklistBoResult;
import com.pulse.appointment_schedule.service.bto.CreateBlacklistBto;
import com.pulse.appointment_schedule.service.bto.DeleteBlacklistBto;
import com.pulse.appointment_schedule.service.bto.UpdateBlacklistBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "0b3db571-c81b-30ca-836a-ff812980e5aa")
public class BaseBlacklistBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    @AutoGenerated(locked = true)
    protected CreateBlacklistBoResult createBlacklistBase(CreateBlacklistBto createBlacklistBto) {
        if (createBlacklistBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateBlacklistBoResult boResult = new CreateBlacklistBoResult();
        BlacklistBO blacklistBO = createCreateBlacklist(boResult, createBlacklistBto);
        boResult.setRootBo(blacklistBO);
        return boResult;
    }

    /** 数据库创建一行 */
    @AutoGenerated(locked = true)
    private BlacklistBO createCreateBlacklist(
            BaseBlacklistBOService.CreateBlacklistBoResult boResult,
            CreateBlacklistBto createBlacklistBto) {
        BlacklistBO blacklistBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        if (blacklistBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", blacklistBO.getId(), "blacklist");
                throw new IgnoredException(400, "黑名单已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "blacklist",
                        blacklistBO.getId());
                throw new IgnoredException(400, "黑名单已存在");
            }
        } else {
            blacklistBO = new BlacklistBO();
            if (pkExist) {
                blacklistBO.setId(String.valueOf(this.idGenerator.allocateId("blacklist")));
            } else {
                blacklistBO.setId(String.valueOf(this.idGenerator.allocateId("blacklist")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBlacklistBto, "__$validPropertySet"),
                    "patientId")) {
                blacklistBO.setPatientId(createBlacklistBto.getPatientId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBlacklistBto, "__$validPropertySet"),
                    "patientTag")) {
                blacklistBO.setPatientTag(createBlacklistBto.getPatientTag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBlacklistBto, "__$validPropertySet"),
                    "limitTime")) {
                blacklistBO.setLimitTime(createBlacklistBto.getLimitTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBlacklistBto, "__$validPropertySet"),
                    "limitReason")) {
                blacklistBO.setLimitReason(createBlacklistBto.getLimitReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBlacklistBto, "__$validPropertySet"),
                    "personType")) {
                blacklistBO.setPersonType(createBlacklistBto.getPersonType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBlacklistBto, "__$validPropertySet"),
                    "blacklistSource")) {
                blacklistBO.setBlacklistSource(createBlacklistBto.getBlacklistSource());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createBlacklistBto);
            addedBto.setBo(blacklistBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return blacklistBO;
    }

    @AutoGenerated(locked = true)
    protected DeleteBlacklistBoResult deleteBlacklistBase(DeleteBlacklistBto deleteBlacklistBto) {
        if (deleteBlacklistBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteBlacklistBoResult boResult = new DeleteBlacklistBoResult();
        BlacklistBO blacklistBO = deleteDeleteBlacklistOnMissThrowEx(boResult, deleteBlacklistBto);
        boResult.setRootBo(blacklistBO);
        return boResult;
    }

    /** 删除对象:deleteBlacklist,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private BlacklistBO deleteDeleteBlacklistOnMissThrowEx(
            BaseBlacklistBOService.DeleteBlacklistBoResult boResult,
            DeleteBlacklistBto deleteBlacklistBto) {
        BlacklistBO blacklistBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteBlacklistBto.getId() == null);
        if (!allNull && !found) {
            blacklistBO = BlacklistBO.getById(deleteBlacklistBto.getId());
            found = true;
        }
        if (blacklistBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(blacklistBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteBlacklistBto);
            deletedBto.setEntity(blacklistBO.convertToBlacklist());
            boResult.getDeletedBtoList().add(deletedBto);
            return blacklistBO;
        }
    }

    /** 修改黑名单信息 */
    @AutoGenerated(locked = true)
    protected UpdateBlacklistBoResult updateBlacklistBase(UpdateBlacklistBto updateBlacklistBto) {
        if (updateBlacklistBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateBlacklistBoResult boResult = new UpdateBlacklistBoResult();
        BlacklistBO blacklistBO = updateUpdateBlacklistOnMissThrowEx(boResult, updateBlacklistBto);
        boResult.setRootBo(blacklistBO);
        return boResult;
    }

    /** 更新对象:updateBlacklist,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private BlacklistBO updateUpdateBlacklistOnMissThrowEx(
            BaseBlacklistBOService.UpdateBlacklistBoResult boResult,
            UpdateBlacklistBto updateBlacklistBto) {
        BlacklistBO blacklistBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateBlacklistBto.getId() == null);
        if (!allNull && !found) {
            blacklistBO = BlacklistBO.getById(updateBlacklistBto.getId());
            found = true;
        }
        if (blacklistBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(blacklistBO.convertToBlacklist());
            updatedBto.setBto(updateBlacklistBto);
            updatedBto.setBo(blacklistBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateBlacklistBto, "__$validPropertySet"),
                    "patientTag")) {
                blacklistBO.setPatientTag(updateBlacklistBto.getPatientTag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateBlacklistBto, "__$validPropertySet"),
                    "limitTime")) {
                blacklistBO.setLimitTime(updateBlacklistBto.getLimitTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateBlacklistBto, "__$validPropertySet"),
                    "limitReason")) {
                blacklistBO.setLimitReason(updateBlacklistBto.getLimitReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateBlacklistBto, "__$validPropertySet"),
                    "personType")) {
                blacklistBO.setPersonType(updateBlacklistBto.getPersonType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateBlacklistBto, "__$validPropertySet"),
                    "blacklistSource")) {
                blacklistBO.setBlacklistSource(updateBlacklistBto.getBlacklistSource());
            }
            return blacklistBO;
        }
    }

    public static class CreateBlacklistBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public BlacklistBO getRootBo() {
            return (BlacklistBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateBlacklistBto, BlacklistBO> getCreatedBto(
                CreateBlacklistBto createBlacklistBto) {
            return this.getAddedResult(createBlacklistBto);
        }

        @AutoGenerated(locked = true)
        public Blacklist getDeleted_Blacklist() {
            return (Blacklist) CollectionUtil.getFirst(this.getDeletedEntityList(Blacklist.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateBlacklistBto, Blacklist, BlacklistBO> getUpdatedBto(
                CreateBlacklistBto createBlacklistBto) {
            return super.getUpdatedResult(createBlacklistBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateBlacklistBto, BlacklistBO> getUnmodifiedBto(
                CreateBlacklistBto createBlacklistBto) {
            return super.getUnmodifiedResult(createBlacklistBto);
        }
    }

    public static class DeleteBlacklistBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public BlacklistBO getRootBo() {
            return (BlacklistBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteBlacklistBto, BlacklistBO> getCreatedBto(
                DeleteBlacklistBto deleteBlacklistBto) {
            return this.getAddedResult(deleteBlacklistBto);
        }

        @AutoGenerated(locked = true)
        public Blacklist getDeleted_Blacklist() {
            return (Blacklist) CollectionUtil.getFirst(this.getDeletedEntityList(Blacklist.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteBlacklistBto, Blacklist, BlacklistBO> getUpdatedBto(
                DeleteBlacklistBto deleteBlacklistBto) {
            return super.getUpdatedResult(deleteBlacklistBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteBlacklistBto, BlacklistBO> getUnmodifiedBto(
                DeleteBlacklistBto deleteBlacklistBto) {
            return super.getUnmodifiedResult(deleteBlacklistBto);
        }
    }

    public static class UpdateBlacklistBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public BlacklistBO getRootBo() {
            return (BlacklistBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateBlacklistBto, BlacklistBO> getCreatedBto(
                UpdateBlacklistBto updateBlacklistBto) {
            return this.getAddedResult(updateBlacklistBto);
        }

        @AutoGenerated(locked = true)
        public Blacklist getDeleted_Blacklist() {
            return (Blacklist) CollectionUtil.getFirst(this.getDeletedEntityList(Blacklist.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateBlacklistBto, Blacklist, BlacklistBO> getUpdatedBto(
                UpdateBlacklistBto updateBlacklistBto) {
            return super.getUpdatedResult(updateBlacklistBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateBlacklistBto, BlacklistBO> getUnmodifiedBto(
                UpdateBlacklistBto updateBlacklistBto) {
            return super.getUnmodifiedResult(updateBlacklistBto);
        }
    }
}
