package com.pulse.appointment_schedule.service;

import com.pulse.appointment_schedule.common.enums.AppointmentStatusEnum;
import com.pulse.appointment_schedule.common.enums.SchedulingstatusEnum;
import com.pulse.appointment_schedule.manager.dto.*;
import com.pulse.appointment_schedule.persist.qto.GetAutoScheduleLogQto;
import com.pulse.appointment_schedule.persist.qto.ListAppointmentScheduleQto;
import com.pulse.appointment_schedule.persist.qto.ListSchedulePlanQto;
import com.pulse.appointment_schedule.service.bto.MergeAppointmentScheduleBto;
import com.pulse.appointment_schedule.service.bto.MergeAutoScheduleLogBto;
import com.pulse.appointment_schedule.service.bto.MergeScheduleSlotDetailBto;
import com.pulse.appointment_schedule.service.bto.MergeWaitTimeConfingBto;
import com.pulse.appointment_schedule.service.query.AppointmentScheduleDtoQueryService;
import com.pulse.appointment_schedule.service.query.AutoScheduleLogDtoQueryService;
import com.pulse.appointment_schedule.service.query.SchedulePlanDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** Auto generated Service */
@Controller
@Validated
@AutoGenerated(locked = false, uuid = "af953d00-fc3d-3bdb-8648-eea1f55b9ab7")
public class AutoAppointmentScheduleService {

    @Resource private AppointmentScheduleBOService appointmentScheduleBOService;

    @Resource private AutoScheduleLogDtoQueryService autoScheduleLogDtoQueryService;

    @Resource private SchedulePlanDtoQueryService schedulePlanDtoQueryService;

    @Resource private AppointmentScheduleDtoQueryService appointmentScheduleDtoQueryService;

    @Resource private AutoScheduleLogBOService autoScheduleLogBOService;

    @Resource private SchedulingSlotDetailBOService schedulingSlotDetailBOService;

    @Resource private WaitTimeConfigBOService waitTimeConfigBOService;

    @PublicInterface(
            id = "8fffe1cc-c924-4efe-8e85-50a45e365825",
            module = "appointment_schedule",
            moduleId = "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
            version = "1748335721644",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "8fffe1cc-c924-4efe-8e85-50a45e365825")
    public Boolean autoMergeAppointmentSchedule(String branchInstitutionId) {
        // TODO implement method
        LocalDate now = LocalDate.now();
        int count = 60; // 后期改成参数
        while (count > 0) {
            LocalDate day = now.plusDays(count - 1);
            Date dayDate = Date.from(day.atStartOfDay(ZoneId.systemDefault()).toInstant());
            // 查询日志是否已经过排班
            List<AutoScheduleLogDto> autoScheduleLogDto =
                    autoScheduleLogDtoQueryService.getAutoScheduleLog(
                            new GetAutoScheduleLogQto() {
                                {
                                    setBranchInstitutionIdIs(branchInstitutionId);
                                    setScheduleDayIs(dayDate);
                                }
                            });
            if (autoScheduleLogDto.size() == 0) {
                // 根据星期院区获取一周排班信息
                List<SchedulePlanDto> schedulePlanDtoList =
                        schedulePlanDtoQueryService.queryByListSchedulePlan(
                                new ListSchedulePlanQto() {
                                    {
                                        setBranchInstitutionIdIs(branchInstitutionId);
                                        setDayOfWeekIs(
                                                String.valueOf(day.getDayOfWeek().getValue()));
                                    }
                                });
                if (schedulePlanDtoList.size() == 0) {
                    count--;
                    continue;
                }
                // 过滤启用时间早于等于当天或未设置的排班
                schedulePlanDtoList =
                        schedulePlanDtoList.stream()
                                .filter(
                                        w ->
                                                w.getActivationDate() == null
                                                        || w.getActivationDate().compareTo(dayDate)
                                                                <= 0)
                                .collect(Collectors.toList());

                // 根据日期获取每日排班信息
                List<AppointmentScheduleDto> appointmentScheduleDtoList =
                        appointmentScheduleDtoQueryService.listAppointmentSchedule(
                                new ListAppointmentScheduleQto() {
                                    {
                                        setRegisterTimeBiggerThanEqual(dayDate);
                                        setRegisterTimeLessThanEqual(dayDate);
                                    }
                                });

                if (schedulePlanDtoList.size() > 0) {
                    for (SchedulePlanDto schedulePlanDto : schedulePlanDtoList) {
                        // 获取一周排班对应的当日排班
                        AppointmentScheduleDto appointmentScheduleDto =
                                appointmentScheduleDtoList.stream()
                                        .filter(
                                                o ->
                                                        o.getSchedulePlanId()
                                                                .equals(schedulePlanDto.getId()))
                                        .findFirst()
                                        .orElse(null);
                        createAppointmentScheduleBySchedulePlan(
                                schedulePlanDto, appointmentScheduleDto, day, schedulePlanDto);
                    }
                }

                MergeAutoScheduleLogBto mergeAutoScheduleLogBto =
                        new MergeAutoScheduleLogBto() {
                            {
                                setBranchInstitutionId(branchInstitutionId);
                                setScheduleDay(dayDate);
                            }
                        };
                // 插入排班日志信息
                autoScheduleLogBOService.mergeAutoScheduleLog(mergeAutoScheduleLogBto);
            }
            count--;
        }
        return true;
    }

    public void createAppointmentScheduleBySchedulePlan(
            SchedulePlanDto schedulePlanDto,
            AppointmentScheduleDto appointmentScheduleDtoList,
            LocalDate day,
            SchedulePlanDto oldschedulePlanDto) {

        if (schedulePlanDto == null) {
            throw new IllegalArgumentException("传入的排班计划不能为空!");
        }
        // 隔周算法处理
        if (schedulePlanDto.getVisitEveryFewWeek() > 0) {
            LocalDate activationLocalDate =
                    schedulePlanDto
                            .getActivationDate()
                            .toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
            long days = ChronoUnit.DAYS.between(activationLocalDate, day);
            if (days % (7 * (schedulePlanDto.getVisitEveryFewWeek() + 1)) >= 7) {
                return;
            }
        }
        // 如果每日排班没生成就生成
        if (appointmentScheduleDtoList == null) {
            // 生成每日排班
            MergeAppointmentScheduleBto mergeAppointmentScheduleBto =
                    new MergeAppointmentScheduleBto() {
                        {
                            setSchedulePlanId(schedulePlanDto.getId());
                            setSchedulingBusinessType(schedulePlanDto.getPlanBusinessType());
                            setDayOfWeek(schedulePlanDto.getDayOfWeek());
                            setAppId(""); // todo应用ID怎么获取？
                            setBranchInstitutionId(schedulePlanDto.getBranchInstitutionId());
                            setDepartmentId(schedulePlanDto.getDepartmentId());
                            setTimeDescription(schedulePlanDto.getTimeDescription());
                            setClinicRegisterTypeId(schedulePlanDto.getClinicRegisterTypeId());
                            setDoctorId(schedulePlanDto.getDoctorId());
                            setRoomId(schedulePlanDto.getRoomId());
                            setRegisterTime(
                                    Date.from(
                                            day.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                            setLimitNumber(schedulePlanDto.getLimitNumber());
                            setAddNumber(schedulePlanDto.getAddNumber());
                            setStatus(SchedulingstatusEnum.NORMAL);
                            setRemark(schedulePlanDto.getRemark());
                        }
                    };
            String appointmentScheduleID =
                    appointmentScheduleBOService.mergeAppointmentSchedule(
                            mergeAppointmentScheduleBto);
            // 生成每日排班号源
            if (schedulePlanDto.getSchedulingSlotDetailList().size() > 0) {
                for (SchedulingSlotDetailBaseDto schedulingSlotDetailBaseDto :
                        schedulePlanDto.getSchedulingSlotDetailList()) {
                    MergeScheduleSlotDetailBto mergeScheduleSlotDetailBto =
                            new MergeScheduleSlotDetailBto() {
                                {
                                    setAppointmentScheduleId(appointmentScheduleID);

                                    setAppointmentCategoryId(
                                            schedulingSlotDetailBaseDto.getAppointmentCategoryId());
                                    setScheduleSlotNumber(
                                            schedulingSlotDetailBaseDto.getScheduleSlotNumber());
                                    setAppointmentStatus(AppointmentStatusEnum.NORMAL);
                                }
                            };
                    schedulingSlotDetailBOService.mergeScheduleSlotDetail(
                            mergeScheduleSlotDetailBto);
                }
            }
            // 生成每日排班候诊时间
            if (schedulePlanDto.getWaitTimeConfigList().size() > 0) {
                for (WaitTimeConfigBaseDto waitTimeConfigBaseDto :
                        schedulePlanDto.getWaitTimeConfigList()) {
                    MergeWaitTimeConfingBto mergeWaitTimeConfingBto =
                            new MergeWaitTimeConfingBto() {
                                {
                                    setStartTime(waitTimeConfigBaseDto.getStartTime());
                                    setEndTime(waitTimeConfigBaseDto.getEndTime());
                                    setStartNumber(waitTimeConfigBaseDto.getStartNumber());
                                    setEndNumber(waitTimeConfigBaseDto.getEndNumber());
                                    setAppointmentScheduleId(appointmentScheduleID);
                                }
                            };
                    waitTimeConfigBOService.mergeWaitTimeConfing(mergeWaitTimeConfingBto);
                }
            }
        } else {
            appointmentScheduleDtoList.setClinicRegisterTypeId(
                    schedulePlanDto.getClinicRegisterTypeId());
            appointmentScheduleDtoList.setDoctorId(schedulePlanDto.getDoctorId());
            appointmentScheduleDtoList.setDepartmentId(schedulePlanDto.getDepartmentId());
            appointmentScheduleDtoList.setSchedulingBusinessType(
                    schedulePlanDto.getPlanBusinessType());
            appointmentScheduleDtoList.setAddNumber(schedulePlanDto.getAddNumber());
            appointmentScheduleDtoList.setRemark(schedulePlanDto.getRemark());

            // 调整一周排班以后，分别判断每日排班的【挂号类别】和【诊室】两个字段是否相同，不同则该2个字段不更新；其余字段全部更新为一周排班的内容；
            if (oldschedulePlanDto == null
                    || oldschedulePlanDto.getRoomId() == appointmentScheduleDtoList.getRoomId()) {

                if (appointmentScheduleDtoList.getRoomId() != schedulePlanDto.getRoomId()) {
                    // todo修改一周排班和每日排班的诊室数据 预约患者信息里关联排班的的诊室位置也要同步修改
                }
                appointmentScheduleDtoList.setRoomId(schedulePlanDto.getRoomId());
            }
            if (oldschedulePlanDto == null
                    || oldschedulePlanDto.getClinicRegisterTypeId()
                            == appointmentScheduleDtoList.getClinicRegisterTypeId()) {
                if (appointmentScheduleDtoList.getClinicRegisterTypeId()
                        != schedulePlanDto.getClinicRegisterTypeId()) {
                    appointmentScheduleDtoList.setClinicRegisterTypeId(
                            schedulePlanDto.getClinicRegisterTypeId());
                }
            }
            // 对于一周排班对应的每日排班中已停诊的数据，限号数不覆盖之外
            if (appointmentScheduleDtoList.getStatus() != SchedulingstatusEnum.SUSPENDED) {
                // 当每日排班限号数与一周排班限号数不同时，不同的这天限号数不同步更新
                if (oldschedulePlanDto == null
                        || oldschedulePlanDto.getLimitNumber()
                                == appointmentScheduleDtoList.getLimitNumber()) {
                    appointmentScheduleDtoList.setLimitNumber(schedulePlanDto.getLimitNumber());
                }
            }
        }
    }
}
