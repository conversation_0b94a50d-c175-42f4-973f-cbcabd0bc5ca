package com.pulse.appointment_schedule.service.query.collector;

import com.pulse.appointment_schedule.manager.dto.SchedulePlanBaseDto;
import com.pulse.appointment_schedule.manager.dto.SchedulingSlotDetailBaseDto;
import com.pulse.appointment_schedule.manager.dto.WaitTimeConfigBaseDto;
import com.pulse.appointment_schedule.service.SchedulePlanBaseDtoService;
import com.pulse.appointment_schedule.service.SchedulingSlotDetailBaseDtoService;
import com.pulse.appointment_schedule.service.WaitTimeConfigBaseDtoService;
import com.pulse.appointment_schedule.service.query.assembler.SchedulePlanDtoDataAssembler.SchedulePlanDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装SchedulePlanDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "4876cfe6-3407-3699-ac10-44fd5cf1fad8")
public class SchedulePlanDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanBaseDtoService schedulePlanBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanDtoDataCollector schedulePlanDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulingSlotDetailBaseDtoService schedulingSlotDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private WaitTimeConfigBaseDtoService waitTimeConfigBaseDtoService;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "8424d2c4-272a-3718-bcad-7990755873a1")
    public void collectDataDefault(SchedulePlanDtoDataHolder dataHolder) {
        schedulePlanDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "94e51cf2-90bd-3000-83f8-a0f127a33b8e")
    private void fillDataWhenNecessary(SchedulePlanDtoDataHolder dataHolder) {
        List<SchedulePlanBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.waitTimeConfigList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(SchedulePlanBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<WaitTimeConfigBaseDto> baseDtoList =
                    waitTimeConfigBaseDtoService.getBySchedlePlanIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(WaitTimeConfigBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<WaitTimeConfigBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(WaitTimeConfigBaseDto::getSchedlePlanId));
            dataHolder.waitTimeConfigList =
                    rootDtoList.stream()
                            .map(SchedulePlanBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.schedulingSlotDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(SchedulePlanBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<SchedulingSlotDetailBaseDto> baseDtoList =
                    schedulingSlotDetailBaseDtoService
                            .getBySchedulePlanIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(SchedulingSlotDetailBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<SchedulingSlotDetailBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            SchedulingSlotDetailBaseDto::getSchedulePlanId));
            dataHolder.schedulingSlotDetailList =
                    rootDtoList.stream()
                            .map(SchedulePlanBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
