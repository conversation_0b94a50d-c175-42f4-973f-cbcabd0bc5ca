package com.pulse.appointment_schedule.service.index.entity;

import com.pulse.appointment_schedule.persist.mapper.SearchExpertScheduleRuleQtoDao;
import com.pulse.appointment_schedule.persist.qto.SearchExpertScheduleRuleQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "25ce3087-88bc-4e0c-9501-05a35c6676c8|QTO|SERVICE")
public class SearchExpertScheduleRuleQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchExpertScheduleRuleQtoDao searchExpertScheduleRuleMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "25ce3087-88bc-4e0c-9501-05a35c6676c8-query")
    public List<String> query(SearchExpertScheduleRuleQto qto) {
        return searchExpertScheduleRuleMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchExpertScheduleRuleQto qto) {
        return searchExpertScheduleRuleMapper.count(qto);
    }
}
