package com.pulse.appointment_schedule.service.converter;

import com.pulse.appointment_schedule.manager.dto.ClinicRegisterPriceItemDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "e2430b06-a406-3742-a37e-27f97e6197b1")
public class ClinicRegisterPriceItemDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ClinicRegisterPriceItemDto> ClinicRegisterPriceItemDtoConverter(
            List<ClinicRegisterPriceItemDto> clinicRegisterPriceItemDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return clinicRegisterPriceItemDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
