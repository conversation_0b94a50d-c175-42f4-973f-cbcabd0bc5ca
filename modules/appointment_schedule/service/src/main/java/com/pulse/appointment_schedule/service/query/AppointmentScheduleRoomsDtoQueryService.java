package com.pulse.appointment_schedule.service.query;

import com.pulse.appointment_schedule.manager.converter.AppointmentScheduleRoomsDtoConverter;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleRoomsDto;
import com.pulse.appointment_schedule.persist.qto.SearchAppointmentScheduleRoomsQto;
import com.pulse.appointment_schedule.service.AppointmentScheduleBaseDtoService;
import com.pulse.appointment_schedule.service.index.entity.SearchAppointmentScheduleRoomsQtoService;
import com.pulse.appointment_schedule.service.query.assembler.AppointmentScheduleRoomsDtoDataAssembler;
import com.pulse.appointment_schedule.service.query.assembler.AppointmentScheduleRoomsDtoDataAssembler.AppointmentScheduleRoomsDtoDataHolder;
import com.pulse.appointment_schedule.service.query.collector.AppointmentScheduleRoomsDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** AppointmentScheduleRoomsDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "492d0a63-72da-39be-8fe3-e2e30370419f")
public class AppointmentScheduleRoomsDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleBaseDtoService appointmentScheduleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleRoomsDtoConverter appointmentScheduleRoomsDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleRoomsDtoDataAssembler appointmentScheduleRoomsDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleRoomsDtoDataCollector appointmentScheduleRoomsDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchAppointmentScheduleRoomsQtoService searchAppointmentScheduleRoomsQtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "4527c788-e042-3873-a451-792746c4c523")
    private List<AppointmentScheduleRoomsDto> toDtoList(
            List<String> ids, AppointmentScheduleRoomsDtoDataHolder dataHolder) {
        List<AppointmentScheduleBaseDto> baseDtoList =
                appointmentScheduleBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, AppointmentScheduleRoomsDto> dtoMap =
                appointmentScheduleRoomsDtoConverter
                        .convertFromAppointmentScheduleBaseDtoToAppointmentScheduleRoomsDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        AppointmentScheduleRoomsDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchAppointmentScheduleRoomsQto查询AppointmentScheduleRoomsDto列表,上限10000 */
    @PublicInterface(id = "77ecc5da-**************-60d502534900", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "da0fe563-745c-3f94-877e-6b137b95ee18")
    public List<AppointmentScheduleRoomsDto> searchAppointmentScheduleRooms(
            @Valid @NotNull SearchAppointmentScheduleRoomsQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchAppointmentScheduleRoomsQtoService.query(qto);
        AppointmentScheduleRoomsDtoDataHolder dataHolder =
                new AppointmentScheduleRoomsDtoDataHolder();
        List<AppointmentScheduleRoomsDto> result = toDtoList(ids, dataHolder);
        appointmentScheduleRoomsDtoDataCollector.collectDataDefault(dataHolder);
        appointmentScheduleRoomsDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
