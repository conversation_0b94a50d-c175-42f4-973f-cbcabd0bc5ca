package com.pulse.appointment_schedule.service.query;

import com.pulse.appointment_schedule.manager.converter.ClinicRegisterTypeItemDtoConverter;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterTypeBaseDto;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterTypeItemDto;
import com.pulse.appointment_schedule.persist.qto.ListClinicRegisterTypeItemQto;
import com.pulse.appointment_schedule.service.ClinicRegisterTypeBaseDtoService;
import com.pulse.appointment_schedule.service.index.entity.ListClinicRegisterTypeItemQtoService;
import com.pulse.appointment_schedule.service.query.assembler.ClinicRegisterTypeItemDtoDataAssembler;
import com.pulse.appointment_schedule.service.query.assembler.ClinicRegisterTypeItemDtoDataAssembler.ClinicRegisterTypeItemDtoDataHolder;
import com.pulse.appointment_schedule.service.query.collector.ClinicRegisterTypeItemDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** ClinicRegisterTypeItemDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "0c55e515-918e-3f66-9b07-c0fa78467c86")
public class ClinicRegisterTypeItemDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterTypeBaseDtoService clinicRegisterTypeBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterTypeItemDtoConverter clinicRegisterTypeItemDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterTypeItemDtoDataAssembler clinicRegisterTypeItemDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterTypeItemDtoDataCollector clinicRegisterTypeItemDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ListClinicRegisterTypeItemQtoService listClinicRegisterTypeItemQtoService;

    /** 根据ListClinicRegisterTypeItemQto查询ClinicRegisterTypeItemDto列表,瀑布流 */
    @PublicInterface(id = "d2189111-fa69-499f-ad83-7750947109bc", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "3547301f-5d89-37b5-b2e9-be1a12ad456d")
    public VSQueryResult<ClinicRegisterTypeItemDto> queryByListClinicRegisterTypeItemWaterfall(
            @Valid @NotNull ListClinicRegisterTypeItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listClinicRegisterTypeItemQtoService.queryWaterfall(qto);
        ClinicRegisterTypeItemDtoDataHolder dataHolder = new ClinicRegisterTypeItemDtoDataHolder();
        List<ClinicRegisterTypeItemDto> dtoList = toDtoList(ids, dataHolder);
        clinicRegisterTypeItemDtoDataCollector.collectDataDefault(dataHolder);
        clinicRegisterTypeItemDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(listClinicRegisterTypeItemQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "bade2be9-a0d3-3a35-b796-804c8cfceb52")
    private List<ClinicRegisterTypeItemDto> toDtoList(
            List<String> ids, ClinicRegisterTypeItemDtoDataHolder dataHolder) {
        List<ClinicRegisterTypeBaseDto> baseDtoList =
                clinicRegisterTypeBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, ClinicRegisterTypeItemDto> dtoMap =
                clinicRegisterTypeItemDtoConverter
                        .convertFromClinicRegisterTypeBaseDtoToClinicRegisterTypeItemDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ClinicRegisterTypeItemDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListClinicRegisterTypeItemQto查询ClinicRegisterTypeItemDto列表,分页 */
    @PublicInterface(id = "e8dddbc7-4bf3-4b1f-adde-a47ebd926090", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "ce0dc342-090d-3d02-a040-d0020dd83b1a")
    public VSQueryResult<ClinicRegisterTypeItemDto> listClinicRegisterTypeItemPaged(
            @Valid @NotNull ListClinicRegisterTypeItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listClinicRegisterTypeItemQtoService.queryPaged(qto);
        ClinicRegisterTypeItemDtoDataHolder dataHolder = new ClinicRegisterTypeItemDtoDataHolder();
        List<ClinicRegisterTypeItemDto> dtoList = toDtoList(ids, dataHolder);
        clinicRegisterTypeItemDtoDataCollector.collectDataDefault(dataHolder);
        clinicRegisterTypeItemDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listClinicRegisterTypeItemQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
