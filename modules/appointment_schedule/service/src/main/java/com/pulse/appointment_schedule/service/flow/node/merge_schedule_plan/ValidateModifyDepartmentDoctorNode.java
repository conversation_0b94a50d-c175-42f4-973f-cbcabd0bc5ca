package com.pulse.appointment_schedule.service.flow.node.merge_schedule_plan;

import com.pulse.appointment_schedule.service.SchedulePlanDtoService;
import com.pulse.appointment_schedule.service.flow.context.MergeSchedulePlanContext;
import com.vs.code.AutoGenerated;
import com.vs.flow.node.NodeIfComponent;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("appointmentSchedule-mergeSchedulePlan-validateModifyDepartmentDoctor")
@AutoGenerated(locked = false, uuid = "8ee5f464-ce72-4536-9d4b-8e4bcc03c17b|FLOW_NODE|DEFINITION")
public class ValidateModifyDepartmentDoctorNode extends NodeIfComponent {
    @Resource SchedulePlanDtoService schedulePlanDtoService;

    /**
     * 实现流程判断逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "8ee5f464-ce72-4536-9d4b-8e4bcc03c17b")
    public boolean processIf() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        MergeSchedulePlanContext context = getFirstContextBean();

        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑
        System.out.println("validate_modify_department_doctor");
        return context.getModifyFlag();
    }
}
