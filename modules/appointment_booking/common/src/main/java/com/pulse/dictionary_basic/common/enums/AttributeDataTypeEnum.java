package com.pulse.dictionary_basic.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "4b53f1a6-5774-4169-a547-677075ca38ab|ENUM|DEFINITION")
public enum AttributeDataTypeEnum {

    /** 字符串 */
    STRING(),

    /** 布尔 */
    BOOLEAN(),

    /** 数字 */
    NUMBER(),

    /** 日期 */
    DATE(),

    /** 日期时间 */
    DATETIME(),

    /** json复杂结构值 */
    JSON();

    @AutoGenerated(locked = true)
    AttributeDataTypeEnum() {}
}
