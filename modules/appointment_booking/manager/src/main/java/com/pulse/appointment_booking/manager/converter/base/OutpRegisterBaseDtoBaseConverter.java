package com.pulse.appointment_booking.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.appointment_booking.persist.dos.OutpRegister;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "0ffd3ab4-3d96-455b-af1a-511e22420b0f|DTO|BASE_CONVERTER")
public class OutpRegisterBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OutpRegisterBaseDto convertFromOutpRegisterToOutpRegisterBaseDto(
            OutpRegister outpRegister) {
        return convertFromOutpRegisterToOutpRegisterBaseDto(List.of(outpRegister)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OutpRegisterBaseDto> convertFromOutpRegisterToOutpRegisterBaseDto(
            List<OutpRegister> outpRegisterList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpRegisterList)) {
            return new ArrayList<>();
        }
        List<OutpRegisterBaseDto> outpRegisterBaseDtoList = new ArrayList<>();
        for (OutpRegister outpRegister : outpRegisterList) {
            if (outpRegister == null) {
                continue;
            }
            OutpRegisterBaseDto outpRegisterBaseDto = new OutpRegisterBaseDto();
            outpRegisterBaseDto.setId(outpRegister.getId());
            outpRegisterBaseDto.setOutpAppointId(outpRegister.getOutpAppointId());
            outpRegisterBaseDto.setPatientId(outpRegister.getPatientId());
            outpRegisterBaseDto.setVisitCardId(outpRegister.getVisitCardId());
            outpRegisterBaseDto.setClinicRegisterTypeId(outpRegister.getClinicRegisterTypeId());
            outpRegisterBaseDto.setMdtFeeType(outpRegister.getMdtFeeType());
            outpRegisterBaseDto.setSourceAppId(outpRegister.getSourceAppId());
            outpRegisterBaseDto.setSourceCampusId(outpRegister.getSourceCampusId());
            outpRegisterBaseDto.setTallyStatus(outpRegister.getTallyStatus());
            outpRegisterBaseDto.setRegisterNumber(outpRegister.getRegisterNumber());
            outpRegisterBaseDto.setPatientName(outpRegister.getPatientName());
            outpRegisterBaseDto.setTimeDescription(outpRegister.getTimeDescription());
            outpRegisterBaseDto.setAge(outpRegister.getAge());
            outpRegisterBaseDto.setGender(outpRegister.getGender());
            outpRegisterBaseDto.setIdentityType(outpRegister.getIdentityType());
            outpRegisterBaseDto.setInsuranceType(outpRegister.getInsuranceType());
            outpRegisterBaseDto.setInsuranceNumber(outpRegister.getInsuranceNumber());
            outpRegisterBaseDto.setRegisterStatus(outpRegister.getRegisterStatus());
            outpRegisterBaseDto.setOperatorId(outpRegister.getOperatorId());
            outpRegisterBaseDto.setOperatorDepartmentId(outpRegister.getOperatorDepartmentId());
            outpRegisterBaseDto.setRegisterDate(outpRegister.getRegisterDate());
            outpRegisterBaseDto.setSettleNumber(outpRegister.getSettleNumber());
            outpRegisterBaseDto.setCancellationInitiatorId(
                    outpRegister.getCancellationInitiatorId());
            outpRegisterBaseDto.setCancellationInitiationTime(
                    outpRegister.getCancellationInitiationTime());
            outpRegisterBaseDto.setReviewerId(outpRegister.getReviewerId());
            outpRegisterBaseDto.setReviewTime(outpRegister.getReviewTime());
            outpRegisterBaseDto.setChargeStatus(outpRegister.getChargeStatus());
            outpRegisterBaseDto.setTotalCost(outpRegister.getTotalCost());
            outpRegisterBaseDto.setTotalCharge(outpRegister.getTotalCharge());
            outpRegisterBaseDto.setReturnOperatorId(outpRegister.getReturnOperatorId());
            outpRegisterBaseDto.setReturnDate(outpRegister.getReturnDate());
            outpRegisterBaseDto.setReturnSettleNumber(outpRegister.getReturnSettleNumber());
            outpRegisterBaseDto.setReturnReason(outpRegister.getReturnReason());
            outpRegisterBaseDto.setChargePriceScheduleId(outpRegister.getChargePriceScheduleId());
            outpRegisterBaseDto.setDiscountCategory(outpRegister.getDiscountCategory());
            outpRegisterBaseDto.setTallyReceiptNumber(outpRegister.getTallyReceiptNumber());
            outpRegisterBaseDto.setChargeTypeCode(outpRegister.getChargeTypeCode());
            outpRegisterBaseDto.setIdentityCode(outpRegister.getIdentityCode());
            outpRegisterBaseDto.setTakeAppointOperatorId(outpRegister.getTakeAppointOperatorId());
            outpRegisterBaseDto.setTakeAppointDate(outpRegister.getTakeAppointDate());
            outpRegisterBaseDto.setRegistrationDoctorId(outpRegister.getRegistrationDoctorId());
            outpRegisterBaseDto.setRegistrationDepartmentId(
                    outpRegister.getRegistrationDepartmentId());
            outpRegisterBaseDto.setOutpatientRegistrationCategory(
                    outpRegister.getOutpatientRegistrationCategory());
            outpRegisterBaseDto.setSpecifiedDiseaseFlag(outpRegister.getSpecifiedDiseaseFlag());
            outpRegisterBaseDto.setSpecifiedDiseaseCode(outpRegister.getSpecifiedDiseaseCode());
            outpRegisterBaseDto.setWaitingStartTime(outpRegister.getWaitingStartTime());
            outpRegisterBaseDto.setWaitingEndTime(outpRegister.getWaitingEndTime());
            outpRegisterBaseDto.setRegisterReviewStatus(outpRegister.getRegisterReviewStatus());
            outpRegisterBaseDto.setReviewReason(outpRegister.getReviewReason());
            outpRegisterBaseDto.setReviewExplain(outpRegister.getReviewExplain());
            outpRegisterBaseDto.setHealthOfficialsFee(outpRegister.getHealthOfficialsFee());
            outpRegisterBaseDto.setInternationalPatientFee(
                    outpRegister.getInternationalPatientFee());
            outpRegisterBaseDto.setLockVersion(outpRegister.getLockVersion());
            outpRegisterBaseDto.setCreatedAt(outpRegister.getCreatedAt());
            outpRegisterBaseDto.setUpdatedAt(outpRegister.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            outpRegisterBaseDtoList.add(outpRegisterBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return outpRegisterBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
