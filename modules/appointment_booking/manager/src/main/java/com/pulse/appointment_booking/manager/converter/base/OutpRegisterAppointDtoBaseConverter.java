package com.pulse.appointment_booking.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterBaseDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterAppointDto;
import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "c138b18c-2894-4844-8d80-c0d7d5b77887|DTO|BASE_CONVERTER")
public class OutpRegisterAppointDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterBaseDtoManager outpRegisterBaseDtoManager;

    @AutoGenerated(locked = true)
    public OutpRegisterBaseDto convertFromOutpRegisterAppointDtoToOutpRegisterBaseDto(
            OutpRegisterAppointDto outpRegisterAppointDto) {
        return convertFromOutpRegisterAppointDtoToOutpRegisterBaseDto(
                        List.of(outpRegisterAppointDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OutpRegisterBaseDto> convertFromOutpRegisterAppointDtoToOutpRegisterBaseDto(
            List<OutpRegisterAppointDto> outpRegisterAppointDtoList) {
        if (CollectionUtil.isEmpty(outpRegisterAppointDtoList)) {
            return new ArrayList<>();
        }
        return outpRegisterBaseDtoManager.getByIds(
                outpRegisterAppointDtoList.stream()
                        .map(OutpRegisterAppointDto::getId)
                        .collect(Collectors.toList()));
    }

    @AutoGenerated(locked = true)
    public OutpRegisterAppointDto convertFromOutpRegisterBaseDtoToOutpRegisterAppointDto(
            OutpRegisterBaseDto outpRegisterBaseDto) {
        return convertFromOutpRegisterBaseDtoToOutpRegisterAppointDto(List.of(outpRegisterBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<OutpRegisterAppointDto> convertFromOutpRegisterBaseDtoToOutpRegisterAppointDto(
            List<OutpRegisterBaseDto> outpRegisterBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpRegisterBaseDtoList)) {
            return new ArrayList<>();
        }
        List<OutpRegisterAppointDto> outpRegisterAppointDtoList = new ArrayList<>();
        for (OutpRegisterBaseDto outpRegisterBaseDto : outpRegisterBaseDtoList) {
            if (outpRegisterBaseDto == null) {
                continue;
            }
            OutpRegisterAppointDto outpRegisterAppointDto = new OutpRegisterAppointDto();
            outpRegisterAppointDto.setId(outpRegisterBaseDto.getId());
            outpRegisterAppointDto.setPatientId(outpRegisterBaseDto.getPatientId());
            outpRegisterAppointDto.setVisitCardId(outpRegisterBaseDto.getVisitCardId());
            outpRegisterAppointDto.setClinicRegisterTypeId(
                    outpRegisterBaseDto.getClinicRegisterTypeId());
            outpRegisterAppointDto.setSourceAppId(outpRegisterBaseDto.getSourceAppId());
            outpRegisterAppointDto.setSourceCampusId(outpRegisterBaseDto.getSourceCampusId());
            outpRegisterAppointDto.setTallyStatus(outpRegisterBaseDto.getTallyStatus());
            outpRegisterAppointDto.setRegisterNumber(outpRegisterBaseDto.getRegisterNumber());
            outpRegisterAppointDto.setPatientName(outpRegisterBaseDto.getPatientName());
            outpRegisterAppointDto.setAge(outpRegisterBaseDto.getAge());
            outpRegisterAppointDto.setIdentityType(outpRegisterBaseDto.getIdentityType());
            outpRegisterAppointDto.setInsuranceType(outpRegisterBaseDto.getInsuranceType());
            outpRegisterAppointDto.setInsuranceNumber(outpRegisterBaseDto.getInsuranceNumber());
            outpRegisterAppointDto.setRegisterStatus(outpRegisterBaseDto.getRegisterStatus());
            outpRegisterAppointDto.setOperatorId(outpRegisterBaseDto.getOperatorId());
            outpRegisterAppointDto.setRegisterDate(outpRegisterBaseDto.getRegisterDate());
            outpRegisterAppointDto.setSettleNumber(outpRegisterBaseDto.getSettleNumber());
            outpRegisterAppointDto.setCancellationInitiatorId(
                    outpRegisterBaseDto.getCancellationInitiatorId());
            outpRegisterAppointDto.setCancellationInitiationTime(
                    outpRegisterBaseDto.getCancellationInitiationTime());
            outpRegisterAppointDto.setReviewerId(outpRegisterBaseDto.getReviewerId());
            outpRegisterAppointDto.setReviewTime(outpRegisterBaseDto.getReviewTime());
            outpRegisterAppointDto.setTotalCost(outpRegisterBaseDto.getTotalCost());
            outpRegisterAppointDto.setTotalCharge(outpRegisterBaseDto.getTotalCharge());
            outpRegisterAppointDto.setReturnOperatorId(outpRegisterBaseDto.getReturnOperatorId());
            outpRegisterAppointDto.setReturnDate(outpRegisterBaseDto.getReturnDate());
            outpRegisterAppointDto.setReturnSettleNumber(
                    outpRegisterBaseDto.getReturnSettleNumber());
            outpRegisterAppointDto.setReturnReason(outpRegisterBaseDto.getReturnReason());
            outpRegisterAppointDto.setChargePriceScheduleId(
                    outpRegisterBaseDto.getChargePriceScheduleId());
            outpRegisterAppointDto.setDiscountCategory(outpRegisterBaseDto.getDiscountCategory());
            outpRegisterAppointDto.setTallyReceiptNumber(
                    outpRegisterBaseDto.getTallyReceiptNumber());
            outpRegisterAppointDto.setChargeTypeCode(outpRegisterBaseDto.getChargeTypeCode());
            outpRegisterAppointDto.setIdentityCode(outpRegisterBaseDto.getIdentityCode());
            outpRegisterAppointDto.setTakeAppointOperatorId(
                    outpRegisterBaseDto.getTakeAppointOperatorId());
            outpRegisterAppointDto.setTakeAppointDate(outpRegisterBaseDto.getTakeAppointDate());
            outpRegisterAppointDto.setRegistrationDoctorId(
                    outpRegisterBaseDto.getRegistrationDoctorId());
            outpRegisterAppointDto.setRegistrationDepartmentId(
                    outpRegisterBaseDto.getRegistrationDepartmentId());
            outpRegisterAppointDto.setOutpatientRegistrationCategory(
                    outpRegisterBaseDto.getOutpatientRegistrationCategory());
            outpRegisterAppointDto.setSpecifiedDiseaseFlag(
                    outpRegisterBaseDto.getSpecifiedDiseaseFlag());
            outpRegisterAppointDto.setSpecifiedDiseaseCode(
                    outpRegisterBaseDto.getSpecifiedDiseaseCode());
            outpRegisterAppointDto.setWaitingStartTime(outpRegisterBaseDto.getWaitingStartTime());
            outpRegisterAppointDto.setWaitingEndTime(outpRegisterBaseDto.getWaitingEndTime());
            outpRegisterAppointDto.setRegisterReviewStatus(
                    outpRegisterBaseDto.getRegisterReviewStatus());
            outpRegisterAppointDto.setReviewReason(outpRegisterBaseDto.getReviewReason());
            outpRegisterAppointDto.setReviewExplain(outpRegisterBaseDto.getReviewExplain());
            outpRegisterAppointDto.setCreatedAt(outpRegisterBaseDto.getCreatedAt());
            outpRegisterAppointDto.setUpdatedAt(outpRegisterBaseDto.getUpdatedAt());
            outpRegisterAppointDto.setMdtFeeType(outpRegisterBaseDto.getMdtFeeType());
            outpRegisterAppointDto.setOutpAppointId(outpRegisterBaseDto.getOutpAppointId());
            outpRegisterAppointDto.setTimeDescription(outpRegisterBaseDto.getTimeDescription());
            outpRegisterAppointDto.setGender(outpRegisterBaseDto.getGender());
            outpRegisterAppointDto.setChargeStatus(outpRegisterBaseDto.getChargeStatus());
            outpRegisterAppointDto.setHealthOfficialsFee(
                    outpRegisterBaseDto.getHealthOfficialsFee());
            outpRegisterAppointDto.setInternationalPatientFee(
                    outpRegisterBaseDto.getInternationalPatientFee());
            outpRegisterAppointDto.setOperatorDepartmentId(
                    outpRegisterBaseDto.getOperatorDepartmentId());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            outpRegisterAppointDtoList.add(outpRegisterAppointDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return outpRegisterAppointDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
