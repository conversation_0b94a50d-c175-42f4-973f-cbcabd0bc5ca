package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.TimePeriodEo;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.organization.common.enums.GenderLimitEnum;
import com.pulse.organization.common.enums.MedicalServiceTypeEnum;
import com.pulse.organization.common.enums.OrganizationPropertyEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.StorageTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "d42fc739-6714-4273-b32e-470a8fe339f8|DTO|DEFINITION")
public class DepartmentBaseDto {
    /** 核算科室组织id */
    @AutoGenerated(locked = true, uuid = "d5e3d4c2-ac5c-41d7-af7d-c66e491386c9")
    private String accountingDepartmentOrganizationId;

    /** 年龄下限 */
    @AutoGenerated(locked = true, uuid = "dcbff58f-0f36-4f11-9e88-25eaeb0df49c")
    private Long ageLowerLimit;

    /** 年龄上限 */
    @AutoGenerated(locked = true, uuid = "093d5e1f-b4dd-4420-91b4-65883460e2be")
    private Long ageUpperLimit;

    /** 平均处方限额 */
    @AutoGenerated(locked = true, uuid = "4d9ee60e-ff16-46be-acfb-7a0645eb3dd1")
    private BigDecimal averagePrescriptionLimit;

    /** 院区组织id */
    @AutoGenerated(locked = true, uuid = "e619e2a5-3c26-441e-8fc6-dc1d11ddc599")
    private String campusOrganizationId;

    /** 成本科室组织id */
    @AutoGenerated(locked = true, uuid = "1b54d9bb-bbbf-4842-bdc6-997f24c0a867")
    private String costDepartmentOrganizationId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "cdc00f49-aed1-40f1-931d-902dccbf9562")
    private Date createdAt;

    /** 创建者id */
    @AutoGenerated(locked = true, uuid = "2360b38d-99ec-452f-8e4e-12a42d743db0")
    private String createdBy;

    /** 默认处方类型 */
    @AutoGenerated(locked = true, uuid = "71c0e798-8b3d-448d-9616-aad9b6387f69")
    private String defaultPrescriptionType;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "f10c8469-7873-4663-83c8-a6a08fbce820")
    private Long deletedAt;

    /** 删除者id */
    @AutoGenerated(locked = true, uuid = "7f5afec3-8d62-4495-9702-69aaa822e588")
    private String deletedBy;

    /** 科室层级 */
    @AutoGenerated(locked = true, uuid = "037a5dd7-e36e-45db-99d3-e9724e6d22c3")
    private Long departmentLevel;

    /** 科室性质 */
    @AutoGenerated(locked = true, uuid = "74cf529a-a333-4697-8c80-47fbd25961e6")
    private OrganizationPropertyEnum departmentProperty;

    /** 药品分类列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b2981bd9-4f5f-4bb9-8f0a-b831adb20c12")
    private List<String> drugCatalogList;

    /** 药物类别列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "99a978b2-5518-4783-891c-d8a6596c08e1")
    private List<DrugTypeEnum> drugTypeList;

    /** 性别限制 */
    @AutoGenerated(locked = true, uuid = "8d0a0158-2bfc-4655-81e1-f7b22814dc81")
    private GenderLimitEnum genderLimit;

    /** 人事科室组织id */
    @AutoGenerated(locked = true, uuid = "12a9ca0d-598b-413a-acbc-d2eee60d5c84")
    private String hrDepartmentOrganizationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b36b18b3-54f8-421c-bdb8-7f95367fd3b5")
    private String id;

    /** 针剂领第二天量 */
    @AutoGenerated(locked = true, uuid = "145828b2-8bcd-4ccd-853c-4af523eb58c5")
    private BigDecimal injectionSecondDayAmount;

    /** 管理科室组织id */
    @AutoGenerated(locked = true, uuid = "828a46f1-5367-4b19-93b4-503cfb44d9e5")
    private String manageDepartmentOrganizationId;

    /** 病案科室代码 */
    @AutoGenerated(locked = true, uuid = "93ff9b15-04e8-4f32-add7-6941b712a3fe")
    private String medicalRecordDepartmentCode;

    /** 医疗服务类型 */
    @AutoGenerated(locked = true, uuid = "a242c66a-6da5-4017-8df7-0f4cce92e6ac")
    private MedicalServiceTypeEnum medicalServiceType;

    /** 开放时间段 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d656e2f0-82e2-44dc-a27a-e3b22cb2e249")
    private TimePeriodEo openTimePeriod;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "bdd77bf1-8181-4295-a81f-51c1347552de")
    private String organizationId;

    /** 上级科室组织id */
    @AutoGenerated(locked = true, uuid = "54fc70db-19b7-4b38-a0fc-92af97e61e59")
    private String parentDepartmentOrganizationId;

    /** 省平台作废标志 */
    @AutoGenerated(locked = true, uuid = "acecd112-85ba-43ed-a7ca-f3fb399d13e3")
    private Boolean provincePlatformCancelFlag;

    /** 挂号科室启用标志 */
    @AutoGenerated(locked = true, uuid = "a17024f4-016e-4a65-b5d4-7c4c0ed572c4")
    private Boolean registerDepartmentEnableFlag;

    /** 挂号科室标志 */
    @AutoGenerated(locked = true, uuid = "5be11166-b637-41f4-8305-683118484fbd")
    private Boolean registerDepartmentFlag;

    /** 专科标志 */
    @AutoGenerated(locked = true, uuid = "d54f3fdc-c9c9-42bb-8ceb-6ae1c643bf6f")
    private Boolean specialtyFlag;

    /** 标准科室目录id */
    @AutoGenerated(locked = true, uuid = "6809730e-2d46-4b13-8614-2124fd867c61")
    private String standardDepartmentCatalogId;

    /** 库房类型 */
    @AutoGenerated(locked = true, uuid = "685c3e52-b5d8-47f1-b425-8e6f1a71e794")
    private StorageTypeEnum storageType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "e6c2eaba-e7f7-4450-8a3c-bbf791a1b8e0")
    private Date updatedAt;

    /** 更新者id */
    @AutoGenerated(locked = true, uuid = "c12692a8-3b74-4624-ad0e-98d01a782cd7")
    private String updatedBy;
}
