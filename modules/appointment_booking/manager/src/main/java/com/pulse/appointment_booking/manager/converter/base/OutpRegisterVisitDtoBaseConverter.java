package com.pulse.appointment_booking.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterBaseDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.appointment_booking.manager.dto.OutpRegisterVisitDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "c3813169-1d4b-49e4-8f2b-d96491e257c2|DTO|BASE_CONVERTER")
public class OutpRegisterVisitDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterBaseDtoManager outpRegisterBaseDtoManager;

    @AutoGenerated(locked = true)
    public OutpRegisterVisitDto convertFromOutpRegisterBaseDtoToOutpRegisterVisitDto(
            OutpRegisterBaseDto outpRegisterBaseDto) {
        return convertFromOutpRegisterBaseDtoToOutpRegisterVisitDto(List.of(outpRegisterBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<OutpRegisterVisitDto> convertFromOutpRegisterBaseDtoToOutpRegisterVisitDto(
            List<OutpRegisterBaseDto> outpRegisterBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpRegisterBaseDtoList)) {
            return new ArrayList<>();
        }
        List<OutpRegisterVisitDto> outpRegisterVisitDtoList = new ArrayList<>();
        for (OutpRegisterBaseDto outpRegisterBaseDto : outpRegisterBaseDtoList) {
            if (outpRegisterBaseDto == null) {
                continue;
            }
            OutpRegisterVisitDto outpRegisterVisitDto = new OutpRegisterVisitDto();
            outpRegisterVisitDto.setId(outpRegisterBaseDto.getId());
            outpRegisterVisitDto.setPatientId(outpRegisterBaseDto.getPatientId());
            outpRegisterVisitDto.setVisitCardId(outpRegisterBaseDto.getVisitCardId());
            outpRegisterVisitDto.setClinicRegisterTypeId(
                    outpRegisterBaseDto.getClinicRegisterTypeId());
            outpRegisterVisitDto.setSourceAppId(outpRegisterBaseDto.getSourceAppId());
            outpRegisterVisitDto.setSourceCampusId(outpRegisterBaseDto.getSourceCampusId());
            outpRegisterVisitDto.setTallyStatus(outpRegisterBaseDto.getTallyStatus());
            outpRegisterVisitDto.setRegisterNumber(outpRegisterBaseDto.getRegisterNumber());
            outpRegisterVisitDto.setPatientName(outpRegisterBaseDto.getPatientName());
            outpRegisterVisitDto.setAge(outpRegisterBaseDto.getAge());
            outpRegisterVisitDto.setIdentityType(outpRegisterBaseDto.getIdentityType());
            outpRegisterVisitDto.setInsuranceType(outpRegisterBaseDto.getInsuranceType());
            outpRegisterVisitDto.setInsuranceNumber(outpRegisterBaseDto.getInsuranceNumber());
            outpRegisterVisitDto.setRegisterStatus(outpRegisterBaseDto.getRegisterStatus());
            outpRegisterVisitDto.setOperatorId(outpRegisterBaseDto.getOperatorId());
            outpRegisterVisitDto.setRegisterDate(outpRegisterBaseDto.getRegisterDate());
            outpRegisterVisitDto.setSettleNumber(outpRegisterBaseDto.getSettleNumber());
            outpRegisterVisitDto.setCancellationInitiatorId(
                    outpRegisterBaseDto.getCancellationInitiatorId());
            outpRegisterVisitDto.setCancellationInitiationTime(
                    outpRegisterBaseDto.getCancellationInitiationTime());
            outpRegisterVisitDto.setReviewerId(outpRegisterBaseDto.getReviewerId());
            outpRegisterVisitDto.setReviewTime(outpRegisterBaseDto.getReviewTime());
            outpRegisterVisitDto.setTotalCost(outpRegisterBaseDto.getTotalCost());
            outpRegisterVisitDto.setTotalCharge(outpRegisterBaseDto.getTotalCharge());
            outpRegisterVisitDto.setReturnOperatorId(outpRegisterBaseDto.getReturnOperatorId());
            outpRegisterVisitDto.setReturnDate(outpRegisterBaseDto.getReturnDate());
            outpRegisterVisitDto.setReturnSettleNumber(outpRegisterBaseDto.getReturnSettleNumber());
            outpRegisterVisitDto.setReturnReason(outpRegisterBaseDto.getReturnReason());
            outpRegisterVisitDto.setChargePriceScheduleId(
                    outpRegisterBaseDto.getChargePriceScheduleId());
            outpRegisterVisitDto.setDiscountCategory(outpRegisterBaseDto.getDiscountCategory());
            outpRegisterVisitDto.setTallyReceiptNumber(outpRegisterBaseDto.getTallyReceiptNumber());
            outpRegisterVisitDto.setChargeTypeCode(outpRegisterBaseDto.getChargeTypeCode());
            outpRegisterVisitDto.setIdentityCode(outpRegisterBaseDto.getIdentityCode());
            outpRegisterVisitDto.setTakeAppointOperatorId(
                    outpRegisterBaseDto.getTakeAppointOperatorId());
            outpRegisterVisitDto.setTakeAppointDate(outpRegisterBaseDto.getTakeAppointDate());
            outpRegisterVisitDto.setRegistrationDoctorId(
                    outpRegisterBaseDto.getRegistrationDoctorId());
            outpRegisterVisitDto.setRegistrationDepartmentId(
                    outpRegisterBaseDto.getRegistrationDepartmentId());
            outpRegisterVisitDto.setOutpatientRegistrationCategory(
                    outpRegisterBaseDto.getOutpatientRegistrationCategory());
            outpRegisterVisitDto.setSpecifiedDiseaseFlag(
                    outpRegisterBaseDto.getSpecifiedDiseaseFlag());
            outpRegisterVisitDto.setSpecifiedDiseaseCode(
                    outpRegisterBaseDto.getSpecifiedDiseaseCode());
            outpRegisterVisitDto.setWaitingStartTime(outpRegisterBaseDto.getWaitingStartTime());
            outpRegisterVisitDto.setWaitingEndTime(outpRegisterBaseDto.getWaitingEndTime());
            outpRegisterVisitDto.setRegisterReviewStatus(
                    outpRegisterBaseDto.getRegisterReviewStatus());
            outpRegisterVisitDto.setReviewReason(outpRegisterBaseDto.getReviewReason());
            outpRegisterVisitDto.setReviewExplain(outpRegisterBaseDto.getReviewExplain());
            outpRegisterVisitDto.setCreatedAt(outpRegisterBaseDto.getCreatedAt());
            outpRegisterVisitDto.setUpdatedAt(outpRegisterBaseDto.getUpdatedAt());
            outpRegisterVisitDto.setMdtFeeType(outpRegisterBaseDto.getMdtFeeType());
            outpRegisterVisitDto.setOutpAppointId(outpRegisterBaseDto.getOutpAppointId());
            outpRegisterVisitDto.setTimeDescription(outpRegisterBaseDto.getTimeDescription());
            outpRegisterVisitDto.setGender(outpRegisterBaseDto.getGender());
            outpRegisterVisitDto.setChargeStatus(outpRegisterBaseDto.getChargeStatus());
            outpRegisterVisitDto.setHealthOfficialsFee(outpRegisterBaseDto.getHealthOfficialsFee());
            outpRegisterVisitDto.setInternationalPatientFee(
                    outpRegisterBaseDto.getInternationalPatientFee());
            outpRegisterVisitDto.setOperatorDepartmentId(
                    outpRegisterBaseDto.getOperatorDepartmentId());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            outpRegisterVisitDtoList.add(outpRegisterVisitDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return outpRegisterVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBaseDto convertFromOutpRegisterVisitDtoToOutpRegisterBaseDto(
            OutpRegisterVisitDto outpRegisterVisitDto) {
        return convertFromOutpRegisterVisitDtoToOutpRegisterBaseDto(List.of(outpRegisterVisitDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OutpRegisterBaseDto> convertFromOutpRegisterVisitDtoToOutpRegisterBaseDto(
            List<OutpRegisterVisitDto> outpRegisterVisitDtoList) {
        if (CollectionUtil.isEmpty(outpRegisterVisitDtoList)) {
            return new ArrayList<>();
        }
        return outpRegisterBaseDtoManager.getByIds(
                outpRegisterVisitDtoList.stream()
                        .map(OutpRegisterVisitDto::getId)
                        .collect(Collectors.toList()));
    }
}
