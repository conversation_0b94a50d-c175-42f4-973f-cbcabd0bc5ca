package com.pulse.dictionary_basic.manager.dto;

import com.pulse.dictionary_basic.common.enums.AttributeDataTypeEnum;
import com.pulse.dictionary_basic.common.enums.InputModeEnum;
import com.pulse.dictionary_basic.common.enums.ValidationMethodEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "62cbe87d-07d3-40da-bd09-c65cff9a3a73|DTO|DEFINITION")
public class AttributeDefinitionBaseDto {
    /** 属性代码 */
    @AutoGenerated(locked = true, uuid = "9e7832b6-fa71-4c00-a367-6fe6df52e3e8")
    private String attributeCode;

    /** 属性名称 */
    @AutoGenerated(locked = true, uuid = "010c66bd-7cac-41cc-89ee-33db1c68f528")
    private String attributeName;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "39bd0a96-3a4b-455c-ac10-c3a92db74cca")
    private Date createdAt;

    /** 数据类型 */
    @AutoGenerated(locked = true, uuid = "c6c13240-73eb-4563-8686-c10598b03b5f")
    private AttributeDataTypeEnum dataType;

    /** 默认值 */
    @AutoGenerated(locked = true, uuid = "06b4465c-e145-466d-9be4-44b51415d48c")
    private String defaultValue;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "bf033ca8-4a27-42dc-8a03-0e19108d3d55")
    private Long deletedAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "7ce2576b-1eef-472f-a52d-e4d56de1d703")
    private String description;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "72e50e7f-4ad2-4a7b-b49b-bff006e5eebb")
    private Boolean enableFlag;

    /** 实体类型 */
    @AutoGenerated(locked = true, uuid = "82351fd2-7835-4ca3-84a5-f5d3072cd7a6")
    private String entityType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "55371608-0f16-49c5-9b54-46c8d82e5671")
    private String id;

    /** 录入方式 */
    @AutoGenerated(locked = true, uuid = "ba4abb47-371c-4c21-9d05-21d880d8acd2")
    private InputModeEnum inputMode;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "67b3db60-d165-4df1-8f48-2730c6234a69")
    private Long lockVersion;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "05e189e1-e70a-4e06-b1c2-a6c5b39607d6")
    private Long sortNumber;

    /** 关联实体子类型 */
    @AutoGenerated(locked = true, uuid = "edc33226-0a22-4769-98ea-395c90e635b7")
    private String subEntityType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "023c48d4-1673-4676-8414-d685e1e62575")
    private Date updatedAt;

    /** 验证方法 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ee32c71d-fec1-4b67-9d5a-6e2240c15209")
    private List<ValidationMethodEnum> validationMethodList;

    /** 验证规则 */
    @AutoGenerated(locked = true, uuid = "a1fa7ab3-f77c-49b4-8ef4-7209d0b5b8f8")
    private String validationRule;

    /** 值范围 */
    @AutoGenerated(locked = true, uuid = "3085634d-16f4-4b0d-bc3a-1d8b54e88219")
    private String valueRange;
}
