package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "f0df3b14-0e74-41a9-ae8a-d93977874aad|DTO|DEFINITION")
public class OrganizationDepartmentDto {
    /** 简称 */
    @AutoGenerated(locked = true, uuid = "dee0448f-773e-421a-b460-f37aee15798a")
    private String abbreviation;

    /** 组织地址 */
    @AutoGenerated(locked = true, uuid = "2317bb1f-e3d5-4c36-bba6-cddf1f77a507")
    private String address;

    /** 别名 */
    @AutoGenerated(locked = true, uuid = "3faccb96-2f2a-4417-8118-8b4eb04c4428")
    private String alias;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "2eb3519a-c153-46ec-9e28-6ad48663c065")
    private String contactNumber;

    /** 联系人 */
    @AutoGenerated(locked = true, uuid = "96a8b8b5-ccde-4f45-b052-6620724970cc")
    private String contactPerson;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "6b01f78f-dfc6-4be3-9ecb-9a45a9a523a3")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "a1699ccc-76bd-4002-bc5a-91c4a3085d2f")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "1c80c2e9-335a-40d9-b0c8-f86b9acc2e51")
    private Long deletedAt;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "042614b8-0973-4e33-bef7-261a5a84d383")
    private String deletedBy;

    /** 科室 */
    @Valid
    @AutoGenerated(locked = true, uuid = "81ee01c6-ec84-4bb4-b945-df714a7ca012")
    private DepartmentDto department;

    /** 组织描述 */
    @AutoGenerated(locked = true, uuid = "e4806690-f612-46b7-beb0-8b21c7f0873e")
    private String description;

    /** 英文名 */
    @AutoGenerated(locked = true, uuid = "930d9e0b-962e-4de4-99ab-a194a96ecfca")
    private String englishName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "edbac5b0-d510-4aa0-90a8-51ca5c292fa5")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "70f0df01-1d45-4ee9-95b6-b36df77c4a2e")
    private InputCodeEo inputCode;

    /** 作废标记 */
    @AutoGenerated(locked = true, uuid = "6b4ddf24-4ae1-4b1a-be2c-0287af905a00")
    private Boolean invalidFlag;

    /** 组织名称 */
    @AutoGenerated(locked = true, uuid = "*************-4a7d-ac8f-dada5263bee1")
    private String name;

    /** 组织层级 */
    @AutoGenerated(locked = true, uuid = "d0788c05-c12b-473d-933c-e5290fbaf879")
    private Long organizationLevel;

    /** 上级组织ID */
    @AutoGenerated(locked = true, uuid = "7c1e7078-8895-408c-90c0-e6a6151c4416")
    private String parentId;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "8f1c4ba4-c057-4fcd-9f16-8fcc84085936")
    private Long sortNumber;

    /** 组织状态 */
    @AutoGenerated(locked = true, uuid = "ebf0bb70-6171-4c76-8dec-12dff9245f2d")
    private OrganizationStatusEnum status;

    /** 组织类型 */
    @AutoGenerated(locked = true, uuid = "53801548-ddc2-4877-8c06-8032f552b5cb")
    private OrganizationTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "9bec2486-33be-421f-9069-744ff22abbc9")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "482dbaf1-35a1-479b-9374-f06272cc6b3c")
    private String updatedBy;
}
