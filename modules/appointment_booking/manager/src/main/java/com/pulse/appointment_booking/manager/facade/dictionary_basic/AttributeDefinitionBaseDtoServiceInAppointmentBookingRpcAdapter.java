package com.pulse.appointment_booking.manager.facade.dictionary_basic;

import com.pulse.appointment_booking.manager.facade.dictionary_basic.base.AttributeDefinitionBaseDtoServiceInAppointmentBookingBaseRpcAdapter;
import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96")
@AutoGenerated(locked = false, uuid = "522dfbfe-f636-3a3a-ba0e-0aad0621fc90")
public class AttributeDefinitionBaseDtoServiceInAppointmentBookingRpcAdapter
        extends AttributeDefinitionBaseDtoServiceInAppointmentBookingBaseRpcAdapter {

    @RpcRefer(id = "24939867-3bd1-4238-8cd1-4046e3b7d016", version = "1743572096606")
    @AutoGenerated(locked = false, uuid = "24939867-3bd1-4238-8cd1-4046e3b7d016|RPC|ADAPTER")
    public AttributeDefinitionBaseDto getById(String id) {
        return super.getById(id);
    }
}
