package com.pulse.appointment_booking.manager.facade.appointment_schedule;

import com.pulse.appointment_booking.manager.facade.appointment_schedule.base.SchedulingSlotDetailBOServiceInAppointmentBookingBaseRpcAdapter;
import com.pulse.appointment_schedule.manager.dto.SchedulingSlotDetailBaseDto;
import com.pulse.appointment_schedule.service.bto.UpdateScheduleSlotDetailBto;
import com.pulse.appointment_schedule.service.bto.UpdateSchedulingSlotDetailStatusBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96")
@AutoGenerated(locked = false, uuid = "d554f4cb-56bd-32a2-8eaf-e2cabd963e25")
public class SchedulingSlotDetailBOServiceInAppointmentBookingRpcAdapter
        extends SchedulingSlotDetailBOServiceInAppointmentBookingBaseRpcAdapter {

    @RpcRefer(id = "04c78878-d6c8-4add-9260-0f026e8e4fdd", version = "1747185560290")
    @AutoGenerated(locked = false, uuid = "04c78878-d6c8-4add-9260-0f026e8e4fdd|RPC|ADAPTER")
    public List<SchedulingSlotDetailBaseDto> getSchedulingSlotDetail(
            String appointmentScheduleId,
            String platformId,
            String appointmentClassificationId,
            Date registerTime) {
        return super.getSchedulingSlotDetail(
                appointmentScheduleId, platformId, appointmentClassificationId, registerTime);
    }

    @RpcRefer(id = "19a99691-71a9-4955-a7c6-c8fcb9f08964", version = "1747880665842")
    @AutoGenerated(locked = false, uuid = "19a99691-71a9-4955-a7c6-c8fcb9f08964|RPC|ADAPTER")
    public String updateSchedulingSlotDetailStatus(
            UpdateSchedulingSlotDetailStatusBto updateSchedulingSlotDetailStatusBto) {
        return super.updateSchedulingSlotDetailStatus(updateSchedulingSlotDetailStatusBto);
    }

    @RpcRefer(id = "6fa9d3ec-6c15-4d6c-b81e-f0d4c7793713", version = "1747189432997")
    @AutoGenerated(locked = false, uuid = "6fa9d3ec-6c15-4d6c-b81e-f0d4c7793713|RPC|ADAPTER")
    public String updateScheduleSlotDetail(
            UpdateScheduleSlotDetailBto updateScheduleSlotDetailBto) {
        return super.updateScheduleSlotDetail(updateScheduleSlotDetailBto);
    }
}
