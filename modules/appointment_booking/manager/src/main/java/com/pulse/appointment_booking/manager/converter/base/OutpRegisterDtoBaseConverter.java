package com.pulse.appointment_booking.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterBaseDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.appointment_booking.manager.dto.OutpRegisterDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "b438e52c-5ce1-4af0-8f3f-f95ff5a6f1a2|DTO|BASE_CONVERTER")
public class OutpRegisterDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterBaseDtoManager outpRegisterBaseDtoManager;

    @AutoGenerated(locked = true)
    public OutpRegisterDto convertFromOutpRegisterBaseDtoToOutpRegisterDto(
            OutpRegisterBaseDto outpRegisterBaseDto) {
        return convertFromOutpRegisterBaseDtoToOutpRegisterDto(List.of(outpRegisterBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<OutpRegisterDto> convertFromOutpRegisterBaseDtoToOutpRegisterDto(
            List<OutpRegisterBaseDto> outpRegisterBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpRegisterBaseDtoList)) {
            return new ArrayList<>();
        }
        List<OutpRegisterDto> outpRegisterDtoList = new ArrayList<>();
        for (OutpRegisterBaseDto outpRegisterBaseDto : outpRegisterBaseDtoList) {
            if (outpRegisterBaseDto == null) {
                continue;
            }
            OutpRegisterDto outpRegisterDto = new OutpRegisterDto();
            outpRegisterDto.setId(outpRegisterBaseDto.getId());
            outpRegisterDto.setPatientId(outpRegisterBaseDto.getPatientId());
            outpRegisterDto.setVisitCardId(outpRegisterBaseDto.getVisitCardId());
            outpRegisterDto.setClinicRegisterTypeId(outpRegisterBaseDto.getClinicRegisterTypeId());
            outpRegisterDto.setSourceAppId(outpRegisterBaseDto.getSourceAppId());
            outpRegisterDto.setSourceCampusId(outpRegisterBaseDto.getSourceCampusId());
            outpRegisterDto.setTallyStatus(outpRegisterBaseDto.getTallyStatus());
            outpRegisterDto.setRegisterNumber(outpRegisterBaseDto.getRegisterNumber());
            outpRegisterDto.setPatientName(outpRegisterBaseDto.getPatientName());
            outpRegisterDto.setAge(outpRegisterBaseDto.getAge());
            outpRegisterDto.setIdentityType(outpRegisterBaseDto.getIdentityType());
            outpRegisterDto.setInsuranceType(outpRegisterBaseDto.getInsuranceType());
            outpRegisterDto.setInsuranceNumber(outpRegisterBaseDto.getInsuranceNumber());
            outpRegisterDto.setRegisterStatus(outpRegisterBaseDto.getRegisterStatus());
            outpRegisterDto.setOperatorId(outpRegisterBaseDto.getOperatorId());
            outpRegisterDto.setRegisterDate(outpRegisterBaseDto.getRegisterDate());
            outpRegisterDto.setSettleNumber(outpRegisterBaseDto.getSettleNumber());
            outpRegisterDto.setCancellationInitiatorId(
                    outpRegisterBaseDto.getCancellationInitiatorId());
            outpRegisterDto.setCancellationInitiationTime(
                    outpRegisterBaseDto.getCancellationInitiationTime());
            outpRegisterDto.setReviewerId(outpRegisterBaseDto.getReviewerId());
            outpRegisterDto.setReviewTime(outpRegisterBaseDto.getReviewTime());
            outpRegisterDto.setTotalCost(outpRegisterBaseDto.getTotalCost());
            outpRegisterDto.setTotalCharge(outpRegisterBaseDto.getTotalCharge());
            outpRegisterDto.setReturnOperatorId(outpRegisterBaseDto.getReturnOperatorId());
            outpRegisterDto.setReturnDate(outpRegisterBaseDto.getReturnDate());
            outpRegisterDto.setReturnSettleNumber(outpRegisterBaseDto.getReturnSettleNumber());
            outpRegisterDto.setReturnReason(outpRegisterBaseDto.getReturnReason());
            outpRegisterDto.setChargePriceScheduleId(
                    outpRegisterBaseDto.getChargePriceScheduleId());
            outpRegisterDto.setDiscountCategory(outpRegisterBaseDto.getDiscountCategory());
            outpRegisterDto.setTallyReceiptNumber(outpRegisterBaseDto.getTallyReceiptNumber());
            outpRegisterDto.setChargeTypeCode(outpRegisterBaseDto.getChargeTypeCode());
            outpRegisterDto.setIdentityCode(outpRegisterBaseDto.getIdentityCode());
            outpRegisterDto.setTakeAppointOperatorId(
                    outpRegisterBaseDto.getTakeAppointOperatorId());
            outpRegisterDto.setTakeAppointDate(outpRegisterBaseDto.getTakeAppointDate());
            outpRegisterDto.setRegistrationDoctorId(outpRegisterBaseDto.getRegistrationDoctorId());
            outpRegisterDto.setRegistrationDepartmentId(
                    outpRegisterBaseDto.getRegistrationDepartmentId());
            outpRegisterDto.setOutpatientRegistrationCategory(
                    outpRegisterBaseDto.getOutpatientRegistrationCategory());
            outpRegisterDto.setSpecifiedDiseaseFlag(outpRegisterBaseDto.getSpecifiedDiseaseFlag());
            outpRegisterDto.setSpecifiedDiseaseCode(outpRegisterBaseDto.getSpecifiedDiseaseCode());
            outpRegisterDto.setWaitingStartTime(outpRegisterBaseDto.getWaitingStartTime());
            outpRegisterDto.setWaitingEndTime(outpRegisterBaseDto.getWaitingEndTime());
            outpRegisterDto.setRegisterReviewStatus(outpRegisterBaseDto.getRegisterReviewStatus());
            outpRegisterDto.setReviewReason(outpRegisterBaseDto.getReviewReason());
            outpRegisterDto.setReviewExplain(outpRegisterBaseDto.getReviewExplain());
            outpRegisterDto.setCreatedAt(outpRegisterBaseDto.getCreatedAt());
            outpRegisterDto.setUpdatedAt(outpRegisterBaseDto.getUpdatedAt());
            outpRegisterDto.setMdtFeeType(outpRegisterBaseDto.getMdtFeeType());
            outpRegisterDto.setOutpAppointId(outpRegisterBaseDto.getOutpAppointId());
            outpRegisterDto.setTimeDescription(outpRegisterBaseDto.getTimeDescription());
            outpRegisterDto.setGender(outpRegisterBaseDto.getGender());
            outpRegisterDto.setChargeStatus(outpRegisterBaseDto.getChargeStatus());
            outpRegisterDto.setHealthOfficialsFee(outpRegisterBaseDto.getHealthOfficialsFee());
            outpRegisterDto.setInternationalPatientFee(
                    outpRegisterBaseDto.getInternationalPatientFee());
            outpRegisterDto.setOperatorDepartmentId(outpRegisterBaseDto.getOperatorDepartmentId());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            outpRegisterDtoList.add(outpRegisterDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return outpRegisterDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBaseDto convertFromOutpRegisterDtoToOutpRegisterBaseDto(
            OutpRegisterDto outpRegisterDto) {
        return convertFromOutpRegisterDtoToOutpRegisterBaseDto(List.of(outpRegisterDto)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OutpRegisterBaseDto> convertFromOutpRegisterDtoToOutpRegisterBaseDto(
            List<OutpRegisterDto> outpRegisterDtoList) {
        if (CollectionUtil.isEmpty(outpRegisterDtoList)) {
            return new ArrayList<>();
        }
        return outpRegisterBaseDtoManager.getByIds(
                outpRegisterDtoList.stream()
                        .map(OutpRegisterDto::getId)
                        .collect(Collectors.toList()));
    }
}
