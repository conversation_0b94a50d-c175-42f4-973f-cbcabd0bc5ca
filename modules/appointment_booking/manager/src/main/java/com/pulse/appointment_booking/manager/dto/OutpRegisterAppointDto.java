package com.pulse.appointment_booking.manager.dto;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "c138b18c-2894-4844-8d80-c0d7d5b77887|DTO|DEFINITION")
public class OutpRegisterAppointDto {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "372fbbe4-9877-41ef-b6ee-fdfa30dc2500")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "bfd4038e-ac06-4d03-8eab-dcb7dfad6e32")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "e8957599-3ba5-4942-8119-eeb2bc960a67")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "ca86a06e-a5e8-4b9a-819b-0f865067cd81")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "74ab4c09-9541-454a-a5fb-7bd5dd68c827")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "e0d0a82b-27bd-4366-b148-0f379a70303e")
    private String chargeTypeCode;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "54f99594-beea-4679-b498-e6f34e722664")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "57705a2d-d160-43ee-aba4-58daa5ad4f44")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "3f98a4f3-de53-499a-b3fc-2af1b5c9041f")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "0daaf239-4f7c-4ddf-ab80-7234b2e21d1c")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "4861874d-6e93-4290-b32f-088ea1d50e52")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "fc542691-1b76-4c53-bec2-0f059f073a7f")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "a058bbef-e1f1-485b-acb0-a3cd5b49898a")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "60754e2a-0ff8-4429-a4da-c3f6e1d97a1e")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "ae287be1-946c-479f-bb01-4b78c8a544a7")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "f7deb9fe-2149-4a3c-ac1d-14e9d8ae6654")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "d3e514dc-14f2-4c38-a830-bf7c29b704a1")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "13616c3f-f551-426c-9afa-2234ea7a2ba4")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "a4a2647f-a062-4e5a-8117-d27977cd02ed")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "e65a5780-ff3d-40d3-bbb0-b7f8554c60dd")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "c512bd24-9b67-4afb-90d1-47fe790e8881")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "caf2367b-af36-49dd-91eb-d0eff5cb9b9f")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "1027c4af-898e-445f-b728-be7203c47250")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "6808d946-cbc0-4be2-9e0c-2e2998b3b563")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "f4b7c234-63a5-4594-8036-a89644341684")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "19ce9abd-5660-4ceb-bb13-a3adfc77e580")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "d34b05ed-3a31-495a-9568-953c208319db")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "1ed63956-4937-464a-b705-6cc9d65dabbb")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "c65871f7-fcde-4d34-b488-cc97626dc103")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "2d23e21f-bbdb-419b-90d2-2cd4ef56d340")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "8bab6c9e-6724-4d3c-88d7-536ba0d76734")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "acb5f68a-ba09-4983-9033-e035d6f5d153")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "dc9dc37d-63ab-43e5-a808-a69ae8f33262")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "37bf8f31-3162-4f03-a77f-3b975396a5fb")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "e7c08d1d-d099-4021-89ca-3c55502e98c3")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "b232cf9e-31ff-4c83-99b5-21b97d05044b")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "f5de30bb-bc3d-4d63-baa3-248ba33fafd0")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "6b97e2ff-4763-49bd-97a4-d45837a8f1b7")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "2960a350-3b23-48f2-9f28-ccbb5782eb6d")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "da8e2c48-0e28-45ea-8b34-826894a373b8")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "17286484-636a-4248-b379-7940cbe5d40c")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "cbfbf9d4-4560-4b13-bb8c-9777dadf3d44")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "9062a4ab-c8df-48c4-aef3-d15e2e9e2f79")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "357e2b35-cd0d-44b3-b776-50d03a5d7055")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "edcba27a-08ce-4188-a179-c13afd9a9325")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "7204bcf7-bbaa-43d3-88da-a4ee22ad8b5c")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "2f832cc9-cf61-4f36-93bc-43691aabe1c3")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "23cee928-1f65-4002-bb9c-b920e22ae0ff")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "254a73f6-95e2-4750-9b33-9d917255b650")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "ededa0ee-e39a-4f32-b78a-ec18f18097a9")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "756fad04-5133-4f61-a50a-************")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "8d9990ed-e0d9-4ea8-8a26-41f72dba3282")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "e86fe754-36e4-499f-83d9-b51508da17af")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b44c3bec-3325-4116-92fc-8e800077914b")
    private TimeEo waitingStartTime;
}
