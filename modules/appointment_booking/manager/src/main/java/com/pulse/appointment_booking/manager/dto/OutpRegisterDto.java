package com.pulse.appointment_booking.manager.dto;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "b438e52c-5ce1-4af0-8f3f-f95ff5a6f1a2|DTO|DEFINITION")
public class OutpRegisterDto {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "89f5f50a-9c9b-413f-928f-c5fcb2f3f101")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "5cd0eeae-a94a-46dc-8a99-124600270eeb")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "58a52d53-ef82-451e-b95c-b97e73ea6bfb")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "c70244ab-8cc4-4286-aab5-2227e9bd7343")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "51d20242-3874-438c-a09a-cbfb6c04d807")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "0eea3c9c-58db-421f-83e5-d34c2c75bc3f")
    private String chargeTypeCode;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "e3bbcdec-e588-4fb8-99a6-371d55172e56")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "13787e63-2c3b-4a3f-8fa6-4b9a02cf3042")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "9dbbb946-d318-4714-8d83-640e46b2a04e")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "2034bbd9-1f53-4a58-b6dd-c78bf204de36")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "d1be4abc-34cf-4059-a6d0-bc59ebc694b3")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f46e766b-444d-4aff-8729-fdc411d69b9c")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "d0a8d0ef-3675-4d68-aae5-1c2d79c5b867")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "87f0262b-3e48-4ad7-b385-ecf4794bdd3e")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "f5e3eb2f-dcd7-4777-80b3-176ae4f8eb86")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "20a5c94b-4636-48b5-85f6-97efdd8d3d50")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "6ef609c8-f95a-4774-a449-541fd2238cab")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "f451065f-88fa-4b39-baf9-fa3611f0aedc")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "127994e2-b996-4d4c-89f5-3d22244b3cfd")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "c1e0f139-8e08-464d-b6cf-1946d80dd63a")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "109c4e07-56ad-4b5b-a571-d1117d019156")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "295ed846-7838-483e-84de-a778e2861fe5")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "af2d6903-adfc-4cbe-b45e-bd6fc5b04839")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "1d35e35b-279f-4a0d-8fc9-7d9bf0079e6b")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "2717e14e-f616-46f3-b6d5-886f021390ae")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "bc8dc4d3-5659-44ed-901f-fbfb37cf7435")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "46bbe827-aedd-41e3-9c3c-613ce963d8dd")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "b96ef6ef-a073-48c8-bc3d-0a507166782d")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "551fbe26-7b91-482e-8a8e-56faa6dc659d")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "62ebfffe-13eb-4428-9478-9d13acc35369")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "d2d109ff-f3ef-446a-bec5-5aa2a137fe74")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "2967afb1-f41b-475b-9d64-433f6da18e2c")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "3a22c5a9-d01e-4c12-9901-9c50de4d222b")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "34f7e211-00ea-4832-99c1-cc0276d42f64")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "31e8a889-1787-44ae-89d0-59dd4d996168")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "0fa5d72e-0db0-40ce-ba5e-71dd3bb8d5a9")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "55352150-f726-4795-8574-fa248858a028")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "7e804e93-949c-4fa8-b758-eeb0e9342c03")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "b72236fc-3add-42ac-82da-7e68199da480")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "5a63c010-5c87-4957-8ea2-d3ca46224602")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "850dd711-4398-4a7e-9db2-6d9d212b1ecc")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "a0bd02f9-44ff-4cd9-b925-191561b0e4bf")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "6420206f-53bd-407c-9f42-70bd0535e71a")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "6b6ddbfa-791d-4f9d-90f3-1378e48de0e9")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "01a392be-4050-40fa-bbe8-a554590e9062")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "6a313f6d-32b7-4782-b7e7-b9cd7741ed6e")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "ade336ef-05f9-4286-92cd-a631ead3e100")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "04d0ffd6-b84a-418e-b780-f91396161a48")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "b650a7ce-4ef1-46e4-ad2d-91ac3544e861")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "9338d2c0-01cf-47f4-ae99-754e4985c973")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "641e589b-07af-4f93-8eaa-bdcb4cccae2a")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "8b183a38-c0cb-4b25-a0c5-6c633c5b4d4d")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "88edb0c5-5758-4387-924b-58d9845ca5b3")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d1686382-33c8-494f-9795-233d3f57e5b0")
    private TimeEo waitingStartTime;
}
