package com.pulse.appointment_booking.manager.bo.base;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_booking.manager.bo.OutpRegisterBO;
import com.pulse.appointment_booking.persist.dos.OutpRegister;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.dictionary_basic.persist.eo.converter.AgeEoConverter;
import com.pulse.dictionary_basic.persist.eo.converter.TimeEoConverter;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@DoNotModify
@Table(name = "outp_register")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "ef79153c-bab2-3400-a393-fe562b7ab29f")
public abstract class BaseOutpRegisterBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 患者年龄 */
    @Column(name = "age")
    @Valid
    @AutoGenerated(locked = true, uuid = "9332e8e9-5061-4255-bb33-96252f8abb89")
    @Convert(converter = AgeEoConverter.class)
    private AgeEo age;

    /** 退号发起时间 */
    @Column(name = "cancellation_initiation_time")
    @AutoGenerated(locked = true, uuid = "0430841d-a414-4c86-bb22-09f906c24787")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @Column(name = "cancellation_initiator_id")
    @AutoGenerated(locked = true, uuid = "164d4f6e-cdea-4b18-aa5c-16cbe3cf7bbc")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @Column(name = "charge_price_schedule_id")
    @AutoGenerated(locked = true, uuid = "dc2fa9dd-ade8-4ab3-b54c-4500aa914c4e")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @Column(name = "charge_status")
    @AutoGenerated(locked = true, uuid = "2991906f-8b6f-47dc-b39d-bf8721277adf")
    @Enumerated(EnumType.STRING)
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @Column(name = "charge_type_code")
    @AutoGenerated(locked = true, uuid = "89f38cee-ee63-4eb2-907f-5c487655f939")
    private String chargeTypeCode;

    /** 挂号类别ID */
    @Column(name = "clinic_register_type_id")
    @AutoGenerated(locked = true, uuid = "2fa5f21f-051f-4450-954b-75edb3c34a00")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "6a6378b1-b204-4e5b-b8c7-a8476bd9523f")
    private Date createdAt;

    /** 优惠类别 */
    @Column(name = "discount_category")
    @AutoGenerated(locked = true, uuid = "38cd6dfb-ac56-4e11-8ac6-6f563a313160")
    private String discountCategory;

    /** 性别 */
    @Column(name = "gender")
    @AutoGenerated(locked = true, uuid = "7c51cc63-d988-4f12-8a4f-9f0a79bd331d")
    @Enumerated(EnumType.STRING)
    private GenderEnum gender;

    /** 保健干部费用 */
    @Column(name = "health_officials_fee")
    @AutoGenerated(locked = true, uuid = "4f441f5c-8dee-4a37-85cb-53c2be81fbb5")
    private String healthOfficialsFee;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "40103ee4-9ca4-4a8f-b63f-646261748d0a")
    @Id
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @Column(name = "identity_code")
    @AutoGenerated(locked = true, uuid = "ef047963-addb-49da-ab5b-e8b25dcd51e7")
    private String identityCode;

    /** 患者身份 */
    @Column(name = "identity_type")
    @AutoGenerated(locked = true, uuid = "ef9079ed-684a-4cd7-8302-7c03597718d8")
    private String identityType;

    /** 医疗保障号 */
    @Column(name = "insurance_number")
    @AutoGenerated(locked = true, uuid = "905ea49d-ee4c-4180-b71b-fd7bd87b443c")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @Column(name = "insurance_type")
    @AutoGenerated(locked = true, uuid = "7053c30e-6815-4dde-9bf7-1a8f03a8c390")
    private String insuranceType;

    /** 国际病人费用 */
    @Column(name = "international_patient_fee")
    @AutoGenerated(locked = true, uuid = "3bccb8fe-54c5-42fc-b737-245e8f88ea5d")
    private String internationalPatientFee;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "9469aaf7-4ed6-42ed-89dd-971280fac583")
    @Version
    private Long lockVersion;

    /** MDT费用类型 */
    @Column(name = "mdt_fee_type")
    @AutoGenerated(locked = true, uuid = "fca2aa56-fbd9-4e89-8766-bca7b43e093a")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @Column(name = "operator_department_id")
    @AutoGenerated(locked = true, uuid = "fce5337f-8226-4013-8ae2-1a069cf87c53")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @Column(name = "operator_id")
    @AutoGenerated(locked = true, uuid = "c46e0922-641f-42b4-8877-005c31baf772")
    private String operatorId;

    /** 预约ID */
    @Column(name = "outp_appoint_id")
    @AutoGenerated(locked = true, uuid = "7cd7cc11-ece8-4d50-a52a-300f4063aacf")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @Column(name = "outpatient_registration_category")
    @AutoGenerated(locked = true, uuid = "408ddc07-fffc-4025-8252-a414e438f377")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @Column(name = "patient_id")
    @AutoGenerated(locked = true, uuid = "672090c8-efbc-4060-b304-c2f36fa48d70")
    private String patientId;

    /** 患者姓名 */
    @Column(name = "patient_name")
    @AutoGenerated(locked = true, uuid = "9129162e-ea5a-4a9a-a672-1683477e5de7")
    private String patientName;

    /** 挂号日期 */
    @Column(name = "register_date")
    @AutoGenerated(locked = true, uuid = "7116630a-d544-46f2-93e8-10c836192af5")
    private Date registerDate;

    /** 排班号序 */
    @Column(name = "register_number")
    @AutoGenerated(locked = true, uuid = "3a1fce72-d112-4469-beed-4c4ee70a101a")
    private Long registerNumber;

    /** 挂号审核状态 */
    @Column(name = "register_review_status")
    @AutoGenerated(locked = true, uuid = "96f541e1-b792-4973-a9ae-df16da612a24")
    @Enumerated(EnumType.STRING)
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @Column(name = "register_status")
    @AutoGenerated(locked = true, uuid = "d3151ff5-886a-404e-bffb-86b63ea1dded")
    @Enumerated(EnumType.STRING)
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @Column(name = "registration_department_id")
    @AutoGenerated(locked = true, uuid = "9b781d5f-8b73-473d-a3aa-ddadd6a6e0d9")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @Column(name = "registration_doctor_id")
    @AutoGenerated(locked = true, uuid = "7ca3f6b3-6983-40cd-8142-fe16ae25e3d2")
    private String registrationDoctorId;

    /** 退号日期 */
    @Column(name = "return_date")
    @AutoGenerated(locked = true, uuid = "c9bb86b4-c728-436e-97f2-33aa76ceb492")
    private Date returnDate;

    /** 退号操作员id */
    @Column(name = "return_operator_id")
    @AutoGenerated(locked = true, uuid = "daa2c2b6-b549-409a-876a-3c03afbf0fb3")
    private String returnOperatorId;

    /** 退号原因 */
    @Column(name = "return_reason")
    @AutoGenerated(locked = true, uuid = "06c32ee4-b872-47dd-88b6-d317bf9fd312")
    private String returnReason;

    /** 退号费用结算ID */
    @Column(name = "return_settle_number")
    @AutoGenerated(locked = true, uuid = "c94f33ec-6b81-406d-a52c-e18ae1c7af89")
    private String returnSettleNumber;

    /** 审核说明 */
    @Column(name = "review_explain")
    @AutoGenerated(locked = true, uuid = "7c9afd92-63e0-4902-9a20-c2871f542226")
    private String reviewExplain;

    /** 审核原因 */
    @Column(name = "review_reason")
    @AutoGenerated(locked = true, uuid = "914b84de-1bfd-41bd-b51e-be3943658ead")
    private String reviewReason;

    /** 审核时间 */
    @Column(name = "review_time")
    @AutoGenerated(locked = true, uuid = "8a5b6d1b-4c9b-4400-9027-4184a63db19a")
    private Date reviewTime;

    /** 审核人id */
    @Column(name = "reviewer_id")
    @AutoGenerated(locked = true, uuid = "58c9a787-f320-42de-8275-68c9caf6d4d9")
    private String reviewerId;

    /** 挂号结算编号 */
    @Column(name = "settle_number")
    @AutoGenerated(locked = true, uuid = "55b3e9e7-393a-401f-8842-976997f1eb8c")
    private String settleNumber;

    /** 来源应用ID */
    @Column(name = "source_app_id")
    @AutoGenerated(locked = true, uuid = "54ed6282-7b70-4ed5-af1a-05a5d8332af8")
    private String sourceAppId;

    /** 来源院区ID */
    @Column(name = "source_campus_id")
    @AutoGenerated(locked = true, uuid = "7622067d-5315-421f-afcc-de071b858961")
    private String sourceCampusId;

    /** 规定病种编码 */
    @Column(name = "specified_disease_code")
    @AutoGenerated(locked = true, uuid = "abc86b71-64b6-4e88-8273-37b66501034f")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @Column(name = "specified_disease_flag")
    @AutoGenerated(locked = true, uuid = "2e749764-27a2-456a-96d6-1b4fdfc20fc0")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @Column(name = "take_appoint_date")
    @AutoGenerated(locked = true, uuid = "9fe5b61a-3bfe-46eb-ae6b-0d5fbec13067")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @Column(name = "take_appoint_operator_id")
    @AutoGenerated(locked = true, uuid = "4d7f613f-827a-41fc-933b-90a145daac6b")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @Column(name = "tally_receipt_number")
    @AutoGenerated(locked = true, uuid = "3a0f8b81-3707-473b-a332-87e51cc14f74")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @Column(name = "tally_status")
    @AutoGenerated(locked = true, uuid = "f68246fb-6c96-4dac-83c0-f260416f4018")
    private String tallyStatus;

    /** 午别 */
    @Column(name = "time_description")
    @AutoGenerated(locked = true, uuid = "cc374fa2-e9c8-406c-b3d5-0aff0f7e68b0")
    @Enumerated(EnumType.STRING)
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @Column(name = "total_charge")
    @AutoGenerated(locked = true, uuid = "e112c288-7d67-41bb-af6e-7507110b6b3f")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @Column(name = "total_cost")
    @AutoGenerated(locked = true, uuid = "dd07aba5-8be2-4af0-ac5f-b9ac19205e59")
    private BigDecimal totalCost;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "aa49ffe2-8770-4a3f-9630-be7895b6ee20")
    private Date updatedAt;

    /** 就诊卡号 */
    @Column(name = "visit_card_id")
    @AutoGenerated(locked = true, uuid = "30c4a727-0a2b-4bf1-927e-71de259f1931")
    private String visitCardId;

    /** 候诊结束时间 */
    @Column(name = "waiting_end_time")
    @Valid
    @AutoGenerated(locked = true, uuid = "4cbaf7ed-a0dd-4055-994b-9a46ba6c2440")
    @Convert(converter = TimeEoConverter.class)
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Column(name = "waiting_start_time")
    @Valid
    @AutoGenerated(locked = true, uuid = "d30e4f8d-d166-4324-ac7a-f33edafeff1d")
    @Convert(converter = TimeEoConverter.class)
    private TimeEo waitingStartTime;

    @AutoGenerated(locked = true)
    public OutpRegister convertToOutpRegister() {
        OutpRegister entity = new OutpRegister();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "outpAppointId",
                "patientId",
                "visitCardId",
                "clinicRegisterTypeId",
                "mdtFeeType",
                "sourceAppId",
                "sourceCampusId",
                "tallyStatus",
                "registerNumber",
                "patientName",
                "timeDescription",
                "age",
                "gender",
                "identityType",
                "insuranceType",
                "insuranceNumber",
                "registerStatus",
                "operatorId",
                "operatorDepartmentId",
                "registerDate",
                "settleNumber",
                "cancellationInitiatorId",
                "cancellationInitiationTime",
                "reviewerId",
                "reviewTime",
                "chargeStatus",
                "totalCost",
                "totalCharge",
                "returnOperatorId",
                "returnDate",
                "returnSettleNumber",
                "returnReason",
                "chargePriceScheduleId",
                "discountCategory",
                "tallyReceiptNumber",
                "chargeTypeCode",
                "identityCode",
                "takeAppointOperatorId",
                "takeAppointDate",
                "registrationDoctorId",
                "registrationDepartmentId",
                "outpatientRegistrationCategory",
                "specifiedDiseaseFlag",
                "specifiedDiseaseCode",
                "waitingStartTime",
                "waitingEndTime",
                "registerReviewStatus",
                "reviewReason",
                "reviewExplain",
                "healthOfficialsFee",
                "internationalPatientFee",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public AgeEo getAge() {
        return this.age;
    }

    @AutoGenerated(locked = true)
    public static OutpRegisterBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        OutpRegisterBO outpRegister =
                (OutpRegisterBO)
                        session.createQuery("from OutpRegisterBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return outpRegister;
    }

    @AutoGenerated(locked = true)
    public Date getCancellationInitiationTime() {
        return this.cancellationInitiationTime;
    }

    @AutoGenerated(locked = true)
    public String getCancellationInitiatorId() {
        return this.cancellationInitiatorId;
    }

    @AutoGenerated(locked = true)
    public String getChargePriceScheduleId() {
        return this.chargePriceScheduleId;
    }

    @AutoGenerated(locked = true)
    public ChargeStatusEnum getChargeStatus() {
        return this.chargeStatus;
    }

    @AutoGenerated(locked = true)
    public String getChargeTypeCode() {
        return this.chargeTypeCode;
    }

    @AutoGenerated(locked = true)
    public String getClinicRegisterTypeId() {
        return this.clinicRegisterTypeId;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getDiscountCategory() {
        return this.discountCategory;
    }

    @AutoGenerated(locked = true)
    public GenderEnum getGender() {
        return this.gender;
    }

    @AutoGenerated(locked = true)
    public String getHealthOfficialsFee() {
        return this.healthOfficialsFee;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getIdentityCode() {
        return this.identityCode;
    }

    @AutoGenerated(locked = true)
    public String getIdentityType() {
        return this.identityType;
    }

    @AutoGenerated(locked = true)
    public String getInsuranceNumber() {
        return this.insuranceNumber;
    }

    @AutoGenerated(locked = true)
    public String getInsuranceType() {
        return this.insuranceType;
    }

    @AutoGenerated(locked = true)
    public String getInternationalPatientFee() {
        return this.internationalPatientFee;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getMdtFeeType() {
        return this.mdtFeeType;
    }

    @AutoGenerated(locked = true)
    public String getOperatorDepartmentId() {
        return this.operatorDepartmentId;
    }

    @AutoGenerated(locked = true)
    public String getOperatorId() {
        return this.operatorId;
    }

    @AutoGenerated(locked = true)
    public String getOutpAppointId() {
        return this.outpAppointId;
    }

    @AutoGenerated(locked = true)
    public String getOutpatientRegistrationCategory() {
        return this.outpatientRegistrationCategory;
    }

    @AutoGenerated(locked = true)
    public String getPatientId() {
        return this.patientId;
    }

    @AutoGenerated(locked = true)
    public String getPatientName() {
        return this.patientName;
    }

    @AutoGenerated(locked = true)
    public Date getRegisterDate() {
        return this.registerDate;
    }

    @AutoGenerated(locked = true)
    public Long getRegisterNumber() {
        return this.registerNumber;
    }

    @AutoGenerated(locked = true)
    public RegisterReviewEnum getRegisterReviewStatus() {
        return this.registerReviewStatus;
    }

    @AutoGenerated(locked = true)
    public RegisterStatusEnum getRegisterStatus() {
        return this.registerStatus;
    }

    @AutoGenerated(locked = true)
    public String getRegistrationDepartmentId() {
        return this.registrationDepartmentId;
    }

    @AutoGenerated(locked = true)
    public String getRegistrationDoctorId() {
        return this.registrationDoctorId;
    }

    @AutoGenerated(locked = true)
    public Date getReturnDate() {
        return this.returnDate;
    }

    @AutoGenerated(locked = true)
    public String getReturnOperatorId() {
        return this.returnOperatorId;
    }

    @AutoGenerated(locked = true)
    public String getReturnReason() {
        return this.returnReason;
    }

    @AutoGenerated(locked = true)
    public String getReturnSettleNumber() {
        return this.returnSettleNumber;
    }

    @AutoGenerated(locked = true)
    public String getReviewExplain() {
        return this.reviewExplain;
    }

    @AutoGenerated(locked = true)
    public String getReviewReason() {
        return this.reviewReason;
    }

    @AutoGenerated(locked = true)
    public Date getReviewTime() {
        return this.reviewTime;
    }

    @AutoGenerated(locked = true)
    public String getReviewerId() {
        return this.reviewerId;
    }

    @AutoGenerated(locked = true)
    public String getSettleNumber() {
        return this.settleNumber;
    }

    @AutoGenerated(locked = true)
    public String getSourceAppId() {
        return this.sourceAppId;
    }

    @AutoGenerated(locked = true)
    public String getSourceCampusId() {
        return this.sourceCampusId;
    }

    @AutoGenerated(locked = true)
    public String getSpecifiedDiseaseCode() {
        return this.specifiedDiseaseCode;
    }

    @AutoGenerated(locked = true)
    public Boolean getSpecifiedDiseaseFlag() {
        return this.specifiedDiseaseFlag;
    }

    @AutoGenerated(locked = true)
    public Date getTakeAppointDate() {
        return this.takeAppointDate;
    }

    @AutoGenerated(locked = true)
    public String getTakeAppointOperatorId() {
        return this.takeAppointOperatorId;
    }

    @AutoGenerated(locked = true)
    public String getTallyReceiptNumber() {
        return this.tallyReceiptNumber;
    }

    @AutoGenerated(locked = true)
    public String getTallyStatus() {
        return this.tallyStatus;
    }

    @AutoGenerated(locked = true)
    public TimeDescriptionEnum getTimeDescription() {
        return this.timeDescription;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getTotalCharge() {
        return this.totalCharge;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getTotalCost() {
        return this.totalCost;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getVisitCardId() {
        return this.visitCardId;
    }

    @AutoGenerated(locked = true)
    public TimeEo getWaitingEndTime() {
        return this.waitingEndTime;
    }

    @AutoGenerated(locked = true)
    public TimeEo getWaitingStartTime() {
        return this.waitingStartTime;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setAge(AgeEo age) {
        this.age = age;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setCancellationInitiationTime(Date cancellationInitiationTime) {
        this.cancellationInitiationTime = cancellationInitiationTime;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setCancellationInitiatorId(String cancellationInitiatorId) {
        this.cancellationInitiatorId = cancellationInitiatorId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setChargePriceScheduleId(String chargePriceScheduleId) {
        this.chargePriceScheduleId = chargePriceScheduleId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setChargeStatus(ChargeStatusEnum chargeStatus) {
        this.chargeStatus = chargeStatus;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setChargeTypeCode(String chargeTypeCode) {
        this.chargeTypeCode = chargeTypeCode;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setClinicRegisterTypeId(String clinicRegisterTypeId) {
        this.clinicRegisterTypeId = clinicRegisterTypeId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setDiscountCategory(String discountCategory) {
        this.discountCategory = discountCategory;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setGender(GenderEnum gender) {
        this.gender = gender;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setHealthOfficialsFee(String healthOfficialsFee) {
        this.healthOfficialsFee = healthOfficialsFee;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setId(String id) {
        this.id = id;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setIdentityCode(String identityCode) {
        this.identityCode = identityCode;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setIdentityType(String identityType) {
        this.identityType = identityType;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setInternationalPatientFee(String internationalPatientFee) {
        this.internationalPatientFee = internationalPatientFee;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setMdtFeeType(String mdtFeeType) {
        this.mdtFeeType = mdtFeeType;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setOperatorDepartmentId(String operatorDepartmentId) {
        this.operatorDepartmentId = operatorDepartmentId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setOperatorId(String operatorId) {
        this.operatorId = operatorId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setOutpAppointId(String outpAppointId) {
        this.outpAppointId = outpAppointId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setOutpatientRegistrationCategory(String outpatientRegistrationCategory) {
        this.outpatientRegistrationCategory = outpatientRegistrationCategory;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setPatientId(String patientId) {
        this.patientId = patientId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setPatientName(String patientName) {
        this.patientName = patientName;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setRegisterNumber(Long registerNumber) {
        this.registerNumber = registerNumber;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setRegisterReviewStatus(RegisterReviewEnum registerReviewStatus) {
        this.registerReviewStatus = registerReviewStatus;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setRegisterStatus(RegisterStatusEnum registerStatus) {
        this.registerStatus = registerStatus;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setRegistrationDepartmentId(String registrationDepartmentId) {
        this.registrationDepartmentId = registrationDepartmentId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setRegistrationDoctorId(String registrationDoctorId) {
        this.registrationDoctorId = registrationDoctorId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReturnOperatorId(String returnOperatorId) {
        this.returnOperatorId = returnOperatorId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReturnSettleNumber(String returnSettleNumber) {
        this.returnSettleNumber = returnSettleNumber;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReviewExplain(String reviewExplain) {
        this.reviewExplain = reviewExplain;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setReviewerId(String reviewerId) {
        this.reviewerId = reviewerId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setSettleNumber(String settleNumber) {
        this.settleNumber = settleNumber;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setSourceCampusId(String sourceCampusId) {
        this.sourceCampusId = sourceCampusId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setSpecifiedDiseaseCode(String specifiedDiseaseCode) {
        this.specifiedDiseaseCode = specifiedDiseaseCode;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setSpecifiedDiseaseFlag(Boolean specifiedDiseaseFlag) {
        this.specifiedDiseaseFlag = specifiedDiseaseFlag;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTakeAppointDate(Date takeAppointDate) {
        this.takeAppointDate = takeAppointDate;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTakeAppointOperatorId(String takeAppointOperatorId) {
        this.takeAppointOperatorId = takeAppointOperatorId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTallyReceiptNumber(String tallyReceiptNumber) {
        this.tallyReceiptNumber = tallyReceiptNumber;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTallyStatus(String tallyStatus) {
        this.tallyStatus = tallyStatus;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTimeDescription(TimeDescriptionEnum timeDescription) {
        this.timeDescription = timeDescription;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTotalCharge(BigDecimal totalCharge) {
        this.totalCharge = totalCharge;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setVisitCardId(String visitCardId) {
        this.visitCardId = visitCardId;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setWaitingEndTime(TimeEo waitingEndTime) {
        this.waitingEndTime = waitingEndTime;
        return (OutpRegisterBO) this;
    }

    @AutoGenerated(locked = true)
    public OutpRegisterBO setWaitingStartTime(TimeEo waitingStartTime) {
        this.waitingStartTime = waitingStartTime;
        return (OutpRegisterBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
