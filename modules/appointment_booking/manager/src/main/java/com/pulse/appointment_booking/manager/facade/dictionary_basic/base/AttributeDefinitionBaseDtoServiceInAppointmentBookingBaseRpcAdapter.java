package com.pulse.appointment_booking.manager.facade.dictionary_basic.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "c521e7d1-60ab-3dd0-8ebc-5726052c6c8a")
public class AttributeDefinitionBaseDtoServiceInAppointmentBookingBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "24939867-3bd1-4238-8cd1-4046e3b7d016|RPC|BASE_ADAPTER")
    public AttributeDefinitionBaseDto getById(String id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/dictionary_basic/24939867-3bd1-4238-8cd1-4046e3b7d016/AttributeDefinitionBaseDtoService-getById",
                        "com.pulse.dictionary_basic.service.AttributeDefinitionBaseDtoService",
                        "getById",
                        paramMap,
                        paramTypeMap,
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
                        "003f087c-177d-4a69-81e6-c6650b5f6080"),
                new TypeReference<>() {});
    }
}
