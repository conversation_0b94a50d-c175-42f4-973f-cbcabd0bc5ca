package com.pulse.appointment_booking.manager.bo;

import com.pulse.appointment_booking.manager.bo.base.BaseOutpAppointBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "outp_appoint")
@Entity
@AutoGenerated(locked = false, uuid = "17b627ab-8774-4409-ac08-ac9362c2c5ed|BO|DEFINITION")
public class OutpAppointBO extends BaseOutpAppointBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "17b627ab-8774-4409-ac08-ac9362c2c5ed|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
