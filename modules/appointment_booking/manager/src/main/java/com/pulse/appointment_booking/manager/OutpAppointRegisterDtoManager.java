package com.pulse.appointment_booking.manager;

import com.pulse.appointment_booking.manager.dto.OutpAppointRegisterDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "6ded3f54-5f2e-4ef8-a834-2ed2d6753084|DTO|MANAGER")
public interface OutpAppointRegisterDtoManager {

    @AutoGenerated(locked = true, uuid = "018c84c2-2a62-3d0a-b018-f084aabbd95e")
    List<OutpAppointRegisterDto> getByDepartmentIds(List<String> departmentId);

    @AutoGenerated(locked = true, uuid = "2067f848-773a-37c2-babb-e53af7eb39ac")
    List<OutpAppointRegisterDto> getByAppointmentScheduleIds(List<String> appointmentScheduleId);

    @AutoGenerated(locked = true, uuid = "5cc625da-5e42-393f-a8bd-ff13f26553d6")
    List<OutpAppointRegisterDto> getByPatientId(String patientId);

    @AutoGenerated(locked = true, uuid = "6fefad8d-0ba6-3b50-aea6-90f00c59be7f")
    List<OutpAppointRegisterDto> getByAppointmentScheduleId(String appointmentScheduleId);

    @AutoGenerated(locked = true, uuid = "b03b89a7-6678-3044-ae6b-00852a03da2a")
    List<OutpAppointRegisterDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "c8dbb6b0-35b3-33d1-b7cf-927c3a8b6d98")
    List<OutpAppointRegisterDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId);

    @AutoGenerated(locked = true, uuid = "d1655dac-b18a-3849-b410-9ecd9b31a5f6")
    List<OutpAppointRegisterDto> getByClinicRegisterTypeId(String clinicRegisterTypeId);

    @AutoGenerated(locked = true, uuid = "d9c1eda1-2666-30a8-85f2-8ffb5362f84d")
    OutpAppointRegisterDto getById(String id);

    @AutoGenerated(locked = true, uuid = "dc128076-c04f-34f5-917c-97b71fe0621d")
    List<OutpAppointRegisterDto> getByPatientIds(List<String> patientId);

    @AutoGenerated(locked = true, uuid = "fe43db5a-5553-3f19-b105-e3038ddd460b")
    List<OutpAppointRegisterDto> getByDepartmentId(String departmentId);
}
