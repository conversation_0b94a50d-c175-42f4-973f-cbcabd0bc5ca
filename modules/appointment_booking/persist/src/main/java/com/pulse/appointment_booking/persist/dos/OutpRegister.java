package com.pulse.appointment_booking.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@TableName(value = "outp_register", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "99614200-b7fd-4038-a4c5-c4392200d749|ENTITY|DEFINITION")
public class OutpRegister {
    @Valid
    @AutoGenerated(locked = true, uuid = "9332e8e9-5061-4255-bb33-96252f8abb89")
    @TableField(value = "age", typeHandler = JacksonTypeHandler.class)
    private AgeEo age;

    @AutoGenerated(locked = true, uuid = "0430841d-a414-4c86-bb22-09f906c24787")
    @TableField(value = "cancellation_initiation_time")
    private Date cancellationInitiationTime;

    @AutoGenerated(locked = true, uuid = "164d4f6e-cdea-4b18-aa5c-16cbe3cf7bbc")
    @TableField(value = "cancellation_initiator_id")
    private String cancellationInitiatorId;

    @AutoGenerated(locked = true, uuid = "dc2fa9dd-ade8-4ab3-b54c-4500aa914c4e")
    @TableField(value = "charge_price_schedule_id")
    private String chargePriceScheduleId;

    @AutoGenerated(locked = true, uuid = "2991906f-8b6f-47dc-b39d-bf8721277adf")
    @TableField(value = "charge_status")
    private ChargeStatusEnum chargeStatus;

    @AutoGenerated(locked = true, uuid = "89f38cee-ee63-4eb2-907f-5c487655f939")
    @TableField(value = "charge_type_code")
    private String chargeTypeCode;

    @AutoGenerated(locked = true, uuid = "2fa5f21f-051f-4450-954b-75edb3c34a00")
    @TableField(value = "clinic_register_type_id")
    private String clinicRegisterTypeId;

    @AutoGenerated(locked = true, uuid = "6a6378b1-b204-4e5b-b8c7-a8476bd9523f")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "38cd6dfb-ac56-4e11-8ac6-6f563a313160")
    @TableField(value = "discount_category")
    private String discountCategory;

    @AutoGenerated(locked = true, uuid = "7c51cc63-d988-4f12-8a4f-9f0a79bd331d")
    @TableField(value = "gender")
    private GenderEnum gender;

    @AutoGenerated(locked = true, uuid = "4f441f5c-8dee-4a37-85cb-53c2be81fbb5")
    @TableField(value = "health_officials_fee")
    private String healthOfficialsFee;

    @AutoGenerated(locked = true, uuid = "40103ee4-9ca4-4a8f-b63f-646261748d0a")
    @TableId(value = "id")
    @NotNull(message = "主键不能为空")
    private String id;

    @AutoGenerated(locked = true, uuid = "ef047963-addb-49da-ab5b-e8b25dcd51e7")
    @TableField(value = "identity_code")
    private String identityCode;

    @AutoGenerated(locked = true, uuid = "ef9079ed-684a-4cd7-8302-7c03597718d8")
    @TableField(value = "identity_type")
    private String identityType;

    @AutoGenerated(locked = true, uuid = "905ea49d-ee4c-4180-b71b-fd7bd87b443c")
    @TableField(value = "insurance_number")
    private String insuranceNumber;

    @AutoGenerated(locked = true, uuid = "7053c30e-6815-4dde-9bf7-1a8f03a8c390")
    @TableField(value = "insurance_type")
    private String insuranceType;

    @AutoGenerated(locked = true, uuid = "3bccb8fe-54c5-42fc-b737-245e8f88ea5d")
    @TableField(value = "international_patient_fee")
    private String internationalPatientFee;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "9469aaf7-4ed6-42ed-89dd-971280fac583")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "fca2aa56-fbd9-4e89-8766-bca7b43e093a")
    @TableField(value = "mdt_fee_type")
    private String mdtFeeType;

    @AutoGenerated(locked = true, uuid = "fce5337f-8226-4013-8ae2-1a069cf87c53")
    @TableField(value = "operator_department_id")
    private String operatorDepartmentId;

    @AutoGenerated(locked = true, uuid = "c46e0922-641f-42b4-8877-005c31baf772")
    @TableField(value = "operator_id")
    private String operatorId;

    @AutoGenerated(locked = true, uuid = "7cd7cc11-ece8-4d50-a52a-300f4063aacf")
    @TableField(value = "outp_appoint_id")
    private String outpAppointId;

    @AutoGenerated(locked = true, uuid = "408ddc07-fffc-4025-8252-a414e438f377")
    @TableField(value = "outpatient_registration_category")
    private String outpatientRegistrationCategory;

    @AutoGenerated(locked = true, uuid = "672090c8-efbc-4060-b304-c2f36fa48d70")
    @TableField(value = "patient_id")
    private String patientId;

    @AutoGenerated(locked = true, uuid = "9129162e-ea5a-4a9a-a672-1683477e5de7")
    @TableField(value = "patient_name")
    private String patientName;

    @AutoGenerated(locked = true, uuid = "7116630a-d544-46f2-93e8-10c836192af5")
    @TableField(value = "register_date")
    private Date registerDate;

    @AutoGenerated(locked = true, uuid = "3a1fce72-d112-4469-beed-4c4ee70a101a")
    @TableField(value = "register_number")
    private Long registerNumber;

    @AutoGenerated(locked = true, uuid = "96f541e1-b792-4973-a9ae-df16da612a24")
    @TableField(value = "register_review_status")
    private RegisterReviewEnum registerReviewStatus;

    @AutoGenerated(locked = true, uuid = "d3151ff5-886a-404e-bffb-86b63ea1dded")
    @TableField(value = "register_status")
    private RegisterStatusEnum registerStatus;

    @AutoGenerated(locked = true, uuid = "9b781d5f-8b73-473d-a3aa-ddadd6a6e0d9")
    @TableField(value = "registration_department_id")
    private String registrationDepartmentId;

    @AutoGenerated(locked = true, uuid = "7ca3f6b3-6983-40cd-8142-fe16ae25e3d2")
    @TableField(value = "registration_doctor_id")
    private String registrationDoctorId;

    @AutoGenerated(locked = true, uuid = "c9bb86b4-c728-436e-97f2-33aa76ceb492")
    @TableField(value = "return_date")
    private Date returnDate;

    @AutoGenerated(locked = true, uuid = "daa2c2b6-b549-409a-876a-3c03afbf0fb3")
    @TableField(value = "return_operator_id")
    private String returnOperatorId;

    @AutoGenerated(locked = true, uuid = "06c32ee4-b872-47dd-88b6-d317bf9fd312")
    @TableField(value = "return_reason")
    private String returnReason;

    @AutoGenerated(locked = true, uuid = "c94f33ec-6b81-406d-a52c-e18ae1c7af89")
    @TableField(value = "return_settle_number")
    private String returnSettleNumber;

    @AutoGenerated(locked = true, uuid = "7c9afd92-63e0-4902-9a20-c2871f542226")
    @TableField(value = "review_explain")
    private String reviewExplain;

    @AutoGenerated(locked = true, uuid = "914b84de-1bfd-41bd-b51e-be3943658ead")
    @TableField(value = "review_reason")
    private String reviewReason;

    @AutoGenerated(locked = true, uuid = "8a5b6d1b-4c9b-4400-9027-4184a63db19a")
    @TableField(value = "review_time")
    private Date reviewTime;

    @AutoGenerated(locked = true, uuid = "58c9a787-f320-42de-8275-68c9caf6d4d9")
    @TableField(value = "reviewer_id")
    private String reviewerId;

    @AutoGenerated(locked = true, uuid = "55b3e9e7-393a-401f-8842-976997f1eb8c")
    @TableField(value = "settle_number")
    private String settleNumber;

    @AutoGenerated(locked = true, uuid = "54ed6282-7b70-4ed5-af1a-05a5d8332af8")
    @TableField(value = "source_app_id")
    private String sourceAppId;

    @AutoGenerated(locked = true, uuid = "7622067d-5315-421f-afcc-de071b858961")
    @TableField(value = "source_campus_id")
    private String sourceCampusId;

    @AutoGenerated(locked = true, uuid = "abc86b71-64b6-4e88-8273-37b66501034f")
    @TableField(value = "specified_disease_code")
    private String specifiedDiseaseCode;

    @AutoGenerated(locked = true, uuid = "2e749764-27a2-456a-96d6-1b4fdfc20fc0")
    @TableField(value = "specified_disease_flag")
    private Boolean specifiedDiseaseFlag;

    @AutoGenerated(locked = true, uuid = "9fe5b61a-3bfe-46eb-ae6b-0d5fbec13067")
    @TableField(value = "take_appoint_date")
    private Date takeAppointDate;

    @AutoGenerated(locked = true, uuid = "4d7f613f-827a-41fc-933b-90a145daac6b")
    @TableField(value = "take_appoint_operator_id")
    private String takeAppointOperatorId;

    @AutoGenerated(locked = true, uuid = "3a0f8b81-3707-473b-a332-87e51cc14f74")
    @TableField(value = "tally_receipt_number")
    private String tallyReceiptNumber;

    @AutoGenerated(locked = true, uuid = "f68246fb-6c96-4dac-83c0-f260416f4018")
    @TableField(value = "tally_status")
    private String tallyStatus;

    @AutoGenerated(locked = true, uuid = "cc374fa2-e9c8-406c-b3d5-0aff0f7e68b0")
    @TableField(value = "time_description")
    private TimeDescriptionEnum timeDescription;

    @AutoGenerated(locked = true, uuid = "e112c288-7d67-41bb-af6e-7507110b6b3f")
    @TableField(value = "total_charge")
    private BigDecimal totalCharge;

    @AutoGenerated(locked = true, uuid = "dd07aba5-8be2-4af0-ac5f-b9ac19205e59")
    @TableField(value = "total_cost")
    private BigDecimal totalCost;

    @AutoGenerated(locked = true, uuid = "aa49ffe2-8770-4a3f-9630-be7895b6ee20")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "30c4a727-0a2b-4bf1-927e-71de259f1931")
    @TableField(value = "visit_card_id")
    private String visitCardId;

    @Valid
    @AutoGenerated(locked = true, uuid = "4cbaf7ed-a0dd-4055-994b-9a46ba6c2440")
    @TableField(value = "waiting_end_time", typeHandler = JacksonTypeHandler.class)
    private TimeEo waitingEndTime;

    @Valid
    @AutoGenerated(locked = true, uuid = "d30e4f8d-d166-4324-ac7a-f33edafeff1d")
    @TableField(value = "waiting_start_time", typeHandler = JacksonTypeHandler.class)
    private TimeEo waitingStartTime;
}
