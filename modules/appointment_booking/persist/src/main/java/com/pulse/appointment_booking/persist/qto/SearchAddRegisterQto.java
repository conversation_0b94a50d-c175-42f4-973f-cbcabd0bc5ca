package com.pulse.appointment_booking.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "aac05257-20f3-4fa0-95cb-2782bc4a3a04|QTO|DEFINITION")
public class SearchAddRegisterQto {
    /** 挂号类别id appointment_schedule.clinic_register_type_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "ff319572-79d7-43fd-a034-5c1e3d7d9d4c")
    private List<String> appointmentScheduleClinicRegisterTypeIdIn;

    /** 手机号 outp_appoint.cellphone */
    @AutoGenerated(locked = true, uuid = "4575fa37-c9e8-4ac3-bc73-ed70a67b1756")
    private String cellphoneIs;

    /** 病案号 outp_appoint.display_id */
    @AutoGenerated(locked = true, uuid = "fe7e9de9-9097-4b8a-b0f2-2663b5e333a1")
    private String displayIdIs;

    @AutoGenerated(locked = true, uuid = "d9719de0-98d9-476d-a585-286870b26bab")
    private Integer from;

    /** 实名认证证件号码 outp_appoint.identification_number */
    @AutoGenerated(locked = true, uuid = "0a472640-65bc-4d80-853f-4bc25a1cd197")
    private String identificationNumberIs;

    /** 患者名称 outp_appoint.patient_name */
    @AutoGenerated(locked = true, uuid = "036d5161-a2af-4670-be2a-fa3737fc9f8a")
    private String patientNameLike;

    @AutoGenerated(locked = true, uuid = "cb6872d1-aed8-4a71-87d8-0e02088b6544")
    private Integer size;

    /** 就诊卡ID outp_appoint.visit_card_id */
    @AutoGenerated(locked = true, uuid = "9d564d21-1644-4fb9-a47a-4ea96b457510")
    private String visitCardIdIs;

    /** 就诊日期 outp_appoint.visit_date */
    @AutoGenerated(locked = true, uuid = "8720f928-ac03-496d-a61c-f2a154de8894")
    private Date visitDateBiggerThanEqual;

    /** 就诊日期 outp_appoint.visit_date */
    @AutoGenerated(locked = true, uuid = "5669adb8-78cc-4f2c-9c4d-2dff423b3d55")
    private Date visitDateLessThanEqual;
}
