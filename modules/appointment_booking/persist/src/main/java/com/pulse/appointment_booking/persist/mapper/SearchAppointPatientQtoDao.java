package com.pulse.appointment_booking.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.persist.qto.SearchAppointPatientQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "8de3e0df-1697-44c8-a81c-0ba39dbc55a4|QTO|DAO")
public class SearchAppointPatientQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询预约患者 */
    @AutoGenerated(locked = false, uuid = "8de3e0df-1697-44c8-a81c-0ba39dbc55a4-count")
    public Integer count(SearchAppointPatientQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(outp_appoint.id) FROM outp_appoint LEFT JOIN appointment_schedule"
                    + " \"appointmentSchedule\" on outp_appoint.appointment_schedule_id ="
                    + " \"appointmentSchedule\".id WHERE outp_appoint.operate_date <="
                    + " #operateDateLessThanEqual AND outp_appoint.operate_date >="
                    + " #operateDateBiggerThanEqual AND \"appointmentSchedule\".day_of_week in"
                    + " #appointmentScheduleDayOfWeekIn AND"
                    + " \"appointmentSchedule\".time_description in"
                    + " #appointmentScheduleTimeDescriptionIn AND outp_appoint.campus_id ="
                    + " #campusIdIs AND outp_appoint.appoint_status in #appointStatusIn AND EXISTS"
                    + " ( SELECT \"outpRegisterVisitList\".id FROM outp_register"
                    + " \"outpRegisterVisitList\" WHERE outp_appoint.id ="
                    + " \"outpRegisterVisitList\".outp_appoint_id AND ( EXISTS ( SELECT"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".id FROM outp_visit"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\" WHERE"
                    + " \"outpRegisterVisitList\".id ="
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".outp_register_id AND ("
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".visit_status in"
                    + " #visitStatusIn AND"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".triage_status in"
                    + " #triageStatusIn ) ) ) ) AND outp_appoint.cancel_reason = #cancelReasonIs"
                    + " AND outp_appoint.source_id = #sourceIdIs AND"
                    + " outp_appoint.appointment_category_id = #appointmentCategoryIdIs AND"
                    + " outp_appoint.clinic_register_type_id in #clinicRegisterTypeIdIn AND"
                    + " outp_appoint.doctor_id = #doctorIdIs AND"
                    + " outp_appoint.operator_department_id = #visitDepartmentIdIs AND"
                    + " outp_appoint.patient_name like #patientNameLike AND outp_appoint.display_id"
                    + " = #displayIdIs AND outp_appoint.visit_card_id = #visitCardIdIs AND"
                    + " outp_appoint.identification_number = #identificationNumberIs AND"
                    + " outp_appoint.cellphone = #cellphoneIs AND"
                    + " outp_appoint.identification_number = #identificationNumberIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleTimeDescriptionIn())) {
            conditionToRemove.add("#appointmentScheduleTimeDescriptionIn");
        }
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleDayOfWeekIn())) {
            conditionToRemove.add("#appointmentScheduleDayOfWeekIn");
        }
        if (qto.getOperateDateBiggerThanEqual() == null) {
            conditionToRemove.add("#operateDateBiggerThanEqual");
        }
        if (qto.getOperateDateLessThanEqual() == null) {
            conditionToRemove.add("#operateDateLessThanEqual");
        }
        if (qto.getAppointmentCategoryIdIs() == null) {
            conditionToRemove.add("#appointmentCategoryIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        if (qto.getIdentificationNumberIs() == null) {
            conditionToRemove.add("#identificationNumberIs");
        }
        if (qto.getIdentificationNumberIs() == null) {
            conditionToRemove.add("#identificationNumberIs");
        }
        if (qto.getVisitDepartmentIdIs() == null) {
            conditionToRemove.add("#visitDepartmentIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getAppointStatusIn())) {
            conditionToRemove.add("#appointStatusIn");
        }
        if (qto.getPatientNameLike() == null) {
            conditionToRemove.add("#patientNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getTriageStatusIn())) {
            conditionToRemove.add("#triageStatusIn");
        }
        if (qto.getCancelReasonIs() == null) {
            conditionToRemove.add("#cancelReasonIs");
        }
        if (CollectionUtil.isEmpty(qto.getVisitStatusIn())) {
            conditionToRemove.add("#visitStatusIn");
        }
        if (qto.getVisitCardIdIs() == null) {
            conditionToRemove.add("#visitCardIdIs");
        }
        if (qto.getDisplayIdIs() == null) {
            conditionToRemove.add("#displayIdIs");
        }
        if (qto.getCellphoneIs() == null) {
            conditionToRemove.add("#cellphoneIs");
        }
        if (qto.getCampusIdIs() == null) {
            conditionToRemove.add("#campusIdIs");
        }
        if (qto.getSourceIdIs() == null) {
            conditionToRemove.add("#sourceIdIs");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"department_department\"");
        softDeleteTableAlias.add("\"department\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#appointmentScheduleTimeDescriptionIn",
                                CollectionUtil.isEmpty(
                                                qto.getAppointmentScheduleTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleTimeDescriptionIn()
                                                        .size()))
                        .replace(
                                "#appointmentScheduleDayOfWeekIn",
                                CollectionUtil.isEmpty(qto.getAppointmentScheduleDayOfWeekIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleDayOfWeekIn().size()))
                        .replace("#operateDateBiggerThanEqual", "?")
                        .replace("#operateDateLessThanEqual", "?")
                        .replace("#appointmentCategoryIdIs", "?")
                        .replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()))
                        .replace("#identificationNumberIs", "?")
                        .replace("#identificationNumberIs", "?")
                        .replace("#visitDepartmentIdIs", "?")
                        .replace(
                                "#appointStatusIn",
                                CollectionUtil.isEmpty(qto.getAppointStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getAppointStatusIn().size()))
                        .replace("#patientNameLike", "?")
                        .replace(
                                "#triageStatusIn",
                                CollectionUtil.isEmpty(qto.getTriageStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTriageStatusIn().size()))
                        .replace("#cancelReasonIs", "?")
                        .replace(
                                "#visitStatusIn",
                                CollectionUtil.isEmpty(qto.getVisitStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getVisitStatusIn().size()))
                        .replace("#visitCardIdIs", "?")
                        .replace("#displayIdIs", "?")
                        .replace("#cellphoneIs", "?")
                        .replace("#campusIdIs", "?")
                        .replace("#sourceIdIs", "?")
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleTimeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getAppointmentScheduleTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#appointmentScheduleDayOfWeekIn")) {
                sqlParams.addAll(qto.getAppointmentScheduleDayOfWeekIn());
            } else if (paramName.equalsIgnoreCase("#operateDateBiggerThanEqual")) {
                sqlParams.add(qto.getOperateDateBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#operateDateLessThanEqual")) {
                sqlParams.add(qto.getOperateDateLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#appointmentCategoryIdIs")) {
                sqlParams.add(qto.getAppointmentCategoryIdIs());
            } else if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            } else if (paramName.equalsIgnoreCase("#identificationNumberIs")) {
                sqlParams.add(qto.getIdentificationNumberIs());
            } else if (paramName.equalsIgnoreCase("#identificationNumberIs")) {
                sqlParams.add(qto.getIdentificationNumberIs());
            } else if (paramName.equalsIgnoreCase("#visitDepartmentIdIs")) {
                sqlParams.add(qto.getVisitDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#appointStatusIn")) {
                sqlParams.addAll(
                        qto.getAppointStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#patientNameLike")) {
                sqlParams.add("%" + qto.getPatientNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#triageStatusIn")) {
                sqlParams.addAll(qto.getTriageStatusIn());
            } else if (paramName.equalsIgnoreCase("#cancelReasonIs")) {
                sqlParams.add(qto.getCancelReasonIs());
            } else if (paramName.equalsIgnoreCase("#visitStatusIn")) {
                sqlParams.addAll(
                        qto.getVisitStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#visitCardIdIs")) {
                sqlParams.add(qto.getVisitCardIdIs());
            } else if (paramName.equalsIgnoreCase("#displayIdIs")) {
                sqlParams.add(qto.getDisplayIdIs());
            } else if (paramName.equalsIgnoreCase("#cellphoneIs")) {
                sqlParams.add(qto.getCellphoneIs());
            } else if (paramName.equalsIgnoreCase("#campusIdIs")) {
                sqlParams.add(qto.getCampusIdIs());
            } else if (paramName.equalsIgnoreCase("#sourceIdIs")) {
                sqlParams.add(qto.getSourceIdIs());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询预约患者 */
    @AutoGenerated(locked = false, uuid = "8de3e0df-1697-44c8-a81c-0ba39dbc55a4-query-all")
    public List<String> query(SearchAppointPatientQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT outp_appoint.id FROM outp_appoint LEFT JOIN appointment_schedule"
                    + " \"appointmentSchedule\" on outp_appoint.appointment_schedule_id ="
                    + " \"appointmentSchedule\".id WHERE outp_appoint.operate_date <="
                    + " #operateDateLessThanEqual AND outp_appoint.operate_date >="
                    + " #operateDateBiggerThanEqual AND \"appointmentSchedule\".day_of_week in"
                    + " #appointmentScheduleDayOfWeekIn AND"
                    + " \"appointmentSchedule\".time_description in"
                    + " #appointmentScheduleTimeDescriptionIn AND outp_appoint.campus_id ="
                    + " #campusIdIs AND outp_appoint.appoint_status in #appointStatusIn AND EXISTS"
                    + " ( SELECT \"outpRegisterVisitList\".id FROM outp_register"
                    + " \"outpRegisterVisitList\" WHERE outp_appoint.id ="
                    + " \"outpRegisterVisitList\".outp_appoint_id AND ( EXISTS ( SELECT"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".id FROM outp_visit"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\" WHERE"
                    + " \"outpRegisterVisitList\".id ="
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".outp_register_id AND ("
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".visit_status in"
                    + " #visitStatusIn AND"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".triage_status in"
                    + " #triageStatusIn ) ) ) ) AND outp_appoint.cancel_reason = #cancelReasonIs"
                    + " AND outp_appoint.source_id = #sourceIdIs AND"
                    + " outp_appoint.appointment_category_id = #appointmentCategoryIdIs AND"
                    + " outp_appoint.clinic_register_type_id in #clinicRegisterTypeIdIn AND"
                    + " outp_appoint.doctor_id = #doctorIdIs AND"
                    + " outp_appoint.operator_department_id = #visitDepartmentIdIs AND"
                    + " outp_appoint.patient_name like #patientNameLike AND outp_appoint.display_id"
                    + " = #displayIdIs AND outp_appoint.visit_card_id = #visitCardIdIs AND"
                    + " outp_appoint.identification_number = #identificationNumberIs AND"
                    + " outp_appoint.cellphone = #cellphoneIs AND"
                    + " outp_appoint.identification_number = #identificationNumberIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleTimeDescriptionIn())) {
            conditionToRemove.add("#appointmentScheduleTimeDescriptionIn");
        }
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleDayOfWeekIn())) {
            conditionToRemove.add("#appointmentScheduleDayOfWeekIn");
        }
        if (qto.getOperateDateBiggerThanEqual() == null) {
            conditionToRemove.add("#operateDateBiggerThanEqual");
        }
        if (qto.getOperateDateLessThanEqual() == null) {
            conditionToRemove.add("#operateDateLessThanEqual");
        }
        if (qto.getAppointmentCategoryIdIs() == null) {
            conditionToRemove.add("#appointmentCategoryIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        if (qto.getIdentificationNumberIs() == null) {
            conditionToRemove.add("#identificationNumberIs");
        }
        if (qto.getIdentificationNumberIs() == null) {
            conditionToRemove.add("#identificationNumberIs");
        }
        if (qto.getVisitDepartmentIdIs() == null) {
            conditionToRemove.add("#visitDepartmentIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getAppointStatusIn())) {
            conditionToRemove.add("#appointStatusIn");
        }
        if (qto.getPatientNameLike() == null) {
            conditionToRemove.add("#patientNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getTriageStatusIn())) {
            conditionToRemove.add("#triageStatusIn");
        }
        if (qto.getCancelReasonIs() == null) {
            conditionToRemove.add("#cancelReasonIs");
        }
        if (CollectionUtil.isEmpty(qto.getVisitStatusIn())) {
            conditionToRemove.add("#visitStatusIn");
        }
        if (qto.getVisitCardIdIs() == null) {
            conditionToRemove.add("#visitCardIdIs");
        }
        if (qto.getDisplayIdIs() == null) {
            conditionToRemove.add("#displayIdIs");
        }
        if (qto.getCellphoneIs() == null) {
            conditionToRemove.add("#cellphoneIs");
        }
        if (qto.getCampusIdIs() == null) {
            conditionToRemove.add("#campusIdIs");
        }
        if (qto.getSourceIdIs() == null) {
            conditionToRemove.add("#sourceIdIs");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"department_department\"");
        softDeleteTableAlias.add("\"department\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#appointmentScheduleTimeDescriptionIn",
                                CollectionUtil.isEmpty(
                                                qto.getAppointmentScheduleTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleTimeDescriptionIn()
                                                        .size()))
                        .replace(
                                "#appointmentScheduleDayOfWeekIn",
                                CollectionUtil.isEmpty(qto.getAppointmentScheduleDayOfWeekIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleDayOfWeekIn().size()))
                        .replace("#operateDateBiggerThanEqual", "?")
                        .replace("#operateDateLessThanEqual", "?")
                        .replace("#appointmentCategoryIdIs", "?")
                        .replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()))
                        .replace("#identificationNumberIs", "?")
                        .replace("#identificationNumberIs", "?")
                        .replace("#visitDepartmentIdIs", "?")
                        .replace(
                                "#appointStatusIn",
                                CollectionUtil.isEmpty(qto.getAppointStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getAppointStatusIn().size()))
                        .replace("#patientNameLike", "?")
                        .replace(
                                "#triageStatusIn",
                                CollectionUtil.isEmpty(qto.getTriageStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTriageStatusIn().size()))
                        .replace("#cancelReasonIs", "?")
                        .replace(
                                "#visitStatusIn",
                                CollectionUtil.isEmpty(qto.getVisitStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getVisitStatusIn().size()))
                        .replace("#visitCardIdIs", "?")
                        .replace("#displayIdIs", "?")
                        .replace("#cellphoneIs", "?")
                        .replace("#campusIdIs", "?")
                        .replace("#sourceIdIs", "?")
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleTimeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getAppointmentScheduleTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#appointmentScheduleDayOfWeekIn")) {
                sqlParams.addAll(qto.getAppointmentScheduleDayOfWeekIn());
            } else if (paramName.equalsIgnoreCase("#operateDateBiggerThanEqual")) {
                sqlParams.add(qto.getOperateDateBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#operateDateLessThanEqual")) {
                sqlParams.add(qto.getOperateDateLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#appointmentCategoryIdIs")) {
                sqlParams.add(qto.getAppointmentCategoryIdIs());
            } else if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            } else if (paramName.equalsIgnoreCase("#identificationNumberIs")) {
                sqlParams.add(qto.getIdentificationNumberIs());
            } else if (paramName.equalsIgnoreCase("#identificationNumberIs")) {
                sqlParams.add(qto.getIdentificationNumberIs());
            } else if (paramName.equalsIgnoreCase("#visitDepartmentIdIs")) {
                sqlParams.add(qto.getVisitDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#appointStatusIn")) {
                sqlParams.addAll(
                        qto.getAppointStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#patientNameLike")) {
                sqlParams.add("%" + qto.getPatientNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#triageStatusIn")) {
                sqlParams.addAll(qto.getTriageStatusIn());
            } else if (paramName.equalsIgnoreCase("#cancelReasonIs")) {
                sqlParams.add(qto.getCancelReasonIs());
            } else if (paramName.equalsIgnoreCase("#visitStatusIn")) {
                sqlParams.addAll(
                        qto.getVisitStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#visitCardIdIs")) {
                sqlParams.add(qto.getVisitCardIdIs());
            } else if (paramName.equalsIgnoreCase("#displayIdIs")) {
                sqlParams.add(qto.getDisplayIdIs());
            } else if (paramName.equalsIgnoreCase("#cellphoneIs")) {
                sqlParams.add(qto.getCellphoneIs());
            } else if (paramName.equalsIgnoreCase("#campusIdIs")) {
                sqlParams.add(qto.getCampusIdIs());
            } else if (paramName.equalsIgnoreCase("#sourceIdIs")) {
                sqlParams.add(qto.getSourceIdIs());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  outp_appoint.updated_by asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询预约患者 */
    @AutoGenerated(locked = false, uuid = "8de3e0df-1697-44c8-a81c-0ba39dbc55a4-query-paginate")
    public List<String> queryPaged(SearchAppointPatientQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT outp_appoint.id FROM outp_appoint LEFT JOIN appointment_schedule"
                    + " \"appointmentSchedule\" on outp_appoint.appointment_schedule_id ="
                    + " \"appointmentSchedule\".id WHERE outp_appoint.operate_date <="
                    + " #operateDateLessThanEqual AND outp_appoint.operate_date >="
                    + " #operateDateBiggerThanEqual AND \"appointmentSchedule\".day_of_week in"
                    + " #appointmentScheduleDayOfWeekIn AND"
                    + " \"appointmentSchedule\".time_description in"
                    + " #appointmentScheduleTimeDescriptionIn AND outp_appoint.campus_id ="
                    + " #campusIdIs AND outp_appoint.appoint_status in #appointStatusIn AND EXISTS"
                    + " ( SELECT \"outpRegisterVisitList\".id FROM outp_register"
                    + " \"outpRegisterVisitList\" WHERE outp_appoint.id ="
                    + " \"outpRegisterVisitList\".outp_appoint_id AND ( EXISTS ( SELECT"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".id FROM outp_visit"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\" WHERE"
                    + " \"outpRegisterVisitList\".id ="
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".outp_register_id AND ("
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".visit_status in"
                    + " #visitStatusIn AND"
                    + " \"outpRegisterVisitList_outpVisitEncounterList\".triage_status in"
                    + " #triageStatusIn ) ) ) ) AND outp_appoint.cancel_reason = #cancelReasonIs"
                    + " AND outp_appoint.source_id = #sourceIdIs AND"
                    + " outp_appoint.appointment_category_id = #appointmentCategoryIdIs AND"
                    + " outp_appoint.clinic_register_type_id in #clinicRegisterTypeIdIn AND"
                    + " outp_appoint.doctor_id = #doctorIdIs AND"
                    + " outp_appoint.operator_department_id = #visitDepartmentIdIs AND"
                    + " outp_appoint.patient_name like #patientNameLike AND outp_appoint.display_id"
                    + " = #displayIdIs AND outp_appoint.visit_card_id = #visitCardIdIs AND"
                    + " outp_appoint.identification_number = #identificationNumberIs AND"
                    + " outp_appoint.cellphone = #cellphoneIs AND"
                    + " outp_appoint.identification_number = #identificationNumberIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleTimeDescriptionIn())) {
            conditionToRemove.add("#appointmentScheduleTimeDescriptionIn");
        }
        if (CollectionUtil.isEmpty(qto.getAppointmentScheduleDayOfWeekIn())) {
            conditionToRemove.add("#appointmentScheduleDayOfWeekIn");
        }
        if (qto.getOperateDateBiggerThanEqual() == null) {
            conditionToRemove.add("#operateDateBiggerThanEqual");
        }
        if (qto.getOperateDateLessThanEqual() == null) {
            conditionToRemove.add("#operateDateLessThanEqual");
        }
        if (qto.getAppointmentCategoryIdIs() == null) {
            conditionToRemove.add("#appointmentCategoryIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        if (qto.getIdentificationNumberIs() == null) {
            conditionToRemove.add("#identificationNumberIs");
        }
        if (qto.getIdentificationNumberIs() == null) {
            conditionToRemove.add("#identificationNumberIs");
        }
        if (qto.getVisitDepartmentIdIs() == null) {
            conditionToRemove.add("#visitDepartmentIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getAppointStatusIn())) {
            conditionToRemove.add("#appointStatusIn");
        }
        if (qto.getPatientNameLike() == null) {
            conditionToRemove.add("#patientNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getTriageStatusIn())) {
            conditionToRemove.add("#triageStatusIn");
        }
        if (qto.getCancelReasonIs() == null) {
            conditionToRemove.add("#cancelReasonIs");
        }
        if (CollectionUtil.isEmpty(qto.getVisitStatusIn())) {
            conditionToRemove.add("#visitStatusIn");
        }
        if (qto.getVisitCardIdIs() == null) {
            conditionToRemove.add("#visitCardIdIs");
        }
        if (qto.getDisplayIdIs() == null) {
            conditionToRemove.add("#displayIdIs");
        }
        if (qto.getCellphoneIs() == null) {
            conditionToRemove.add("#cellphoneIs");
        }
        if (qto.getCampusIdIs() == null) {
            conditionToRemove.add("#campusIdIs");
        }
        if (qto.getSourceIdIs() == null) {
            conditionToRemove.add("#sourceIdIs");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"department_department\"");
        softDeleteTableAlias.add("\"department\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#appointmentScheduleTimeDescriptionIn",
                                CollectionUtil.isEmpty(
                                                qto.getAppointmentScheduleTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleTimeDescriptionIn()
                                                        .size()))
                        .replace(
                                "#appointmentScheduleDayOfWeekIn",
                                CollectionUtil.isEmpty(qto.getAppointmentScheduleDayOfWeekIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getAppointmentScheduleDayOfWeekIn().size()))
                        .replace("#operateDateBiggerThanEqual", "?")
                        .replace("#operateDateLessThanEqual", "?")
                        .replace("#appointmentCategoryIdIs", "?")
                        .replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()))
                        .replace("#identificationNumberIs", "?")
                        .replace("#identificationNumberIs", "?")
                        .replace("#visitDepartmentIdIs", "?")
                        .replace(
                                "#appointStatusIn",
                                CollectionUtil.isEmpty(qto.getAppointStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getAppointStatusIn().size()))
                        .replace("#patientNameLike", "?")
                        .replace(
                                "#triageStatusIn",
                                CollectionUtil.isEmpty(qto.getTriageStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTriageStatusIn().size()))
                        .replace("#cancelReasonIs", "?")
                        .replace(
                                "#visitStatusIn",
                                CollectionUtil.isEmpty(qto.getVisitStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getVisitStatusIn().size()))
                        .replace("#visitCardIdIs", "?")
                        .replace("#displayIdIs", "?")
                        .replace("#cellphoneIs", "?")
                        .replace("#campusIdIs", "?")
                        .replace("#sourceIdIs", "?")
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleTimeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getAppointmentScheduleTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#appointmentScheduleDayOfWeekIn")) {
                sqlParams.addAll(qto.getAppointmentScheduleDayOfWeekIn());
            } else if (paramName.equalsIgnoreCase("#operateDateBiggerThanEqual")) {
                sqlParams.add(qto.getOperateDateBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#operateDateLessThanEqual")) {
                sqlParams.add(qto.getOperateDateLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#appointmentCategoryIdIs")) {
                sqlParams.add(qto.getAppointmentCategoryIdIs());
            } else if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            } else if (paramName.equalsIgnoreCase("#identificationNumberIs")) {
                sqlParams.add(qto.getIdentificationNumberIs());
            } else if (paramName.equalsIgnoreCase("#identificationNumberIs")) {
                sqlParams.add(qto.getIdentificationNumberIs());
            } else if (paramName.equalsIgnoreCase("#visitDepartmentIdIs")) {
                sqlParams.add(qto.getVisitDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#appointStatusIn")) {
                sqlParams.addAll(
                        qto.getAppointStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#patientNameLike")) {
                sqlParams.add("%" + qto.getPatientNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#triageStatusIn")) {
                sqlParams.addAll(qto.getTriageStatusIn());
            } else if (paramName.equalsIgnoreCase("#cancelReasonIs")) {
                sqlParams.add(qto.getCancelReasonIs());
            } else if (paramName.equalsIgnoreCase("#visitStatusIn")) {
                sqlParams.addAll(
                        qto.getVisitStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#visitCardIdIs")) {
                sqlParams.add(qto.getVisitCardIdIs());
            } else if (paramName.equalsIgnoreCase("#displayIdIs")) {
                sqlParams.add(qto.getDisplayIdIs());
            } else if (paramName.equalsIgnoreCase("#cellphoneIs")) {
                sqlParams.add(qto.getCellphoneIs());
            } else if (paramName.equalsIgnoreCase("#campusIdIs")) {
                sqlParams.add(qto.getCampusIdIs());
            } else if (paramName.equalsIgnoreCase("#sourceIdIs")) {
                sqlParams.add(qto.getSourceIdIs());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  outp_appoint.updated_by asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
