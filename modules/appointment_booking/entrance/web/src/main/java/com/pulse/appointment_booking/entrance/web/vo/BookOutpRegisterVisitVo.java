package com.pulse.appointment_booking.entrance.web.vo;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9668105f-0d4b-456c-807b-8304c8bbe108|VO|DEFINITION")
public class BookOutpRegisterVisitVo {
    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0a6040db-725e-49a0-9c06-bb8459b4fcdf")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "da8741c4-c319-4ae4-917b-a51f02524c18")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "05d13e92-3a41-482e-9c50-870f06dc143c")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "a5e74f1b-0d1e-4970-8886-1b3e56d5dcf3")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "3ccfabdb-151a-4428-b5ca-8a0d61929542")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "754ed8cb-9a4b-4cec-8cbe-a589923e5b17")
    private String chargeTypeCode;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "c06afdc5-67eb-4948-8033-4aae1ee529fe")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9f98d9c0-4e51-4eda-af69-fceb75001774")
    private Date createdAt;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "783050bb-fed3-47aa-b672-c226867479b7")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "fa39a627-7549-4eaf-9e97-e79f28db7b9e")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "c6f4a54c-8244-402b-b0d0-94518ac8ea3f")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2eca5218-85f5-4e42-8721-7df7a2bb14a7")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "3b160861-5f3c-437e-a135-363f8f1160df")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "21a927bc-f0a5-4aac-90c3-6052a0ed29a8")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "3b858a90-8514-465f-8727-6e4d2ea6ce2d")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "76da02c2-e693-4db8-82a8-d422dd1506a8")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "a5144c35-36bd-4c34-ba30-26b3c832c3ed")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "efd30ac1-d905-49a1-836d-8454fe3e757e")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "f290c93f-61b6-4478-b911-29660744d6e5")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "4b09fb2a-a358-4072-9c1c-e25ea2d04189")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "fd2f1791-ef94-45bc-8203-99a3eb88180c")
    private String outpAppointId;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "636f3586-24bf-4ce7-8590-7e9c090f394c")
    @NotNull(message = "主键不能为空")
    private List<BookOutpVisitEncounterVo> outpVisitEncounterList;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "03dda93b-58c6-4567-acf0-51f260c8a98e")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "73a9318f-ffe9-4659-bead-b648474c7efa")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "0b6001d2-85ee-4b40-8a53-31a26dedf027")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "e90c9ddf-cc7a-4d67-bfde-1aa8623722bf")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "52147d76-0a8d-499e-b2f7-4878a03a9f7c")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "290da506-a436-459d-94f9-87d486f37f30")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "5e99b31f-53a7-4d32-93cc-6d58d419da3e")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "*************-45ed-a2bb-112a61cd4ebb")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "bfa5912d-916a-4b9b-b447-26cf8aef1afa")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "0907c837-d457-4313-8a0e-423b93db9fd1")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "30c06820-51bc-40c2-8900-0bcd2741a146")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "6c75dae5-306a-43f4-b852-723c650ff1ed")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "7cfbedce-dd64-49b4-9671-e5dc6d304e0d")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "227497dc-98a9-45e9-b02d-86f9f779a01c")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "b9e1daed-034c-48e1-9d27-1549536e510b")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "5ec995b9-d7b5-4a4b-a6b9-8076d867ec1b")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "ac44532d-616d-4b12-8d70-542b331dca8a")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "d22a89e6-0f37-4bf4-bd85-32bba30386a0")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "b353fbdb-cfda-4920-aa09-c33ca1537d51")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "9838635a-77e2-430c-bafb-34d3422dd1f8")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "3e8c2360-6aaf-4b7b-a95a-5c7fd1d9d13f")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "5f5acbd9-8358-4b7d-833d-e57808dce723")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "7865b8c0-ebde-451e-a9ce-fff01c512f60")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "d1cc98dd-071d-4274-bfd0-d13e8a571297")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "125a9061-8c4d-46ed-8d11-c848dce01f9c")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "50a73f4c-4eee-4682-942f-d4bc5c1315e9")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "4649ba32-19a6-40a0-9d21-c0a31ce1bad8")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "0508f1f9-cb2c-46c9-9c3d-39f77a94e4a7")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "9cd74330-4708-4d40-9ae1-f6c1d7d6f74e")
    private BigDecimal totalCost;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "8f6db22d-3a8b-4e3e-9606-5adb91f94209")
    private Date updatedAt;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "e2fb6745-24b7-41bf-8cd2-cd9de64008f8")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "5ad5b2f5-d88e-483e-b717-6f0423fdafd1")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0bbcc7f1-f60c-494e-acd1-25bb735f8fa1")
    private TimeEo waitingStartTime;
}
