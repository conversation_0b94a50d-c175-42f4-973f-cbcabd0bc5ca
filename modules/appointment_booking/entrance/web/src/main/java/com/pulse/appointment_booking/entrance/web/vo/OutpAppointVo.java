package com.pulse.appointment_booking.entrance.web.vo;

import com.pulse.appointment_booking.common.enums.AppointStatusEnum;
import com.pulse.dictionary_basic.common.enums.ClinicVisitTypeEnum;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9f9a1fbd-0f3f-4ca7-8621-5822722ba8fc|VO|DEFINITION")
public class OutpAppointVo {
    /** 应用ID */
    @AutoGenerated(locked = true, uuid = "52d4d137-8493-4722-bb46-205790ff4786")
    private String appId;

    /** 预约凭证号 */
    @AutoGenerated(locked = true, uuid = "405fb499-3cd6-4080-9a4f-2f503525ecd6")
    private String appointCertificationNumber;

    /** 预约状态 */
    @AutoGenerated(locked = true, uuid = "0f95da5b-7574-4089-bd56-f6f296c8bb92")
    private AppointStatusEnum appointStatus;

    /** 号源类型ID */
    @AutoGenerated(locked = true, uuid = "8af3d976-226d-4be9-9afe-35884b64a12d")
    private String appointmentCategoryId;

    /** 排班ID */
    @AutoGenerated(locked = true, uuid = "b67c2ea8-850a-4c6f-94d9-7bd3ff739b22")
    private String appointmentScheduleId;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "79fdebcb-783b-4568-a53a-40b5fe5bedec")
    private Date birthDate;

    /** 院区id */
    @AutoGenerated(locked = true, uuid = "e7da63fb-d6eb-4407-a48e-979a02387b6c")
    private String campusId;

    /** 取消日期 */
    @AutoGenerated(locked = true, uuid = "94d1204b-b1aa-421b-9f1c-6231af14e011")
    private Date cancelDate;

    /** 取消人员ID */
    @AutoGenerated(locked = true, uuid = "93764b05-9309-4dbf-a077-854c0a10aa5c")
    private String cancelOperatorId;

    /** 取消原因 */
    @AutoGenerated(locked = true, uuid = "c5e59ecb-0ada-4d85-aedf-1fcf7ad1a9c3")
    private String cancelReason;

    /** 取消预约类型 */
    @AutoGenerated(locked = true, uuid = "b2845cc2-8afd-4e8e-8b9e-bdb345bf955c")
    private String cancelType;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "65f993ba-beb6-4675-9e18-cfe472b9d232")
    private String cellphone;

    /** 是否收费 */
    @AutoGenerated(locked = true, uuid = "ec958765-c9ea-42e6-bf46-d2343bb5f924")
    private Boolean chargeIs;

    /** 挂号类别ID */
    @AutoGenerated(locked = true, uuid = "80609d5e-2ac8-40a0-b448-9b1ff5154f8d")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "2838f00e-4cf6-4e2a-b946-555e4b420ba9")
    private Date createdAt;

    /** 创建人 */
    @AutoGenerated(locked = true, uuid = "e3395337-e22b-4842-bf3d-ea7a420d179a")
    private String createdBy;

    /** 科室ID */
    @AutoGenerated(locked = true, uuid = "c5829fd5-30c1-4e5b-8ad3-19b0beba32d2")
    private String departmentId;

    /** 病案号 */
    @AutoGenerated(locked = true, uuid = "44e3e5f4-6928-475b-b15f-c48c68cffcaa")
    private String displayId;

    /** 医生ID */
    @AutoGenerated(locked = true, uuid = "d39529c0-4e6d-42f4-be0f-89b2952698aa")
    private String doctorId;

    /** 预约编号 取号时,预约编号+取号密码作为凭证 */
    @AutoGenerated(locked = true, uuid = "6b4847b9-f8e2-4d78-a92b-3b9c8141879c")
    private String externalRegisterId;

    /** 加号标志 */
    @AutoGenerated(locked = true, uuid = "32cd59e4-ad56-4903-863c-a81af1d38700")
    private Boolean extraRegistrationFlag;

    /** 家庭地址 */
    @AutoGenerated(locked = true, uuid = "c42a47b2-e248-4c6d-a0e6-303dfe021ae5")
    private String homeAddress;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4b20f6a9-ca6b-4857-9fdd-9d0784fa03b4")
    private String id;

    /** 患者预约实名认证证件类型 */
    @AutoGenerated(locked = true, uuid = "58c823d3-1479-4961-a0cb-a6b50d87bb01")
    private String identificationClassId;

    /** 实名认证证件号码 */
    @AutoGenerated(locked = true, uuid = "8e8da6dc-8ae9-4eaf-95ee-25884707add4")
    private String identificationNumber;

    /** 互联网收费标志 */
    @AutoGenerated(locked = true, uuid = "f6d290b0-9591-412f-b072-39e4a3c6ec30")
    private Boolean internetPaymentFlag;

    /** MDT诊疗费类别 */
    @AutoGenerated(locked = true, uuid = "ca31868a-26fb-4310-8f86-589f44b377bd")
    private String mdtFeeType;

    /** 预约日期 */
    @AutoGenerated(locked = true, uuid = "f6fe522e-b707-4f4f-9e64-78adc00f5cd8")
    private Date operateDate;

    /** 就诊科室代码 */
    @AutoGenerated(locked = true, uuid = "a09376d2-8520-470e-b89a-4532ec93f652")
    private String operatorDepartmentId;

    /** 预约人员ID */
    @AutoGenerated(locked = true, uuid = "9096d21c-4093-4ec8-b89d-78d1b75222c0")
    private String operatorId;

    /** 门诊住院标志 */
    @AutoGenerated(locked = true, uuid = "69944c64-c07f-4a44-a01f-36102c673cf2")
    private ClinicVisitTypeEnum outpInpType;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "bbf300e7-e69d-41a2-bb73-209bd1a60367")
    private String patientId;

    /** 患者名称 未建档患者使用 */
    @AutoGenerated(locked = true, uuid = "c60ce6f4-f8db-4488-a5e9-055f8a7e0af1")
    private String patientName;

    /** 省预约平台上传标志 */
    @AutoGenerated(locked = true, uuid = "532c2d95-7db6-4833-84d8-67f103d7ce14")
    private Boolean provinceUploadFlag;

    /** 代理挂号标志 */
    @AutoGenerated(locked = true, uuid = "bbc866cc-1752-4ce2-8f66-ff9ce91112ec")
    private Boolean proxyRegistrationFlag;

    /** 提醒手机 */
    @AutoGenerated(locked = true, uuid = "79663c00-d5ba-4982-bd48-094675ea9fd6")
    private String reminderMobile;

    /** 号源序号 */
    @AutoGenerated(locked = true, uuid = "a84fe162-962f-4757-898e-7be54f4f8b06")
    private Long serialNumber;

    /** 来源ID */
    @AutoGenerated(locked = true, uuid = "3310572c-0f71-4ef0-b1e0-6076e333ce91")
    private String sourceId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "53e82bf0-b65a-49b5-8b73-a2cb953c4633")
    private Date updatedAt;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "08cedd46-9738-4702-abff-a4e185b4caa8")
    private String updatedBy;

    /** 就诊卡ID */
    @AutoGenerated(locked = true, uuid = "f697d191-6311-46b2-96fc-b784715ca01a")
    private String visitCardId;

    /** 就诊日期 */
    @AutoGenerated(locked = true, uuid = "fea3cc73-f886-4bb7-9374-181e36ca8630")
    private Date visitDate;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c0fa97fc-ee8b-4dba-9c96-ea392d7fee85")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7dd72ba4-f243-47ca-bbde-9f1615e5e809")
    private TimeEo waitingStartTime;
}
