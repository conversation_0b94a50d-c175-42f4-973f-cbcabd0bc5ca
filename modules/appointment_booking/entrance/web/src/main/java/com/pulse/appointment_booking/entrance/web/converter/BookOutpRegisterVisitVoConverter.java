package com.pulse.appointment_booking.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.entrance.web.query.assembler.BookOutpRegisterVisitVoDataAssembler;
import com.pulse.appointment_booking.entrance.web.query.assembler.BookOutpRegisterVisitVoDataAssembler.BookOutpRegisterVisitVoDataHolder;
import com.pulse.appointment_booking.entrance.web.query.collector.BookOutpRegisterVisitVoDataCollector;
import com.pulse.appointment_booking.entrance.web.vo.BookOutpRegisterVisitVo;
import com.pulse.appointment_booking.entrance.web.vo.BookOutpVisitEncounterVo;
import com.pulse.appointment_booking.manager.dto.OutpRegisterVisitDto;
import com.pulse.appointment_booking.service.OutpRegisterBaseDtoService;
import com.pulse.visit.manager.dto.OutpVisitEncounterDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到BookOutpRegisterVisitVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9668105f-0d4b-456c-807b-8304c8bbe108|VO|CONVERTER")
public class BookOutpRegisterVisitVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private BookOutpRegisterVisitVoDataAssembler bookOutpRegisterVisitVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private BookOutpRegisterVisitVoDataCollector bookOutpRegisterVisitVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private BookOutpVisitEncounterVoConverter bookOutpVisitEncounterVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterBaseDtoService outpRegisterBaseDtoService;

    /** 把OutpRegisterVisitDto转换成BookOutpRegisterVisitVo */
    @AutoGenerated(locked = true, uuid = "1ed0f667-cebc-367a-864b-fede893e12b8")
    public BookOutpRegisterVisitVo convertToBookOutpRegisterVisitVo(OutpRegisterVisitDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToBookOutpRegisterVisitVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装BookOutpRegisterVisitVo数据 */
    @AutoGenerated(locked = true, uuid = "6bb978a7-7761-303e-b99a-8c3295add013")
    public BookOutpRegisterVisitVo convertAndAssembleData(OutpRegisterVisitDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OutpRegisterVisitDto转换成BookOutpRegisterVisitVo */
    @AutoGenerated(locked = false, uuid = "9668105f-0d4b-456c-807b-8304c8bbe108-converter-Map")
    public Map<OutpRegisterVisitDto, BookOutpRegisterVisitVo> convertToBookOutpRegisterVisitVoMap(
            List<OutpRegisterVisitDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<OutpVisitEncounterDto, BookOutpVisitEncounterVo> outpVisitEncounterListMap =
                bookOutpVisitEncounterVoConverter.convertToBookOutpVisitEncounterVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getOutpVisitEncounterList()))
                                .flatMap(dto -> dto.getOutpVisitEncounterList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OutpRegisterVisitDto, BookOutpRegisterVisitVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            BookOutpRegisterVisitVo vo =
                                                    new BookOutpRegisterVisitVo();
                                            vo.setId(dto.getId());
                                            vo.setPatientId(dto.getPatientId());
                                            vo.setVisitCardId(dto.getVisitCardId());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            vo.setSourceAppId(dto.getSourceAppId());
                                            vo.setSourceCampusId(dto.getSourceCampusId());
                                            vo.setTallyStatus(dto.getTallyStatus());
                                            vo.setRegisterNumber(dto.getRegisterNumber());
                                            vo.setPatientName(dto.getPatientName());
                                            vo.setAge(dto.getAge());
                                            vo.setIdentityType(dto.getIdentityType());
                                            vo.setInsuranceType(dto.getInsuranceType());
                                            vo.setInsuranceNumber(dto.getInsuranceNumber());
                                            vo.setRegisterStatus(dto.getRegisterStatus());
                                            vo.setOperatorId(dto.getOperatorId());
                                            vo.setRegisterDate(dto.getRegisterDate());
                                            vo.setSettleNumber(dto.getSettleNumber());
                                            vo.setCancellationInitiatorId(
                                                    dto.getCancellationInitiatorId());
                                            vo.setCancellationInitiationTime(
                                                    dto.getCancellationInitiationTime());
                                            vo.setReviewerId(dto.getReviewerId());
                                            vo.setReviewTime(dto.getReviewTime());
                                            vo.setTotalCost(dto.getTotalCost());
                                            vo.setTotalCharge(dto.getTotalCharge());
                                            vo.setReturnOperatorId(dto.getReturnOperatorId());
                                            vo.setReturnDate(dto.getReturnDate());
                                            vo.setReturnSettleNumber(dto.getReturnSettleNumber());
                                            vo.setReturnReason(dto.getReturnReason());
                                            vo.setChargePriceScheduleId(
                                                    dto.getChargePriceScheduleId());
                                            vo.setDiscountCategory(dto.getDiscountCategory());
                                            vo.setTallyReceiptNumber(dto.getTallyReceiptNumber());
                                            vo.setChargeTypeCode(dto.getChargeTypeCode());
                                            vo.setIdentityCode(dto.getIdentityCode());
                                            vo.setTakeAppointOperatorId(
                                                    dto.getTakeAppointOperatorId());
                                            vo.setTakeAppointDate(dto.getTakeAppointDate());
                                            vo.setRegistrationDoctorId(
                                                    dto.getRegistrationDoctorId());
                                            vo.setRegistrationDepartmentId(
                                                    dto.getRegistrationDepartmentId());
                                            vo.setOutpatientRegistrationCategory(
                                                    dto.getOutpatientRegistrationCategory());
                                            vo.setSpecifiedDiseaseFlag(
                                                    dto.getSpecifiedDiseaseFlag());
                                            vo.setSpecifiedDiseaseCode(
                                                    dto.getSpecifiedDiseaseCode());
                                            vo.setWaitingStartTime(dto.getWaitingStartTime());
                                            vo.setWaitingEndTime(dto.getWaitingEndTime());
                                            vo.setRegisterReviewStatus(
                                                    dto.getRegisterReviewStatus());
                                            vo.setReviewReason(dto.getReviewReason());
                                            vo.setReviewExplain(dto.getReviewExplain());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setMdtFeeType(dto.getMdtFeeType());
                                            vo.setOutpAppointId(dto.getOutpAppointId());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setGender(dto.getGender());
                                            vo.setOutpVisitEncounterList(
                                                    dto.getOutpVisitEncounterList() == null
                                                            ? null
                                                            : dto
                                                                    .getOutpVisitEncounterList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    outpVisitEncounterListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setChargeStatus(dto.getChargeStatus());
                                            vo.setHealthOfficialsFee(dto.getHealthOfficialsFee());
                                            vo.setInternationalPatientFee(
                                                    dto.getInternationalPatientFee());
                                            vo.setOperatorDepartmentId(
                                                    dto.getOperatorDepartmentId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OutpRegisterVisitDto转换成BookOutpRegisterVisitVo */
    @AutoGenerated(locked = true, uuid = "9668105f-0d4b-456c-807b-8304c8bbe108-converter-list")
    public List<BookOutpRegisterVisitVo> convertToBookOutpRegisterVisitVoList(
            List<OutpRegisterVisitDto> dtoList) {
        return new ArrayList<>(convertToBookOutpRegisterVisitVoMap(dtoList).values());
    }

    /** 使用默认方式组装BookOutpRegisterVisitVo列表数据 */
    @AutoGenerated(locked = true, uuid = "cb39f52a-16ea-3e74-9e24-e84ecfd8dc08")
    public List<BookOutpRegisterVisitVo> convertAndAssembleDataList(
            List<OutpRegisterVisitDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        BookOutpRegisterVisitVoDataHolder dataHolder = new BookOutpRegisterVisitVoDataHolder();
        dataHolder.setRootBaseDtoList(
                outpRegisterBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OutpRegisterVisitDto::getId)
                                .collect(Collectors.toList())));
        Map<String, BookOutpRegisterVisitVo> voMap =
                convertToBookOutpRegisterVisitVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        bookOutpRegisterVisitVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        bookOutpRegisterVisitVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
