package com.pulse.appointment_booking.service.bto;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;

import javax.validation.Valid;

/**
 * <b>[源自]</b> OutpRegister
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "f90c0f83-6ae2-4735-8978-a0d84ddd5d21|BTO|DEFINITION")
public class MergeOutpRegisterBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "950542de-5d19-4c9a-8396-ed6398881b75")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "df67a37d-3b01-4152-a849-b0b3a520deb1")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "a4891238-898b-407e-9e97-52d5f669de8f")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "336c4c77-ed30-416f-8f6d-71deac25585f")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "afb872a6-b4f7-45e8-a051-2d2f0e168494")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "15f38129-ad2f-445b-a2e1-70940a60475d")
    private String chargeTypeCode;

    /** 挂号类别ID */
    @AutoGenerated(locked = true, uuid = "872a4a2e-b402-4212-96fe-453bea1cf4a9")
    private String clinicRegisterTypeId;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "56bf360e-2dd6-42dd-84d1-89a679ebf3ad")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "3eebdb03-333d-43e7-afbf-b12865855639")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "8994368f-9823-4cef-9122-ae17672ba027")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4c81366d-1040-4468-9057-4f0fab641831")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "2991687a-6b70-41e3-90e1-7f4490a323dc")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "26ba0d1f-8784-4d12-84df-60e3c9931151")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "ffbe82a6-ae46-4b9c-9df5-c4db6f2cb0c1")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "4f61d283-62ee-448a-a3af-aef5e3398f45")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "1b42c4a6-cb63-40fa-bd58-dbb3a6f8a620")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "858533da-8119-427d-8185-2c7bac3098ff")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "7ea58b52-466a-42f7-9ffb-69ed984cf72d")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "617822e1-1a59-43bd-bad4-f91cad01068c")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "07b4de4a-7a5e-4235-bde3-37ff7230fd6f")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "a27eb1e0-edc1-4fa4-9e46-e88a9924069b")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "5db20073-1fcd-4e40-9dba-6c0c185207be")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "d2604c62-2a9c-4846-995e-11cf4109f414")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "d6fefef3-b404-49fb-a63f-8dc4c718604f")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "b1fbeb32-f960-4201-a95f-1770efe8c2f5")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "3584deaa-**************-51e80ee612ad")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "caa81525-03e2-4d63-8593-f1b4c831e0ff")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "89842940-1a2b-418f-9acd-ffa46255d771")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "1a56c236-a91e-4d46-a2fe-8962b7048980")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "a68649d9-be7a-4ec4-a4d8-a5e6cc4221b4")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "5a28bf99-c89d-428a-a5f6-b10e98bbb8eb")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "e3215ccf-7020-46a9-9147-e11904cb50a8")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "f49cfafd-951f-4323-900a-8b0da22f89ba")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "5280ddf3-5ee8-4c53-b687-3157b490eab3")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "29577a85-f73a-4090-9a1d-d68a45e4702c")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "72a35ea6-161b-4a62-8dfb-c9153bbda0c3")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "ae9783bd-5a91-4ee4-96e3-d225cf53ede1")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "8091889f-570a-45d9-bcee-5ed0439c2954")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "00e0f1fb-9649-4f8a-961e-6ec3be5ea9c4")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "29c2122b-fbd0-48cb-8d7d-fc0e9dfcbb4f")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "3b34a776-9ba1-42ae-889d-2ce06a02560a")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "1b443a8d-b52b-4ac7-9e1d-0923fcc53942")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "9a57e0fc-a920-427f-9654-bcce00223b82")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "d3ba6e77-28a3-485e-9fae-baa1a48d3847")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "9d09cb38-8080-465c-a848-1f057491252f")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "fdc130c3-0824-4b1b-ba54-f484d5a199f2")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "f4614b69-4db1-4621-b0d9-d2051778bdcc")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "9d56dfad-9a05-490b-b24e-910672601ee6")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "c31a781e-05c4-45a0-b399-be5519b1f30c")
    private BigDecimal totalCost;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "3bb455b2-5bae-4273-9da5-949045e5b5ea")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "86c13d7e-5893-489a-bfb1-68974fdf256e")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1866dea9-c45e-4abe-ba41-f8fa28b161af")
    private TimeEo waitingStartTime;

    @AutoGenerated(locked = true)
    public void setAge(AgeEo age) {
        this.__$validPropertySet.add("age");
        this.age = age;
    }

    @AutoGenerated(locked = true)
    public void setCancellationInitiationTime(Date cancellationInitiationTime) {
        this.__$validPropertySet.add("cancellationInitiationTime");
        this.cancellationInitiationTime = cancellationInitiationTime;
    }

    @AutoGenerated(locked = true)
    public void setCancellationInitiatorId(String cancellationInitiatorId) {
        this.__$validPropertySet.add("cancellationInitiatorId");
        this.cancellationInitiatorId = cancellationInitiatorId;
    }

    @AutoGenerated(locked = true)
    public void setChargePriceScheduleId(String chargePriceScheduleId) {
        this.__$validPropertySet.add("chargePriceScheduleId");
        this.chargePriceScheduleId = chargePriceScheduleId;
    }

    @AutoGenerated(locked = true)
    public void setChargeStatus(ChargeStatusEnum chargeStatus) {
        this.__$validPropertySet.add("chargeStatus");
        this.chargeStatus = chargeStatus;
    }

    @AutoGenerated(locked = true)
    public void setChargeTypeCode(String chargeTypeCode) {
        this.__$validPropertySet.add("chargeTypeCode");
        this.chargeTypeCode = chargeTypeCode;
    }

    @AutoGenerated(locked = true)
    public void setClinicRegisterTypeId(String clinicRegisterTypeId) {
        this.__$validPropertySet.add("clinicRegisterTypeId");
        this.clinicRegisterTypeId = clinicRegisterTypeId;
    }

    @AutoGenerated(locked = true)
    public void setDiscountCategory(String discountCategory) {
        this.__$validPropertySet.add("discountCategory");
        this.discountCategory = discountCategory;
    }

    @AutoGenerated(locked = true)
    public void setGender(GenderEnum gender) {
        this.__$validPropertySet.add("gender");
        this.gender = gender;
    }

    @AutoGenerated(locked = true)
    public void setHealthOfficialsFee(String healthOfficialsFee) {
        this.__$validPropertySet.add("healthOfficialsFee");
        this.healthOfficialsFee = healthOfficialsFee;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setIdentityCode(String identityCode) {
        this.__$validPropertySet.add("identityCode");
        this.identityCode = identityCode;
    }

    @AutoGenerated(locked = true)
    public void setIdentityType(String identityType) {
        this.__$validPropertySet.add("identityType");
        this.identityType = identityType;
    }

    @AutoGenerated(locked = true)
    public void setInsuranceNumber(String insuranceNumber) {
        this.__$validPropertySet.add("insuranceNumber");
        this.insuranceNumber = insuranceNumber;
    }

    @AutoGenerated(locked = true)
    public void setInsuranceType(String insuranceType) {
        this.__$validPropertySet.add("insuranceType");
        this.insuranceType = insuranceType;
    }

    @AutoGenerated(locked = true)
    public void setInternationalPatientFee(String internationalPatientFee) {
        this.__$validPropertySet.add("internationalPatientFee");
        this.internationalPatientFee = internationalPatientFee;
    }

    @AutoGenerated(locked = true)
    public void setMdtFeeType(String mdtFeeType) {
        this.__$validPropertySet.add("mdtFeeType");
        this.mdtFeeType = mdtFeeType;
    }

    @AutoGenerated(locked = true)
    public void setOperatorDepartmentId(String operatorDepartmentId) {
        this.__$validPropertySet.add("operatorDepartmentId");
        this.operatorDepartmentId = operatorDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setOperatorId(String operatorId) {
        this.__$validPropertySet.add("operatorId");
        this.operatorId = operatorId;
    }

    @AutoGenerated(locked = true)
    public void setOutpAppointId(String outpAppointId) {
        this.__$validPropertySet.add("outpAppointId");
        this.outpAppointId = outpAppointId;
    }

    @AutoGenerated(locked = true)
    public void setOutpatientRegistrationCategory(String outpatientRegistrationCategory) {
        this.__$validPropertySet.add("outpatientRegistrationCategory");
        this.outpatientRegistrationCategory = outpatientRegistrationCategory;
    }

    @AutoGenerated(locked = true)
    public void setPatientId(String patientId) {
        this.__$validPropertySet.add("patientId");
        this.patientId = patientId;
    }

    @AutoGenerated(locked = true)
    public void setPatientName(String patientName) {
        this.__$validPropertySet.add("patientName");
        this.patientName = patientName;
    }

    @AutoGenerated(locked = true)
    public void setRegisterDate(Date registerDate) {
        this.__$validPropertySet.add("registerDate");
        this.registerDate = registerDate;
    }

    @AutoGenerated(locked = true)
    public void setRegisterNumber(Long registerNumber) {
        this.__$validPropertySet.add("registerNumber");
        this.registerNumber = registerNumber;
    }

    @AutoGenerated(locked = true)
    public void setRegisterReviewStatus(RegisterReviewEnum registerReviewStatus) {
        this.__$validPropertySet.add("registerReviewStatus");
        this.registerReviewStatus = registerReviewStatus;
    }

    @AutoGenerated(locked = true)
    public void setRegisterStatus(RegisterStatusEnum registerStatus) {
        this.__$validPropertySet.add("registerStatus");
        this.registerStatus = registerStatus;
    }

    @AutoGenerated(locked = true)
    public void setRegistrationDepartmentId(String registrationDepartmentId) {
        this.__$validPropertySet.add("registrationDepartmentId");
        this.registrationDepartmentId = registrationDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setRegistrationDoctorId(String registrationDoctorId) {
        this.__$validPropertySet.add("registrationDoctorId");
        this.registrationDoctorId = registrationDoctorId;
    }

    @AutoGenerated(locked = true)
    public void setReturnDate(Date returnDate) {
        this.__$validPropertySet.add("returnDate");
        this.returnDate = returnDate;
    }

    @AutoGenerated(locked = true)
    public void setReturnOperatorId(String returnOperatorId) {
        this.__$validPropertySet.add("returnOperatorId");
        this.returnOperatorId = returnOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setReturnReason(String returnReason) {
        this.__$validPropertySet.add("returnReason");
        this.returnReason = returnReason;
    }

    @AutoGenerated(locked = true)
    public void setReturnSettleNumber(String returnSettleNumber) {
        this.__$validPropertySet.add("returnSettleNumber");
        this.returnSettleNumber = returnSettleNumber;
    }

    @AutoGenerated(locked = true)
    public void setReviewExplain(String reviewExplain) {
        this.__$validPropertySet.add("reviewExplain");
        this.reviewExplain = reviewExplain;
    }

    @AutoGenerated(locked = true)
    public void setReviewReason(String reviewReason) {
        this.__$validPropertySet.add("reviewReason");
        this.reviewReason = reviewReason;
    }

    @AutoGenerated(locked = true)
    public void setReviewTime(Date reviewTime) {
        this.__$validPropertySet.add("reviewTime");
        this.reviewTime = reviewTime;
    }

    @AutoGenerated(locked = true)
    public void setReviewerId(String reviewerId) {
        this.__$validPropertySet.add("reviewerId");
        this.reviewerId = reviewerId;
    }

    @AutoGenerated(locked = true)
    public void setSettleNumber(String settleNumber) {
        this.__$validPropertySet.add("settleNumber");
        this.settleNumber = settleNumber;
    }

    @AutoGenerated(locked = true)
    public void setSourceAppId(String sourceAppId) {
        this.__$validPropertySet.add("sourceAppId");
        this.sourceAppId = sourceAppId;
    }

    @AutoGenerated(locked = true)
    public void setSourceCampusId(String sourceCampusId) {
        this.__$validPropertySet.add("sourceCampusId");
        this.sourceCampusId = sourceCampusId;
    }

    @AutoGenerated(locked = true)
    public void setSpecifiedDiseaseCode(String specifiedDiseaseCode) {
        this.__$validPropertySet.add("specifiedDiseaseCode");
        this.specifiedDiseaseCode = specifiedDiseaseCode;
    }

    @AutoGenerated(locked = true)
    public void setSpecifiedDiseaseFlag(Boolean specifiedDiseaseFlag) {
        this.__$validPropertySet.add("specifiedDiseaseFlag");
        this.specifiedDiseaseFlag = specifiedDiseaseFlag;
    }

    @AutoGenerated(locked = true)
    public void setTakeAppointDate(Date takeAppointDate) {
        this.__$validPropertySet.add("takeAppointDate");
        this.takeAppointDate = takeAppointDate;
    }

    @AutoGenerated(locked = true)
    public void setTakeAppointOperatorId(String takeAppointOperatorId) {
        this.__$validPropertySet.add("takeAppointOperatorId");
        this.takeAppointOperatorId = takeAppointOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setTallyReceiptNumber(String tallyReceiptNumber) {
        this.__$validPropertySet.add("tallyReceiptNumber");
        this.tallyReceiptNumber = tallyReceiptNumber;
    }

    @AutoGenerated(locked = true)
    public void setTallyStatus(String tallyStatus) {
        this.__$validPropertySet.add("tallyStatus");
        this.tallyStatus = tallyStatus;
    }

    @AutoGenerated(locked = true)
    public void setTimeDescription(TimeDescriptionEnum timeDescription) {
        this.__$validPropertySet.add("timeDescription");
        this.timeDescription = timeDescription;
    }

    @AutoGenerated(locked = true)
    public void setTotalCharge(BigDecimal totalCharge) {
        this.__$validPropertySet.add("totalCharge");
        this.totalCharge = totalCharge;
    }

    @AutoGenerated(locked = true)
    public void setTotalCost(BigDecimal totalCost) {
        this.__$validPropertySet.add("totalCost");
        this.totalCost = totalCost;
    }

    @AutoGenerated(locked = true)
    public void setVisitCardId(String visitCardId) {
        this.__$validPropertySet.add("visitCardId");
        this.visitCardId = visitCardId;
    }

    @AutoGenerated(locked = true)
    public void setWaitingEndTime(TimeEo waitingEndTime) {
        this.__$validPropertySet.add("waitingEndTime");
        this.waitingEndTime = waitingEndTime;
    }

    @AutoGenerated(locked = true)
    public void setWaitingStartTime(TimeEo waitingStartTime) {
        this.__$validPropertySet.add("waitingStartTime");
        this.waitingStartTime = waitingStartTime;
    }
}
