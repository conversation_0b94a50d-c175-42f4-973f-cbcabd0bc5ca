package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterVisitDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterVisitDto;
import com.pulse.appointment_booking.service.converter.OutpRegisterVisitDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "c3813169-1d4b-49e4-8f2b-d96491e257c2|DTO|SERVICE")
public class OutpRegisterVisitDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterVisitDtoManager outpRegisterVisitDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterVisitDtoServiceConverter outpRegisterVisitDtoServiceConverter;

    @PublicInterface(
            id = "5f265037-b51a-421c-a514-07410dd927ec",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741632")
    @AutoGenerated(locked = false, uuid = "1563f737-7e88-3422-8a18-1d4236d00ce3")
    public List<OutpRegisterVisitDto> getByOutpAppointIds(
            @Valid @NotNull(message = "预约ID不能为空") List<String> outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        outpAppointId = new ArrayList<>(new HashSet<>(outpAppointId));
        List<OutpRegisterVisitDto> outpRegisterVisitDtoList =
                outpRegisterVisitDtoManager.getByOutpAppointIds(outpAppointId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterVisitDtoServiceConverter.OutpRegisterVisitDtoConverter(
                outpRegisterVisitDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "2b918744-9791-4da2-8e3d-83af6b532a36",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741592")
    @AutoGenerated(locked = false, uuid = "5e5850b4-4bec-327e-a011-23e1bc9c4f1b")
    public OutpRegisterVisitDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpRegisterVisitDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "d8625744-3843-4fe8-b7bf-f6eb30ae2951",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741622")
    @AutoGenerated(locked = false, uuid = "5f4e4f22-0cfd-31da-aa69-a1172e2c48eb")
    public List<OutpRegisterVisitDto> getByOutpAppointId(
            @NotNull(message = "预约ID不能为空") String outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOutpAppointIds(Arrays.asList(outpAppointId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "e789c8ca-329f-476c-8de7-23d5c00104a3",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741611")
    @AutoGenerated(locked = false, uuid = "681b0bee-3166-3478-af65-6607b753749e")
    public List<OutpRegisterVisitDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpRegisterVisitDto> outpRegisterVisitDtoList =
                outpRegisterVisitDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterVisitDtoServiceConverter.OutpRegisterVisitDtoConverter(
                outpRegisterVisitDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
