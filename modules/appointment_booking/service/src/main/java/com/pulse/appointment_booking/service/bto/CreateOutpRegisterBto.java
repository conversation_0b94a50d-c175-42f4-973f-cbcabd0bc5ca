package com.pulse.appointment_booking.service.bto;

import com.pulse.appointment_booking.common.enums.ChargeStatusEnum;
import com.pulse.appointment_booking.common.enums.RegisterReviewEnum;
import com.pulse.appointment_booking.common.enums.RegisterStatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;

import javax.validation.Valid;

/**
 * <b>[源自]</b> OutpRegister
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "80701d49-f6f0-4f35-8cf0-0d431ecc182b|BTO|DEFINITION")
public class CreateOutpRegisterBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 患者年龄 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0d78c7b4-581a-453b-927b-db8d7b313963")
    private AgeEo age;

    /** 退号发起时间 */
    @AutoGenerated(locked = true, uuid = "7bf2518f-6929-4337-a74a-be313e52403a")
    private Date cancellationInitiationTime;

    /** 退号发起人id */
    @AutoGenerated(locked = true, uuid = "7a1052ba-03d6-4261-88a3-3e2c39e2855f")
    private String cancellationInitiatorId;

    /** 收费方案ID */
    @AutoGenerated(locked = true, uuid = "dce2abae-9fca-4488-bde5-63b8141e0ff8")
    private String chargePriceScheduleId;

    /** 收费状态 */
    @AutoGenerated(locked = true, uuid = "8e43afb1-f03c-4f7e-a4bd-444315616ebd")
    private ChargeStatusEnum chargeStatus;

    /** 费别编码 */
    @AutoGenerated(locked = true, uuid = "4d88ccbf-3c74-41a1-b7f7-fd6c40d2f8be")
    private String chargeTypeCode;

    /** 挂号类别ID */
    @AutoGenerated(locked = true, uuid = "80735f12-c2b7-41bd-bbf3-584fcb678b47")
    private String clinicRegisterTypeId;

    /** 优惠类别 */
    @AutoGenerated(locked = true, uuid = "a318f7de-a7ae-4d3d-ad3a-ea6bc8327810")
    private String discountCategory;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "ed334ff3-48de-453a-911e-d7e56004173d")
    private GenderEnum gender;

    /** 保健干部费用 */
    @AutoGenerated(locked = true, uuid = "e5c16926-d191-41df-b1bf-6bed783fa21c")
    private String healthOfficialsFee;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2810ffac-ac2e-4813-9e8c-c16743b2c5d5")
    private String id;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "fafac0fa-5ae1-4fc9-840b-4a1a6ac4e683")
    private String identityCode;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "37bd56f3-73b4-4c1f-aaa0-76dd62d4cf76")
    private String identityType;

    /** 医疗保障号 */
    @AutoGenerated(locked = true, uuid = "9563226d-b76f-4a7b-90f3-39713b6e4f95")
    private String insuranceNumber;

    /** 医疗保障类型 */
    @AutoGenerated(locked = true, uuid = "5e5ce58b-0f86-4dc0-abcf-6ce5bec532d2")
    private String insuranceType;

    /** 国际病人费用 */
    @AutoGenerated(locked = true, uuid = "38a5644d-ac9d-4c65-a8d2-4007a28c0b6e")
    private String internationalPatientFee;

    /** MDT费用类型 */
    @AutoGenerated(locked = true, uuid = "1eddaec6-e615-4e8f-ab1c-aa4ed0b2c70d")
    private String mdtFeeType;

    /** 挂号操作科室ID */
    @AutoGenerated(locked = true, uuid = "fb787037-96fe-40e6-afa0-a884dbb7099e")
    private String operatorDepartmentId;

    /** 挂号操作员ID */
    @AutoGenerated(locked = true, uuid = "d721d4bb-e5b4-40f5-b164-e56838a6d78a")
    private String operatorId;

    /** 预约ID */
    @AutoGenerated(locked = true, uuid = "02cd8d92-fb15-4621-97e0-93e4b892d148")
    private String outpAppointId;

    /** 门诊挂号大类 */
    @AutoGenerated(locked = true, uuid = "6e65f189-1f4a-4ed5-bc4a-f947dd5e00b6")
    private String outpatientRegistrationCategory;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "a160e649-44ca-4630-94f2-a7347d1fc79d")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "90af186b-fce9-4b7f-b5ff-3955f164d35f")
    private String patientName;

    /** 挂号日期 */
    @AutoGenerated(locked = true, uuid = "437e05a6-1f2a-480a-9bf2-181e4555c166")
    private Date registerDate;

    /** 排班号序 */
    @AutoGenerated(locked = true, uuid = "4d3a12a6-b4e1-4c24-8aa8-76ca843cb038")
    private Long registerNumber;

    /** 挂号审核状态 */
    @AutoGenerated(locked = true, uuid = "6c946774-1254-40f3-9419-8326e8635e8d")
    private RegisterReviewEnum registerReviewStatus;

    /** 挂号状态 */
    @AutoGenerated(locked = true, uuid = "59242fab-c59c-41cc-a60e-a2a512f26314")
    private RegisterStatusEnum registerStatus;

    /** 挂号科室id */
    @AutoGenerated(locked = true, uuid = "3d908265-4cd3-49fa-9802-2a676c80ef6d")
    private String registrationDepartmentId;

    /** 挂号医生id */
    @AutoGenerated(locked = true, uuid = "24d0676d-2bdc-4f8e-b4db-2376dfc0d7d4")
    private String registrationDoctorId;

    /** 退号日期 */
    @AutoGenerated(locked = true, uuid = "69590995-61ff-402f-bd4e-cafbd91edbc8")
    private Date returnDate;

    /** 退号操作员id */
    @AutoGenerated(locked = true, uuid = "8cedfac6-7c73-46bc-800c-03444a05830a")
    private String returnOperatorId;

    /** 退号原因 */
    @AutoGenerated(locked = true, uuid = "2aeac22a-b822-4cb0-a9a9-e464e3299db5")
    private String returnReason;

    /** 退号费用结算ID */
    @AutoGenerated(locked = true, uuid = "a0d33391-cba0-4cc2-84a8-b8ef37747fd8")
    private String returnSettleNumber;

    /** 审核说明 */
    @AutoGenerated(locked = true, uuid = "728e8a58-94d3-40b8-8355-656c80de7ba9")
    private String reviewExplain;

    /** 审核原因 */
    @AutoGenerated(locked = true, uuid = "c0c493d2-ff0d-4b72-ab6c-dbe0530875f8")
    private String reviewReason;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "77b358f9-cabb-438a-9865-7019ad59aa6b")
    private Date reviewTime;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "16afefd0-06cc-4870-973e-e86c00e568a1")
    private String reviewerId;

    /** 挂号结算编号 */
    @AutoGenerated(locked = true, uuid = "85cab15c-387b-4154-8817-7af59e1b780a")
    private String settleNumber;

    /** 来源应用ID */
    @AutoGenerated(locked = true, uuid = "70de982f-3003-44ea-9962-28559f44854a")
    private String sourceAppId;

    /** 来源院区ID */
    @AutoGenerated(locked = true, uuid = "4a8bf2e5-654f-4c4b-8fe9-6405d2b5a051")
    private String sourceCampusId;

    /** 规定病种编码 */
    @AutoGenerated(locked = true, uuid = "115a5640-6d9b-4128-b7b9-ef9241c40426")
    private String specifiedDiseaseCode;

    /** 规定病种标识 */
    @AutoGenerated(locked = true, uuid = "9e28284c-de34-417a-b31e-ed6d9abbf625")
    private Boolean specifiedDiseaseFlag;

    /** 取预约号时间 */
    @AutoGenerated(locked = true, uuid = "1585349f-41f3-41e7-869d-512ead93c999")
    private Date takeAppointDate;

    /** 取预约号操作员ID */
    @AutoGenerated(locked = true, uuid = "6bdeb06c-2163-4cf8-95cf-fb6df515b86f")
    private String takeAppointOperatorId;

    /** 托收费用单据号 */
    @AutoGenerated(locked = true, uuid = "727099e3-64ab-4a7a-9ac4-15dda4892e68")
    private String tallyReceiptNumber;

    /** 托收状态 */
    @AutoGenerated(locked = true, uuid = "16f792f0-0b27-41bf-9b98-876c814c0c51")
    private String tallyStatus;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "6198eae2-**************-93125c35f130")
    private TimeDescriptionEnum timeDescription;

    /** 实收总额 */
    @AutoGenerated(locked = true, uuid = "41459a8d-6e08-4bb5-8748-cd913b73291d")
    private BigDecimal totalCharge;

    /** 应收总额 */
    @AutoGenerated(locked = true, uuid = "1c3197c8-c4b4-4cd5-9a3f-d4c3a58a9f6e")
    private BigDecimal totalCost;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "78978b05-6d96-45ea-8cf7-c291475ef51e")
    private String visitCardId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7cca0448-6af9-4430-aba2-369735b1252e")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "42e15415-7b6a-40de-8df5-580eaa6953fb")
    private TimeEo waitingStartTime;

    @AutoGenerated(locked = true)
    public void setAge(AgeEo age) {
        this.__$validPropertySet.add("age");
        this.age = age;
    }

    @AutoGenerated(locked = true)
    public void setCancellationInitiationTime(Date cancellationInitiationTime) {
        this.__$validPropertySet.add("cancellationInitiationTime");
        this.cancellationInitiationTime = cancellationInitiationTime;
    }

    @AutoGenerated(locked = true)
    public void setCancellationInitiatorId(String cancellationInitiatorId) {
        this.__$validPropertySet.add("cancellationInitiatorId");
        this.cancellationInitiatorId = cancellationInitiatorId;
    }

    @AutoGenerated(locked = true)
    public void setChargePriceScheduleId(String chargePriceScheduleId) {
        this.__$validPropertySet.add("chargePriceScheduleId");
        this.chargePriceScheduleId = chargePriceScheduleId;
    }

    @AutoGenerated(locked = true)
    public void setChargeStatus(ChargeStatusEnum chargeStatus) {
        this.__$validPropertySet.add("chargeStatus");
        this.chargeStatus = chargeStatus;
    }

    @AutoGenerated(locked = true)
    public void setChargeTypeCode(String chargeTypeCode) {
        this.__$validPropertySet.add("chargeTypeCode");
        this.chargeTypeCode = chargeTypeCode;
    }

    @AutoGenerated(locked = true)
    public void setClinicRegisterTypeId(String clinicRegisterTypeId) {
        this.__$validPropertySet.add("clinicRegisterTypeId");
        this.clinicRegisterTypeId = clinicRegisterTypeId;
    }

    @AutoGenerated(locked = true)
    public void setDiscountCategory(String discountCategory) {
        this.__$validPropertySet.add("discountCategory");
        this.discountCategory = discountCategory;
    }

    @AutoGenerated(locked = true)
    public void setGender(GenderEnum gender) {
        this.__$validPropertySet.add("gender");
        this.gender = gender;
    }

    @AutoGenerated(locked = true)
    public void setHealthOfficialsFee(String healthOfficialsFee) {
        this.__$validPropertySet.add("healthOfficialsFee");
        this.healthOfficialsFee = healthOfficialsFee;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setIdentityCode(String identityCode) {
        this.__$validPropertySet.add("identityCode");
        this.identityCode = identityCode;
    }

    @AutoGenerated(locked = true)
    public void setIdentityType(String identityType) {
        this.__$validPropertySet.add("identityType");
        this.identityType = identityType;
    }

    @AutoGenerated(locked = true)
    public void setInsuranceNumber(String insuranceNumber) {
        this.__$validPropertySet.add("insuranceNumber");
        this.insuranceNumber = insuranceNumber;
    }

    @AutoGenerated(locked = true)
    public void setInsuranceType(String insuranceType) {
        this.__$validPropertySet.add("insuranceType");
        this.insuranceType = insuranceType;
    }

    @AutoGenerated(locked = true)
    public void setInternationalPatientFee(String internationalPatientFee) {
        this.__$validPropertySet.add("internationalPatientFee");
        this.internationalPatientFee = internationalPatientFee;
    }

    @AutoGenerated(locked = true)
    public void setMdtFeeType(String mdtFeeType) {
        this.__$validPropertySet.add("mdtFeeType");
        this.mdtFeeType = mdtFeeType;
    }

    @AutoGenerated(locked = true)
    public void setOperatorDepartmentId(String operatorDepartmentId) {
        this.__$validPropertySet.add("operatorDepartmentId");
        this.operatorDepartmentId = operatorDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setOperatorId(String operatorId) {
        this.__$validPropertySet.add("operatorId");
        this.operatorId = operatorId;
    }

    @AutoGenerated(locked = true)
    public void setOutpAppointId(String outpAppointId) {
        this.__$validPropertySet.add("outpAppointId");
        this.outpAppointId = outpAppointId;
    }

    @AutoGenerated(locked = true)
    public void setOutpatientRegistrationCategory(String outpatientRegistrationCategory) {
        this.__$validPropertySet.add("outpatientRegistrationCategory");
        this.outpatientRegistrationCategory = outpatientRegistrationCategory;
    }

    @AutoGenerated(locked = true)
    public void setPatientId(String patientId) {
        this.__$validPropertySet.add("patientId");
        this.patientId = patientId;
    }

    @AutoGenerated(locked = true)
    public void setPatientName(String patientName) {
        this.__$validPropertySet.add("patientName");
        this.patientName = patientName;
    }

    @AutoGenerated(locked = true)
    public void setRegisterDate(Date registerDate) {
        this.__$validPropertySet.add("registerDate");
        this.registerDate = registerDate;
    }

    @AutoGenerated(locked = true)
    public void setRegisterNumber(Long registerNumber) {
        this.__$validPropertySet.add("registerNumber");
        this.registerNumber = registerNumber;
    }

    @AutoGenerated(locked = true)
    public void setRegisterReviewStatus(RegisterReviewEnum registerReviewStatus) {
        this.__$validPropertySet.add("registerReviewStatus");
        this.registerReviewStatus = registerReviewStatus;
    }

    @AutoGenerated(locked = true)
    public void setRegisterStatus(RegisterStatusEnum registerStatus) {
        this.__$validPropertySet.add("registerStatus");
        this.registerStatus = registerStatus;
    }

    @AutoGenerated(locked = true)
    public void setRegistrationDepartmentId(String registrationDepartmentId) {
        this.__$validPropertySet.add("registrationDepartmentId");
        this.registrationDepartmentId = registrationDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setRegistrationDoctorId(String registrationDoctorId) {
        this.__$validPropertySet.add("registrationDoctorId");
        this.registrationDoctorId = registrationDoctorId;
    }

    @AutoGenerated(locked = true)
    public void setReturnDate(Date returnDate) {
        this.__$validPropertySet.add("returnDate");
        this.returnDate = returnDate;
    }

    @AutoGenerated(locked = true)
    public void setReturnOperatorId(String returnOperatorId) {
        this.__$validPropertySet.add("returnOperatorId");
        this.returnOperatorId = returnOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setReturnReason(String returnReason) {
        this.__$validPropertySet.add("returnReason");
        this.returnReason = returnReason;
    }

    @AutoGenerated(locked = true)
    public void setReturnSettleNumber(String returnSettleNumber) {
        this.__$validPropertySet.add("returnSettleNumber");
        this.returnSettleNumber = returnSettleNumber;
    }

    @AutoGenerated(locked = true)
    public void setReviewExplain(String reviewExplain) {
        this.__$validPropertySet.add("reviewExplain");
        this.reviewExplain = reviewExplain;
    }

    @AutoGenerated(locked = true)
    public void setReviewReason(String reviewReason) {
        this.__$validPropertySet.add("reviewReason");
        this.reviewReason = reviewReason;
    }

    @AutoGenerated(locked = true)
    public void setReviewTime(Date reviewTime) {
        this.__$validPropertySet.add("reviewTime");
        this.reviewTime = reviewTime;
    }

    @AutoGenerated(locked = true)
    public void setReviewerId(String reviewerId) {
        this.__$validPropertySet.add("reviewerId");
        this.reviewerId = reviewerId;
    }

    @AutoGenerated(locked = true)
    public void setSettleNumber(String settleNumber) {
        this.__$validPropertySet.add("settleNumber");
        this.settleNumber = settleNumber;
    }

    @AutoGenerated(locked = true)
    public void setSourceAppId(String sourceAppId) {
        this.__$validPropertySet.add("sourceAppId");
        this.sourceAppId = sourceAppId;
    }

    @AutoGenerated(locked = true)
    public void setSourceCampusId(String sourceCampusId) {
        this.__$validPropertySet.add("sourceCampusId");
        this.sourceCampusId = sourceCampusId;
    }

    @AutoGenerated(locked = true)
    public void setSpecifiedDiseaseCode(String specifiedDiseaseCode) {
        this.__$validPropertySet.add("specifiedDiseaseCode");
        this.specifiedDiseaseCode = specifiedDiseaseCode;
    }

    @AutoGenerated(locked = true)
    public void setSpecifiedDiseaseFlag(Boolean specifiedDiseaseFlag) {
        this.__$validPropertySet.add("specifiedDiseaseFlag");
        this.specifiedDiseaseFlag = specifiedDiseaseFlag;
    }

    @AutoGenerated(locked = true)
    public void setTakeAppointDate(Date takeAppointDate) {
        this.__$validPropertySet.add("takeAppointDate");
        this.takeAppointDate = takeAppointDate;
    }

    @AutoGenerated(locked = true)
    public void setTakeAppointOperatorId(String takeAppointOperatorId) {
        this.__$validPropertySet.add("takeAppointOperatorId");
        this.takeAppointOperatorId = takeAppointOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setTallyReceiptNumber(String tallyReceiptNumber) {
        this.__$validPropertySet.add("tallyReceiptNumber");
        this.tallyReceiptNumber = tallyReceiptNumber;
    }

    @AutoGenerated(locked = true)
    public void setTallyStatus(String tallyStatus) {
        this.__$validPropertySet.add("tallyStatus");
        this.tallyStatus = tallyStatus;
    }

    @AutoGenerated(locked = true)
    public void setTimeDescription(TimeDescriptionEnum timeDescription) {
        this.__$validPropertySet.add("timeDescription");
        this.timeDescription = timeDescription;
    }

    @AutoGenerated(locked = true)
    public void setTotalCharge(BigDecimal totalCharge) {
        this.__$validPropertySet.add("totalCharge");
        this.totalCharge = totalCharge;
    }

    @AutoGenerated(locked = true)
    public void setTotalCost(BigDecimal totalCost) {
        this.__$validPropertySet.add("totalCost");
        this.totalCost = totalCost;
    }

    @AutoGenerated(locked = true)
    public void setVisitCardId(String visitCardId) {
        this.__$validPropertySet.add("visitCardId");
        this.visitCardId = visitCardId;
    }

    @AutoGenerated(locked = true)
    public void setWaitingEndTime(TimeEo waitingEndTime) {
        this.__$validPropertySet.add("waitingEndTime");
        this.waitingEndTime = waitingEndTime;
    }

    @AutoGenerated(locked = true)
    public void setWaitingStartTime(TimeEo waitingStartTime) {
        this.__$validPropertySet.add("waitingStartTime");
        this.waitingStartTime = waitingStartTime;
    }
}
