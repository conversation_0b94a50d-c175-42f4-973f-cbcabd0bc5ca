package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterBaseDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.appointment_booking.service.converter.OutpRegisterBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "0ffd3ab4-3d96-455b-af1a-511e22420b0f|DTO|SERVICE")
public class OutpRegisterBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterBaseDtoManager outpRegisterBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterBaseDtoServiceConverter outpRegisterBaseDtoServiceConverter;

    @PublicInterface(
            id = "457f5456-0e0b-46f9-bd86-bf51ad8a8a58",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741538")
    @AutoGenerated(locked = false, uuid = "1563f737-7e88-3422-8a18-1d4236d00ce3")
    public List<OutpRegisterBaseDto> getByOutpAppointIds(
            @Valid @NotNull(message = "预约ID不能为空") List<String> outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        outpAppointId = new ArrayList<>(new HashSet<>(outpAppointId));
        List<OutpRegisterBaseDto> outpRegisterBaseDtoList =
                outpRegisterBaseDtoManager.getByOutpAppointIds(outpAppointId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterBaseDtoServiceConverter.OutpRegisterBaseDtoConverter(
                outpRegisterBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "71dff654-93d0-4af0-8af7-73945db50fc9",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741513")
    @AutoGenerated(locked = false, uuid = "5e5850b4-4bec-327e-a011-23e1bc9c4f1b")
    public OutpRegisterBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpRegisterBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "c6547bbe-19d8-43d1-8091-f43a46bd21c2",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741530")
    @AutoGenerated(locked = false, uuid = "5f4e4f22-0cfd-31da-aa69-a1172e2c48eb")
    public List<OutpRegisterBaseDto> getByOutpAppointId(
            @NotNull(message = "预约ID不能为空") String outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOutpAppointIds(Arrays.asList(outpAppointId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "3db0cabd-e2b5-4046-a19a-e2ab0d813ee6",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741522")
    @AutoGenerated(locked = false, uuid = "681b0bee-3166-3478-af65-6607b753749e")
    public List<OutpRegisterBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpRegisterBaseDto> outpRegisterBaseDtoList = outpRegisterBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterBaseDtoServiceConverter.OutpRegisterBaseDtoConverter(
                outpRegisterBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
