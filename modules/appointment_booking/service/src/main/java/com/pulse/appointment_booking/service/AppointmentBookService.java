package com.pulse.appointment_booking.service;

import com.pulse.appointment_booking.common.enums.AppointStatusEnum;
import com.pulse.appointment_booking.manager.dto.OutpAppointBaseDto;
import com.pulse.appointment_booking.manager.facade.appointment_schedule.SchedulingSlotDetailBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.appointment_schedule.base.AppointmentScheduleBaseDtoServiceInAppointmentBookingBaseRpcAdapter;
import com.pulse.appointment_booking.manager.facade.dictionary_basic.AttributeDefinitionBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.organization.DepartmentBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.parameter.base.ParameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter;
import com.pulse.appointment_booking.manager.facade.patient_information.PatientVisitDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.persist.qto.QueryAppointRecordQto;
import com.pulse.appointment_schedule.common.enums.SchedulingstatusEnum;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.appointment_schedule.manager.dto.SchedulingSlotDetailBaseDto;
import com.pulse.organization.common.enums.GenderLimitEnum;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.patient_information.manager.dto.PatientVisitDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/** Auto generated Service */
@Controller
@Validated
@AutoGenerated(locked = false, uuid = "93c262f6-aeda-35b5-979f-a5ae2aadb6ef")
public class AppointmentBookService {
    @Resource
    PatientVisitDtoServiceInAppointmentBookingRpcAdapter
            patientVisitDtoServiceInAppointmentBookingRpcAdapterl;

    @Resource
    DepartmentBaseDtoServiceInAppointmentBookingRpcAdapter
            departmentBaseDtoServiceInAppointmentBookingRpcAdapter;

    @Resource
    private ParameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter
            parameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter;

    @Resource
    private AppointmentScheduleBaseDtoServiceInAppointmentBookingBaseRpcAdapter
            appointmentScheduleBaseDtoServiceInAppointmentBookingBaseRpcAdapter;

    @Resource
    private SchedulingSlotDetailBaseDtoServiceInAppointmentBookingRpcAdapter
            schedulingSlotDetailBaseDtoServiceInAppointmentBookingRpcAdapter;

    @Resource private OutpAppointBaseDtoService outpAppointBaseDtoService;

    @Resource private OuptAppointService ouptAppointService;

    @Resource
    private AttributeDefinitionBaseDtoServiceInAppointmentBookingRpcAdapter
            attributeDefinitionBaseDtoServiceInAppointmentBookingRpcAdapter;

    @PublicInterface(
            id = "168ff8c6-3ad0-42ac-8cf3-0d2e2db3246c",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            version = "1748259011333",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "168ff8c6-3ad0-42ac-8cf3-0d2e2db3246c")
    /** 验证每天病人最多可以预约号数 */
    public Boolean validateLimitOutpAppoint(String identificationNumber, Date today) {
        // TODO implement method
        // 获取排班
        QueryAppointRecordQto queryAppointRecordQto = new QueryAppointRecordQto();
        queryAppointRecordQto.setFrom(0);
        queryAppointRecordQto.setSize(100);
        queryAppointRecordQto.setVisitDate(today);
        queryAppointRecordQto.setIdentificationClassId("");
        queryAppointRecordQto.setIdentificationNumber(identificationNumber);
        Long count =
                ouptAppointService.queryAppointmentRecords(queryAppointRecordQto).stream().count();

        // 获取参数
        long number = 2;
        ParameterConfigWithValueDto code =
                parameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter.getByCode(
                        "每个病人每天最多可预约号数");
        if (code != null) {
            number = Long.parseLong(code.getDefaultValue());
        }
        if (count >= number) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "您在今天已预约【" + count + "】个号源,不能继续预约!");
        }
        return true;
    }

    @PublicInterface(
            id = "3203830c-5531-4ccc-9141-b58b0e8ee85c",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            version = "1748260053000",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "3203830c-5531-4ccc-9141-b58b0e8ee85c")
    /** 验证排班是否休诊 */
    public Boolean validateIsScheduleClosed(String appointmentScheduleId) {
        // TODO implement method
        AppointmentScheduleBaseDto appointmentScheduleBaseDto =
                appointmentScheduleBaseDtoServiceInAppointmentBookingBaseRpcAdapter.getById(
                        appointmentScheduleId);
        long number = 0;
        ParameterConfigWithValueDto code =
                parameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter.getByCode(
                        "是否验证排班休诊");
        if (code != null) {
            number = Long.parseLong(code.getDefaultValue());
        }
        if (number > 0) {
            return true;
        }
        // 验证排班
        if (appointmentScheduleBaseDto == null) {
            throw new IgnoredException(
                    ErrorCode.SYS_ERROR, "当前排班ID【" + appointmentScheduleId + "】查询不到!");
        }
        if (SchedulingstatusEnum.CLOSED.equals(appointmentScheduleBaseDto.getStatus())) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "当前排班已经休诊!");
        }
        return true;
    }

    @PublicInterface(
            id = "3bca7d64-fa49-4949-bd08-fbaf43f56cca",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            version = "1748257739060",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "3bca7d64-fa49-4949-bd08-fbaf43f56cca")
    /** 验证每个排班病人最多可以预约号数 */
    public Boolean validateLimitApportion(String patientId, String appointmentScheduleId) {
        // TODO implement method
        if (patientId == null || patientId.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "病人ID不能为空!");
        }
        if (appointmentScheduleId == null || appointmentScheduleId.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "排班ID不能为空!");
        }
        // 获取预约列表
        List<OutpAppointBaseDto> outpAppointBaseDtoList =
                outpAppointBaseDtoService.getByPatientId(patientId);
        long count =
                outpAppointBaseDtoList.stream()
                        .filter(dto -> appointmentScheduleId.equals(dto.getAppointmentScheduleId()))
                        .count();
        // 获取参数
        long number = 2;
        ParameterConfigWithValueDto code =
                parameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter.getByCode(
                        "每个排班病人最多可以预约号数");
        if (code != null) {
            number = Long.parseLong(code.getDefaultValue());
        }

        if (count >= number) {
            throw new IgnoredException(
                    ErrorCode.SYS_ERROR,
                    "您在排班【" + appointmentScheduleId + "】" + "已预约【" + number + "】个号源,不能继续预约!");
        }

        return true;
    }

    @PublicInterface(
            id = "52cf7190-4ade-4d07-b6c4-239739abfa17",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            version = "1748310584702",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "52cf7190-4ade-4d07-b6c4-239739abfa17")
    /** 验证号源是否被占用 */
    public Boolean validateIsOccupied(String appointmentScheduleId, Long serialNumber) {
        // TODO implement method
        List<SchedulingSlotDetailBaseDto> schedulingSlotDetailBaseDtos =
                schedulingSlotDetailBaseDtoServiceInAppointmentBookingRpcAdapter
                        .getByAppointmentScheduleId(appointmentScheduleId);

        List<OutpAppointBaseDto> ouptAppointBaseDtoList =
                outpAppointBaseDtoService.getByAppointmentScheduleId(appointmentScheduleId);

        if (schedulingSlotDetailBaseDtos != null && !schedulingSlotDetailBaseDtos.isEmpty()) {
            for (SchedulingSlotDetailBaseDto detailBaseDto : schedulingSlotDetailBaseDtos) {
                if (serialNumber.equals(detailBaseDto.getScheduleSlotNumber())
                        && (detailBaseDto.getAppointmentStatus() != null
                                && !AppointStatusEnum.NOT_TAKEN.equals(
                                        detailBaseDto.getAppointmentStatus()))) {
                    throw new IgnoredException(
                            ErrorCode.SYS_ERROR,
                            "当前号源已【" + detailBaseDto.getAppointmentStatus() + "】!");
                }
            }
        }

        // 检查号序是否被占用
        if (ouptAppointBaseDtoList != null && !ouptAppointBaseDtoList.isEmpty()) {
            boolean isOccupied =
                    ouptAppointBaseDtoList.stream()
                            .anyMatch(
                                    dto ->
                                            serialNumber.equals(dto.getSerialNumber())
                                                    && (AppointStatusEnum.NOT_TAKEN.equals(
                                                                    dto.getAppointStatus())
                                                            || AppointStatusEnum.TAKEN.equals(
                                                                    dto.getAppointStatus())));

            if (isOccupied) {
                throw new IgnoredException(ErrorCode.SYS_ERROR, "该号序【" + serialNumber + "】已被占用!");
            }
        }
        return true;
    }

    @PublicInterface(
            id = "cbdd79de-b3d2-433b-83e3-6b9425b1c043",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            version = "1748313011558",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "cbdd79de-b3d2-433b-83e3-6b9425b1c043")
    /** 验证加号 */
    public Boolean validateAddNumber(String appointmentScheduleId, String platformId) {
        // TODO implement method
        // 获取预约列表
        List<OutpAppointBaseDto> ouptAppointBaseDtoList =
                outpAppointBaseDtoService.getByAppointmentScheduleId(appointmentScheduleId);

        if (ouptAppointBaseDtoList == null || ouptAppointBaseDtoList.isEmpty()) {
            return true; // 没有记录直接通过验证
        }

        // 获取参数
        long number = 2;
        ParameterConfigWithValueDto code =
                parameterConfigWithValueDtoServiceInAppointmentBookingBaseRpcAdapter.getByCode(
                        "省平台浙大钉独家放号数量");
        if (code != null) {
            number = Long.parseLong(code.getDefaultValue());
        }

        long count =
                ouptAppointBaseDtoList.stream()
                        .filter(dto -> platformId.equals(dto.getSourceId()))
                        .count();

        if (count >= number) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "省平台浙大钉独家放号数量不能超过" + number + "2次!");
        }
        return null;
    }

    @PublicInterface(
            id = "f7d1f406-93c5-455e-996f-92b24d74158d",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            version = "1748251061774",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "f7d1f406-93c5-455e-996f-92b24d74158d")
    /** 验证年龄性别限制 */
    public Boolean validateAgeGenderLimit(String patientId, String departmentId) {
        // TODO implement method
        if (patientId == null || patientId.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "病人ID不能为空!");
        }
        if (departmentId == null || departmentId.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "科室ID不能为空!");
        }
        PatientVisitDto patientVisitDto =
                patientVisitDtoServiceInAppointmentBookingRpcAdapterl.getById(patientId);
        if (patientVisitDto == null || patientVisitDto.getBirthday() == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "未找到病人出生日期信息!");
        }
        // 计算年龄
        long age =
                java.time.temporal.ChronoUnit.YEARS.between(
                        patientVisitDto
                                .getBirthday()
                                .toInstant()
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate(),
                        java.time.LocalDate.now());
        String gender = patientVisitDto.getGender();
        DepartmentBaseDto departmentBaseDto =
                departmentBaseDtoServiceInAppointmentBookingRpcAdapter.getByOrganizationId(
                        departmentId);
        if (departmentBaseDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "未找到科室信息!");
        }

        // 检查年龄限制
        Long ageUpperLimit = departmentBaseDto.getAgeUpperLimit(); // 上限
        Long ageLowerLimit = departmentBaseDto.getAgeLowerLimit(); // 下限
        if (ageLowerLimit != null && age < ageLowerLimit) {
            throw new IgnoredException(
                    ErrorCode.SYS_ERROR, "患者年龄小于科室最小年龄限制" + ageLowerLimit + "岁!");
        }
        if (ageUpperLimit != null && age > ageUpperLimit) {
            throw new IgnoredException(
                    ErrorCode.SYS_ERROR, "患者年龄大于科室最大年龄限制" + ageUpperLimit + "岁!");
        }

        // 检查性别限制
        GenderLimitEnum genderLimit = departmentBaseDto.getGenderLimit();
        if (genderLimit == null) {
            if (genderLimit == GenderLimitEnum.NO_LIMIT) {
                return true;
            } else {
                /*     AttributeDefinitionBaseDto attributeDefinitionBaseDto = attributeDefinitionBaseDtoServiceInAppointmentBookingRpcAdapter.getById(gender);
                String attributeCode = attributeDefinitionBaseDto.getAttributeCode();
                if (genderLimit == GenderLimitEnum.LIMIT_MALE&&"1".equals(attributeCode)) {
                    throw new IgnoredException(ErrorCode.SYS_ERROR, "患者性别与科室限制不符!");
                }
                if (genderLimit == GenderLimitEnum.LIMIT_FEMALE&&"0".equals(attributeCode)) {
                    throw new IgnoredException(ErrorCode.SYS_ERROR, "患者性别与科室限制不符!");
                }*/
            }
        }

        return true;
    }
}
