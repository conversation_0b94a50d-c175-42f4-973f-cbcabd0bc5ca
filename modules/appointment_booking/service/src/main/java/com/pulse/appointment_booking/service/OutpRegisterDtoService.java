package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterDto;
import com.pulse.appointment_booking.service.converter.OutpRegisterDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "b438e52c-5ce1-4af0-8f3f-f95ff5a6f1a2|DTO|SERVICE")
public class OutpRegisterDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterDtoManager outpRegisterDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterDtoServiceConverter outpRegisterDtoServiceConverter;

    @PublicInterface(
            id = "bf928b0a-55cb-489e-ac62-e36d5c30335c",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741579")
    @AutoGenerated(locked = false, uuid = "1563f737-7e88-3422-8a18-1d4236d00ce3")
    public List<OutpRegisterDto> getByOutpAppointIds(
            @Valid @NotNull(message = "预约ID不能为空") List<String> outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        outpAppointId = new ArrayList<>(new HashSet<>(outpAppointId));
        List<OutpRegisterDto> outpRegisterDtoList =
                outpRegisterDtoManager.getByOutpAppointIds(outpAppointId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterDtoServiceConverter.OutpRegisterDtoConverter(outpRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "462e4106-03e6-4155-b717-eba693b75789",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741549")
    @AutoGenerated(locked = false, uuid = "5e5850b4-4bec-327e-a011-23e1bc9c4f1b")
    public OutpRegisterDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpRegisterDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "372baa3b-b68b-453e-9353-f6fca98b3ca4",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741572")
    @AutoGenerated(locked = false, uuid = "5f4e4f22-0cfd-31da-aa69-a1172e2c48eb")
    public List<OutpRegisterDto> getByOutpAppointId(
            @NotNull(message = "预约ID不能为空") String outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOutpAppointIds(Arrays.asList(outpAppointId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "79a904ca-81d7-4944-8483-c8410f8e7e07",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741564")
    @AutoGenerated(locked = false, uuid = "681b0bee-3166-3478-af65-6607b753749e")
    public List<OutpRegisterDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpRegisterDto> outpRegisterDtoList = outpRegisterDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterDtoServiceConverter.OutpRegisterDtoConverter(outpRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
