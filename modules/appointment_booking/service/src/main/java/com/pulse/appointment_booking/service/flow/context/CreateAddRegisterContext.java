package com.pulse.appointment_booking.service.flow.context;

import com.pulse.appointment_booking.service.bto.CreateOuptAppointBto;
import com.pulse.appointment_booking.service.bto.CreateOutpRegisterBto;
import com.pulse.visit.service.bto.CreateOutpVisitBto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

/** 流程：createAddRegister的上下文，用于传递入参和出参 */
@Getter
@Setter
@AutoGenerated(locked = false, uuid = "a1ef4fb9-34e4-48a1-b337-3df0a61945b0|FLOW|CONTEXT")
public class CreateAddRegisterContext extends AppointmentBookingContext {
    private CreateOuptAppointBto createOuptAppointBto = new CreateOuptAppointBto();
    private CreateOutpRegisterBto createOutpRegisterBto = new CreateOutpRegisterBto();
    private CreateOutpVisitBto createOutpVisitBto = new CreateOutpVisitBto();
    private String ouptAppointID;
    private String outpRegisterID;
    private String outpvisitID;
    private String slotDetailID;
}
