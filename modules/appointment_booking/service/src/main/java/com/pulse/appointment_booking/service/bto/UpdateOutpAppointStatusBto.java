package com.pulse.appointment_booking.service.bto;

import com.pulse.appointment_booking.common.enums.AppointStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;

/**
 * <b>[源自]</b> OutpAppoint
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "9329de54-81e5-4874-bbee-ae88c9ecb749|BTO|DEFINITION")
public class UpdateOutpAppointStatusBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 预约状态 */
    @AutoGenerated(locked = true, uuid = "dd4d93ac-e9fe-41cb-bbc9-e8d224ea638b")
    private AppointStatusEnum appointStatus;

    /** 取消日期 */
    @AutoGenerated(locked = true, uuid = "3e37466f-8196-4d14-96ff-ffd8f5f5d082")
    private Date cancelDate;

    /** 取消原因 */
    @AutoGenerated(locked = true, uuid = "ad8b348e-93a4-4274-85ca-3ef75a073ab2")
    private String cancelReason;

    /** 取消预约类型 */
    @AutoGenerated(locked = true, uuid = "5759a807-8bf8-4a7a-a99a-c07b714a02e1")
    private String cancelType;

    /** 是否收费 */
    @AutoGenerated(locked = true, uuid = "f40f08df-23a3-460c-a5b5-e62d71d9b83a")
    private Boolean chargeIs;

    /** 科室ID */
    @AutoGenerated(locked = true, uuid = "bfdf2908-276d-4818-8bfa-7ef9ce5f501c")
    private String departmentId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "07e1be39-3c35-4e5a-bd40-7a9f5ca01d5a")
    private String id;

    /** 患者名称 未建档患者使用 */
    @AutoGenerated(locked = true, uuid = "aeb0f793-53d3-4c67-b103-fcbd986372fb")
    private String patientName;

    /** 代理挂号标志 */
    @AutoGenerated(locked = true, uuid = "fa1065ef-1c9a-4204-a46a-a8ccfb38f5a6")
    private Boolean proxyRegistrationFlag;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "1d01dfff-195c-4bac-b21c-1159573f281a")
    private String updatedBy;

    /** 就诊卡ID */
    @AutoGenerated(locked = true, uuid = "42b790c1-64a6-460f-9c58-120fa70ba47d")
    private String visitCardId;

    @AutoGenerated(locked = true)
    public void setAppointStatus(AppointStatusEnum appointStatus) {
        this.__$validPropertySet.add("appointStatus");
        this.appointStatus = appointStatus;
    }

    @AutoGenerated(locked = true)
    public void setCancelDate(Date cancelDate) {
        this.__$validPropertySet.add("cancelDate");
        this.cancelDate = cancelDate;
    }

    @AutoGenerated(locked = true)
    public void setCancelReason(String cancelReason) {
        this.__$validPropertySet.add("cancelReason");
        this.cancelReason = cancelReason;
    }

    @AutoGenerated(locked = true)
    public void setCancelType(String cancelType) {
        this.__$validPropertySet.add("cancelType");
        this.cancelType = cancelType;
    }

    @AutoGenerated(locked = true)
    public void setChargeIs(Boolean chargeIs) {
        this.__$validPropertySet.add("chargeIs");
        this.chargeIs = chargeIs;
    }

    @AutoGenerated(locked = true)
    public void setDepartmentId(String departmentId) {
        this.__$validPropertySet.add("departmentId");
        this.departmentId = departmentId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setPatientName(String patientName) {
        this.__$validPropertySet.add("patientName");
        this.patientName = patientName;
    }

    @AutoGenerated(locked = true)
    public void setProxyRegistrationFlag(Boolean proxyRegistrationFlag) {
        this.__$validPropertySet.add("proxyRegistrationFlag");
        this.proxyRegistrationFlag = proxyRegistrationFlag;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    @AutoGenerated(locked = true)
    public void setVisitCardId(String visitCardId) {
        this.__$validPropertySet.add("visitCardId");
        this.visitCardId = visitCardId;
    }
}
