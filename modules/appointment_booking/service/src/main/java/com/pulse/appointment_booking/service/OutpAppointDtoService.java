package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpAppointDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpAppointDto;
import com.pulse.appointment_booking.service.converter.OutpAppointDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "8e9a3c34-3acc-47d2-9476-8386a15106e3|DTO|SERVICE")
public class OutpAppointDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointDtoManager outpAppointDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointDtoServiceConverter outpAppointDtoServiceConverter;

    @PublicInterface(id = "dc72c89a-a872-4f2e-bbf7-72dc8099dd86", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "106db4f3-11bb-374e-a11b-cd2d64a75a3f")
    public List<OutpAppointDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpAppointDto> outpAppointDtoList = outpAppointDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointDtoServiceConverter.OutpAppointDtoConverter(outpAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "f588f31c-ad2d-409a-956b-88a79362d5c1",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992983")
    @AutoGenerated(locked = false, uuid = "33cf4676-0e9b-3d26-9335-4c66da02b7d9")
    public List<OutpAppointDto> getByAppointmentScheduleId(
            @NotNull(message = "排班ID不能为空") String appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAppointmentScheduleIds(Arrays.asList(appointmentScheduleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "78ded3d2-63e5-4cd1-bb72-0c7a48e882f0", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "4686ab67-fac2-35a9-bf5e-64786b5a1147")
    public List<OutpAppointDto> getByAppointmentScheduleIds(
            @Valid @NotNull(message = "排班ID不能为空") List<String> appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        appointmentScheduleId = new ArrayList<>(new HashSet<>(appointmentScheduleId));
        List<OutpAppointDto> outpAppointDtoList =
                outpAppointDtoManager.getByAppointmentScheduleIds(appointmentScheduleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointDtoServiceConverter.OutpAppointDtoConverter(outpAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "936689d6-dd15-4e33-8bdb-3767e4d0dfd3", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "9b4864fd-336e-30a5-b67b-8b3f46674da9")
    public List<OutpAppointDto> getByPatientIds(
            @Valid @NotNull(message = "患者ID不能为空") List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        patientId = new ArrayList<>(new HashSet<>(patientId));
        List<OutpAppointDto> outpAppointDtoList = outpAppointDtoManager.getByPatientIds(patientId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointDtoServiceConverter.OutpAppointDtoConverter(outpAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f7290a92-f128-46e5-8f56-8a8d89083984", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "9ddd6a7d-9d53-3058-b8b2-96c1ec90d7f2")
    public List<OutpAppointDto> getByDepartmentIds(
            @Valid @NotNull(message = "科室ID不能为空") List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        departmentId = new ArrayList<>(new HashSet<>(departmentId));
        List<OutpAppointDto> outpAppointDtoList =
                outpAppointDtoManager.getByDepartmentIds(departmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointDtoServiceConverter.OutpAppointDtoConverter(outpAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0ff72b39-5aae-41e4-b793-381554f36185", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "b34e72bf-63c3-3428-8278-05e988c205cb")
    public List<OutpAppointDto> getByDepartmentId(
            @NotNull(message = "科室ID不能为空") String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDepartmentIds(Arrays.asList(departmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "56eca212-f890-47d8-b3cf-8107f2775de1", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "b8ff8b85-e6c0-3cef-928c-b63104535523")
    public OutpAppointDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "4bc6f612-e886-44b5-8e64-00815350adcf", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "bf20a759-c7f2-3426-9f58-6e6a19270060")
    public List<OutpAppointDto> getByPatientId(@NotNull(message = "患者ID不能为空") String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPatientIds(Arrays.asList(patientId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "a2129fb8-a180-4487-8d30-4701506f32db",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992975")
    @AutoGenerated(locked = false, uuid = "dcdfa95e-ea13-34bf-b0ca-de804e428502")
    public List<OutpAppointDto> getByClinicRegisterTypeIds(
            @Valid @NotNull(message = "挂号类别ID不能为空") List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        clinicRegisterTypeId = new ArrayList<>(new HashSet<>(clinicRegisterTypeId));
        List<OutpAppointDto> outpAppointDtoList =
                outpAppointDtoManager.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointDtoServiceConverter.OutpAppointDtoConverter(outpAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "27da1674-a3d9-4164-b51c-c08513fed6c8", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "f557a9b3-6b6d-3463-b32a-8ad43563f4ec")
    public List<OutpAppointDto> getByClinicRegisterTypeId(
            @NotNull(message = "挂号类别ID不能为空") String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
