package com.pulse.appointment_booking.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.appointment_booking.manager.bo.*;
import com.pulse.appointment_booking.manager.bo.OutpRegisterBO;
import com.pulse.appointment_booking.persist.dos.OutpRegister;
import com.pulse.appointment_booking.service.base.BaseOutpRegisterBOService.CreateOutpRegisterBoResult;
import com.pulse.appointment_booking.service.base.BaseOutpRegisterBOService.MergeOutpRegisterBoResult;
import com.pulse.appointment_booking.service.base.BaseOutpRegisterBOService.UpdateReviewBoResult;
import com.pulse.appointment_booking.service.bto.CreateOutpRegisterBto;
import com.pulse.appointment_booking.service.bto.MergeOutpRegisterBto;
import com.pulse.appointment_booking.service.bto.UpdateReviewBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "9aeda50d-9e3b-3589-96a4-6bea1f576ea4")
public class BaseOutpRegisterBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private OutpRegisterBO createCreateOutpRegisterOnDuplicateThrowEx(
            BaseOutpRegisterBOService.CreateOutpRegisterBoResult boResult,
            CreateOutpRegisterBto createOutpRegisterBto) {
        OutpRegisterBO outpRegisterBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createOutpRegisterBto.getId() == null);
        if (!allNull && !found) {
            outpRegisterBO = OutpRegisterBO.getById(createOutpRegisterBto.getId());
            if (outpRegisterBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (outpRegisterBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", outpRegisterBO.getId(), "outp_register");
                throw new IgnoredException(400, "挂号已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "outp_register",
                        outpRegisterBO.getId(),
                        "outp_register");
                throw new IgnoredException(400, "挂号已存在");
            }
        } else {
            outpRegisterBO = new OutpRegisterBO();
            if (pkExist) {
                outpRegisterBO.setId(createOutpRegisterBto.getId());
            } else {
                outpRegisterBO.setId(String.valueOf(this.idGenerator.allocateId("outp_register")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "outpAppointId")) {
                outpRegisterBO.setOutpAppointId(createOutpRegisterBto.getOutpAppointId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "patientId")) {
                outpRegisterBO.setPatientId(createOutpRegisterBto.getPatientId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "visitCardId")) {
                outpRegisterBO.setVisitCardId(createOutpRegisterBto.getVisitCardId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "clinicRegisterTypeId")) {
                outpRegisterBO.setClinicRegisterTypeId(
                        createOutpRegisterBto.getClinicRegisterTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "mdtFeeType")) {
                outpRegisterBO.setMdtFeeType(createOutpRegisterBto.getMdtFeeType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "sourceAppId")) {
                outpRegisterBO.setSourceAppId(createOutpRegisterBto.getSourceAppId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "sourceCampusId")) {
                outpRegisterBO.setSourceCampusId(createOutpRegisterBto.getSourceCampusId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "tallyStatus")) {
                outpRegisterBO.setTallyStatus(createOutpRegisterBto.getTallyStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "registerNumber")) {
                outpRegisterBO.setRegisterNumber(createOutpRegisterBto.getRegisterNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "patientName")) {
                outpRegisterBO.setPatientName(createOutpRegisterBto.getPatientName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "timeDescription")) {
                outpRegisterBO.setTimeDescription(createOutpRegisterBto.getTimeDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "age")) {
                outpRegisterBO.setAge(createOutpRegisterBto.getAge());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "gender")) {
                outpRegisterBO.setGender(createOutpRegisterBto.getGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "identityType")) {
                outpRegisterBO.setIdentityType(createOutpRegisterBto.getIdentityType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "insuranceType")) {
                outpRegisterBO.setInsuranceType(createOutpRegisterBto.getInsuranceType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "insuranceNumber")) {
                outpRegisterBO.setInsuranceNumber(createOutpRegisterBto.getInsuranceNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "registerStatus")) {
                outpRegisterBO.setRegisterStatus(createOutpRegisterBto.getRegisterStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "operatorId")) {
                outpRegisterBO.setOperatorId(createOutpRegisterBto.getOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "registerDate")) {
                outpRegisterBO.setRegisterDate(createOutpRegisterBto.getRegisterDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "settleNumber")) {
                outpRegisterBO.setSettleNumber(createOutpRegisterBto.getSettleNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "cancellationInitiatorId")) {
                outpRegisterBO.setCancellationInitiatorId(
                        createOutpRegisterBto.getCancellationInitiatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "cancellationInitiationTime")) {
                outpRegisterBO.setCancellationInitiationTime(
                        createOutpRegisterBto.getCancellationInitiationTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "reviewerId")) {
                outpRegisterBO.setReviewerId(createOutpRegisterBto.getReviewerId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "reviewTime")) {
                outpRegisterBO.setReviewTime(createOutpRegisterBto.getReviewTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "totalCost")) {
                outpRegisterBO.setTotalCost(createOutpRegisterBto.getTotalCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "totalCharge")) {
                outpRegisterBO.setTotalCharge(createOutpRegisterBto.getTotalCharge());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "returnOperatorId")) {
                outpRegisterBO.setReturnOperatorId(createOutpRegisterBto.getReturnOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "returnDate")) {
                outpRegisterBO.setReturnDate(createOutpRegisterBto.getReturnDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "returnSettleNumber")) {
                outpRegisterBO.setReturnSettleNumber(createOutpRegisterBto.getReturnSettleNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "returnReason")) {
                outpRegisterBO.setReturnReason(createOutpRegisterBto.getReturnReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "chargePriceScheduleId")) {
                outpRegisterBO.setChargePriceScheduleId(
                        createOutpRegisterBto.getChargePriceScheduleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "discountCategory")) {
                outpRegisterBO.setDiscountCategory(createOutpRegisterBto.getDiscountCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "tallyReceiptNumber")) {
                outpRegisterBO.setTallyReceiptNumber(createOutpRegisterBto.getTallyReceiptNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "chargeTypeCode")) {
                outpRegisterBO.setChargeTypeCode(createOutpRegisterBto.getChargeTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "identityCode")) {
                outpRegisterBO.setIdentityCode(createOutpRegisterBto.getIdentityCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "takeAppointOperatorId")) {
                outpRegisterBO.setTakeAppointOperatorId(
                        createOutpRegisterBto.getTakeAppointOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "takeAppointDate")) {
                outpRegisterBO.setTakeAppointDate(createOutpRegisterBto.getTakeAppointDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "registrationDoctorId")) {
                outpRegisterBO.setRegistrationDoctorId(
                        createOutpRegisterBto.getRegistrationDoctorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "registrationDepartmentId")) {
                outpRegisterBO.setRegistrationDepartmentId(
                        createOutpRegisterBto.getRegistrationDepartmentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "outpatientRegistrationCategory")) {
                outpRegisterBO.setOutpatientRegistrationCategory(
                        createOutpRegisterBto.getOutpatientRegistrationCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "specifiedDiseaseFlag")) {
                outpRegisterBO.setSpecifiedDiseaseFlag(
                        createOutpRegisterBto.getSpecifiedDiseaseFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "specifiedDiseaseCode")) {
                outpRegisterBO.setSpecifiedDiseaseCode(
                        createOutpRegisterBto.getSpecifiedDiseaseCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "waitingStartTime")) {
                outpRegisterBO.setWaitingStartTime(createOutpRegisterBto.getWaitingStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "waitingEndTime")) {
                outpRegisterBO.setWaitingEndTime(createOutpRegisterBto.getWaitingEndTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "registerReviewStatus")) {
                outpRegisterBO.setRegisterReviewStatus(
                        createOutpRegisterBto.getRegisterReviewStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "reviewReason")) {
                outpRegisterBO.setReviewReason(createOutpRegisterBto.getReviewReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "reviewExplain")) {
                outpRegisterBO.setReviewExplain(createOutpRegisterBto.getReviewExplain());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "chargeStatus")) {
                outpRegisterBO.setChargeStatus(createOutpRegisterBto.getChargeStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "healthOfficialsFee")) {
                outpRegisterBO.setHealthOfficialsFee(createOutpRegisterBto.getHealthOfficialsFee());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "internationalPatientFee")) {
                outpRegisterBO.setInternationalPatientFee(
                        createOutpRegisterBto.getInternationalPatientFee());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createOutpRegisterBto, "__$validPropertySet"),
                    "operatorDepartmentId")) {
                outpRegisterBO.setOperatorDepartmentId(
                        createOutpRegisterBto.getOperatorDepartmentId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createOutpRegisterBto);
            addedBto.setBo(outpRegisterBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return outpRegisterBO;
    }

    /** 数据库创建一行 */
    @AutoGenerated(locked = true)
    private OutpRegisterBO createMergeOutpRegisterOnDuplicateUpdate(
            BaseOutpRegisterBOService.MergeOutpRegisterBoResult boResult,
            MergeOutpRegisterBto mergeOutpRegisterBto) {
        OutpRegisterBO outpRegisterBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeOutpRegisterBto.getId() == null);
        if (!allNull && !found) {
            outpRegisterBO = OutpRegisterBO.getById(mergeOutpRegisterBto.getId());
            if (outpRegisterBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (outpRegisterBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(outpRegisterBO.convertToOutpRegister());
                updatedBto.setBto(mergeOutpRegisterBto);
                updatedBto.setBo(outpRegisterBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "outpAppointId")) {
                    outpRegisterBO.setOutpAppointId(mergeOutpRegisterBto.getOutpAppointId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "patientId")) {
                    outpRegisterBO.setPatientId(mergeOutpRegisterBto.getPatientId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "visitCardId")) {
                    outpRegisterBO.setVisitCardId(mergeOutpRegisterBto.getVisitCardId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "clinicRegisterTypeId")) {
                    outpRegisterBO.setClinicRegisterTypeId(
                            mergeOutpRegisterBto.getClinicRegisterTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "mdtFeeType")) {
                    outpRegisterBO.setMdtFeeType(mergeOutpRegisterBto.getMdtFeeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "sourceAppId")) {
                    outpRegisterBO.setSourceAppId(mergeOutpRegisterBto.getSourceAppId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "sourceCampusId")) {
                    outpRegisterBO.setSourceCampusId(mergeOutpRegisterBto.getSourceCampusId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "tallyStatus")) {
                    outpRegisterBO.setTallyStatus(mergeOutpRegisterBto.getTallyStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerNumber")) {
                    outpRegisterBO.setRegisterNumber(mergeOutpRegisterBto.getRegisterNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "patientName")) {
                    outpRegisterBO.setPatientName(mergeOutpRegisterBto.getPatientName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "timeDescription")) {
                    outpRegisterBO.setTimeDescription(mergeOutpRegisterBto.getTimeDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "age")) {
                    outpRegisterBO.setAge(mergeOutpRegisterBto.getAge());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "gender")) {
                    outpRegisterBO.setGender(mergeOutpRegisterBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "identityType")) {
                    outpRegisterBO.setIdentityType(mergeOutpRegisterBto.getIdentityType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "insuranceType")) {
                    outpRegisterBO.setInsuranceType(mergeOutpRegisterBto.getInsuranceType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "insuranceNumber")) {
                    outpRegisterBO.setInsuranceNumber(mergeOutpRegisterBto.getInsuranceNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerStatus")) {
                    outpRegisterBO.setRegisterStatus(mergeOutpRegisterBto.getRegisterStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "operatorId")) {
                    outpRegisterBO.setOperatorId(mergeOutpRegisterBto.getOperatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerDate")) {
                    outpRegisterBO.setRegisterDate(mergeOutpRegisterBto.getRegisterDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "settleNumber")) {
                    outpRegisterBO.setSettleNumber(mergeOutpRegisterBto.getSettleNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "cancellationInitiatorId")) {
                    outpRegisterBO.setCancellationInitiatorId(
                            mergeOutpRegisterBto.getCancellationInitiatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "cancellationInitiationTime")) {
                    outpRegisterBO.setCancellationInitiationTime(
                            mergeOutpRegisterBto.getCancellationInitiationTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewerId")) {
                    outpRegisterBO.setReviewerId(mergeOutpRegisterBto.getReviewerId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewTime")) {
                    outpRegisterBO.setReviewTime(mergeOutpRegisterBto.getReviewTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "totalCost")) {
                    outpRegisterBO.setTotalCost(mergeOutpRegisterBto.getTotalCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "totalCharge")) {
                    outpRegisterBO.setTotalCharge(mergeOutpRegisterBto.getTotalCharge());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnOperatorId")) {
                    outpRegisterBO.setReturnOperatorId(mergeOutpRegisterBto.getReturnOperatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnDate")) {
                    outpRegisterBO.setReturnDate(mergeOutpRegisterBto.getReturnDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnSettleNumber")) {
                    outpRegisterBO.setReturnSettleNumber(
                            mergeOutpRegisterBto.getReturnSettleNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnReason")) {
                    outpRegisterBO.setReturnReason(mergeOutpRegisterBto.getReturnReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "chargePriceScheduleId")) {
                    outpRegisterBO.setChargePriceScheduleId(
                            mergeOutpRegisterBto.getChargePriceScheduleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "discountCategory")) {
                    outpRegisterBO.setDiscountCategory(mergeOutpRegisterBto.getDiscountCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "tallyReceiptNumber")) {
                    outpRegisterBO.setTallyReceiptNumber(
                            mergeOutpRegisterBto.getTallyReceiptNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "chargeTypeCode")) {
                    outpRegisterBO.setChargeTypeCode(mergeOutpRegisterBto.getChargeTypeCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "identityCode")) {
                    outpRegisterBO.setIdentityCode(mergeOutpRegisterBto.getIdentityCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "takeAppointOperatorId")) {
                    outpRegisterBO.setTakeAppointOperatorId(
                            mergeOutpRegisterBto.getTakeAppointOperatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "takeAppointDate")) {
                    outpRegisterBO.setTakeAppointDate(mergeOutpRegisterBto.getTakeAppointDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registrationDoctorId")) {
                    outpRegisterBO.setRegistrationDoctorId(
                            mergeOutpRegisterBto.getRegistrationDoctorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registrationDepartmentId")) {
                    outpRegisterBO.setRegistrationDepartmentId(
                            mergeOutpRegisterBto.getRegistrationDepartmentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "outpatientRegistrationCategory")) {
                    outpRegisterBO.setOutpatientRegistrationCategory(
                            mergeOutpRegisterBto.getOutpatientRegistrationCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "specifiedDiseaseFlag")) {
                    outpRegisterBO.setSpecifiedDiseaseFlag(
                            mergeOutpRegisterBto.getSpecifiedDiseaseFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "specifiedDiseaseCode")) {
                    outpRegisterBO.setSpecifiedDiseaseCode(
                            mergeOutpRegisterBto.getSpecifiedDiseaseCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "waitingStartTime")) {
                    outpRegisterBO.setWaitingStartTime(mergeOutpRegisterBto.getWaitingStartTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "waitingEndTime")) {
                    outpRegisterBO.setWaitingEndTime(mergeOutpRegisterBto.getWaitingEndTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerReviewStatus")) {
                    outpRegisterBO.setRegisterReviewStatus(
                            mergeOutpRegisterBto.getRegisterReviewStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewReason")) {
                    outpRegisterBO.setReviewReason(mergeOutpRegisterBto.getReviewReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewExplain")) {
                    outpRegisterBO.setReviewExplain(mergeOutpRegisterBto.getReviewExplain());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "chargeStatus")) {
                    outpRegisterBO.setChargeStatus(mergeOutpRegisterBto.getChargeStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "healthOfficialsFee")) {
                    outpRegisterBO.setHealthOfficialsFee(
                            mergeOutpRegisterBto.getHealthOfficialsFee());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "internationalPatientFee")) {
                    outpRegisterBO.setInternationalPatientFee(
                            mergeOutpRegisterBto.getInternationalPatientFee());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "operatorDepartmentId")) {
                    outpRegisterBO.setOperatorDepartmentId(
                            mergeOutpRegisterBto.getOperatorDepartmentId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(outpRegisterBO.convertToOutpRegister());
                updatedBto.setBto(mergeOutpRegisterBto);
                updatedBto.setBo(outpRegisterBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "outpAppointId")) {
                    outpRegisterBO.setOutpAppointId(mergeOutpRegisterBto.getOutpAppointId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "patientId")) {
                    outpRegisterBO.setPatientId(mergeOutpRegisterBto.getPatientId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "visitCardId")) {
                    outpRegisterBO.setVisitCardId(mergeOutpRegisterBto.getVisitCardId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "clinicRegisterTypeId")) {
                    outpRegisterBO.setClinicRegisterTypeId(
                            mergeOutpRegisterBto.getClinicRegisterTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "mdtFeeType")) {
                    outpRegisterBO.setMdtFeeType(mergeOutpRegisterBto.getMdtFeeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "sourceAppId")) {
                    outpRegisterBO.setSourceAppId(mergeOutpRegisterBto.getSourceAppId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "sourceCampusId")) {
                    outpRegisterBO.setSourceCampusId(mergeOutpRegisterBto.getSourceCampusId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "tallyStatus")) {
                    outpRegisterBO.setTallyStatus(mergeOutpRegisterBto.getTallyStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerNumber")) {
                    outpRegisterBO.setRegisterNumber(mergeOutpRegisterBto.getRegisterNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "patientName")) {
                    outpRegisterBO.setPatientName(mergeOutpRegisterBto.getPatientName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "timeDescription")) {
                    outpRegisterBO.setTimeDescription(mergeOutpRegisterBto.getTimeDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "age")) {
                    outpRegisterBO.setAge(mergeOutpRegisterBto.getAge());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "gender")) {
                    outpRegisterBO.setGender(mergeOutpRegisterBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "identityType")) {
                    outpRegisterBO.setIdentityType(mergeOutpRegisterBto.getIdentityType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "insuranceType")) {
                    outpRegisterBO.setInsuranceType(mergeOutpRegisterBto.getInsuranceType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "insuranceNumber")) {
                    outpRegisterBO.setInsuranceNumber(mergeOutpRegisterBto.getInsuranceNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerStatus")) {
                    outpRegisterBO.setRegisterStatus(mergeOutpRegisterBto.getRegisterStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "operatorId")) {
                    outpRegisterBO.setOperatorId(mergeOutpRegisterBto.getOperatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerDate")) {
                    outpRegisterBO.setRegisterDate(mergeOutpRegisterBto.getRegisterDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "settleNumber")) {
                    outpRegisterBO.setSettleNumber(mergeOutpRegisterBto.getSettleNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "cancellationInitiatorId")) {
                    outpRegisterBO.setCancellationInitiatorId(
                            mergeOutpRegisterBto.getCancellationInitiatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "cancellationInitiationTime")) {
                    outpRegisterBO.setCancellationInitiationTime(
                            mergeOutpRegisterBto.getCancellationInitiationTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewerId")) {
                    outpRegisterBO.setReviewerId(mergeOutpRegisterBto.getReviewerId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewTime")) {
                    outpRegisterBO.setReviewTime(mergeOutpRegisterBto.getReviewTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "totalCost")) {
                    outpRegisterBO.setTotalCost(mergeOutpRegisterBto.getTotalCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "totalCharge")) {
                    outpRegisterBO.setTotalCharge(mergeOutpRegisterBto.getTotalCharge());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnOperatorId")) {
                    outpRegisterBO.setReturnOperatorId(mergeOutpRegisterBto.getReturnOperatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnDate")) {
                    outpRegisterBO.setReturnDate(mergeOutpRegisterBto.getReturnDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnSettleNumber")) {
                    outpRegisterBO.setReturnSettleNumber(
                            mergeOutpRegisterBto.getReturnSettleNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "returnReason")) {
                    outpRegisterBO.setReturnReason(mergeOutpRegisterBto.getReturnReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "chargePriceScheduleId")) {
                    outpRegisterBO.setChargePriceScheduleId(
                            mergeOutpRegisterBto.getChargePriceScheduleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "discountCategory")) {
                    outpRegisterBO.setDiscountCategory(mergeOutpRegisterBto.getDiscountCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "tallyReceiptNumber")) {
                    outpRegisterBO.setTallyReceiptNumber(
                            mergeOutpRegisterBto.getTallyReceiptNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "chargeTypeCode")) {
                    outpRegisterBO.setChargeTypeCode(mergeOutpRegisterBto.getChargeTypeCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "identityCode")) {
                    outpRegisterBO.setIdentityCode(mergeOutpRegisterBto.getIdentityCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "takeAppointOperatorId")) {
                    outpRegisterBO.setTakeAppointOperatorId(
                            mergeOutpRegisterBto.getTakeAppointOperatorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "takeAppointDate")) {
                    outpRegisterBO.setTakeAppointDate(mergeOutpRegisterBto.getTakeAppointDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registrationDoctorId")) {
                    outpRegisterBO.setRegistrationDoctorId(
                            mergeOutpRegisterBto.getRegistrationDoctorId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registrationDepartmentId")) {
                    outpRegisterBO.setRegistrationDepartmentId(
                            mergeOutpRegisterBto.getRegistrationDepartmentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "outpatientRegistrationCategory")) {
                    outpRegisterBO.setOutpatientRegistrationCategory(
                            mergeOutpRegisterBto.getOutpatientRegistrationCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "specifiedDiseaseFlag")) {
                    outpRegisterBO.setSpecifiedDiseaseFlag(
                            mergeOutpRegisterBto.getSpecifiedDiseaseFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "specifiedDiseaseCode")) {
                    outpRegisterBO.setSpecifiedDiseaseCode(
                            mergeOutpRegisterBto.getSpecifiedDiseaseCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "waitingStartTime")) {
                    outpRegisterBO.setWaitingStartTime(mergeOutpRegisterBto.getWaitingStartTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "waitingEndTime")) {
                    outpRegisterBO.setWaitingEndTime(mergeOutpRegisterBto.getWaitingEndTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "registerReviewStatus")) {
                    outpRegisterBO.setRegisterReviewStatus(
                            mergeOutpRegisterBto.getRegisterReviewStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewReason")) {
                    outpRegisterBO.setReviewReason(mergeOutpRegisterBto.getReviewReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "reviewExplain")) {
                    outpRegisterBO.setReviewExplain(mergeOutpRegisterBto.getReviewExplain());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "chargeStatus")) {
                    outpRegisterBO.setChargeStatus(mergeOutpRegisterBto.getChargeStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "healthOfficialsFee")) {
                    outpRegisterBO.setHealthOfficialsFee(
                            mergeOutpRegisterBto.getHealthOfficialsFee());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "internationalPatientFee")) {
                    outpRegisterBO.setInternationalPatientFee(
                            mergeOutpRegisterBto.getInternationalPatientFee());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeOutpRegisterBto, "__$validPropertySet"),
                        "operatorDepartmentId")) {
                    outpRegisterBO.setOperatorDepartmentId(
                            mergeOutpRegisterBto.getOperatorDepartmentId());
                }
            }
        } else {
            outpRegisterBO = new OutpRegisterBO();
            if (pkExist) {
                outpRegisterBO.setId(mergeOutpRegisterBto.getId());
            } else {
                outpRegisterBO.setId(String.valueOf(this.idGenerator.allocateId("outp_register")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "outpAppointId")) {
                outpRegisterBO.setOutpAppointId(mergeOutpRegisterBto.getOutpAppointId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "patientId")) {
                outpRegisterBO.setPatientId(mergeOutpRegisterBto.getPatientId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "visitCardId")) {
                outpRegisterBO.setVisitCardId(mergeOutpRegisterBto.getVisitCardId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "clinicRegisterTypeId")) {
                outpRegisterBO.setClinicRegisterTypeId(
                        mergeOutpRegisterBto.getClinicRegisterTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "mdtFeeType")) {
                outpRegisterBO.setMdtFeeType(mergeOutpRegisterBto.getMdtFeeType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "sourceAppId")) {
                outpRegisterBO.setSourceAppId(mergeOutpRegisterBto.getSourceAppId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "sourceCampusId")) {
                outpRegisterBO.setSourceCampusId(mergeOutpRegisterBto.getSourceCampusId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "tallyStatus")) {
                outpRegisterBO.setTallyStatus(mergeOutpRegisterBto.getTallyStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "registerNumber")) {
                outpRegisterBO.setRegisterNumber(mergeOutpRegisterBto.getRegisterNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "patientName")) {
                outpRegisterBO.setPatientName(mergeOutpRegisterBto.getPatientName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "timeDescription")) {
                outpRegisterBO.setTimeDescription(mergeOutpRegisterBto.getTimeDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "age")) {
                outpRegisterBO.setAge(mergeOutpRegisterBto.getAge());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "gender")) {
                outpRegisterBO.setGender(mergeOutpRegisterBto.getGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "identityType")) {
                outpRegisterBO.setIdentityType(mergeOutpRegisterBto.getIdentityType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "insuranceType")) {
                outpRegisterBO.setInsuranceType(mergeOutpRegisterBto.getInsuranceType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "insuranceNumber")) {
                outpRegisterBO.setInsuranceNumber(mergeOutpRegisterBto.getInsuranceNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "registerStatus")) {
                outpRegisterBO.setRegisterStatus(mergeOutpRegisterBto.getRegisterStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "operatorId")) {
                outpRegisterBO.setOperatorId(mergeOutpRegisterBto.getOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "registerDate")) {
                outpRegisterBO.setRegisterDate(mergeOutpRegisterBto.getRegisterDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "settleNumber")) {
                outpRegisterBO.setSettleNumber(mergeOutpRegisterBto.getSettleNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "cancellationInitiatorId")) {
                outpRegisterBO.setCancellationInitiatorId(
                        mergeOutpRegisterBto.getCancellationInitiatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "cancellationInitiationTime")) {
                outpRegisterBO.setCancellationInitiationTime(
                        mergeOutpRegisterBto.getCancellationInitiationTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "reviewerId")) {
                outpRegisterBO.setReviewerId(mergeOutpRegisterBto.getReviewerId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "reviewTime")) {
                outpRegisterBO.setReviewTime(mergeOutpRegisterBto.getReviewTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "totalCost")) {
                outpRegisterBO.setTotalCost(mergeOutpRegisterBto.getTotalCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "totalCharge")) {
                outpRegisterBO.setTotalCharge(mergeOutpRegisterBto.getTotalCharge());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "returnOperatorId")) {
                outpRegisterBO.setReturnOperatorId(mergeOutpRegisterBto.getReturnOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "returnDate")) {
                outpRegisterBO.setReturnDate(mergeOutpRegisterBto.getReturnDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "returnSettleNumber")) {
                outpRegisterBO.setReturnSettleNumber(mergeOutpRegisterBto.getReturnSettleNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "returnReason")) {
                outpRegisterBO.setReturnReason(mergeOutpRegisterBto.getReturnReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "chargePriceScheduleId")) {
                outpRegisterBO.setChargePriceScheduleId(
                        mergeOutpRegisterBto.getChargePriceScheduleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "discountCategory")) {
                outpRegisterBO.setDiscountCategory(mergeOutpRegisterBto.getDiscountCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "tallyReceiptNumber")) {
                outpRegisterBO.setTallyReceiptNumber(mergeOutpRegisterBto.getTallyReceiptNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "chargeTypeCode")) {
                outpRegisterBO.setChargeTypeCode(mergeOutpRegisterBto.getChargeTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "identityCode")) {
                outpRegisterBO.setIdentityCode(mergeOutpRegisterBto.getIdentityCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "takeAppointOperatorId")) {
                outpRegisterBO.setTakeAppointOperatorId(
                        mergeOutpRegisterBto.getTakeAppointOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "takeAppointDate")) {
                outpRegisterBO.setTakeAppointDate(mergeOutpRegisterBto.getTakeAppointDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "registrationDoctorId")) {
                outpRegisterBO.setRegistrationDoctorId(
                        mergeOutpRegisterBto.getRegistrationDoctorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "registrationDepartmentId")) {
                outpRegisterBO.setRegistrationDepartmentId(
                        mergeOutpRegisterBto.getRegistrationDepartmentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "outpatientRegistrationCategory")) {
                outpRegisterBO.setOutpatientRegistrationCategory(
                        mergeOutpRegisterBto.getOutpatientRegistrationCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "specifiedDiseaseFlag")) {
                outpRegisterBO.setSpecifiedDiseaseFlag(
                        mergeOutpRegisterBto.getSpecifiedDiseaseFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "specifiedDiseaseCode")) {
                outpRegisterBO.setSpecifiedDiseaseCode(
                        mergeOutpRegisterBto.getSpecifiedDiseaseCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "waitingStartTime")) {
                outpRegisterBO.setWaitingStartTime(mergeOutpRegisterBto.getWaitingStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "waitingEndTime")) {
                outpRegisterBO.setWaitingEndTime(mergeOutpRegisterBto.getWaitingEndTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "registerReviewStatus")) {
                outpRegisterBO.setRegisterReviewStatus(
                        mergeOutpRegisterBto.getRegisterReviewStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "reviewReason")) {
                outpRegisterBO.setReviewReason(mergeOutpRegisterBto.getReviewReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "reviewExplain")) {
                outpRegisterBO.setReviewExplain(mergeOutpRegisterBto.getReviewExplain());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "chargeStatus")) {
                outpRegisterBO.setChargeStatus(mergeOutpRegisterBto.getChargeStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "healthOfficialsFee")) {
                outpRegisterBO.setHealthOfficialsFee(mergeOutpRegisterBto.getHealthOfficialsFee());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "internationalPatientFee")) {
                outpRegisterBO.setInternationalPatientFee(
                        mergeOutpRegisterBto.getInternationalPatientFee());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeOutpRegisterBto, "__$validPropertySet"),
                    "operatorDepartmentId")) {
                outpRegisterBO.setOperatorDepartmentId(
                        mergeOutpRegisterBto.getOperatorDepartmentId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeOutpRegisterBto);
            addedBto.setBo(outpRegisterBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return outpRegisterBO;
    }

    /** 创建挂号信息 */
    @AutoGenerated(locked = true)
    protected CreateOutpRegisterBoResult createOutpRegisterBase(
            CreateOutpRegisterBto createOutpRegisterBto) {
        if (createOutpRegisterBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateOutpRegisterBoResult boResult = new CreateOutpRegisterBoResult();
        OutpRegisterBO outpRegisterBO =
                createCreateOutpRegisterOnDuplicateThrowEx(boResult, createOutpRegisterBto);
        boResult.setRootBo(outpRegisterBO);
        return boResult;
    }

    /** 保存挂号信息 */
    @AutoGenerated(locked = true)
    protected MergeOutpRegisterBoResult mergeOutpRegisterBase(
            MergeOutpRegisterBto mergeOutpRegisterBto) {
        if (mergeOutpRegisterBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeOutpRegisterBoResult boResult = new MergeOutpRegisterBoResult();
        OutpRegisterBO outpRegisterBO =
                createMergeOutpRegisterOnDuplicateUpdate(boResult, mergeOutpRegisterBto);
        boResult.setRootBo(outpRegisterBO);
        return boResult;
    }

    /** 更新审核信息 */
    @AutoGenerated(locked = true)
    protected UpdateReviewBoResult updateReviewBase(UpdateReviewBto updateReviewBto) {
        if (updateReviewBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateReviewBoResult boResult = new UpdateReviewBoResult();
        OutpRegisterBO outpRegisterBO = updateUpdateReviewOnMissThrowEx(boResult, updateReviewBto);
        boResult.setRootBo(outpRegisterBO);
        return boResult;
    }

    /** 更新对象:updateReview,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OutpRegisterBO updateUpdateReviewOnMissThrowEx(
            BaseOutpRegisterBOService.UpdateReviewBoResult boResult,
            UpdateReviewBto updateReviewBto) {
        OutpRegisterBO outpRegisterBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateReviewBto.getId() == null);
        if (!allNull && !found) {
            outpRegisterBO = OutpRegisterBO.getById(updateReviewBto.getId());
            found = true;
        }
        if (outpRegisterBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(outpRegisterBO.convertToOutpRegister());
            updatedBto.setBto(updateReviewBto);
            updatedBto.setBo(outpRegisterBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "reviewerId")) {
                outpRegisterBO.setReviewerId(updateReviewBto.getReviewerId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "reviewTime")) {
                outpRegisterBO.setReviewTime(updateReviewBto.getReviewTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "registerReviewStatus")) {
                outpRegisterBO.setRegisterReviewStatus(updateReviewBto.getRegisterReviewStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "reviewReason")) {
                outpRegisterBO.setReviewReason(updateReviewBto.getReviewReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "reviewExplain")) {
                outpRegisterBO.setReviewExplain(updateReviewBto.getReviewExplain());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "chargeStatus")) {
                outpRegisterBO.setChargeStatus(updateReviewBto.getChargeStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "healthOfficialsFee")) {
                outpRegisterBO.setHealthOfficialsFee(updateReviewBto.getHealthOfficialsFee());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "internationalPatientFee")) {
                outpRegisterBO.setInternationalPatientFee(
                        updateReviewBto.getInternationalPatientFee());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(updateReviewBto, "__$validPropertySet"),
                    "operatorDepartmentId")) {
                outpRegisterBO.setOperatorDepartmentId(updateReviewBto.getOperatorDepartmentId());
            }
            return outpRegisterBO;
        }
    }

    public static class UpdateReviewBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OutpRegisterBO getRootBo() {
            return (OutpRegisterBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateReviewBto, OutpRegisterBO> getCreatedBto(
                UpdateReviewBto updateReviewBto) {
            return this.getAddedResult(updateReviewBto);
        }

        @AutoGenerated(locked = true)
        public OutpRegister getDeleted_OutpRegister() {
            return (OutpRegister)
                    CollectionUtil.getFirst(this.getDeletedEntityList(OutpRegister.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateReviewBto, OutpRegister, OutpRegisterBO> getUpdatedBto(
                UpdateReviewBto updateReviewBto) {
            return super.getUpdatedResult(updateReviewBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateReviewBto, OutpRegisterBO> getUnmodifiedBto(
                UpdateReviewBto updateReviewBto) {
            return super.getUnmodifiedResult(updateReviewBto);
        }
    }

    public static class MergeOutpRegisterBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OutpRegisterBO getRootBo() {
            return (OutpRegisterBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeOutpRegisterBto, OutpRegisterBO> getCreatedBto(
                MergeOutpRegisterBto mergeOutpRegisterBto) {
            return this.getAddedResult(mergeOutpRegisterBto);
        }

        @AutoGenerated(locked = true)
        public OutpRegister getDeleted_OutpRegister() {
            return (OutpRegister)
                    CollectionUtil.getFirst(this.getDeletedEntityList(OutpRegister.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeOutpRegisterBto, OutpRegister, OutpRegisterBO> getUpdatedBto(
                MergeOutpRegisterBto mergeOutpRegisterBto) {
            return super.getUpdatedResult(mergeOutpRegisterBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeOutpRegisterBto, OutpRegisterBO> getUnmodifiedBto(
                MergeOutpRegisterBto mergeOutpRegisterBto) {
            return super.getUnmodifiedResult(mergeOutpRegisterBto);
        }
    }

    public static class CreateOutpRegisterBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OutpRegisterBO getRootBo() {
            return (OutpRegisterBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOutpRegisterBto, OutpRegisterBO> getCreatedBto(
                CreateOutpRegisterBto createOutpRegisterBto) {
            return this.getAddedResult(createOutpRegisterBto);
        }

        @AutoGenerated(locked = true)
        public OutpRegister getDeleted_OutpRegister() {
            return (OutpRegister)
                    CollectionUtil.getFirst(this.getDeletedEntityList(OutpRegister.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOutpRegisterBto, OutpRegister, OutpRegisterBO> getUpdatedBto(
                CreateOutpRegisterBto createOutpRegisterBto) {
            return super.getUpdatedResult(createOutpRegisterBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOutpRegisterBto, OutpRegisterBO> getUnmodifiedBto(
                CreateOutpRegisterBto createOutpRegisterBto) {
            return super.getUnmodifiedResult(createOutpRegisterBto);
        }
    }
}
