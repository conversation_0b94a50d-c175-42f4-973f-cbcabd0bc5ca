package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpRegisterAppointDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpRegisterAppointDto;
import com.pulse.appointment_booking.service.converter.OutpRegisterAppointDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "c138b18c-2894-4844-8d80-c0d7d5b77887|DTO|SERVICE")
public class OutpRegisterAppointDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterAppointDtoManager outpRegisterAppointDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpRegisterAppointDtoServiceConverter outpRegisterAppointDtoServiceConverter;

    @PublicInterface(
            id = "461d4832-0edb-4fe1-a655-3e0a8775eb88",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741501")
    @AutoGenerated(locked = false, uuid = "1563f737-7e88-3422-8a18-1d4236d00ce3")
    public List<OutpRegisterAppointDto> getByOutpAppointIds(
            @Valid @NotNull(message = "预约ID不能为空") List<String> outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        outpAppointId = new ArrayList<>(new HashSet<>(outpAppointId));
        List<OutpRegisterAppointDto> outpRegisterAppointDtoList =
                outpRegisterAppointDtoManager.getByOutpAppointIds(outpAppointId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterAppointDtoServiceConverter.OutpRegisterAppointDtoConverter(
                outpRegisterAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "4b7f1588-861f-4b2a-bdb0-ca67c2f70906",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741474")
    @AutoGenerated(locked = false, uuid = "5e5850b4-4bec-327e-a011-23e1bc9c4f1b")
    public OutpRegisterAppointDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpRegisterAppointDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "3e7bc506-1c00-4c54-8b74-d8c48266ae8d",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741492")
    @AutoGenerated(locked = false, uuid = "5f4e4f22-0cfd-31da-aa69-a1172e2c48eb")
    public List<OutpRegisterAppointDto> getByOutpAppointId(
            @NotNull(message = "预约ID不能为空") String outpAppointId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOutpAppointIds(Arrays.asList(outpAppointId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "b3d98e91-e93f-44aa-acac-7a91e36c72e6",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1749000741485")
    @AutoGenerated(locked = false, uuid = "681b0bee-3166-3478-af65-6607b753749e")
    public List<OutpRegisterAppointDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpRegisterAppointDto> outpRegisterAppointDtoList =
                outpRegisterAppointDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpRegisterAppointDtoServiceConverter.OutpRegisterAppointDtoConverter(
                outpRegisterAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
