package com.pulse.appointment_booking.service.flow.node.create_outp_appoint;

import com.pulse.appointment_booking.service.AppointmentBookService;
import com.pulse.appointment_booking.service.bto.CreateOuptAppointBto;
import com.pulse.appointment_booking.service.flow.context.CreateOutpAppointContext;
import com.vs.code.AutoGenerated;
import com.vs.flow.node.NodeIfComponent;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("appointmentBooking-createOutpAppoint-validateOccupy")
@AutoGenerated(locked = false, uuid = "86ac8365-9ae4-400b-897c-de8e16124d41|FLOW_NODE|DEFINITION")
public class ValidateOccupyNode extends NodeIfComponent {

    @Resource private AppointmentBookService appointmentBookService;

    /**
     * 实现流程判断逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "86ac8365-9ae4-400b-897c-de8e16124d41")
    public boolean processIf() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        CreateOutpAppointContext context = getFirstContextBean();
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑
        CreateOuptAppointBto createOuptAppointBto = context.getCreateOuptAppointBto();
        appointmentBookService.validateIsOccupied(
                createOuptAppointBto.getAppointmentScheduleId(),
                createOuptAppointBto.getSerialNumber());
        System.out.println("validate_occupy");
        return true;
    }
}
