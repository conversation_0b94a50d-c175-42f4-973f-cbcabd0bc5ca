package com.pulse.dictionary_basic.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "2c42093c-8332-4c0e-a925-a4079442029b|DTO|DEFINITION")
public class AttributeValueExtDto {
    /** 属性定义 */
    @Valid
    @AutoGenerated(locked = true, uuid = "5c49c497-3284-40ba-961f-0af568ae8efb")
    private AttributeDefinitionBaseDto attribute;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "43739116-246f-4269-ad52-e089687d1d61")
    private Date createdAt;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "59d3ddd9-22a8-4d5c-99a2-eb86ddf6d976")
    private Long deletedAt;

    /** 实体ID */
    @AutoGenerated(locked = true, uuid = "d63bd0e5-eabb-4bac-b959-30598598246f")
    private String entityId;

    /** 关联实体类型 */
    @AutoGenerated(locked = true, uuid = "1145b536-6a46-4a82-9304-35264205e5a7")
    private String entityType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "59b6f318-b91f-4396-8317-fb3e245ff9ca")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "de343043-508f-40ef-8028-1ee82f0dfb33")
    private Date updatedAt;

    /** 值 通用的值字段，和类型无关，统一为字符串形式 */
    @AutoGenerated(locked = true, uuid = "8a0ac557-cbd9-4256-ba70-f3dee7b32203")
    private String value;
}
