package com.pulse.tag.manager.facade.dictionary_basic.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "7326b0b8-2ab4-374c-b72f-98858479b1e0")
public class AttributeDefinitionBaseDtoServiceInTagBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "dbd9b53e-af72-4152-a189-29a0c0394465|RPC|BASE_ADAPTER")
    public List<AttributeDefinitionBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/dictionary_basic/dbd9b53e-af72-4152-a189-29a0c0394465/AttributeDefinitionBaseDtoService-getByIds",
                        "com.pulse.dictionary_basic.service.AttributeDefinitionBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "f29140b8-1fc1-4019-a115-9ec333f6042c",
                        "003f087c-177d-4a69-81e6-c6650b5f6080"),
                new TypeReference<>() {});
    }
}
