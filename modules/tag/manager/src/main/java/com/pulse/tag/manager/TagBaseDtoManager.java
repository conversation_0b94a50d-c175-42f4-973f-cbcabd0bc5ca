package com.pulse.tag.manager;

import com.pulse.tag.manager.dto.TagBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "34321832-aef8-4f40-b915-6105706c5034|DTO|MANAGER")
public interface TagBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "0da50f3c-f660-3239-9c78-4be9ebe75667")
    List<TagBaseDto> getByCodes(List<String> code);

    @AutoGenerated(locked = true, uuid = "180fcde0-7e97-343f-97d5-4f5af3cf6627")
    TagBaseDto getByCode(String code);

    @AutoGenerated(locked = true, uuid = "3a33c18e-0f5e-3e9c-b607-e1e6790c2111")
    List<TagBaseDto> getByTemporalFlags(List<Boolean> temporalFlag);

    @AutoGenerated(locked = true, uuid = "8da7389b-1542-3ea0-9315-abe34bb4d2b5")
    List<TagBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "cbf4a01e-908f-3b2b-9af4-33cf55e5edd6")
    List<TagBaseDto> getByCategoryId(String categoryId);

    @AutoGenerated(locked = true, uuid = "e23ca8cd-ec53-33be-9a6b-283515f11745")
    List<TagBaseDto> getByTemporalFlag(Boolean temporalFlag);

    @AutoGenerated(locked = true, uuid = "edfd72e6-23ee-30fc-8c20-e97429d55f4e")
    List<TagBaseDto> getByCategoryIds(List<String> categoryId);

    @AutoGenerated(locked = true, uuid = "fa2e83a1-2ce5-392d-b0f4-8fdcefa14197")
    TagBaseDto getById(String id);
}
