package com.pulse.tag.manager.bo.base;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;

import com.pulse.tag.manager.bo.EntityTagBO;
import com.pulse.tag.manager.mo.DeleteEntityTagMo;
import com.pulse.tag.persist.dos.EntityTag;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.persist.transactional.message.BaseTransactionalMessageSender;
import com.vs.persist.transactional.message.TransactionalMessage;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.PostLoad;
import javax.persistence.PreRemove;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "entity_tag")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "d3a0e335-5c31-347d-a729-bf7d5950fb95")
public abstract class BaseEntityTagBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "478603e0-0282-5a6f-8866-9dcbd99511be")
    private Date createdAt;

    /** 创建人 关联user.id */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "4fc77c12-9a95-49e7-829e-34f59c80b70a")
    private String createdBy;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "97aeb9cb-d1a8-58d9-bafc-202bd36770f6")
    private Long deletedAt = 0L;

    /** 启用标志 */
    @Column(name = "enable_flag")
    @AutoGenerated(locked = true, uuid = "7f71d5f3-78cf-4b46-87d3-3f9986a637bc")
    private Boolean enableFlag;

    /** 实体ID 业务实体ID */
    @Column(name = "entity_id")
    @AutoGenerated(locked = true, uuid = "bdcafe61-b585-4916-8986-d61793b84661")
    private String entityId;

    /** 实体类型 如PATIENT/DRUG/THERAPY_ORDER等 */
    @Column(name = "entity_type")
    @AutoGenerated(locked = true, uuid = "43dce986-2ed2-49be-9828-286627c7c347")
    private String entityType;

    /** Primary Key */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "2c838957-c118-438a-8739-d3175f16be05")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "9554e945-ef73-43c3-a244-ecded27db9a3")
    @Version
    private Long lockVersion;

    @Transient
    @AutoGenerated(locked = true)
    private Map oldDBValueMap = new HashMap<String, Object>();

    /** 标签ID 关联标签的主键ID */
    @Column(name = "tag_id")
    @AutoGenerated(locked = true, uuid = "b9cebc60-23d7-47e5-9a05-abc1b20307aa")
    private String tagId;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "b4122a70-96ae-5cfb-aa1d-22ec699978c7")
    private Date updatedAt;

    /** 修改人 关联user.id */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "14a2499b-8578-47f6-95c5-74b26249b33f")
    private String updatedBy;

    /** 生效时间 temporal_flag=true时必填 */
    @Column(name = "valid_from")
    @AutoGenerated(locked = true, uuid = "e6477c15-3b90-457f-8d1b-e74ac8b14293")
    private Date validFrom;

    /** 失效时间 可为空表示长期有效 */
    @Column(name = "valid_to")
    @AutoGenerated(locked = true, uuid = "26ff9c45-713a-4f96-a8e0-3f386034f40b")
    private Date validTo;

    @AutoGenerated(locked = true)
    public EntityTag convertToEntityTag() {
        EntityTag entity = new EntityTag();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "entityId",
                "entityType",
                "tagId",
                "validFrom",
                "validTo",
                "enableFlag",
                "createdBy",
                "updatedBy",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static EntityTagBO getByEntityIdAndEntityTypeAndTagIdAndValidFrom(
            String entityId, String entityType, String tagId, Date validFrom) {
        Session session = TransactionalSessionFactory.getSession();
        EntityTagBO entityTag =
                (EntityTagBO)
                        session.createQuery(
                                        "from EntityTagBO where entityId =: entityId  and"
                                            + " entityType =: entityType  and tagId =: tagId  and"
                                            + " validFrom =: validFrom ")
                                .setParameter("entityId", entityId)
                                .setParameter("entityType", entityType)
                                .setParameter("tagId", tagId)
                                .setParameter("validFrom", validFrom)
                                .uniqueResult();
        return entityTag;
    }

    @AutoGenerated(locked = true)
    public static EntityTagBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        EntityTagBO entityTag =
                (EntityTagBO)
                        session.createQuery("from EntityTagBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return entityTag;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnableFlag() {
        return this.enableFlag;
    }

    @AutoGenerated(locked = true)
    public String getEntityId() {
        return this.entityId;
    }

    @AutoGenerated(locked = true)
    public String getEntityType() {
        return this.entityType;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getTagId() {
        return this.tagId;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public Date getValidFrom() {
        return this.validFrom;
    }

    @AutoGenerated(locked = true)
    public Date getValidTo() {
        return this.validTo;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    /** 填充需要用到的用于事件触发比较的DB初始值。 */
    @AutoGenerated(locked = true)
    @PostLoad
    protected void postLoad() {
        if (this.getId() != null) {
            this.oldDBValueMap.put("id", this.getId());
        }
        if (this.getEntityId() != null) {
            this.oldDBValueMap.put("entityId", this.getEntityId());
        }
        if (this.getEntityType() != null) {
            this.oldDBValueMap.put("entityType", this.getEntityType());
        }
        if (this.getTagId() != null) {
            this.oldDBValueMap.put("tagId", this.getTagId());
        }
        if (this.getDeletedAt() != null) {
            this.oldDBValueMap.put("deletedAt", this.getDeletedAt());
        }
    }

    /** 事务提交的更新回调，当前处理领域更新事件的触发事宜 */
    @AutoGenerated(locked = true)
    protected void preCommitUpdate() {
        if (Long.valueOf(0L).equals(oldDBValueMap.get("deletedAt")) && this.deletedAt > 0) {
            // 发送领域事件： DeleteEntityTagMo
            {
                TransactionalMessage transactionalMessage = new TransactionalMessage();
                transactionalMessage.setName("DeleteEntityTagMo");
                DeleteEntityTagMo msg = new DeleteEntityTagMo();
                msg.setId(this.getId());
                msg.setEntityId(this.getEntityId());
                msg.setEntityType(this.getEntityType());
                msg.setTagId(this.getTagId());
                transactionalMessage.setJson(JSONUtil.toJsonStr(msg));
                transactionalMessage.setQueueName(
                        "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d---793aee21-a1d3-417c-848b-1a0992a3fd84");
                transactionalMessage.setScheduleTime(DateUtil.offsetSecond(new Date(), 0));
                BaseTransactionalMessageSender.sendMsg(transactionalMessage);
            }
        }
    }

    /** 持久化数据前的回调，当前处理一些领域事件的触发事宜 */
    @AutoGenerated(locked = true)
    @PreRemove
    protected void preRemove() {
        // 发送领域事件： DeleteEntityTagMo
        {
            TransactionalMessage transactionalMessage = new TransactionalMessage();
            transactionalMessage.setName("DeleteEntityTagMo");
            transactionalMessage.setScheduleTime(DateUtil.offsetSecond(new Date(), 0));
            DeleteEntityTagMo msg = new DeleteEntityTagMo();
            msg.setId(this.getId());
            msg.setEntityId(this.getEntityId());
            msg.setEntityType(this.getEntityType());
            msg.setTagId(this.getTagId());
            transactionalMessage.setJson(JSONUtil.toJsonStr(msg));
            transactionalMessage.setQueueName(
                    "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d---793aee21-a1d3-417c-848b-1a0992a3fd84");
            transactionalMessage.setScheduleTime(new Date());
            BaseTransactionalMessageSender.sendMsg(transactionalMessage);
        }
    }

    /** 变更数据前的回调，当前处理一些领域事件的触发事宜 (记录受影响的对象） */
    @PreUpdate
    @AutoGenerated(locked = true)
    protected void preUpdate() {
        BaseTransactionalMessageSender.onBoChange(this);
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setEnableFlag(Boolean enableFlag) {
        this.enableFlag = enableFlag;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setEntityId(String entityId) {
        this.entityId = entityId;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setEntityType(String entityType) {
        this.entityType = entityType;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setId(String id) {
        this.id = id;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setTagId(String tagId) {
        this.tagId = tagId;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
        return (EntityTagBO) this;
    }

    @AutoGenerated(locked = true)
    public EntityTagBO setValidTo(Date validTo) {
        this.validTo = validTo;
        return (EntityTagBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
