package com.pulse.tag.manager.facade.dictionary_basic;

import com.pulse.dictionary_basic.service.bto.MergeAttributeValueBto;
import com.pulse.tag.manager.facade.dictionary_basic.base.AttributeValueBOServiceInTagBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "f29140b8-1fc1-4019-a115-9ec333f6042c")
@AutoGenerated(locked = false, uuid = "e9148cf5-494c-3219-9d2e-c22705444a23")
public class AttributeValueBOServiceInTagRpcAdapter
        extends AttributeValueBOServiceInTagBaseRpcAdapter {

    /** 保存扩展属性值列表 */
    @RpcRefer(id = "da35a7db-a77b-4c42-9b10-1209c4455de1", version = "1747106755880")
    @AutoGenerated(locked = false, uuid = "da35a7db-a77b-4c42-9b10-1209c4455de1|RPC|ADAPTER")
    public List<String> mergeAttributeValueList(
            List<MergeAttributeValueBto> mergeAttributeValueList,
            String entityType,
            String entityId) {
        return super.mergeAttributeValueList(mergeAttributeValueList, entityType, entityId);
    }
}
