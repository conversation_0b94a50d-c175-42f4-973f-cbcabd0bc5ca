package com.pulse.tag.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "9b2b6baa-cc57-4767-b76b-3b5b36b0e641|QTO|DEFINITION")
public class ListEntityTagExtQto {
    /** 生效时间 entity_tag.valid_from */
    @AutoGenerated(locked = true, uuid = "f99bd6ab-d24d-42d3-9d1f-ab127fb55541")
    private Date currentTime;

    /** 实体ID entity_tag.entity_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "091e4fc5-9af2-4cd8-b1ef-5ba6bb91f3ea")
    private List<String> entityIdIn;

    /** 实体类型 entity_tag.entity_type */
    @AutoGenerated(locked = true, uuid = "472e014b-2e0f-4f16-9f7b-591a6b880e1e")
    private String entityTypeIs;

    /** 生效时间 entity_tag.valid_from */
    @AutoGenerated(locked = true, uuid = "33cbcb3d-2839-4553-a17b-e03c4ebba1f4")
    private Date validFromBiggerThanEqual;

    /** 失效时间 entity_tag.valid_to */
    @AutoGenerated(locked = true, uuid = "c9f4f519-6b72-4535-ba62-a2f921ffa7b3")
    private Date validToLessThanEqual;
}
