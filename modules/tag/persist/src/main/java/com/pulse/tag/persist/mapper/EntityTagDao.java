package com.pulse.tag.persist.mapper;

import com.pulse.tag.persist.dos.EntityTag;
import com.vs.code.AutoGenerated;

import java.util.Date;
import java.util.List;

@AutoGenerated(locked = false, uuid = "5bc29b83-f373-4319-b7fb-02f73d040b57|ENTITY|IDAO")
public interface EntityTagDao {

    @AutoGenerated(locked = true, uuid = "1b78f6b3-99b3-37db-b5ff-77081a3aa20c")
    List<EntityTag> getByValidFromsAndValidTos(List<EntityTag.ValidFromAndValidTo> varList);

    @AutoGenerated(locked = true, uuid = "2387c356-427d-3006-8e02-d31041c7051f")
    List<EntityTag> getByTagId(String tagId);

    @AutoGenerated(locked = true, uuid = "2a23902d-7735-3753-bd29-5532190cf7ea")
    EntityTag getById(String id);

    @AutoGenerated(locked = true, uuid = "5a8dc6ca-e070-399d-8413-7137787a39d7")
    List<EntityTag> getByValidFromAndValidTo(Date validFrom, Date validTo);

    @AutoGenerated(locked = true, uuid = "7b9b6d04-546e-34ce-85dd-a747e0d35cb4")
    List<EntityTag> getByTagIds(List<String> tagId);

    @AutoGenerated(locked = true, uuid = "ace29c18-8edf-3ae3-88a3-781db485f3ee")
    List<EntityTag> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "d7a007a7-731d-3f0e-a536-099262cd31cb")
    EntityTag getByEntityIdAndEntityTypeAndTagIdAndValidFrom(
            String entityId, String entityType, String tagId, Date validFrom);

    @AutoGenerated(locked = true, uuid = "e5c44cab-636b-34fa-8d6c-bf8f88b31758")
    List<EntityTag> getByEntityIdsAndEntityTypesAndTagIdsAndValidFroms(
            List<EntityTag.EntityIdAndEntityTypeAndTagIdAndValidFrom> varList);
}
