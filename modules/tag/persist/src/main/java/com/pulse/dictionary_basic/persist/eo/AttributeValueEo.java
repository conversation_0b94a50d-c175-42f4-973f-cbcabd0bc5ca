package com.pulse.dictionary_basic.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 业务扩展属性值。用于传入实体的扩展属性值 */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "36874401-947e-4d12-a2b1-bf038f7c34f7|EO|DEFINITION")
public class AttributeValueEo {
    /** 实体ID */
    @AutoGenerated(locked = true, uuid = "e37c52cd-0676-43a9-84e0-44d7a43c38c3")
    private String entityId;

    /** 实体类型 */
    @AutoGenerated(locked = true, uuid = "17992b99-746f-4bda-be7a-ed107c2b32be")
    private String entityType;

    /** 业务扩展属性ID */
    @AutoGenerated(locked = true, uuid = "5ba4c89e-0fc8-4100-9ea9-beb9703a617b")
    private String attributeId;

    /** 业务扩展属性值 */
    @AutoGenerated(locked = true, uuid = "9d2799d5-dc5d-4763-aea4-8921d20de924")
    private String attributeValue;

    /** 业务扩展属性值主键ID */
    @AutoGenerated(locked = true, uuid = "5408a52d-8f4b-4577-bd50-e5641b42a9d0")
    private String id;
}
