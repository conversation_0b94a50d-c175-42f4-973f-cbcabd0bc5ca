package com.pulse.tag.service.converter;

import com.pulse.tag.manager.dto.EntityTagExtDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "1477ca72-4a16-309f-91e6-dce15f60b17b")
public class EntityTagExtDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<EntityTagExtDto> EntityTagExtDtoConverter(
            List<EntityTagExtDto> entityTagExtDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return entityTagExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
