package com.pulse.special_drug.manager.facade.drug_dictionary.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "73a8257a-3af1-3448-8436-40aa11311422")
public class DrugOriginSpecificationBaseDtoServiceInSpecialDrugBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "4e5f0238-0b8c-4359-adca-8e6156d8fe8b|RPC|BASE_ADAPTER")
    public DrugOriginSpecificationBaseDto getById(String id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/4e5f0238-0b8c-4359-adca-8e6156d8fe8b/DrugOriginSpecificationBaseDtoService-getById",
                        "com.pulse.drug_dictionary.service.DrugOriginSpecificationBaseDtoService",
                        "getById",
                        paramMap,
                        paramTypeMap,
                        "53d86369-3911-4c5d-bb51-8e771ba6430d",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "e7234d99-43d1-49b8-b8f1-a5249308735c|RPC|BASE_ADAPTER")
    public List<DrugOriginSpecificationBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/e7234d99-43d1-49b8-b8f1-a5249308735c/DrugOriginSpecificationBaseDtoService-getByIds",
                        "com.pulse.drug_dictionary.service.DrugOriginSpecificationBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "53d86369-3911-4c5d-bb51-8e771ba6430d",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }
}
