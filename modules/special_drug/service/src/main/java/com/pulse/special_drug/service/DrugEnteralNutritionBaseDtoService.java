package com.pulse.special_drug.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.special_drug.manager.DrugEnteralNutritionBaseDtoManager;
import com.pulse.special_drug.manager.dto.DrugEnteralNutritionBaseDto;
import com.pulse.special_drug.service.converter.DrugEnteralNutritionBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "2a21f2b7-2d1e-4898-8603-b76ca1e889aa|DTO|SERVICE")
public class DrugEnteralNutritionBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugEnteralNutritionBaseDtoManager drugEnteralNutritionBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugEnteralNutritionBaseDtoServiceConverter drugEnteralNutritionBaseDtoServiceConverter;

    @PublicInterface(id = "1f44074c-c1bb-459e-a929-7aada4a2a5ce", module = "special_drug")
    @AutoGenerated(locked = false, uuid = "02725513-df85-321c-b30e-b2e8fdde82ca")
    public List<DrugEnteralNutritionBaseDto> getByDrugOriginSpecificationId(
            @NotNull(message = "药品产地规格id不能为空") String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "29c85294-6425-4e1b-9d13-4b21213484d5", module = "special_drug")
    @AutoGenerated(locked = false, uuid = "181bae04-b4bf-3027-9d46-1f22ac64fc48")
    public List<DrugEnteralNutritionBaseDto> getByDrugOriginSpecificationIds(
            @Valid @NotNull(message = "药品产地规格id不能为空") List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginSpecificationId = new ArrayList<>(new HashSet<>(drugOriginSpecificationId));
        List<DrugEnteralNutritionBaseDto> drugEnteralNutritionBaseDtoList =
                drugEnteralNutritionBaseDtoManager.getByDrugOriginSpecificationIds(
                        drugOriginSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugEnteralNutritionBaseDtoServiceConverter.DrugEnteralNutritionBaseDtoConverter(
                drugEnteralNutritionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "48240a55-72fd-401b-8f08-62fe9483fa1a", module = "special_drug")
    @AutoGenerated(locked = false, uuid = "3f0d2482-516f-36b6-b372-96000b36d6e5")
    public DrugEnteralNutritionBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugEnteralNutritionBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "6b01a524-b4ce-49e1-a84e-ff179f2e91cc", module = "special_drug")
    @AutoGenerated(locked = false, uuid = "a645d34e-d1f1-3517-be66-a5ee3b33e919")
    public List<DrugEnteralNutritionBaseDto> getByDrugOriginCodes(
            @Valid @NotNull(message = "药品产地编码不能为空") List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginCode = new ArrayList<>(new HashSet<>(drugOriginCode));
        List<DrugEnteralNutritionBaseDto> drugEnteralNutritionBaseDtoList =
                drugEnteralNutritionBaseDtoManager.getByDrugOriginCodes(drugOriginCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugEnteralNutritionBaseDtoServiceConverter.DrugEnteralNutritionBaseDtoConverter(
                drugEnteralNutritionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "ae83b6b6-0714-43fe-8eee-87eee205acce", module = "special_drug")
    @AutoGenerated(locked = false, uuid = "b661363c-7175-35a7-918a-50feb86a0e03")
    public List<DrugEnteralNutritionBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugEnteralNutritionBaseDto> drugEnteralNutritionBaseDtoList =
                drugEnteralNutritionBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugEnteralNutritionBaseDtoServiceConverter.DrugEnteralNutritionBaseDtoConverter(
                drugEnteralNutritionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "50a36b83-336c-461a-b6cd-2bfde7df90e9", module = "special_drug")
    @AutoGenerated(locked = false, uuid = "ce2396bf-52e2-34f0-b1fc-8c257a1c4fee")
    public List<DrugEnteralNutritionBaseDto> getByDrugOriginCode(
            @NotNull(message = "药品产地编码不能为空") String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
