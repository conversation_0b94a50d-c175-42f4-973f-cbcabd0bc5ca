package com.pulse.special_drug.persist.mapper;

import com.pulse.special_drug.persist.qto.ListDrugCentralPurchaseVsSameEffectByCentralPurchaseQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "0f8bff5a-bf3c-40fa-96c0-00cf9e513fd5|QTO|DAO")
public class ListDrugCentralPurchaseVsSameEffectByCentralPurchaseQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 功能：根据集采药品产地编码获取对照列表 */
    @AutoGenerated(locked = false, uuid = "0f8bff5a-bf3c-40fa-96c0-00cf9e513fd5-count")
    public Integer count(ListDrugCentralPurchaseVsSameEffectByCentralPurchaseQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_central_purchase_vs_same_effect.id) FROM"
                    + " drug_central_purchase_vs_same_effect LEFT JOIN drug_origin"
                    + " \"centralPurchaseProductCode\" on"
                    + " drug_central_purchase_vs_same_effect.central_purchase_origin_code ="
                    + " \"centralPurchaseProductCode\".drug_origin_code LEFT JOIN drug_origin"
                    + " \"sameEffectProductCode\" on"
                    + " drug_central_purchase_vs_same_effect.same_effect_origin_code ="
                    + " \"sameEffectProductCode\".drug_origin_code WHERE"
                    + " \"centralPurchaseProductCode\".product_code ="
                    + " #centralPurchaseProductCodeProductCodeIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getCentralPurchaseProductCodeProductCodeIs() == null) {
            conditionToRemove.add("#centralPurchaseProductCodeProductCodeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"centralPurchaseProductCode\"");
        softDeleteTableAlias.add("\"sameEffectProductCode\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql = sql.replace("#centralPurchaseProductCodeProductCodeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#centralPurchaseProductCodeProductCodeIs")) {
                sqlParams.add(qto.getCentralPurchaseProductCodeProductCodeIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 功能：根据集采药品产地编码获取对照列表 */
    @AutoGenerated(locked = false, uuid = "0f8bff5a-bf3c-40fa-96c0-00cf9e513fd5-query-all")
    public List<String> query(ListDrugCentralPurchaseVsSameEffectByCentralPurchaseQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_central_purchase_vs_same_effect.id FROM"
                    + " drug_central_purchase_vs_same_effect LEFT JOIN drug_origin"
                    + " \"centralPurchaseProductCode\" on"
                    + " drug_central_purchase_vs_same_effect.central_purchase_origin_code ="
                    + " \"centralPurchaseProductCode\".drug_origin_code LEFT JOIN drug_origin"
                    + " \"sameEffectProductCode\" on"
                    + " drug_central_purchase_vs_same_effect.same_effect_origin_code ="
                    + " \"sameEffectProductCode\".drug_origin_code WHERE"
                    + " \"centralPurchaseProductCode\".product_code ="
                    + " #centralPurchaseProductCodeProductCodeIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getCentralPurchaseProductCodeProductCodeIs() == null) {
            conditionToRemove.add("#centralPurchaseProductCodeProductCodeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"centralPurchaseProductCode\"");
        softDeleteTableAlias.add("\"sameEffectProductCode\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql = sql.replace("#centralPurchaseProductCodeProductCodeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#centralPurchaseProductCodeProductCodeIs")) {
                sqlParams.add(qto.getCentralPurchaseProductCodeProductCodeIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  \"sameEffectProductCode\".drug_product_name asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
