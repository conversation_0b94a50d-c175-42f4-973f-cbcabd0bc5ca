package com.pulse.special_drug.persist.mapper;

import com.pulse.special_drug.persist.dos.DrugTotalParenteralNutrition;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "ac4a8fea-089c-3d04-a52d-3bd18006d355|ENTITY|IDAO")
public interface DrugTotalParenteralNutritionDao {

    @AutoGenerated(locked = true, uuid = "27f1ada2-b484-385a-a86c-938f9f5f9a94")
    List<DrugTotalParenteralNutrition> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "2aea3072-40fe-373d-bd96-e9f0ad0f677e")
    List<DrugTotalParenteralNutrition> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId);

    @AutoGenerated(locked = true, uuid = "677a8a85-261b-3d34-addf-c940cba01b83")
    List<DrugTotalParenteralNutrition> getByDrugOriginCodes(List<String> drugOriginCode);

    @AutoGenerated(locked = true, uuid = "958a23f2-6abf-3635-9f75-078a1fb8656d")
    List<DrugTotalParenteralNutrition> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId);

    @AutoGenerated(locked = true, uuid = "afe2ec3f-9fe8-3e24-9a36-e9b350eff767")
    DrugTotalParenteralNutrition getById(String id);

    @AutoGenerated(locked = true, uuid = "e22135b6-f1be-3c9b-ab4e-a7e5dc07754c")
    List<DrugTotalParenteralNutrition> getByDrugOriginCode(String drugOriginCode);
}
