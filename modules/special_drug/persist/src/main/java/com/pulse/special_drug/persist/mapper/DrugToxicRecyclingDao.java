package com.pulse.special_drug.persist.mapper;

import com.pulse.special_drug.persist.dos.DrugToxicRecycling;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "8f37597d-0aa5-380a-96eb-744f59d397c0|ENTITY|IDAO")
public interface DrugToxicRecyclingDao {

    @AutoGenerated(locked = true, uuid = "2dacea63-45e0-3ed9-9cdd-5c32677a7a53")
    List<DrugToxicRecycling> getByApplicationIds(List<String> applicationId);

    @AutoGenerated(locked = true, uuid = "45a31018-c4a4-3b3f-b56b-41b5d50a8f3b")
    List<DrugToxicRecycling> getByBatchInventoryIds(List<String> batchInventoryId);

    @AutoGenerated(locked = true, uuid = "77ce46f0-a7fa-3253-a01c-bd04ec80c1ec")
    List<DrugToxicRecycling> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "870b724e-c04a-34d3-bcc2-c990a4c57ef9")
    List<DrugToxicRecycling> getByDrugOriginSpecificationId(String drugOriginSpecificationId);

    @AutoGenerated(locked = true, uuid = "bbb07b98-6226-3582-abf1-92ed25aa7fe6")
    List<DrugToxicRecycling> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId);

    @AutoGenerated(locked = true, uuid = "bfd21e50-5d4c-36f5-bdc2-a39f45adb05d")
    List<DrugToxicRecycling> getByApplicationId(String applicationId);

    @AutoGenerated(locked = true, uuid = "d0adb7dd-9e30-3674-b133-dd9bb736842a")
    DrugToxicRecycling getById(String id);

    @AutoGenerated(locked = true, uuid = "e7aa4926-3971-37c3-bc5b-406415c64e62")
    List<DrugToxicRecycling> getByBatchInventoryId(String batchInventoryId);
}
