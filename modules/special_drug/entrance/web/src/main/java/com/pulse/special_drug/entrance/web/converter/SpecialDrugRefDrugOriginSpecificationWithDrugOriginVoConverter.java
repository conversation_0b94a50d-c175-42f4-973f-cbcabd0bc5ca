package com.pulse.special_drug.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.pulse.special_drug.entrance.web.query.assembler.SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataAssembler;
import com.pulse.special_drug.entrance.web.query.assembler.SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataAssembler.SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataHolder;
import com.pulse.special_drug.entrance.web.query.collector.SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataCollector;
import com.pulse.special_drug.entrance.web.vo.SpecialDrugRefDrugOriginBaseVo;
import com.pulse.special_drug.entrance.web.vo.SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo;
import com.pulse.special_drug.manager.facade.drug_dictionary.DrugOriginSpecificationBaseDtoServiceInSpecialDrugRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "64bb2d99-16c4-4b5a-876a-5454596e172f|VO|CONVERTER")
public class SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoServiceInSpecialDrugRpcAdapter
            drugOriginSpecificationBaseDtoServiceInSpecialDrugRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private SpecialDrugRefDrugOriginBaseVoConverter specialDrugRefDrugOriginBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataAssembler
            specialDrugRefDrugOriginSpecificationWithDrugOriginVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataCollector
            specialDrugRefDrugOriginSpecificationWithDrugOriginVoDataCollector;

    /**
     * 把DrugOriginSpecificationWithDrugOriginDto转换成SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo
     */
    @AutoGenerated(locked = false, uuid = "64bb2d99-16c4-4b5a-876a-5454596e172f-converter-Map")
    public Map<
                    DrugOriginSpecificationWithDrugOriginDto,
                    SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo>
            convertToSpecialDrugRefDrugOriginSpecificationWithDrugOriginVoMap(
                    List<DrugOriginSpecificationWithDrugOriginDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugOriginBaseDto, SpecialDrugRefDrugOriginBaseVo> drugOriginMap =
                specialDrugRefDrugOriginBaseVoConverter.convertToSpecialDrugRefDrugOriginBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugOriginSpecificationWithDrugOriginDto::getDrugOrigin)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<
                        DrugOriginSpecificationWithDrugOriginDto,
                        SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo>
                voMap =
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                dto -> {
                                                    SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo
                                                            vo =
                                                                    new SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo();
                                                    vo.setId(dto.getId());
                                                    vo.setDrugSpecificationDetailId(
                                                            dto.getDrugSpecificationDetailId());
                                                    vo.setDrugOrigin(
                                                            dto.getDrugOrigin() == null
                                                                    ? null
                                                                    : drugOriginMap.get(
                                                                            dto.getDrugOrigin()));
                                                    vo.setSpecificationType(
                                                            dto.getSpecificationType());
                                                    vo.setDrugSpecification(
                                                            dto.getDrugSpecification());
                                                    vo.setUnit(dto.getUnit());
                                                    return vo;
                                                },
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /**
     * 把DrugOriginSpecificationWithDrugOriginDto转换成SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo
     */
    @AutoGenerated(locked = true, uuid = "64bb2d99-16c4-4b5a-876a-5454596e172f-converter-list")
    public List<SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo>
            convertToSpecialDrugRefDrugOriginSpecificationWithDrugOriginVoList(
                    List<DrugOriginSpecificationWithDrugOriginDto> dtoList) {
        return new ArrayList<>(
                convertToSpecialDrugRefDrugOriginSpecificationWithDrugOriginVoMap(dtoList)
                        .values());
    }

    /** 使用默认方式组装SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo数据 */
    @AutoGenerated(locked = true, uuid = "6aaadfd1-e903-355d-bb39-f149621c0088")
    public SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo convertAndAssembleData(
            DrugOriginSpecificationWithDrugOriginDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /**
     * 把DrugOriginSpecificationWithDrugOriginDto转换成SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo
     */
    @AutoGenerated(locked = true, uuid = "*************-3365-a38d-d391fbd2ccb2")
    public SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo
            convertToSpecialDrugRefDrugOriginSpecificationWithDrugOriginVo(
                    DrugOriginSpecificationWithDrugOriginDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToSpecialDrugRefDrugOriginSpecificationWithDrugOriginVoList(List.of(dto))
                .stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo列表数据 */
    @AutoGenerated(locked = true, uuid = "89fba240-d162-3fa6-9e56-48182e30fce7")
    public List<SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo> convertAndAssembleDataList(
            List<DrugOriginSpecificationWithDrugOriginDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataHolder dataHolder =
                new SpecialDrugRefDrugOriginSpecificationWithDrugOriginVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugOriginSpecificationBaseDtoServiceInSpecialDrugRpcAdapter.getByIds(
                        dtoList.stream()
                                .map(DrugOriginSpecificationWithDrugOriginDto::getId)
                                .collect(Collectors.toList())));
        Map<String, SpecialDrugRefDrugOriginSpecificationWithDrugOriginVo> voMap =
                convertToSpecialDrugRefDrugOriginSpecificationWithDrugOriginVoMap(dtoList)
                        .entrySet()
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        specialDrugRefDrugOriginSpecificationWithDrugOriginVoDataCollector.collectDataWithDtoData(
                dtoList, dataHolder);
        specialDrugRefDrugOriginSpecificationWithDrugOriginVoDataAssembler.assembleData(
                voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
