package com.pulse.drug_report.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_report.manager.dto.DrugDetailLedgerBaseDto;
import com.pulse.drug_report.persist.dos.DrugDetailLedger;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "94f4afec-fb66-46b5-8f54-f82821161ac0|DTO|BASE_CONVERTER")
public class DrugDetailLedgerBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugDetailLedgerBaseDto convertFromDrugDetailLedgerToDrugDetailLedgerBaseDto(
            DrugDetailLedger drugDetailLedger) {
        return convertFromDrugDetailLedgerToDrugDetailLedgerBaseDto(List.of(drugDetailLedger))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugDetailLedgerBaseDto> convertFromDrugDetailLedgerToDrugDetailLedgerBaseDto(
            List<DrugDetailLedger> drugDetailLedgerList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugDetailLedgerList)) {
            return new ArrayList<>();
        }
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList = new ArrayList<>();
        for (DrugDetailLedger drugDetailLedger : drugDetailLedgerList) {
            if (drugDetailLedger == null) {
                continue;
            }
            DrugDetailLedgerBaseDto drugDetailLedgerBaseDto = new DrugDetailLedgerBaseDto();
            drugDetailLedgerBaseDto.setId(drugDetailLedger.getId());
            drugDetailLedgerBaseDto.setStorageCode(drugDetailLedger.getStorageCode());
            drugDetailLedgerBaseDto.setCreatedAt(drugDetailLedger.getCreatedAt());
            drugDetailLedgerBaseDto.setUpdatedAt(drugDetailLedger.getUpdatedAt());
            drugDetailLedgerBaseDto.setAccountType(drugDetailLedger.getAccountType());
            drugDetailLedgerBaseDto.setDrugOriginSpecificationId(
                    drugDetailLedger.getDrugOriginSpecificationId());
            drugDetailLedgerBaseDto.setDrugOriginName(drugDetailLedger.getDrugOriginName());
            drugDetailLedgerBaseDto.setAccountantDateTime(drugDetailLedger.getAccountantDateTime());
            drugDetailLedgerBaseDto.setExportImportId(drugDetailLedger.getExportImportId());
            drugDetailLedgerBaseDto.setAmount(drugDetailLedger.getAmount());
            drugDetailLedgerBaseDto.setPurchasePrice(drugDetailLedger.getPurchasePrice());
            drugDetailLedgerBaseDto.setPurchaseCost(drugDetailLedger.getPurchaseCost());
            drugDetailLedgerBaseDto.setRetailPrice(drugDetailLedger.getRetailPrice());
            drugDetailLedgerBaseDto.setRetailCost(drugDetailLedger.getRetailCost());
            drugDetailLedgerBaseDto.setRemark(drugDetailLedger.getRemark());
            drugDetailLedgerBaseDto.setAccountStaffId(drugDetailLedger.getAccountStaffId());
            drugDetailLedgerBaseDto.setMonthlyFinancialReportId(
                    drugDetailLedger.getMonthlyFinancialReportId());
            drugDetailLedgerBaseDto.setAccountingPeriodId(drugDetailLedger.getAccountingPeriodId());
            drugDetailLedgerBaseDto.setImportDetailId(drugDetailLedger.getImportDetailId());
            drugDetailLedgerBaseDto.setExportDetailId(drugDetailLedger.getExportDetailId());
            drugDetailLedgerBaseDto.setDrugLossReportDetailId(
                    drugDetailLedger.getDrugLossReportDetailId());
            drugDetailLedgerBaseDto.setDrugPrescriptionDispenseDetailBatchId(
                    drugDetailLedger.getDrugPrescriptionDispenseDetailBatchId());
            drugDetailLedgerBaseDto.setDrugOrderDispenseDetailBatchId(
                    drugDetailLedger.getDrugOrderDispenseDetailBatchId());
            drugDetailLedgerBaseDto.setDrugBorrowDetailId(drugDetailLedger.getDrugBorrowDetailId());
            drugDetailLedgerBaseDto.setDrugStocktakingDetailId(
                    drugDetailLedger.getDrugStocktakingDetailId());
            drugDetailLedgerBaseDto.setDrugPriceAdjustDetailId(
                    drugDetailLedger.getDrugPriceAdjustDetailId());
            drugDetailLedgerBaseDto.setDrugInventoryBatchId(
                    drugDetailLedger.getDrugInventoryBatchId());
            drugDetailLedgerBaseDto.setDrugOriginCode(drugDetailLedger.getDrugOriginCode());
            drugDetailLedgerBaseDto.setLockVersion(drugDetailLedger.getLockVersion());
            drugDetailLedgerBaseDto.setBusinessDocumentId(drugDetailLedger.getBusinessDocumentId());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugDetailLedgerBaseDtoList.add(drugDetailLedgerBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugDetailLedgerBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
