package com.pulse.drug_report.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_report.manager.DrugDetailLedgerBaseDtoManager;
import com.pulse.drug_report.manager.dto.DrugDetailLedgerBaseDto;
import com.pulse.drug_report.persist.dos.DrugDetailLedger.BusinessDocumentIdAndExportImportId;
import com.pulse.drug_report.persist.eo.UkExportImportIdBusinessDocumentIdEo;
import com.pulse.drug_report.service.converter.DrugDetailLedgerBaseDtoServiceConverter;
import com.pulse.drug_report.service.converter.voConverter.DrugDetailLedgerUkExportImportIdBusinessDocumentIdConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "94f4afec-fb66-46b5-8f54-f82821161ac0|DTO|SERVICE")
public class DrugDetailLedgerBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugDetailLedgerBaseDtoManager drugDetailLedgerBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugDetailLedgerBaseDtoServiceConverter drugDetailLedgerBaseDtoServiceConverter;

    @PublicInterface(
            id = "d7d51014-29fa-4c6c-88b1-8da121f03dd3",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004620")
    @AutoGenerated(locked = false, uuid = "0605b430-7228-3a1a-bccc-26224ad410db")
    public List<DrugDetailLedgerBaseDto> getByDrugOriginSpecificationId(
            @NotNull(message = "药品产地规格id不能为空") String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "61094aa3-4389-4993-ad09-e29d4e07e067",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748518975140")
    @AutoGenerated(locked = false, uuid = "17bf88a0-688c-3a21-ac4d-2198a26bbca6")
    public List<DrugDetailLedgerBaseDto> getByDrugOriginSpecificationIds(
            @Valid @NotNull(message = "药品产地规格id不能为空") List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginSpecificationId = new ArrayList<>(new HashSet<>(drugOriginSpecificationId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugOriginSpecificationIds(
                        drugOriginSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "2751f325-f605-444b-af9d-8ff2342e7c1b",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004544")
    @AutoGenerated(locked = false, uuid = "1d1e1d05-dc44-3352-a025-9c3380e2e56e")
    public List<DrugDetailLedgerBaseDto> getByDrugPriceAdjustDetailIds(
            @Valid @NotNull(message = "调价单明细id不能为空") List<String> drugPriceAdjustDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugPriceAdjustDetailId = new ArrayList<>(new HashSet<>(drugPriceAdjustDetailId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugPriceAdjustDetailIds(
                        drugPriceAdjustDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "340fc436-c771-4487-9b4e-4c2b1a864404",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004606")
    @AutoGenerated(locked = false, uuid = "2a1074f4-0283-3e62-a027-175dea80c1b8")
    public DrugDetailLedgerBaseDto getByDrugStocktakingDetailId(
            @NotNull(message = "盘存单明细id不能为空") String drugStocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDetailLedgerBaseDto> ret =
                getByDrugStocktakingDetailIds(Arrays.asList(drugStocktakingDetailId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "0b1727af-2ee9-4fe1-980d-ac2fc95a0836",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004617")
    @AutoGenerated(locked = false, uuid = "2d88cc22-e61d-3541-86f7-415fbc1fadad")
    public List<DrugDetailLedgerBaseDto> getByDrugOrderDispenseDetailBatchIds(
            @Valid @NotNull(message = "摆药批次明细id不能为空") List<String> drugOrderDispenseDetailBatchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOrderDispenseDetailBatchId =
                new ArrayList<>(new HashSet<>(drugOrderDispenseDetailBatchId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugOrderDispenseDetailBatchIds(
                        drugOrderDispenseDetailBatchId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "5605801f-71c5-4c53-8602-82da303a0415",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004530")
    @AutoGenerated(locked = false, uuid = "3f77b753-aadf-319b-99d7-ab1ed0e8be50")
    public List<DrugDetailLedgerBaseDto> getByDrugOriginCodes(
            @Valid @NotNull(message = "药品产地编码不能为空") List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginCode = new ArrayList<>(new HashSet<>(drugOriginCode));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugOriginCodes(drugOriginCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "b032dec9-a060-41a5-82d6-80ab59b43860",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004634")
    @AutoGenerated(locked = false, uuid = "52659704-eadf-310e-a2a7-5d63aa71c887")
    public List<DrugDetailLedgerBaseDto> getByImportDetailId(
            @NotNull(message = "入库单明细id不能为空") String importDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByImportDetailIds(Arrays.asList(importDetailId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "fd84c8e4-d95e-4d0e-876d-1c3663bbfb30",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004645")
    @AutoGenerated(locked = false, uuid = "55f06d4f-1c8f-3ae9-8d0f-922ccc943d03")
    public List<DrugDetailLedgerBaseDto> getByStorageCodes(
            @Valid @NotNull(message = "库房编码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "146b7cfa-4a8f-45d6-b24d-39d6b6cf02e2",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004526")
    @AutoGenerated(locked = false, uuid = "*************-3013-a094-543ff1cc6362")
    public List<DrugDetailLedgerBaseDto> getByDrugOriginCode(
            @NotNull(message = "药品产地编码不能为空") String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "5480c8ab-84ca-4d2b-87ae-a2d3519825e2",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004599")
    @AutoGenerated(locked = false, uuid = "5a1ef9f9-cd77-38c6-ac5e-21a6e12a6e67")
    public List<DrugDetailLedgerBaseDto> getByExportImportId(
            @NotNull(message = "出入库方式id不能为空") String exportImportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByExportImportIds(Arrays.asList(exportImportId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "bd3adcbd-4d73-4aad-9bc3-2d3d1630de00",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004595")
    @AutoGenerated(locked = false, uuid = "5de4f185-2013-33ee-ba74-17039cc2673d")
    public List<DrugDetailLedgerBaseDto> getByExportImportIdsAndBusinessDocumentIds(
            @Valid @NotNull
                    List<UkExportImportIdBusinessDocumentIdEo>
                            ukExportImportIdBusinessDocumentIdEo) {
        List<BusinessDocumentIdAndExportImportId> businessDocumentIdAndExportImportId =
                ukExportImportIdBusinessDocumentIdEo.stream()
                        .map(
                                DrugDetailLedgerUkExportImportIdBusinessDocumentIdConverter
                                        ::convertFromUkExportImportIdBusinessDocumentIdToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByExportImportIdsAndBusinessDocumentIds(
                        businessDocumentIdAndExportImportId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "*************-4d6c-a705-e0fbe4dc1603",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004541")
    @AutoGenerated(locked = false, uuid = "62b88f27-51db-3a46-adc5-587ba5bbb66e")
    public List<DrugDetailLedgerBaseDto> getByDrugPriceAdjustDetailId(
            @NotNull(message = "调价单明细id不能为空") String drugPriceAdjustDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugPriceAdjustDetailIds(Arrays.asList(drugPriceAdjustDetailId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "83e08bf6-12f8-4dfb-b7e0-4e20efc44f18",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004588")
    @AutoGenerated(locked = false, uuid = "807adcfe-d7ab-3dd9-9840-bf69e26923f9")
    public List<DrugDetailLedgerBaseDto> getByDrugInventoryBatchIds(
            @Valid @NotNull(message = "批次库存id不能为空") List<String> drugInventoryBatchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugInventoryBatchId = new ArrayList<>(new HashSet<>(drugInventoryBatchId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugInventoryBatchIds(drugInventoryBatchId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "a6691e44-f0c7-4ae8-8b18-f95379774c30",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004533")
    @AutoGenerated(locked = false, uuid = "85be560c-da5d-3899-a27e-8b1df21a4917")
    public DrugDetailLedgerBaseDto getByExportDetailId(
            @NotNull(message = "出库单明细id不能为空") String exportDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDetailLedgerBaseDto> ret = getByExportDetailIds(Arrays.asList(exportDetailId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "6c7103c2-3ad6-4221-bf6d-14a6f504d755",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004591")
    @AutoGenerated(locked = false, uuid = "91fe8754-4431-3f58-b54d-198e5cb55c39")
    public List<DrugDetailLedgerBaseDto> getByExportImportIdAndBusinessDocumentId(
            @Valid @NotNull UkExportImportIdBusinessDocumentIdEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByExportImportIdsAndBusinessDocumentIds(Arrays.asList(var));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "c721d9eb-99d5-4da5-a506-2eba7a1a2ffa",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004638")
    @AutoGenerated(locked = false, uuid = "96c240dc-643f-3840-940b-87b0fb8eea05")
    public List<DrugDetailLedgerBaseDto> getByImportDetailIds(
            @Valid @NotNull(message = "入库单明细id不能为空") List<String> importDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        importDetailId = new ArrayList<>(new HashSet<>(importDetailId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByImportDetailIds(importDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "b512c4de-f303-4208-afad-dfbbb74d7fb9",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004566")
    @AutoGenerated(locked = false, uuid = "98645028-e9cd-3633-b3be-f9c88804accd")
    public List<DrugDetailLedgerBaseDto> getByDrugLossReportDetailIds(
            @Valid @NotNull(message = "报损单明细id不能为空") List<String> drugLossReportDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugLossReportDetailId = new ArrayList<>(new HashSet<>(drugLossReportDetailId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugLossReportDetailIds(drugLossReportDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "d475be22-dbb8-49b1-91b3-a5f25008b5a1",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004551")
    @AutoGenerated(locked = false, uuid = "a1524216-bc3f-3dfe-b75b-dc29b030124b")
    public List<DrugDetailLedgerBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "2dadc4bc-1c19-4c4c-8ea7-a8a9c1515485",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004641")
    @AutoGenerated(locked = false, uuid = "ab0923cf-f7f0-38be-9961-bf3b76019fd7")
    public List<DrugDetailLedgerBaseDto> getByStorageCode(
            @NotNull(message = "库房编码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "fc029b70-bc5e-4313-9b82-6072372293a8",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004580")
    @AutoGenerated(locked = false, uuid = "b698ca63-145d-3426-8658-66597f8b165a")
    public List<DrugDetailLedgerBaseDto> getByDrugBorrowDetailIds(
            @Valid @NotNull(message = "借还药明细id不能为空") List<String> drugBorrowDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugBorrowDetailId = new ArrayList<>(new HashSet<>(drugBorrowDetailId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugBorrowDetailIds(drugBorrowDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "907e13e2-d52d-49a3-bac8-6c5679bd46c0",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004602")
    @AutoGenerated(locked = false, uuid = "bdeda9de-510d-3197-b854-b1bd5887979b")
    public List<DrugDetailLedgerBaseDto> getByExportImportIds(
            @Valid @NotNull(message = "出入库方式id不能为空") List<String> exportImportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        exportImportId = new ArrayList<>(new HashSet<>(exportImportId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByExportImportIds(exportImportId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "e5cb44c2-c931-4d72-8510-72ab24c9f809",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "*************")
    @AutoGenerated(locked = false, uuid = "c9a4bcce-949f-35c3-8f79-c10f393e1519")
    public List<DrugDetailLedgerBaseDto> getByAccountingPeriodId(
            @NotNull(message = "会计期间ID不能为空") String accountingPeriodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAccountingPeriodIds(Arrays.asList(accountingPeriodId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "514543e1-ff76-4c32-9602-d38736110fac",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "*************")
    @AutoGenerated(locked = false, uuid = "cd4edba3-76ec-3aa9-8ca1-f803f3717d5d")
    public List<DrugDetailLedgerBaseDto> getByAccountingPeriodIds(
            @Valid @NotNull(message = "会计期间ID不能为空") List<String> accountingPeriodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        accountingPeriodId = new ArrayList<>(new HashSet<>(accountingPeriodId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByAccountingPeriodIds(accountingPeriodId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "b31f822d-4198-4a88-8526-560f7f06407b",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "*************")
    @AutoGenerated(locked = false, uuid = "d0f3d247-d808-3dca-abfe-9440eca936c9")
    public DrugDetailLedgerBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDetailLedgerBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "556e9c98-d7f5-43dd-ba35-0eda3b0c79fb",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004537")
    @AutoGenerated(locked = false, uuid = "d58cb4f5-5879-39f2-8c15-7517824fdfc2")
    public List<DrugDetailLedgerBaseDto> getByExportDetailIds(
            @Valid @NotNull(message = "出库单明细id不能为空") List<String> exportDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        exportDetailId = new ArrayList<>(new HashSet<>(exportDetailId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByExportDetailIds(exportDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "fc1f9d3c-8e43-49ca-90bf-988ccf2e758d",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004562")
    @AutoGenerated(locked = false, uuid = "deb54a8f-13e6-3cbe-917d-cccb571d0165")
    public DrugDetailLedgerBaseDto getByDrugLossReportDetailId(
            @NotNull(message = "报损单明细id不能为空") String drugLossReportDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDetailLedgerBaseDto> ret =
                getByDrugLossReportDetailIds(Arrays.asList(drugLossReportDetailId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "c900f1d4-960c-48e1-b58c-1e58c08191d5",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004609")
    @AutoGenerated(locked = false, uuid = "dfa771aa-3a68-3765-976c-c3aa370c6fc6")
    public List<DrugDetailLedgerBaseDto> getByDrugStocktakingDetailIds(
            @Valid @NotNull(message = "盘存单明细id不能为空") List<String> drugStocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugStocktakingDetailId = new ArrayList<>(new HashSet<>(drugStocktakingDetailId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugStocktakingDetailIds(
                        drugStocktakingDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "057e9b8c-5cad-486b-822b-872a74de9a37",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004573")
    @AutoGenerated(locked = false, uuid = "e0f98ba6-5b3d-30c4-bd06-2863f1c628de")
    public List<DrugDetailLedgerBaseDto> getByMonthlyFinancialReportIds(
            @Valid @NotNull(message = "结转id不能为空") List<String> monthlyFinancialReportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        monthlyFinancialReportId = new ArrayList<>(new HashSet<>(monthlyFinancialReportId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByMonthlyFinancialReportIds(
                        monthlyFinancialReportId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "75c76ed1-d4cb-4706-baa2-f688d0b901e9",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004584")
    @AutoGenerated(locked = false, uuid = "e2e3c51a-6e0e-3f27-9c2a-7d77b49cca8e")
    public List<DrugDetailLedgerBaseDto> getByDrugInventoryBatchId(
            @NotNull(message = "批次库存id不能为空") String drugInventoryBatchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugInventoryBatchIds(Arrays.asList(drugInventoryBatchId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "8a40b861-dc4d-4137-a2ef-f41eb55a46c9",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004577")
    @AutoGenerated(locked = false, uuid = "e6a9f688-b3f2-30d2-a8a3-2f0461d5e3eb")
    public DrugDetailLedgerBaseDto getByDrugBorrowDetailId(
            @NotNull(message = "借还药明细id不能为空") String drugBorrowDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDetailLedgerBaseDto> ret =
                getByDrugBorrowDetailIds(Arrays.asList(drugBorrowDetailId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "661cd3d5-d55a-4118-a8fd-1118a4a368ab",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004613")
    @AutoGenerated(locked = false, uuid = "ef244200-6542-3ac3-8ae1-f752cd25cbcb")
    public List<DrugDetailLedgerBaseDto> getByDrugOrderDispenseDetailBatchId(
            @NotNull(message = "摆药批次明细id不能为空") String drugOrderDispenseDetailBatchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOrderDispenseDetailBatchIds(Arrays.asList(drugOrderDispenseDetailBatchId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "862adb81-a2c4-4188-b926-169ff38ea2c7",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004559")
    @AutoGenerated(locked = false, uuid = "f48dd585-7d3f-3a23-8ea3-ee0f630ffc8b")
    public List<DrugDetailLedgerBaseDto> getByDrugPrescriptionDispenseDetailBatchIds(
            @Valid @NotNull(message = "处方发药批次明细id不能为空")
                    List<String> drugPrescriptionDispenseDetailBatchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugPrescriptionDispenseDetailBatchId =
                new ArrayList<>(new HashSet<>(drugPrescriptionDispenseDetailBatchId));
        List<DrugDetailLedgerBaseDto> drugDetailLedgerBaseDtoList =
                drugDetailLedgerBaseDtoManager.getByDrugPrescriptionDispenseDetailBatchIds(
                        drugPrescriptionDispenseDetailBatchId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugDetailLedgerBaseDtoServiceConverter.DrugDetailLedgerBaseDtoConverter(
                drugDetailLedgerBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "04bf3cce-ee81-4382-a494-8ad93bcafbed",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004555")
    @AutoGenerated(locked = false, uuid = "f5e3484e-2c3e-3a25-ab2b-ed118fee49ab")
    public List<DrugDetailLedgerBaseDto> getByDrugPrescriptionDispenseDetailBatchId(
            @NotNull(message = "处方发药批次明细id不能为空") String drugPrescriptionDispenseDetailBatchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugPrescriptionDispenseDetailBatchIds(
                Arrays.asList(drugPrescriptionDispenseDetailBatchId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "59a2e27d-668f-4795-a38d-f4eea40d6ec2",
            module = "drug_report",
            moduleId = "a2ee32f7-17c4-4f47-9318-9e648ef230a3",
            pubRpc = true,
            version = "1748592004569")
    @AutoGenerated(locked = false, uuid = "ff906149-2bcf-3d48-8284-e8bcf18f90fc")
    public List<DrugDetailLedgerBaseDto> getByMonthlyFinancialReportId(
            @NotNull(message = "结转id不能为空") String monthlyFinancialReportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByMonthlyFinancialReportIds(Arrays.asList(monthlyFinancialReportId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
