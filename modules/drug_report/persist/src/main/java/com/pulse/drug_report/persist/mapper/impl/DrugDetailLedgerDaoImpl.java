package com.pulse.drug_report.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.drug_report.persist.dos.DrugDetailLedger;
import com.pulse.drug_report.persist.mapper.DrugDetailLedgerDao;
import com.pulse.drug_report.persist.mapper.mybatis.DrugDetailLedgerMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "635a7f0a-e4e6-4588-aa04-1fb5ad52e488|ENTITY|DAO")
public class DrugDetailLedgerDaoImpl implements DrugDetailLedgerDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugDetailLedgerMapper drugDetailLedgerMapper;

    @AutoGenerated(locked = true, uuid = "12fc732f-40dc-3cf6-91a4-77d58f5114e1")
    @Override
    public List<DrugDetailLedger> getByExportDetailIds(List<String> exportDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("export_detail_id", exportDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "1c354cbf-c68e-3bc8-8e97-7f73b817113d")
    @Override
    public List<DrugDetailLedger> getByDrugOriginCode(String drugOriginCode) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_code", drugOriginCode).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "1dc9322b-51f5-3dfa-9338-61c9861672e3")
    @Override
    public List<DrugDetailLedger> getByImportDetailId(String importDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("import_detail_id", importDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "21e8a40d-8cf3-3fee-8d44-b64fe2f67eb3")
    @Override
    public List<DrugDetailLedger> getByStorageCode(String storageCode) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("storage_code", storageCode).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "2a995768-38ea-3d17-8d57-3ba4361472b4")
    @Override
    public List<DrugDetailLedger> getByDrugStocktakingDetailIds(
            List<String> drugStocktakingDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_stocktaking_detail_id", drugStocktakingDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "37dac4bc-f5b1-3549-b31b-59a2fe25829f")
    @Override
    public List<DrugDetailLedger> getByExportImportIdAndBusinessDocumentId(
            String exportImportId, String businessDocumentId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("export_import_id", exportImportId)
                .eq("business_document_id", businessDocumentId)
                .orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "4c3053c1-22cd-3239-85f0-f3668480c7a2")
    @Override
    public List<DrugDetailLedger> getByStorageCodes(List<String> storageCode) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("storage_code", storageCode).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "531ce128-7601-3b1e-a9fa-186984733d2f")
    @Override
    public DrugDetailLedger getByExportDetailId(String exportDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("export_detail_id", exportDetailId);
        return drugDetailLedgerMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "55a902a9-8b15-3c56-9c90-636c1d90bad0")
    @Override
    public List<DrugDetailLedger> getByDrugOrderDispenseDetailBatchIds(
            List<String> drugOrderDispenseDetailBatchId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("drug_order_dispense_detail_batch_id", drugOrderDispenseDetailBatchId)
                .orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "5994d827-ca66-37f3-bac4-dd8533257d89")
    @Override
    public List<DrugDetailLedger> getByAccountingPeriodId(String accountingPeriodId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("accounting_period_id", accountingPeriodId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "6142e841-cd31-3e7b-af8e-691f52dada42")
    @Override
    public List<DrugDetailLedger> getByMonthlyFinancialReportId(String monthlyFinancialReportId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("monthly_financial_report_id", monthlyFinancialReportId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "7377f093-7592-3444-bc5d-55e0ebf6977f")
    @Override
    public List<DrugDetailLedger> getByDrugPrescriptionDispenseDetailBatchId(
            String drugPrescriptionDispenseDetailBatchId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq(
                        "drug_prescription_dispense_detail_batch_id",
                        drugPrescriptionDispenseDetailBatchId)
                .orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "75c668c6-d615-3dd6-9d9e-d84bff5e9666")
    @Override
    public List<DrugDetailLedger> getByExportImportIds(List<String> exportImportId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("export_import_id", exportImportId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "86d371c6-3c4b-384f-8e4a-f5be0798de23")
    @Override
    public List<DrugDetailLedger> getByAccountingPeriodIds(List<String> accountingPeriodId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("accounting_period_id", accountingPeriodId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "909e5b4f-5487-3d0d-97c8-94836fdd2774")
    @Override
    public List<DrugDetailLedger> getByDrugInventoryBatchId(String drugInventoryBatchId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_inventory_batch_id", drugInventoryBatchId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9398f096-e826-3c50-912a-05bc1ab2174c")
    @Override
    public List<DrugDetailLedger> getByDrugPriceAdjustDetailId(String drugPriceAdjustDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_price_adjust_detail_id", drugPriceAdjustDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9ebdddc1-496d-37a9-95b5-12f99b9e3696")
    @Override
    public DrugDetailLedger getByDrugBorrowDetailId(String drugBorrowDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_borrow_detail_id", drugBorrowDetailId);
        return drugDetailLedgerMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a08dc734-c982-341f-a3dd-c9d6b3065859")
    @Override
    public DrugDetailLedger getByDrugStocktakingDetailId(String drugStocktakingDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_stocktaking_detail_id", drugStocktakingDetailId);
        return drugDetailLedgerMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a6cdd09c-7705-3654-b1c4-374e6c237f5a")
    @Override
    public DrugDetailLedger getByDrugLossReportDetailId(String drugLossReportDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_loss_report_detail_id", drugLossReportDetailId);
        return drugDetailLedgerMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a96c14f7-28d9-368f-ab97-96757d29b750")
    @Override
    public List<DrugDetailLedger> getByImportDetailIds(List<String> importDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("import_detail_id", importDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "aae34944-0c08-3265-bf82-f56418ed65ad")
    @Override
    public DrugDetailLedger getById(String id) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugDetailLedgerMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b7b11d1c-420c-3d1c-8ce1-307afc30e496")
    @Override
    public List<DrugDetailLedger> getByDrugOriginCodes(List<String> drugOriginCode) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_code", drugOriginCode).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b92f87b0-1901-357b-9224-60439b2e50f4")
    @Override
    public List<DrugDetailLedger> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "ba4d9965-e9d4-3d43-bfd4-a5b7b9883bc6")
    @Override
    public List<DrugDetailLedger> getByDrugPriceAdjustDetailIds(
            List<String> drugPriceAdjustDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_price_adjust_detail_id", drugPriceAdjustDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "bf101a66-ac53-3384-8869-3db9896e54f7")
    @Override
    public List<DrugDetailLedger> getByDrugOrderDispenseDetailBatchId(
            String drugOrderDispenseDetailBatchId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("drug_order_dispense_detail_batch_id", drugOrderDispenseDetailBatchId)
                .orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d7cacee3-ec2b-3c83-9894-7ff2cc4f578a")
    @Override
    public List<DrugDetailLedger> getByExportImportIdsAndBusinessDocumentIds(
            List<DrugDetailLedger.BusinessDocumentIdAndExportImportId> varList) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        for (int i = 0; i < varList.size(); i++) {
            DrugDetailLedger.BusinessDocumentIdAndExportImportId var = varList.get(i);
            if (i == 0) {
                queryWrapper
                        .eq("export_import_id", var.getExportImportId())
                        .eq("business_document_id", var.getBusinessDocumentId());
            } else {
                queryWrapper
                        .or()
                        .eq("export_import_id", var.getExportImportId())
                        .eq("business_document_id", var.getBusinessDocumentId());
            }
        }
        queryWrapper.orderByAsc("id");

        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "db0f4a32-a5bb-3d96-824a-3d661c73145b")
    @Override
    public List<DrugDetailLedger> getByIds(List<String> id) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "de617e9a-12d8-3a02-9e4a-0b1c741e7c96")
    @Override
    public List<DrugDetailLedger> getByDrugPrescriptionDispenseDetailBatchIds(
            List<String> drugPrescriptionDispenseDetailBatchId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in(
                        "drug_prescription_dispense_detail_batch_id",
                        drugPrescriptionDispenseDetailBatchId)
                .orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "dfbb5811-b9c1-3365-b847-a35126095377")
    @Override
    public List<DrugDetailLedger> getByMonthlyFinancialReportIds(
            List<String> monthlyFinancialReportId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("monthly_financial_report_id", monthlyFinancialReportId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e1021182-d53c-33bd-a66d-872c4e708cf8")
    @Override
    public List<DrugDetailLedger> getByDrugLossReportDetailIds(
            List<String> drugLossReportDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_loss_report_detail_id", drugLossReportDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e19af8b6-95a3-39d4-bafb-4d8e4d4e846c")
    @Override
    public List<DrugDetailLedger> getByExportImportId(String exportImportId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("export_import_id", exportImportId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e520191f-ceab-388d-9cd6-9b9ff14a8627")
    @Override
    public List<DrugDetailLedger> getByDrugOriginSpecificationId(String drugOriginSpecificationId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "ef7848bc-ac17-305f-bdd6-125aee4a34b4")
    @Override
    public List<DrugDetailLedger> getByDrugBorrowDetailIds(List<String> drugBorrowDetailId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_borrow_detail_id", drugBorrowDetailId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f95e2ba2-3a04-3f06-8331-72648e255292")
    @Override
    public List<DrugDetailLedger> getByDrugInventoryBatchIds(List<String> drugInventoryBatchId) {
        QueryWrapper<DrugDetailLedger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_inventory_batch_id", drugInventoryBatchId).orderByAsc("id");
        return drugDetailLedgerMapper.selectList(queryWrapper);
    }
}
