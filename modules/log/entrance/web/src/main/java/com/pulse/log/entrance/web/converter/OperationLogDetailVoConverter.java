package com.pulse.log.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.log.entrance.web.query.assembler.OperationLogDetailVoDataAssembler;
import com.pulse.log.entrance.web.vo.OperationLogDetailVo;
import com.pulse.log.manager.dto.OperationLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OperationLogDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "f76220b2-d955-45a8-bf58-a06bcd2f24eb|VO|CONVERTER")
public class OperationLogDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OperationLogDetailVoDataAssembler operationLogDetailVoDataAssembler;

    /** 使用默认方式组装OperationLogDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "8a0c56f5-ac98-37fd-9663-9022a5f50127")
    public OperationLogDetailVo convertAndAssembleData(OperationLogBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OperationLogBaseDto转换成OperationLogDetailVo */
    @AutoGenerated(locked = true, uuid = "e51ec43c-1e09-3f4f-8486-30359efaa71f")
    public OperationLogDetailVo convertToOperationLogDetailVo(OperationLogBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOperationLogDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OperationLogDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "f541f0f8-5eba-39dc-b4a5-644b86b9808e")
    public List<OperationLogDetailVo> convertAndAssembleDataList(
            List<OperationLogBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, OperationLogDetailVo> voMap =
                convertToOperationLogDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        operationLogDetailVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OperationLogBaseDto转换成OperationLogDetailVo */
    @AutoGenerated(locked = false, uuid = "f76220b2-d955-45a8-bf58-a06bcd2f24eb-converter-Map")
    public Map<OperationLogBaseDto, OperationLogDetailVo> convertToOperationLogDetailVoMap(
            List<OperationLogBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<OperationLogBaseDto, OperationLogDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OperationLogDetailVo vo = new OperationLogDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setBatchId(dto.getBatchId());
                                            vo.setBusinessScene(dto.getBusinessScene());
                                            vo.setOperationType(dto.getOperationType());
                                            vo.setSourceBusinessId(dto.getSourceBusinessId());
                                            vo.setPreOperationInfo(dto.getPreOperationInfo());
                                            vo.setPostOperationInfo(dto.getPostOperationInfo());
                                            vo.setRemark(dto.getRemark());
                                            vo.setOperatorUserId(dto.getOperatorUserId());
                                            vo.setOperatorRoleId(dto.getOperatorRoleId());
                                            vo.setOperatorOrganizationId(
                                                    dto.getOperatorOrganizationId());
                                            vo.setOperatorIp(dto.getOperatorIp());
                                            vo.setOperationResult(dto.getOperationResult());
                                            vo.setDigitalSignature(dto.getDigitalSignature());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setOperationTime(dto.getOperationTime());
                                            vo.setOperatorStaffId(dto.getOperatorStaffId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OperationLogBaseDto转换成OperationLogDetailVo */
    @AutoGenerated(locked = true, uuid = "f76220b2-d955-45a8-bf58-a06bcd2f24eb-converter-list")
    public List<OperationLogDetailVo> convertToOperationLogDetailVoList(
            List<OperationLogBaseDto> dtoList) {
        return new ArrayList<>(convertToOperationLogDetailVoMap(dtoList).values());
    }
}
