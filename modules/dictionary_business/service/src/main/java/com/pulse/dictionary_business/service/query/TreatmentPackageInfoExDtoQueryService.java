package com.pulse.dictionary_business.service.query;

import com.pulse.dictionary_business.manager.converter.TreatmentPackageInfoExDtoConverter;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.persist.qto.SearchTreatmentPackageExQto;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.pulse.dictionary_business.service.index.entity.SearchTreatmentPackageExQtoService;
import com.pulse.dictionary_business.service.query.assembler.TreatmentPackageInfoExDtoDataAssembler;
import com.pulse.dictionary_business.service.query.assembler.TreatmentPackageInfoExDtoDataAssembler.TreatmentPackageInfoExDtoDataHolder;
import com.pulse.dictionary_business.service.query.collector.TreatmentPackageInfoExDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** TreatmentPackageInfoExDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "ac379b09-f46c-3489-8c86-5478b943911b")
public class TreatmentPackageInfoExDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchTreatmentPackageExQtoService searchTreatmentPackageExQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExDtoConverter treatmentPackageInfoExDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExDtoDataAssembler treatmentPackageInfoExDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExDtoDataCollector treatmentPackageInfoExDtoDataCollector;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "3fbe6710-a5a0-3fd6-8385-c67819dd792e")
    private List<TreatmentPackageInfoExDto> toDtoList(
            List<String> ids, TreatmentPackageInfoExDtoDataHolder dataHolder) {
        List<TreatmentPackageInfoDto> baseDtoList = treatmentPackageInfoDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, TreatmentPackageInfoExDto> dtoMap =
                treatmentPackageInfoExDtoConverter
                        .convertFromTreatmentPackageInfoDtoToTreatmentPackageInfoExDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        TreatmentPackageInfoExDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchTreatmentPackageExQto查询TreatmentPackageInfoExDto列表,上限500 */
    @PublicInterface(id = "4fd3c557-b351-4f18-bd09-730cff3b46f3", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "97a739ff-7ad1-3f91-a6cc-02323cec5e2a")
    public List<TreatmentPackageInfoExDto> searchTreatmentPackageEx(
            @Valid @NotNull SearchTreatmentPackageExQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchTreatmentPackageExQtoService.query(qto);
        TreatmentPackageInfoExDtoDataHolder dataHolder = new TreatmentPackageInfoExDtoDataHolder();
        List<TreatmentPackageInfoExDto> result = toDtoList(ids, dataHolder);
        treatmentPackageInfoExDtoDataCollector.collectDataDefault(dataHolder);
        treatmentPackageInfoExDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
