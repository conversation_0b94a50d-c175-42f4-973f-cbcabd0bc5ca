package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.TreatmentPackageItemDetailDtoManager;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.service.converter.TreatmentPackageItemDetailDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "135755c3-4f90-4408-9d86-87116330d49c|DTO|SERVICE")
public class TreatmentPackageItemDetailDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDetailDtoManager treatmentPackageItemDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDetailDtoServiceConverter
            treatmentPackageItemDetailDtoServiceConverter;

    @PublicInterface(id = "351f46c8-8eb5-4029-916d-aabb5c27a9c9", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "30a78f1f-bf00-3caa-9617-b5cbef647ab5")
    public List<TreatmentPackageItemDetailDto> getByItemId(
            @NotNull(message = "项目ID不能为空") String itemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByItemIds(Arrays.asList(itemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "18e3bbbc-8b1c-48d3-9354-febae7ab4aae", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "571b2405-fb62-3c60-bfaf-be3d6d4526fc")
    public List<TreatmentPackageItemDetailDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList =
                treatmentPackageItemDetailDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatmentPackageItemDetailDtoServiceConverter.TreatmentPackageItemDetailDtoConverter(
                treatmentPackageItemDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f5fa52dc-14d2-47bd-8ceb-b0b4d3590292", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "86f488ac-e831-330c-ada1-59c8fc680272")
    public List<TreatmentPackageItemDetailDto> getByTreatmentPackageIds(
            @Valid @NotNull(message = "治疗套餐ID不能为空") List<String> treatmentPackageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        treatmentPackageId = new ArrayList<>(new HashSet<>(treatmentPackageId));
        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList =
                treatmentPackageItemDetailDtoManager.getByTreatmentPackageIds(treatmentPackageId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatmentPackageItemDetailDtoServiceConverter.TreatmentPackageItemDetailDtoConverter(
                treatmentPackageItemDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "56668c02-b1db-4ab5-947a-ddb1d22c7e29", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "90543315-e6ea-31b1-a76d-a2c406a45b2b")
    public TreatmentPackageItemDetailDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageItemDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "f9ae77e9-fd23-4c97-b606-79f7df32e85e", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "d6f86d93-b7c6-3179-a771-9f02893c99dc")
    public List<TreatmentPackageItemDetailDto> getByItemIds(
            @Valid @NotNull(message = "项目ID不能为空") List<String> itemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        itemId = new ArrayList<>(new HashSet<>(itemId));
        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList =
                treatmentPackageItemDetailDtoManager.getByItemIds(itemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatmentPackageItemDetailDtoServiceConverter.TreatmentPackageItemDetailDtoConverter(
                treatmentPackageItemDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "9c88639f-72fa-4da5-b720-9e1aeaa3fd3d", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "e2538e26-a45f-3c6d-9dc2-4c314eb07758")
    public List<TreatmentPackageItemDetailDto> getByTreatmentPackageId(
            @NotNull(message = "治疗套餐ID不能为空") String treatmentPackageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByTreatmentPackageIds(Arrays.asList(treatmentPackageId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
