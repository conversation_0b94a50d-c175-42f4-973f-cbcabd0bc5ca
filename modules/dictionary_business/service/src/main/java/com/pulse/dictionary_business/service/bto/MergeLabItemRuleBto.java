package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_business.common.enums.AgeUnitEnum;
import com.pulse.dictionary_business.service.bto.MergeLabItemRuleBto.LabItemRuleBto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> LabItem
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "65436ba4-230a-4f2c-bb7b-cd1a7c11943d|BTO|DEFINITION")
public class MergeLabItemRuleBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3acb06cf-899a-4683-a9a8-0cfe40b9a618")
    private String id;

    @Valid
    @AutoGenerated(locked = true, uuid = "4a0cb10c-e5a6-43e1-9d5e-4ec95c04fcb7")
    private LabItemRuleBto labItemRuleBto;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setLabItemRuleBto(MergeLabItemRuleBto.LabItemRuleBto labItemRuleBto) {
        this.__$validPropertySet.add("labItemRuleBto");
        this.labItemRuleBto = labItemRuleBto;
    }

    /**
     * <b>[源自]</b> LabItemRule
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
     */
    @Getter
    @NoArgsConstructor
    public static class LabItemRuleBto {
        /** 加急类型 */
        @AutoGenerated(locked = true, uuid = "87381eed-8731-4e9a-b645-198bc61710bd")
        private String urgentType;

        /** 限制年龄上限 */
        @AutoGenerated(locked = true, uuid = "9df52cc8-c764-4938-a24e-fda1c0ae6519")
        private Long limitAgeMax;

        /** 年龄限制单位 */
        @AutoGenerated(locked = true, uuid = "da2679e0-56bc-4d40-be4e-560e94d04024")
        private AgeUnitEnum limitAgeUnit;

        /** 限制年龄下限 */
        @AutoGenerated(locked = true, uuid = "82515cff-c4dd-48f2-b064-65601ebb6260")
        private Long limitAgeMin;

        /** 性别限制 */
        @AutoGenerated(locked = true, uuid = "afbce7d9-ac86-4c0e-949e-e80d20eff67b")
        private String limitGender;

        /** 检验项目说明 */
        @AutoGenerated(locked = true, uuid = "389e5453-5a90-44ca-8a96-a8c14608a248")
        private String labItemInstruction;

        /** 适用开单科室列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "95e27aa5-c9ad-42cf-bbe9-a31879c6facd")
        private List<String> applicableOrderDepartmentIdList;

        /** 适用开单医生列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "2caecb83-bc81-4fa2-8b84-d95902f30191")
        private List<String> applicableOrderDoctorIdList;

        /** 不允许的开单部门ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "cec64487-f496-4d47-b8d0-ee20ff1aed6d")
        private List<String> notAllowedOrderDepartmentIdList;

        /** 不允许的开单医生ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "32666288-32ca-4ce6-803c-a5499d77548c")
        private List<String> notAllowedOrderDoctorIdList;

        /** 检验项目ID */
        @AutoGenerated(locked = true, uuid = "16a4a57b-d140-467e-9dcd-3c7af82ddb69")
        private String labItemId;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setUrgentType(String urgentType) {
            this.__$validPropertySet.add("urgentType");
            this.urgentType = urgentType;
        }

        @AutoGenerated(locked = true)
        public void setLimitAgeMax(Long limitAgeMax) {
            this.__$validPropertySet.add("limitAgeMax");
            this.limitAgeMax = limitAgeMax;
        }

        @AutoGenerated(locked = true)
        public void setLimitAgeUnit(AgeUnitEnum limitAgeUnit) {
            this.__$validPropertySet.add("limitAgeUnit");
            this.limitAgeUnit = limitAgeUnit;
        }

        @AutoGenerated(locked = true)
        public void setLimitAgeMin(Long limitAgeMin) {
            this.__$validPropertySet.add("limitAgeMin");
            this.limitAgeMin = limitAgeMin;
        }

        @AutoGenerated(locked = true)
        public void setLimitGender(String limitGender) {
            this.__$validPropertySet.add("limitGender");
            this.limitGender = limitGender;
        }

        @AutoGenerated(locked = true)
        public void setLabItemInstruction(String labItemInstruction) {
            this.__$validPropertySet.add("labItemInstruction");
            this.labItemInstruction = labItemInstruction;
        }

        @AutoGenerated(locked = true)
        public void setApplicableOrderDepartmentIdList(
                List<String> applicableOrderDepartmentIdList) {
            this.__$validPropertySet.add("applicableOrderDepartmentIdList");
            this.applicableOrderDepartmentIdList = applicableOrderDepartmentIdList;
        }

        @AutoGenerated(locked = true)
        public void setApplicableOrderDoctorIdList(List<String> applicableOrderDoctorIdList) {
            this.__$validPropertySet.add("applicableOrderDoctorIdList");
            this.applicableOrderDoctorIdList = applicableOrderDoctorIdList;
        }

        @AutoGenerated(locked = true)
        public void setNotAllowedOrderDepartmentIdList(
                List<String> notAllowedOrderDepartmentIdList) {
            this.__$validPropertySet.add("notAllowedOrderDepartmentIdList");
            this.notAllowedOrderDepartmentIdList = notAllowedOrderDepartmentIdList;
        }

        @AutoGenerated(locked = true)
        public void setNotAllowedOrderDoctorIdList(List<String> notAllowedOrderDoctorIdList) {
            this.__$validPropertySet.add("notAllowedOrderDoctorIdList");
            this.notAllowedOrderDoctorIdList = notAllowedOrderDoctorIdList;
        }

        @AutoGenerated(locked = true)
        public void setLabItemId(String labItemId) {
            this.__$validPropertySet.add("labItemId");
            this.labItemId = labItemId;
        }
    }
}
