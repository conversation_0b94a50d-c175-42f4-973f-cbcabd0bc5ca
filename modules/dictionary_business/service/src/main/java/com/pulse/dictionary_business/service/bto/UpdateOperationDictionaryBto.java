package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> OperationDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "2a35b136-b09f-4979-8dcb-12e6c76207d0|BTO|DEFINITION")
public class UpdateOperationDictionaryBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 代码版本 */
    @AutoGenerated(locked = true, uuid = "2e311be3-8b9a-4caf-86fd-48c3511004a6")
    private String codeVersion;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "eaebc9e6-6ccc-4ca4-9b97-c79b003a3904")
    private String id;

    /** 手术切口等级编码 */
    @AutoGenerated(locked = true, uuid = "a5b70687-f68f-4680-a318-c2ceed5425ec")
    private String incisionLevelCode;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1db3ca58-3491-48df-b11b-316bdec6a2e6")
    private InputCodeEo inputCode;

    /** 手术医保国家编码 */
    @AutoGenerated(locked = true, uuid = "8c91181b-aeac-4dee-b9ef-8768c41f3bc9")
    private String insuranceNationalCode;

    /** 手术医保国家名称 */
    @AutoGenerated(locked = true, uuid = "b19c3698-10d8-4a08-be19-e720a419540f")
    private String insuranceNationalName;

    /** 手术属性 */
    @Valid
    @AutoGenerated(locked = true, uuid = "878635ff-a040-4974-9be9-0f92d58e5bb4")
    private List<String> operationAttributeList;

    /** 手术操作代码 */
    @AutoGenerated(locked = true, uuid = "88fd506a-8589-41d6-a30f-93c58489203a")
    private String operationCode;

    /** 手术操作描述 */
    @AutoGenerated(locked = true, uuid = "a81943e4-af55-4850-b51b-222badd6f2de")
    private String operationDescription;

    /** 手术等级 */
    @AutoGenerated(locked = true, uuid = "f38a581d-dd11-47fb-9f38-b9915b745ae3")
    private String operationLevelCode;

    /** 手术操作名称 */
    @AutoGenerated(locked = true, uuid = "53d1ee7c-b699-4e02-9b8b-662be0aa7ead")
    private String operationName;

    /** 手术国标等级 */
    @AutoGenerated(locked = true, uuid = "c64406a4-5017-4cca-813a-2560f37efc7a")
    private String operationNationalLevel;

    /** 手术操作类型 */
    @AutoGenerated(locked = true, uuid = "de675fdc-878d-4af7-8703-05ecb168ebd4")
    private String operationType;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "88b9f767-56a0-4e07-9ecc-0628c7d09a22")
    private Long sortNumber;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "e7619b1e-6e8b-48c0-9b76-547acb529f39")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCodeVersion(String codeVersion) {
        this.__$validPropertySet.add("codeVersion");
        this.codeVersion = codeVersion;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setIncisionLevelCode(String incisionLevelCode) {
        this.__$validPropertySet.add("incisionLevelCode");
        this.incisionLevelCode = incisionLevelCode;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setInsuranceNationalCode(String insuranceNationalCode) {
        this.__$validPropertySet.add("insuranceNationalCode");
        this.insuranceNationalCode = insuranceNationalCode;
    }

    @AutoGenerated(locked = true)
    public void setInsuranceNationalName(String insuranceNationalName) {
        this.__$validPropertySet.add("insuranceNationalName");
        this.insuranceNationalName = insuranceNationalName;
    }

    @AutoGenerated(locked = true)
    public void setOperationAttribute(List<String> operationAttribute) {
        this.__$validPropertySet.add("operationAttributeList");
        this.operationAttributeList = operationAttribute;
    }

    @AutoGenerated(locked = true)
    public void setOperationAttributeList(List<String> operationAttributeList) {
        this.__$validPropertySet.add("operationAttributeList");
        this.operationAttributeList = operationAttributeList;
    }

    @AutoGenerated(locked = true)
    public void setOperationCode(String operationCode) {
        this.__$validPropertySet.add("operationCode");
        this.operationCode = operationCode;
    }

    @AutoGenerated(locked = true)
    public void setOperationDescription(String operationDescription) {
        this.__$validPropertySet.add("operationDescription");
        this.operationDescription = operationDescription;
    }

    @AutoGenerated(locked = true)
    public void setOperationLevelCode(String operationLevelCode) {
        this.__$validPropertySet.add("operationLevelCode");
        this.operationLevelCode = operationLevelCode;
    }

    @AutoGenerated(locked = true)
    public void setOperationName(String operationName) {
        this.__$validPropertySet.add("operationName");
        this.operationName = operationName;
    }

    @AutoGenerated(locked = true)
    public void setOperationNationalLevel(String operationNationalLevel) {
        this.__$validPropertySet.add("operationNationalLevel");
        this.operationNationalLevel = operationNationalLevel;
    }

    @AutoGenerated(locked = true)
    public void setOperationType(String operationType) {
        this.__$validPropertySet.add("operationType");
        this.operationType = operationType;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
