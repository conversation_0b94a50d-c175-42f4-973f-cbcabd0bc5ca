package com.pulse.dictionary_business.service.query.collector;

import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExclusionOrderTypeItemDetailBaseDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.ExclusionOrderTypeItemDetailBaseDtoService;
import com.pulse.dictionary_business.service.query.assembler.ExclusionOrderTypeItemDetailClinicItemExtDtoDataAssembler.ExclusionOrderTypeItemDetailClinicItemExtDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ExclusionOrderTypeItemDetailClinicItemExtDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "f47403d6-80f1-34a5-920b-b37867faacb9")
public class ExclusionOrderTypeItemDetailClinicItemExtDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExclusionOrderTypeItemDetailBaseDtoService exclusionOrderTypeItemDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExclusionOrderTypeItemDetailClinicItemExtDtoDataCollector
            exclusionOrderTypeItemDetailClinicItemExtDtoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "3a1841db-56f2-3569-9694-69c725ac32cb")
    public void collectDataDefault(
            ExclusionOrderTypeItemDetailClinicItemExtDtoDataHolder dataHolder) {
        exclusionOrderTypeItemDetailClinicItemExtDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "7c6b386f-33b0-3760-8181-ea9758ded6d4")
    private void fillDataWhenNecessary(
            ExclusionOrderTypeItemDetailClinicItemExtDtoDataHolder dataHolder) {
        List<ExclusionOrderTypeItemDetailBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.clinicItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExclusionOrderTypeItemDetailBaseDto::getClinicItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ClinicItemDictionaryBaseDto> baseDtoList =
                    clinicItemDictionaryBaseDtoService
                            .getByClinicItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            ClinicItemDictionaryBaseDto::getClinicItemId))
                            .collect(Collectors.toList());
            Map<String, ClinicItemDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ClinicItemDictionaryBaseDto::getClinicItemId,
                                            Function.identity()));
            dataHolder.clinicItem =
                    rootDtoList.stream()
                            .map(ExclusionOrderTypeItemDetailBaseDto::getClinicItemId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
