package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_business.common.enums.ExclusionTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExclusionOrderType
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "bfe0de45-1e16-4780-9f31-6e1d239e8ebd|BTO|DEFINITION")
public class SaveExclusionOrderTypeItemDetailBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "249466d3-1263-4a41-acc6-9e1296e0ac4a")
    private List<SaveExclusionOrderTypeItemDetailBto.ExclusionOrderTypeItemDetailBto>
            exclusionOrderTypeItemDetailBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f9353b3d-3861-4247-aaba-73d1a5186216")
    private String id;

    @AutoGenerated(locked = true)
    public void setExclusionOrderTypeItemDetailBtoList(
            List<SaveExclusionOrderTypeItemDetailBto.ExclusionOrderTypeItemDetailBto>
                    exclusionOrderTypeItemDetailBtoList) {
        this.__$validPropertySet.add("exclusionOrderTypeItemDetailBtoList");
        this.exclusionOrderTypeItemDetailBtoList = exclusionOrderTypeItemDetailBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExclusionOrderTypeItemDetail
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ExclusionOrderTypeItemDetailBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "8dc7390c-0877-4ceb-8f01-807ab1a64844")
        private String id;

        /** 诊疗项目id */
        @AutoGenerated(locked = true, uuid = "a88b6da3-f320-44c4-bff1-c1702480cc91")
        private String clinicItemId;

        /** 排斥类型 */
        @AutoGenerated(locked = true, uuid = "f9b80b95-3eaa-49cb-9f34-accb75bdacb5")
        private ExclusionTypeEnum exclusionType;

        /** 描述 */
        @AutoGenerated(locked = true, uuid = "2e1e4363-7cf6-45e0-ad7c-8e51e8df061a")
        private String description;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "994d7305-fc09-4fe0-ab41-c869f233e146")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "b99d6f08-7bc0-4463-a939-d03a01deca40")
        private String createdBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setClinicItemId(String clinicItemId) {
            this.__$validPropertySet.add("clinicItemId");
            this.clinicItemId = clinicItemId;
        }

        @AutoGenerated(locked = true)
        public void setExclusionType(ExclusionTypeEnum exclusionType) {
            this.__$validPropertySet.add("exclusionType");
            this.exclusionType = exclusionType;
        }

        @AutoGenerated(locked = true)
        public void setDescription(String description) {
            this.__$validPropertySet.add("description");
            this.description = description;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }
    }
}
