package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ExamItemDeviceDtoManager;
import com.pulse.dictionary_business.manager.dto.ExamItemDeviceDto;
import com.pulse.dictionary_business.service.converter.ExamItemDeviceDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "4ada6c22-b1d2-491b-8534-2b5031574e23|DTO|SERVICE")
public class ExamItemDeviceDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ExamItemDeviceDtoManager examItemDeviceDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ExamItemDeviceDtoServiceConverter examItemDeviceDtoServiceConverter;

    @PublicInterface(id = "ac83c7b7-9d78-432d-bd39-a134928dc09d", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "167f8e64-954f-332f-aaa5-4f6069f30e55")
    public List<ExamItemDeviceDto> getByExamItemId(
            @NotNull(message = "检查项目id不能为空") String examItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByExamItemIds(Arrays.asList(examItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "ce568041-43c1-4dd1-b607-873fff5bf219", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "4c23ab06-3bed-30f0-8dfb-f7c287400987")
    public ExamItemDeviceDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamItemDeviceDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c243c86e-4c16-4b22-ba38-6af6db0ca0f4", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "a4a24c15-e10d-3a6d-a362-cfe968c737c5")
    public List<ExamItemDeviceDto> getByExamDeviceIds(
            @Valid @NotNull(message = "检查设备ID不能为空") List<String> examDeviceId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        examDeviceId = new ArrayList<>(new HashSet<>(examDeviceId));
        List<ExamItemDeviceDto> examItemDeviceDtoList =
                examItemDeviceDtoManager.getByExamDeviceIds(examDeviceId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return examItemDeviceDtoServiceConverter.ExamItemDeviceDtoConverter(examItemDeviceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "df2a0326-7db5-4e8d-a480-50b7883052a6", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "aaf915fb-2945-3814-86bc-a3f94b0f4940")
    public List<ExamItemDeviceDto> getByExamDeviceId(
            @NotNull(message = "检查设备ID不能为空") String examDeviceId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByExamDeviceIds(Arrays.asList(examDeviceId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "eeb85e78-db56-4119-bfb5-20588ee607d0", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "beb5647d-9b37-3e2b-a33c-d32a396af1ff")
    public List<ExamItemDeviceDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ExamItemDeviceDto> examItemDeviceDtoList = examItemDeviceDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return examItemDeviceDtoServiceConverter.ExamItemDeviceDtoConverter(examItemDeviceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "35820f1b-bf3c-4282-8541-f9f1a1b80288", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "c7835ae9-5f73-330b-bb5c-3cb36fb0400b")
    public List<ExamItemDeviceDto> getByExamItemIds(
            @Valid @NotNull(message = "检查项目id不能为空") List<String> examItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        examItemId = new ArrayList<>(new HashSet<>(examItemId));
        List<ExamItemDeviceDto> examItemDeviceDtoList =
                examItemDeviceDtoManager.getByExamItemIds(examItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return examItemDeviceDtoServiceConverter.ExamItemDeviceDtoConverter(examItemDeviceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
