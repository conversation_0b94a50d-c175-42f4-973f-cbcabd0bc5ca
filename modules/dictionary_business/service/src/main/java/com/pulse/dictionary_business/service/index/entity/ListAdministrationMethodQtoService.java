package com.pulse.dictionary_business.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.dictionary_business.persist.mapper.ListAdministrationMethodQtoDao;
import com.pulse.dictionary_business.persist.qto.ListAdministrationMethodQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "6fa467c3-ddd5-437f-b37d-36d16895dd1a|QTO|SERVICE")
public class ListAdministrationMethodQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListAdministrationMethodQtoDao listAdministrationMethodMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "6fa467c3-ddd5-437f-b37d-36d16895dd1a-query-paged")
    public List<String> queryPaged(ListAdministrationMethodQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listAdministrationMethodMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListAdministrationMethodQto qto) {
        return listAdministrationMethodMapper.count(qto);
    }
}
