package com.pulse.dictionary_business.service.query.collector;

import com.pulse.dictionary_business.manager.converter.TreatmentPackageItemDetailDtoConverter;
import com.pulse.dictionary_business.manager.dto.TreatChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatChargeItemBaseDtoService;
import com.pulse.dictionary_business.service.TreatItemBaseDtoService;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.pulse.dictionary_business.service.TreatmentPackageItemDtoService;
import com.pulse.dictionary_business.service.query.assembler.TreatmentPackageInfoExDtoDataAssembler.TreatmentPackageInfoExDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TreatmentPackageInfoExDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "2bd49f8b-c5e3-3323-9ca0-5ccd577ddede")
public class TreatmentPackageInfoExDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TreatChargeItemBaseDtoService treatChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoService treatItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExDtoDataCollector treatmentPackageInfoExDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailDtoConverter treatmentPackageItemDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoService treatmentPackageItemDtoService;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "6b9dad49-0531-3687-9462-7fcd77ad03c3")
    private void fillDataWhenNecessary(TreatmentPackageInfoExDtoDataHolder dataHolder) {
        List<TreatmentPackageInfoDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.treatmentPackageItemDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TreatmentPackageInfoDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatmentPackageItemDto> baseDtoList =
                    treatmentPackageItemDtoService
                            .getByTreatmentPackageIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(TreatmentPackageItemDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TreatmentPackageItemDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            TreatmentPackageItemDto::getTreatmentPackageId));
            Map<String, TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoMap =
                    treatmentPackageItemDetailDtoConverter
                            .convertFromTreatmentPackageItemDtoToTreatmentPackageItemDetailDto(
                                    baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            TreatmentPackageItemDetailDto::getId,
                                            Function.identity()));
            dataHolder.treatmentPackageItemDetailList =
                    rootDtoList.stream()
                            .map(TreatmentPackageInfoDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    treatmentPackageItemDetailDtoMap.get(
                                                            baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.treatmentPackageItemDetailList2ItemInfo == null) {
            Set<String> ids =
                    dataHolder.treatmentPackageItemDetailList.keySet().stream()
                            .map(TreatmentPackageItemDto::getItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatItemBaseDto> baseDtoList =
                    treatItemBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TreatItemBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, TreatItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(TreatItemBaseDto::getId, Function.identity()));
            dataHolder.treatmentPackageItemDetailList2ItemInfo =
                    dataHolder.treatmentPackageItemDetailList.keySet().stream()
                            .map(TreatmentPackageItemDto::getItemId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.treatmentPackageItemDetailList2ItemInfo2TreatChargeItemList == null) {
            Set<String> ids =
                    dataHolder.treatmentPackageItemDetailList2ItemInfo.stream()
                            .map(TreatItemBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatChargeItemBaseDto> baseDtoList =
                    treatChargeItemBaseDtoService.getByTreatItemIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TreatChargeItemBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TreatChargeItemBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TreatChargeItemBaseDto::getTreatItemId));
            dataHolder.treatmentPackageItemDetailList2ItemInfo2TreatChargeItemList =
                    dataHolder.treatmentPackageItemDetailList2ItemInfo.stream()
                            .map(TreatItemBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "b71a41cf-d650-3ccc-bd36-f17bfdac9076")
    public void collectDataDefault(TreatmentPackageInfoExDtoDataHolder dataHolder) {
        treatmentPackageInfoExDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
