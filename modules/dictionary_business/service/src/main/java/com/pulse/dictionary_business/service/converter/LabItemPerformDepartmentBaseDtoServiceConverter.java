package com.pulse.dictionary_business.service.converter;

import com.pulse.dictionary_business.manager.dto.LabItemPerformDepartmentBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "879298a6-7076-3c09-bdd5-b239872dd411")
public class LabItemPerformDepartmentBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<LabItemPerformDepartmentBaseDto> LabItemPerformDepartmentBaseDtoConverter(
            List<LabItemPerformDepartmentBaseDto> labItemPerformDepartmentBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return labItemPerformDepartmentBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
