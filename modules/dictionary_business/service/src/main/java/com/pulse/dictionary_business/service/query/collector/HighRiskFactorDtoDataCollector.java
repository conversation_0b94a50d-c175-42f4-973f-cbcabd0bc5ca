package com.pulse.dictionary_business.service.query.collector;

import com.pulse.dictionary_business.manager.dto.HighRiskDiagnosisBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskDrugBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskFactorRuleBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskItemBaseDto;
import com.pulse.dictionary_business.service.HighRiskDiagnosisBaseDtoService;
import com.pulse.dictionary_business.service.HighRiskDrugBaseDtoService;
import com.pulse.dictionary_business.service.HighRiskFactorRuleBaseDtoService;
import com.pulse.dictionary_business.service.HighRiskItemBaseDtoService;
import com.pulse.dictionary_business.service.query.assembler.HighRiskFactorDtoDataAssembler.HighRiskFactorDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装HighRiskFactorDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "cc7dfb67-98fb-38a1-8853-fbbc1ae1dcde")
public class HighRiskFactorDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private HighRiskDiagnosisBaseDtoService highRiskDiagnosisBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskDrugBaseDtoService highRiskDrugBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskFactorDtoDataCollector highRiskFactorDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskFactorRuleBaseDtoService highRiskFactorRuleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskItemBaseDtoService highRiskItemBaseDtoService;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "b297a7ba-ddff-32f4-b3a9-c7a82ced410a")
    public void collectDataDefault(HighRiskFactorDtoDataHolder dataHolder) {
        highRiskFactorDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "b40b9548-8cfc-3412-9add-d95b95fd3946")
    private void fillDataWhenNecessary(HighRiskFactorDtoDataHolder dataHolder) {
        List<HighRiskFactorRuleBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.highRiskDiagnosisList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<HighRiskDiagnosisBaseDto> baseDtoList =
                    highRiskDiagnosisBaseDtoService
                            .getByHighRiskFactorIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(HighRiskDiagnosisBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<HighRiskDiagnosisBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            HighRiskDiagnosisBaseDto::getHighRiskFactorId));
            dataHolder.highRiskDiagnosisList =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.highRiskDrugList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<HighRiskDrugBaseDto> baseDtoList =
                    highRiskDrugBaseDtoService.getByHighRiskFactorIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(HighRiskDrugBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<HighRiskDrugBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            HighRiskDrugBaseDto::getHighRiskFactorId));
            dataHolder.highRiskDrugList =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.highRiskItemList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<HighRiskItemBaseDto> baseDtoList =
                    highRiskItemBaseDtoService.getByHighRiskFactorIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(HighRiskItemBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<HighRiskItemBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            HighRiskItemBaseDto::getHighRiskFactorId));
            dataHolder.highRiskItemList =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
