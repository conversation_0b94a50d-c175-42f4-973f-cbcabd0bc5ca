package com.pulse.dictionary_business.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.TreatChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TreatmentPackageInfoExDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "fed2063c-f2e8-3021-b4db-2bb29c0bfb11")
public class TreatmentPackageInfoExDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    /** 组装treatmentPackageItemDetailList数据 */
    @AutoGenerated(locked = true, uuid = "67664023-045d-35d0-acd2-2f0c97e35096")
    private void assembleTreatmentPackageItemDetailListData(
            TreatmentPackageInfoExDtoDataAssembler.TreatmentPackageInfoExDtoDataHolder dataHolder) {
        Map<String, TreatItemBaseDto> treatmentPackageItemDetailList2ItemInfo =
                dataHolder.treatmentPackageItemDetailList2ItemInfo.stream()
                        .collect(
                                Collectors.toMap(
                                        TreatItemBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        for (Map.Entry<TreatmentPackageItemDto, TreatmentPackageItemDetailDto>
                treatmentPackageItemDetailList :
                        dataHolder.treatmentPackageItemDetailList.entrySet()) {
            TreatmentPackageItemDetailDto dto = treatmentPackageItemDetailList.getValue();
            dto.setItemInfo(
                    Optional.ofNullable(
                                    treatmentPackageItemDetailList2ItemInfo.get(
                                            treatmentPackageItemDetailList.getKey().getItemId()))
                            .orElse(null));
        }
        assembleTreatmentPackageItemDetailList2ItemInfoData(dataHolder);
    }

    /** 批量自定义组装TreatmentPackageInfoExDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "8a6bb536-3230-3e0c-a8b5-08b66c97d5bb")
    public void assembleDataCustomized(List<TreatmentPackageInfoExDto> dataList) {
        // 自定义数据组装

    }

    /** 组装TreatmentPackageInfoExDto数据 */
    @AutoGenerated(locked = true, uuid = "d5043267-bd50-3b94-9100-b4bbc06c5658")
    public void assembleData(
            List<TreatmentPackageInfoExDto> dtoList,
            TreatmentPackageInfoExDtoDataAssembler.TreatmentPackageInfoExDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, TreatmentPackageInfoDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(
                                Collectors.toMap(
                                        TreatmentPackageInfoDto::getId, Function.identity()));

        Map<String, List<Pair<TreatmentPackageItemDto, TreatmentPackageItemDetailDto>>>
                treatmentPackageItemDetailList =
                        dataHolder.treatmentPackageItemDetailList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getTreatmentPackageId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .treatmentPackageItemDetailList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (TreatmentPackageInfoExDto dto : dtoList) {
            dto.setTreatmentPackageItemDetailList(
                    Optional.ofNullable(treatmentPackageItemDetailList.get(dto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleTreatmentPackageItemDetailListData(dataHolder);

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 组装treatmentPackageItemDetailList2ItemInfo数据 */
    @AutoGenerated(locked = true, uuid = "f2478e11-a06b-3c48-9c26-b001e531d94e")
    private void assembleTreatmentPackageItemDetailList2ItemInfoData(
            TreatmentPackageInfoExDtoDataAssembler.TreatmentPackageInfoExDtoDataHolder dataHolder) {
        Map<String, List<TreatChargeItemBaseDto>>
                treatmentPackageItemDetailList2ItemInfo2TreatChargeItemList =
                        dataHolder
                                .treatmentPackageItemDetailList2ItemInfo2TreatChargeItemList
                                .stream()
                                .collect(
                                        Collectors.groupingBy(
                                                TreatChargeItemBaseDto::getTreatItemId));
        for (TreatItemBaseDto dto : dataHolder.treatmentPackageItemDetailList2ItemInfo) {
            dto.setTreatChargeItemList(
                    Optional.ofNullable(
                                    treatmentPackageItemDetailList2ItemInfo2TreatChargeItemList.get(
                                            dto.getId()))
                            .orElse(new ArrayList<>()));
        }
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class TreatmentPackageInfoExDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatmentPackageInfoDto> rootBaseDtoList;

        /** 持有dto字段treatChargeItemList的Dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatChargeItemBaseDto>
                treatmentPackageItemDetailList2ItemInfo2TreatChargeItemList;

        /** 持有dto字段itemInfo的Dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatItemBaseDto> treatmentPackageItemDetailList2ItemInfo;

        /** 持有dto字段treatmentPackageItemDetailList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TreatmentPackageItemDto, TreatmentPackageItemDetailDto>
                treatmentPackageItemDetailList;
    }
}
