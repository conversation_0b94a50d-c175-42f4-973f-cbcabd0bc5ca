package com.pulse.dictionary_business.service.index.entity;

import com.pulse.dictionary_business.persist.mapper.SearchTreatmentPackageExQtoDao;
import com.pulse.dictionary_business.persist.qto.SearchTreatmentPackageExQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "ddeb1d7b-9b38-41b7-8bc1-da4d67d7f64f|QTO|SERVICE")
public class SearchTreatmentPackageExQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchTreatmentPackageExQtoDao searchTreatmentPackageExMapper;

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchTreatmentPackageExQto qto) {
        return searchTreatmentPackageExMapper.count(qto);
    }

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "ddeb1d7b-9b38-41b7-8bc1-da4d67d7f64f-query")
    public List<String> query(SearchTreatmentPackageExQto qto) {
        return searchTreatmentPackageExMapper.query(qto);
    }
}
