package com.pulse.dictionary_business.service.converter;

import com.pulse.dictionary_business.manager.dto.ExamItemBodyBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "e38e85b0-9571-3c62-af6a-f8f0ecc35ce1")
public class ExamItemBodyBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ExamItemBodyBaseDto> ExamItemBodyBaseDtoConverter(
            List<ExamItemBodyBaseDto> examItemBodyBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return examItemBodyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
