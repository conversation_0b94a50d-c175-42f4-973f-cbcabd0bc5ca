package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.TreatmentPackageInfoExDtoManager;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.service.converter.TreatmentPackageInfoExDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "e7ab6558-9f07-411e-83bd-2c20a8eacf5d|DTO|SERVICE")
public class TreatmentPackageInfoExDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageInfoExDtoManager treatmentPackageInfoExDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageInfoExDtoServiceConverter treatmentPackageInfoExDtoServiceConverter;

    @PublicInterface(id = "40798f86-a78d-4647-b1a5-711e16ef35f9", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "00241b2f-1b92-34e4-872e-8ce9b75d94e8")
    public List<TreatmentPackageInfoExDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<TreatmentPackageInfoExDto> treatmentPackageInfoExDtoList =
                treatmentPackageInfoExDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatmentPackageInfoExDtoServiceConverter.TreatmentPackageInfoExDtoConverter(
                treatmentPackageInfoExDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "d60fae97-1914-409a-84b9-afc129fd1407", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "f0dfa390-89d4-3ef4-9b32-eed0b7e0106a")
    public TreatmentPackageInfoExDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageInfoExDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
