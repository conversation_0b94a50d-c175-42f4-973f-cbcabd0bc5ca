package com.pulse.dictionary_business.service.converter;

import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "f384ded2-355d-3f6e-94f3-be0e68564903")
public class TreatmentPackageInfoExDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<TreatmentPackageInfoExDto> TreatmentPackageInfoExDtoConverter(
            List<TreatmentPackageInfoExDto> treatmentPackageInfoExDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return treatmentPackageInfoExDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
