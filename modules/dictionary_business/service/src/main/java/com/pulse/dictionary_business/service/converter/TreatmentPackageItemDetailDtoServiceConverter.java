package com.pulse.dictionary_business.service.converter;

import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "47f6abca-031b-3a5c-b8f6-9cf4fe310c7f")
public class TreatmentPackageItemDetailDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<TreatmentPackageItemDetailDto> TreatmentPackageItemDetailDtoConverter(
            List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return treatmentPackageItemDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
