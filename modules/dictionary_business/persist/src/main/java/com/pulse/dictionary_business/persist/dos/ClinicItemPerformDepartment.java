package com.pulse.dictionary_business.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@TableName(value = "clinic_item_perform_department", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "80f42619-96d2-4261-be94-ea89d589dfac|ENTITY|DEFINITION")
public class ClinicItemPerformDepartment {
    @AutoGenerated(locked = true, uuid = "a84f8334-5aa5-44bf-be0c-80b491680d3d")
    @TableField(value = "clinic_item_id")
    private String clinicItemId;

    @AutoGenerated(locked = true, uuid = "fe250e9a-ccd6-5b69-ad64-fce6f02e55d3")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "65a53943-5248-4e28-b3ba-0e938a61e16a")
    @TableId(value = "id")
    private Long id;

    @AutoGenerated(locked = true, uuid = "3c4f474c-b73a-4774-9491-0942cfe88ab7")
    @TableField(value = "order_department_id")
    private String orderDepartmentId;

    @AutoGenerated(locked = true, uuid = "2baf5a4e-2773-4283-8b27-8edfd5ffc9b1")
    @TableField(value = "organization_id")
    private String organizationId;

    @AutoGenerated(locked = true, uuid = "71a98453-5314-447c-aff9-f8144aac9a7e")
    @TableField(value = "perform_department_id")
    private String performDepartmentId;

    @AutoGenerated(locked = true, uuid = "e998b398-1df3-5ae2-aa16-21120df42fe2")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "a6764636-c077-4a08-9d2a-57dc97efa5e8")
    @TableField(value = "use_organization_type")
    private String useOrganizationType;

    @Valid
    @AutoGenerated(locked = true, uuid = "f9c012de-c8e8-48ce-ac83-3cb5fdd92585")
    @TableField(value = "use_scope_list", typeHandler = JacksonTypeHandler.class)
    private List<String> useScopeList;
}
