package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.dos.ExamCatalogDictionary;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "adf04bd3-8b64-46c5-9e27-377284b3893e|ENTITY|IDAO")
public interface ExamCatalogDictionaryDao {

    @AutoGenerated(locked = true, uuid = "9f54de35-153e-38a6-bfbf-3d673427fb46")
    List<ExamCatalogDictionary> getByExamTypeId(String examTypeId);

    @AutoGenerated(locked = true, uuid = "c6213894-10a4-3f09-89c4-4a715fea7649")
    List<ExamCatalogDictionary> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "c9d4e50b-ee39-3f26-8534-67bbeeb3d912")
    List<ExamCatalogDictionary> getByExamTypeIds(List<String> examTypeId);

    @AutoGenerated(locked = true, uuid = "d8756d55-b534-3fce-a92a-5ac699728a34")
    ExamCatalogDictionary getById(String id);
}
