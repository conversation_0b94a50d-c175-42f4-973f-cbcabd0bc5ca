package com.pulse.dictionary_business.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.dictionary_business.persist.dos.TreatItem;
import com.pulse.dictionary_business.persist.mapper.TreatItemDao;
import com.pulse.dictionary_business.persist.mapper.mybatis.TreatItemMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "54fd6a63-50de-4bca-9673-e9fb190500fd|ENTITY|DAO")
public class TreatItemDaoImpl implements TreatItemDao {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemMapper treatItemMapper;

    @AutoGenerated(locked = true, uuid = "18dbbcf0-e225-3657-83d6-cc3520109612")
    @Override
    public TreatItem getById(String id) {
        QueryWrapper<TreatItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return treatItemMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "5f1cc742-5f7d-3a49-8d9a-575f38204870")
    @Override
    public List<TreatItem> getByTreatClassId(String treatClassId) {
        QueryWrapper<TreatItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("treat_class_id", treatClassId).orderByAsc("id");
        return treatItemMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f30d1a4d-54f8-3a40-839f-8e08f741b999")
    @Override
    public List<TreatItem> getByTreatClassIds(List<String> treatClassId) {
        QueryWrapper<TreatItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("treat_class_id", treatClassId).orderByAsc("id");
        return treatItemMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f6b51a9e-d835-32ad-a80a-d3534ef4c45e")
    @Override
    public List<TreatItem> getByIds(List<String> id) {
        QueryWrapper<TreatItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return treatItemMapper.selectList(queryWrapper);
    }
}
