package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.qto.SearchTreatItemQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "b648b5a2-3040-422b-bc2a-3f3aa836c68e|QTO|DAO")
public class SearchTreatItemQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查找治疗项目 */
    @AutoGenerated(locked = false, uuid = "b648b5a2-3040-422b-bc2a-3f3aa836c68e-count")
    public Integer count(SearchTreatItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(treat_item.id) FROM treat_item WHERE treat_item.treat_class_id ="
                    + " #treatClassIdIs AND treat_item.treat_way_id = #treatWayIdIs AND"
                    + " treat_item.treat_room_id = #treatRoomIdIs AND treat_item.device_id ="
                    + " #deviceIdIs AND treat_item.treat_class_id = #treatClassIdIs AND"
                    + " treat_item.treat_item_name like #treatItemNameLike AND"
                    + " treat_item.perform_dept_id = #performDeptIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getTreatItemNameLike() == null) {
            conditionToRemove.add("#treatItemNameLike");
        }
        if (qto.getPerformDeptIdIs() == null) {
            conditionToRemove.add("#performDeptIdIs");
        }
        if (qto.getTreatClassIdIs() == null) {
            conditionToRemove.add("#treatClassIdIs");
        }
        if (qto.getTreatClassIdIs() == null) {
            conditionToRemove.add("#treatClassIdIs");
        }
        if (qto.getTreatRoomIdIs() == null) {
            conditionToRemove.add("#treatRoomIdIs");
        }
        if (qto.getTreatWayIdIs() == null) {
            conditionToRemove.add("#treatWayIdIs");
        }
        if (qto.getDeviceIdIs() == null) {
            conditionToRemove.add("#deviceIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#treatItemNameLike", "?")
                        .replace("#performDeptIdIs", "?")
                        .replace("#treatClassIdIs", "?")
                        .replace("#treatClassIdIs", "?")
                        .replace("#treatRoomIdIs", "?")
                        .replace("#treatWayIdIs", "?")
                        .replace("#deviceIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#treatItemNameLike")) {
                sqlParams.add("%" + qto.getTreatItemNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#performDeptIdIs")) {
                sqlParams.add(qto.getPerformDeptIdIs());
            } else if (paramName.equalsIgnoreCase("#treatClassIdIs")) {
                sqlParams.add(qto.getTreatClassIdIs());
            } else if (paramName.equalsIgnoreCase("#treatClassIdIs")) {
                sqlParams.add(qto.getTreatClassIdIs());
            } else if (paramName.equalsIgnoreCase("#treatRoomIdIs")) {
                sqlParams.add(qto.getTreatRoomIdIs());
            } else if (paramName.equalsIgnoreCase("#treatWayIdIs")) {
                sqlParams.add(qto.getTreatWayIdIs());
            } else if (paramName.equalsIgnoreCase("#deviceIdIs")) {
                sqlParams.add(qto.getDeviceIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查找治疗项目 */
    @AutoGenerated(locked = false, uuid = "b648b5a2-3040-422b-bc2a-3f3aa836c68e-query-all")
    public List<String> query(SearchTreatItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT treat_item.id FROM treat_item WHERE treat_item.treat_class_id ="
                    + " #treatClassIdIs AND treat_item.treat_way_id = #treatWayIdIs AND"
                    + " treat_item.treat_room_id = #treatRoomIdIs AND treat_item.device_id ="
                    + " #deviceIdIs AND treat_item.treat_class_id = #treatClassIdIs AND"
                    + " treat_item.treat_item_name like #treatItemNameLike AND"
                    + " treat_item.perform_dept_id = #performDeptIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getTreatItemNameLike() == null) {
            conditionToRemove.add("#treatItemNameLike");
        }
        if (qto.getPerformDeptIdIs() == null) {
            conditionToRemove.add("#performDeptIdIs");
        }
        if (qto.getTreatClassIdIs() == null) {
            conditionToRemove.add("#treatClassIdIs");
        }
        if (qto.getTreatClassIdIs() == null) {
            conditionToRemove.add("#treatClassIdIs");
        }
        if (qto.getTreatRoomIdIs() == null) {
            conditionToRemove.add("#treatRoomIdIs");
        }
        if (qto.getTreatWayIdIs() == null) {
            conditionToRemove.add("#treatWayIdIs");
        }
        if (qto.getDeviceIdIs() == null) {
            conditionToRemove.add("#deviceIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#treatItemNameLike", "?")
                        .replace("#performDeptIdIs", "?")
                        .replace("#treatClassIdIs", "?")
                        .replace("#treatClassIdIs", "?")
                        .replace("#treatRoomIdIs", "?")
                        .replace("#treatWayIdIs", "?")
                        .replace("#deviceIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#treatItemNameLike")) {
                sqlParams.add("%" + qto.getTreatItemNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#performDeptIdIs")) {
                sqlParams.add(qto.getPerformDeptIdIs());
            } else if (paramName.equalsIgnoreCase("#treatClassIdIs")) {
                sqlParams.add(qto.getTreatClassIdIs());
            } else if (paramName.equalsIgnoreCase("#treatClassIdIs")) {
                sqlParams.add(qto.getTreatClassIdIs());
            } else if (paramName.equalsIgnoreCase("#treatRoomIdIs")) {
                sqlParams.add(qto.getTreatRoomIdIs());
            } else if (paramName.equalsIgnoreCase("#treatWayIdIs")) {
                sqlParams.add(qto.getTreatWayIdIs());
            } else if (paramName.equalsIgnoreCase("#deviceIdIs")) {
                sqlParams.add(qto.getDeviceIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  treat_item.id asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查找治疗项目 */
    @AutoGenerated(locked = false, uuid = "b648b5a2-3040-422b-bc2a-3f3aa836c68e-query-paginate")
    public List<String> queryPaged(SearchTreatItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT treat_item.id FROM treat_item WHERE treat_item.treat_class_id ="
                    + " #treatClassIdIs AND treat_item.treat_way_id = #treatWayIdIs AND"
                    + " treat_item.treat_room_id = #treatRoomIdIs AND treat_item.device_id ="
                    + " #deviceIdIs AND treat_item.treat_class_id = #treatClassIdIs AND"
                    + " treat_item.treat_item_name like #treatItemNameLike AND"
                    + " treat_item.perform_dept_id = #performDeptIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getTreatItemNameLike() == null) {
            conditionToRemove.add("#treatItemNameLike");
        }
        if (qto.getPerformDeptIdIs() == null) {
            conditionToRemove.add("#performDeptIdIs");
        }
        if (qto.getTreatClassIdIs() == null) {
            conditionToRemove.add("#treatClassIdIs");
        }
        if (qto.getTreatClassIdIs() == null) {
            conditionToRemove.add("#treatClassIdIs");
        }
        if (qto.getTreatRoomIdIs() == null) {
            conditionToRemove.add("#treatRoomIdIs");
        }
        if (qto.getTreatWayIdIs() == null) {
            conditionToRemove.add("#treatWayIdIs");
        }
        if (qto.getDeviceIdIs() == null) {
            conditionToRemove.add("#deviceIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#treatItemNameLike", "?")
                        .replace("#performDeptIdIs", "?")
                        .replace("#treatClassIdIs", "?")
                        .replace("#treatClassIdIs", "?")
                        .replace("#treatRoomIdIs", "?")
                        .replace("#treatWayIdIs", "?")
                        .replace("#deviceIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#treatItemNameLike")) {
                sqlParams.add("%" + qto.getTreatItemNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#performDeptIdIs")) {
                sqlParams.add(qto.getPerformDeptIdIs());
            } else if (paramName.equalsIgnoreCase("#treatClassIdIs")) {
                sqlParams.add(qto.getTreatClassIdIs());
            } else if (paramName.equalsIgnoreCase("#treatClassIdIs")) {
                sqlParams.add(qto.getTreatClassIdIs());
            } else if (paramName.equalsIgnoreCase("#treatRoomIdIs")) {
                sqlParams.add(qto.getTreatRoomIdIs());
            } else if (paramName.equalsIgnoreCase("#treatWayIdIs")) {
                sqlParams.add(qto.getTreatWayIdIs());
            } else if (paramName.equalsIgnoreCase("#deviceIdIs")) {
                sqlParams.add(qto.getDeviceIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  treat_item.id asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
