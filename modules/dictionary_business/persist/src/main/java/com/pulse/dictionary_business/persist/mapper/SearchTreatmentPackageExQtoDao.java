package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.qto.SearchTreatmentPackageExQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "ddeb1d7b-9b38-41b7-8bc1-da4d67d7f64f|QTO|DAO")
public class SearchTreatmentPackageExQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 检索治疗套餐 */
    @AutoGenerated(locked = false, uuid = "ddeb1d7b-9b38-41b7-8bc1-da4d67d7f64f-count")
    public Integer count(SearchTreatmentPackageExQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(treatment_package_info.id) FROM treatment_package_info WHERE EXISTS ("
                    + " SELECT \"treatmentPackageItemDetailList\".id FROM treatment_package_item"
                    + " \"treatmentPackageItemDetailList\" LEFT JOIN treat_item"
                    + " \"treatmentPackageItemDetailList_itemInfo\" on"
                    + " \"treatmentPackageItemDetailList\".item_id ="
                    + " \"treatmentPackageItemDetailList_itemInfo\".id WHERE"
                    + " treatment_package_info.id ="
                    + " \"treatmentPackageItemDetailList\".treatment_package_id AND ("
                    + " \"treatmentPackageItemDetailList_itemInfo\".treat_item_name ="
                    + " #treatmentPackageItemDetailListItemInfoTreatItemNameIs ) )";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getTreatmentPackageItemDetailListItemInfoTreatItemNameIs() == null) {
            conditionToRemove.add("#treatmentPackageItemDetailListItemInfoTreatItemNameIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#treatmentPackageItemDetailListItemInfoTreatItemNameIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase(
                    "#treatmentPackageItemDetailListItemInfoTreatItemNameIs")) {
                sqlParams.add(qto.getTreatmentPackageItemDetailListItemInfoTreatItemNameIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 检索治疗套餐 */
    @AutoGenerated(locked = false, uuid = "ddeb1d7b-9b38-41b7-8bc1-da4d67d7f64f-query-all")
    public List<String> query(SearchTreatmentPackageExQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT treatment_package_info.id FROM treatment_package_info WHERE EXISTS ( SELECT"
                    + " \"treatmentPackageItemDetailList\".id FROM treatment_package_item"
                    + " \"treatmentPackageItemDetailList\" LEFT JOIN treat_item"
                    + " \"treatmentPackageItemDetailList_itemInfo\" on"
                    + " \"treatmentPackageItemDetailList\".item_id ="
                    + " \"treatmentPackageItemDetailList_itemInfo\".id WHERE"
                    + " treatment_package_info.id ="
                    + " \"treatmentPackageItemDetailList\".treatment_package_id AND ("
                    + " \"treatmentPackageItemDetailList_itemInfo\".treat_item_name ="
                    + " #treatmentPackageItemDetailListItemInfoTreatItemNameIs ) )";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getTreatmentPackageItemDetailListItemInfoTreatItemNameIs() == null) {
            conditionToRemove.add("#treatmentPackageItemDetailListItemInfoTreatItemNameIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#treatmentPackageItemDetailListItemInfoTreatItemNameIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase(
                    "#treatmentPackageItemDetailListItemInfoTreatItemNameIs")) {
                sqlParams.add(qto.getTreatmentPackageItemDetailListItemInfoTreatItemNameIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  treatment_package_info.sort_number asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
