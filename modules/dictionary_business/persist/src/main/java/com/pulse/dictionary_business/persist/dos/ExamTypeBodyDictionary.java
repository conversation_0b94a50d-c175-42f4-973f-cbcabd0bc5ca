package com.pulse.dictionary_business.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@TableName(value = "exam_type_body_dictionary", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "eee01caa-d84e-4c37-833d-20c81a91a840|ENTITY|DEFINITION")
public class ExamTypeBodyDictionary {
    @AutoGenerated(locked = true, uuid = "abcc79da-821a-5c70-bf75-043442c9fabf")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "ed6d2f0b-4ecf-4c2d-823c-341948f224b5")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "dfb40fe2-d1c2-41b7-bb20-a4e2ff134a22")
    @TableField(value = "exam_body_code")
    private String examBodyCode;

    @AutoGenerated(locked = true, uuid = "a082e06b-f718-41aa-81d9-05712ed4460b")
    @TableField(value = "exam_body_name")
    private String examBodyName;

    @AutoGenerated(locked = true, uuid = "d764fd69-8f80-4636-b950-a591520f3e8a")
    @TableField(value = "exam_type_id")
    private String examTypeId;

    @AutoGenerated(locked = true, uuid = "9d1ddf9f-432b-44b1-800a-ecb742480688")
    @TableId(value = "id")
    private String id;

    @Valid
    @AutoGenerated(locked = true, uuid = "b67eb602-d14e-40d0-9f64-66119115fad6")
    @TableField(value = "input_code", typeHandler = JacksonTypeHandler.class)
    private InputCodeEo inputCode;

    @AutoGenerated(locked = true, uuid = "e12ba9dc-a6a6-448b-b304-9d2935edef2f")
    @TableField(value = "sort_number")
    private Long sortNumber;

    @AutoGenerated(locked = true, uuid = "7112a8d2-2785-59a5-91b5-5a25b8e5948d")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "30bbd3a3-43fe-4159-a70b-09152423d344")
    @TableField(value = "updated_by")
    private String updatedBy;
}
