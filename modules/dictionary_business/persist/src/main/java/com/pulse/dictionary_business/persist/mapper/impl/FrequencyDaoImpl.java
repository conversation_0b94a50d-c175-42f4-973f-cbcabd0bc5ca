package com.pulse.dictionary_business.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.dictionary_business.persist.dos.Frequency;
import com.pulse.dictionary_business.persist.mapper.FrequencyDao;
import com.pulse.dictionary_business.persist.mapper.mybatis.FrequencyMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "5b012227-55e5-40e0-a190-82c0f07a4b2b|ENTITY|DAO")
public class FrequencyDaoImpl implements FrequencyDao {
    @AutoGenerated(locked = true)
    @Resource
    private FrequencyMapper frequencyMapper;

    @AutoGenerated(locked = true, uuid = "9d4636b5-f839-3c44-9105-b5ede0dac290")
    @Override
    public Frequency getByDisplayName(String displayName) {
        QueryWrapper<Frequency> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("display_name", displayName);
        return frequencyMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9df482d1-afa5-3f21-a021-2b689531c68d")
    @Override
    public Frequency getByCode(String code) {
        QueryWrapper<Frequency> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return frequencyMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a121817b-2316-3a74-912a-a12c78f3cd86")
    @Override
    public List<Frequency> getByDisplayNames(List<String> displayName) {
        QueryWrapper<Frequency> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("display_name", displayName).orderByAsc("id");
        return frequencyMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a7c0332d-6bb7-35f6-ad32-b4a5aff6dc03")
    @Override
    public List<Frequency> getByIds(List<String> id) {
        QueryWrapper<Frequency> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return frequencyMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "ccbb5726-ec24-3aa7-8891-52bf76d9871c")
    @Override
    public Frequency getById(String id) {
        QueryWrapper<Frequency> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return frequencyMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d203e2ed-5b99-309a-8b53-0fc54796a472")
    @Override
    public List<Frequency> getByCodes(List<String> code) {
        QueryWrapper<Frequency> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("code", code).orderByAsc("id");
        return frequencyMapper.selectList(queryWrapper);
    }
}
