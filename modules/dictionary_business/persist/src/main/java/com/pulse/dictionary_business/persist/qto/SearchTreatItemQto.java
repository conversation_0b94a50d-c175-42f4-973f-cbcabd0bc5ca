package com.pulse.dictionary_business.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "b648b5a2-3040-422b-bc2a-3f3aa836c68e|QTO|DEFINITION")
public class SearchTreatItemQto {
    /** 设备ID treat_item.device_id */
    @AutoGenerated(locked = true, uuid = "3a690b5f-10b6-42cf-942b-c487b2d2ce5d")
    private String deviceIdIs;

    @AutoGenerated(locked = true, uuid = "261db4b4-8509-48f4-8fb6-f6dcaa380d71")
    private Integer from;

    /** 执行科室ID treat_item.perform_dept_id */
    @AutoGenerated(locked = true, uuid = "e8a06b98-92d5-465c-b421-5062d1115eb6")
    private String performDeptIdIs;

    @AutoGenerated(locked = true, uuid = "0a53a6dd-ef2f-420e-8e7f-90a91d45c28e")
    private Integer size;

    /** 治疗类型ID treat_item.treat_class_id */
    @AutoGenerated(locked = true, uuid = "0777691c-de3b-4753-88e1-736cd67860bd")
    private String treatClassIdIs;

    /** 治疗项目名称 treat_item.treat_item_name */
    @AutoGenerated(locked = true, uuid = "a6af1dfd-8059-4c55-8fd3-99862e480576")
    private String treatItemNameLike;

    /** 治疗室ID treat_item.treat_room_id */
    @AutoGenerated(locked = true, uuid = "064cd263-eac0-4b52-8bd2-d9df507ebc8e")
    private String treatRoomIdIs;

    /** 治疗方式ID treat_item.treat_way_id */
    @AutoGenerated(locked = true, uuid = "72ee1189-56f7-44e1-ba3f-02d0d7a82a4e")
    private String treatWayIdIs;
}
