package com.pulse.dictionary_business.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "ddeb1d7b-9b38-41b7-8bc1-da4d67d7f64f|QTO|DEFINITION")
public class SearchTreatmentPackageExQto {
    /** 治疗项目名称 treat_item.treat_item_name */
    @AutoGenerated(locked = true, uuid = "c168dc4f-5b2f-408e-973c-28629520b4ca")
    private String treatmentPackageItemDetailListItemInfoTreatItemNameIs;
}
