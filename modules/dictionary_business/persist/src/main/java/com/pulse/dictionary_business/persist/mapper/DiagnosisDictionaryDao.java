package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.dos.DiagnosisDictionary;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "8704eaa4-6a44-4db1-9b75-8e6eeadedefa|ENTITY|IDAO")
public interface DiagnosisDictionaryDao {

    @AutoGenerated(locked = true, uuid = "24ee2d40-c131-39c8-ad6b-f84aa8115e76")
    List<DiagnosisDictionary> getByDiagnosisNames(List<String> diagnosisName);

    @AutoGenerated(locked = true, uuid = "602dd1d6-85b0-3756-a8e6-e31c76a9c005")
    DiagnosisDictionary getById(String id);

    @AutoGenerated(locked = true, uuid = "abff3053-42f6-355d-83f1-80392c2bac37")
    List<DiagnosisDictionary> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "bbb5a217-0695-3cd4-9b6b-56c69357a38c")
    List<DiagnosisDictionary> getByDiagnosisName(String diagnosisName);

    @AutoGenerated(locked = true, uuid = "cbd55c44-bb8c-3a49-bca3-b1d45799ae6a")
    List<DiagnosisDictionary> getByDiagnosisCodesAndCodeVersions(
            List<DiagnosisDictionary.CodeVersionAndDiagnosisCode> varList);

    @AutoGenerated(locked = true, uuid = "e6e57fcf-952b-34c5-8409-f66ac48ade65")
    DiagnosisDictionary getByDiagnosisCodeAndCodeVersion(String diagnosisCode, String codeVersion);
}
