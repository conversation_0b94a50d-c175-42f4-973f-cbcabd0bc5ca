package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.dos.Frequency;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "5b012227-55e5-40e0-a190-82c0f07a4b2b|ENTITY|IDAO")
public interface FrequencyDao {

    @AutoGenerated(locked = true, uuid = "9d4636b5-f839-3c44-9105-b5ede0dac290")
    Frequency getByDisplayName(String displayName);

    @AutoGenerated(locked = true, uuid = "9df482d1-afa5-3f21-a021-2b689531c68d")
    Frequency getByCode(String code);

    @AutoGenerated(locked = true, uuid = "a121817b-2316-3a74-912a-a12c78f3cd86")
    List<Frequency> getByDisplayNames(List<String> displayName);

    @AutoGenerated(locked = true, uuid = "a7c0332d-6bb7-35f6-ad32-b4a5aff6dc03")
    List<Frequency> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "ccbb5726-ec24-3aa7-8891-52bf76d9871c")
    Frequency getById(String id);

    @AutoGenerated(locked = true, uuid = "d203e2ed-5b99-309a-8b53-0fc54796a472")
    List<Frequency> getByCodes(List<String> code);
}
