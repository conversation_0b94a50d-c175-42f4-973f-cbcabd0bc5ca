package com.pulse.dictionary_business.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_business.persist.eo.AddressEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<AddressEo> */
@Converter
@AutoGenerated(locked = true, uuid = "7a3e6fef-a7f8-356f-be09-3cc337660f78")
public class AddressEoListConverter implements AttributeConverter<List<AddressEo>, String> {

    /** convert List<AddressEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<AddressEo> addressEoList) {
        if (addressEoList == null || addressEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(addressEoList);
        }
    }

    /** convert DB column to List<AddressEo> */
    @AutoGenerated(locked = true)
    public List<AddressEo> convertToEntityAttribute(String addressEoListJson) {
        if (StrUtil.isEmpty(addressEoListJson)) {
            return new ArrayList<AddressEo>();
        } else {
            return JsonUtils.readObject(addressEoListJson, new TypeReference<List<AddressEo>>() {});
        }
    }
}
