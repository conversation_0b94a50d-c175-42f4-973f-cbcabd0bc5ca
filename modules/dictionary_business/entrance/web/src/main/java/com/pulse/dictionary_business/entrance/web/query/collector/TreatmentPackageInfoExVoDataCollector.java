package com.pulse.dictionary_business.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.converter.TreatItemBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageInfoExVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageItemDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageInfoExVoDataAssembler.TreatmentPackageInfoExVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemDetailVo;
import com.pulse.dictionary_business.manager.converter.TreatmentPackageItemDetailDtoConverter;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatItemBaseDtoService;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.pulse.dictionary_business.service.TreatmentPackageItemDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TreatmentPackageInfoExVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "cc862204-a2fc-32c6-993e-7a46bfa4f7a0")
public class TreatmentPackageInfoExVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoService treatItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseVoConverter treatItemBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExVoConverter treatmentPackageInfoExVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExVoDataCollector treatmentPackageInfoExVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailDtoConverter treatmentPackageItemDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailVoConverter treatmentPackageItemDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoService treatmentPackageItemDtoService;

    /** 获取TreatmentPackageInfoExDto数据填充TreatmentPackageInfoExVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "1a271d30-2a6b-3404-a4d4-08b92e23db38")
    public void collectDataWithDtoData(
            List<TreatmentPackageInfoExDto> dtoList,
            TreatmentPackageInfoExVoDataHolder dataHolder) {
        Map<TreatmentPackageItemDto, TreatmentPackageItemDetailDto>
                treatmentPackageItemDetailListBaseDtoDtoMap = new LinkedHashMap<>();
        List<TreatItemBaseDto> treatmentPackageItemDetailList2ItemInfoList = new ArrayList<>();

        for (TreatmentPackageInfoExDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getTreatmentPackageItemDetailList())) {
                Map<String, TreatmentPackageItemDto> treatmentPackageItemDetailListBaseDtoMap =
                        treatmentPackageItemDetailDtoConverter
                                .convertFromTreatmentPackageItemDetailDtoToTreatmentPackageItemDto(
                                        rootDto.getTreatmentPackageItemDetailList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                TreatmentPackageItemDto::getId,
                                                Function.identity()));
                for (TreatmentPackageItemDetailDto treatmentPackageItemDetailListDto :
                        rootDto.getTreatmentPackageItemDetailList()) {
                    TreatmentPackageItemDto treatmentPackageItemDetailListBaseDto =
                            treatmentPackageItemDetailListBaseDtoMap.get(
                                    treatmentPackageItemDetailListDto.getId());
                    treatmentPackageItemDetailListBaseDto.setTreatmentPackageId(rootDto.getId());
                    treatmentPackageItemDetailListBaseDtoDtoMap.put(
                            treatmentPackageItemDetailListBaseDto,
                            treatmentPackageItemDetailListDto);
                    TreatItemBaseDto treatmentPackageItemDetailList2ItemInfoDto =
                            treatmentPackageItemDetailListDto.getItemInfo();
                    if (treatmentPackageItemDetailList2ItemInfoDto != null) {
                        treatmentPackageItemDetailList2ItemInfoList.add(
                                treatmentPackageItemDetailList2ItemInfoDto);
                    }
                }
            }
        }

        // access treatmentPackageItemDetailList
        Map<TreatmentPackageItemDetailDto, TreatmentPackageItemDetailVo>
                treatmentPackageItemDetailListVoMap =
                        treatmentPackageItemDetailVoConverter
                                .convertToTreatmentPackageItemDetailVoMap(
                                        new ArrayList<>(
                                                treatmentPackageItemDetailListBaseDtoDtoMap
                                                        .values()));
        dataHolder.treatmentPackageItemDetailList =
                treatmentPackageItemDetailListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                treatmentPackageItemDetailListVoMap.get(
                                                        treatmentPackageItemDetailListBaseDtoDtoMap
                                                                .get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access treatmentPackageItemDetailList2ItemInfo
        Map<TreatItemBaseDto, TreatItemBaseVo> treatmentPackageItemDetailList2ItemInfoVoMap =
                treatItemBaseVoConverter.convertToTreatItemBaseVoMap(
                        treatmentPackageItemDetailList2ItemInfoList);
        dataHolder.treatmentPackageItemDetailList2ItemInfo =
                treatmentPackageItemDetailList2ItemInfoList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                treatmentPackageItemDetailList2ItemInfoVoMap.get(
                                                        baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "1e2cc239-7d8e-3b89-ba36-3e16c32aabe3")
    public void collectDataDefault(TreatmentPackageInfoExVoDataHolder dataHolder) {
        treatmentPackageInfoExVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "86f6aac6-d8ce-351c-8cb7-f9f4091ac214")
    private void fillDataWhenNecessary(TreatmentPackageInfoExVoDataHolder dataHolder) {
        List<TreatmentPackageInfoDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.treatmentPackageItemDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TreatmentPackageInfoDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatmentPackageItemDto> baseDtoList =
                    treatmentPackageItemDtoService
                            .getByTreatmentPackageIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(TreatmentPackageItemDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TreatmentPackageItemDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            TreatmentPackageItemDto::getTreatmentPackageId));
            Map<String, TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoMap =
                    treatmentPackageItemDetailDtoConverter
                            .convertFromTreatmentPackageItemDtoToTreatmentPackageItemDetailDto(
                                    baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            TreatmentPackageItemDetailDto::getId,
                                            Function.identity()));
            Map<TreatmentPackageItemDetailDto, TreatmentPackageItemDetailVo> dtoVoMap =
                    treatmentPackageItemDetailVoConverter.convertToTreatmentPackageItemDetailVoMap(
                            new ArrayList<>(treatmentPackageItemDetailDtoMap.values()));
            Map<TreatmentPackageItemDto, TreatmentPackageItemDetailVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            treatmentPackageItemDetailDtoMap.containsKey(
                                                    baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            treatmentPackageItemDetailDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.treatmentPackageItemDetailList =
                    rootDtoList.stream()
                            .map(TreatmentPackageInfoDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.treatmentPackageItemDetailList2ItemInfo == null) {
            Set<String> ids =
                    dataHolder.treatmentPackageItemDetailList.keySet().stream()
                            .map(TreatmentPackageItemDto::getItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatItemBaseDto> baseDtoList =
                    treatItemBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TreatItemBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, TreatItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(TreatItemBaseDto::getId, Function.identity()));
            Map<TreatItemBaseDto, TreatItemBaseVo> dtoVoMap =
                    treatItemBaseVoConverter.convertToTreatItemBaseVoMap(baseDtoList);
            Map<TreatItemBaseDto, TreatItemBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.treatmentPackageItemDetailList2ItemInfo =
                    dataHolder.treatmentPackageItemDetailList.keySet().stream()
                            .map(TreatmentPackageItemDto::getItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
