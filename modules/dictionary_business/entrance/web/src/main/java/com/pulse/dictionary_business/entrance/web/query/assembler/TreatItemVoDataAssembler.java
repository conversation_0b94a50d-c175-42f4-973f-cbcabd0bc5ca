package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.TreatClassDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemVo;
import com.pulse.dictionary_business.manager.dto.TreatClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.service.TreatItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TreatItemVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "91348b4b-36f3-38c0-ac07-e06c6fecea2f")
public class TreatItemVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoService treatItemBaseDtoService;

    /** 组装TreatItemVo数据 */
    @AutoGenerated(locked = true, uuid = "30db814e-6e65-3e35-b289-83313f2bf167")
    public void assembleData(
            Map<String, TreatItemVo> voMap,
            TreatItemVoDataAssembler.TreatItemVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<TreatItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<TreatClassDictionaryBaseDto, TreatClassDictionaryVo>> treatClass =
                dataHolder.treatClass.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.treatClass.get(dto)),
                                        (o1, o2) -> o1));

        for (TreatItemBaseDto baseDto : baseDtoList) {
            TreatItemVo vo = voMap.get(baseDto.getId());
            vo.setTreatClass(
                    Optional.ofNullable(treatClass.get(baseDto.getTreatClassId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装TreatItemVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "49a5be87-**************-637dfc4ba33a")
    public void assembleDataCustomized(List<TreatItemVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class TreatItemVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatItemBaseDto> rootBaseDtoList;

        /** 持有字段treatClass的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TreatClassDictionaryBaseDto, TreatClassDictionaryVo> treatClass;
    }
}
