package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.TreatItemBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageItemDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageItemDetailVoDataAssembler.TreatmentPackageItemDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatItemBaseDtoService;
import com.pulse.dictionary_business.service.TreatmentPackageItemDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TreatmentPackageItemDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "96f0abfd-dc98-32ef-84a9-10f8e33f1404")
public class TreatmentPackageItemDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoService treatItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseVoConverter treatItemBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailVoConverter treatmentPackageItemDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailVoDataCollector treatmentPackageItemDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoService treatmentPackageItemDtoService;

    /** 获取TreatmentPackageItemDetailDto数据填充TreatmentPackageItemDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "2be643fc-ea1f-30a3-896a-a33ca0e55f9e")
    public void collectDataWithDtoData(
            List<TreatmentPackageItemDetailDto> dtoList,
            TreatmentPackageItemDetailVoDataHolder dataHolder) {
        List<TreatItemBaseDto> itemInfoList = new ArrayList<>();

        for (TreatmentPackageItemDetailDto rootDto : dtoList) {
            TreatItemBaseDto itemInfoDto = rootDto.getItemInfo();
            if (itemInfoDto != null) {
                itemInfoList.add(itemInfoDto);
            }
        }

        // access itemInfo
        Map<TreatItemBaseDto, TreatItemBaseVo> itemInfoVoMap =
                treatItemBaseVoConverter.convertToTreatItemBaseVoMap(itemInfoList);
        dataHolder.itemInfo =
                itemInfoList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> itemInfoVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "d3df1f4c-4382-3fd6-8a73-7c719e7fcc5a")
    public void collectDataDefault(TreatmentPackageItemDetailVoDataHolder dataHolder) {
        treatmentPackageItemDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "dc558189-a343-3844-a263-b6684779b198")
    private void fillDataWhenNecessary(TreatmentPackageItemDetailVoDataHolder dataHolder) {
        List<TreatmentPackageItemDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.itemInfo == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TreatmentPackageItemDto::getItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatItemBaseDto> baseDtoList =
                    treatItemBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TreatItemBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, TreatItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(TreatItemBaseDto::getId, Function.identity()));
            Map<TreatItemBaseDto, TreatItemBaseVo> dtoVoMap =
                    treatItemBaseVoConverter.convertToTreatItemBaseVoMap(baseDtoList);
            Map<TreatItemBaseDto, TreatItemBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.itemInfo =
                    rootDtoList.stream()
                            .map(TreatmentPackageItemDto::getItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
