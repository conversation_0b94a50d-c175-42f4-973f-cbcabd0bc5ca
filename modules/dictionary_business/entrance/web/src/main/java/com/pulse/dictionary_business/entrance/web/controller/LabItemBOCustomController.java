package com.pulse.dictionary_business.entrance.web.controller;

import cn.hutool.core.collection.CollUtil;

import com.pulse.dictionary_business.manager.dto.LabClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemBaseDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBOService;
import com.pulse.dictionary_business.service.LabClassDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.LabItemBOService;
import com.pulse.dictionary_business.service.LabItemBaseDtoService;
import com.pulse.dictionary_business.service.bto.CreateClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.EnableClinicItemBto;
import com.pulse.dictionary_business.service.bto.MergeLabItemRuleBto;
import com.pulse.dictionary_business.service.bto.SaveClinicItemChargeItemBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.UpdateLabItemBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "f6312f52-28bb-3974-a836-7fe086b0d0da")
public class LabItemBOCustomController {
    @AutoGenerated(locked = true)
    @Resource
    private LabItemBOService labItemBOService;

    @Resource private LabClassDictionaryBaseDtoService labClassDictionaryBaseDtoService;

    @Resource private LabItemBaseDtoService labItemBaseDtoService;
    @Resource private ClinicItemDictionaryBOService clinicItemDictionaryBOService;

    /** 保存关联检验项目列表 */
    @PublicInterface(id = "3cdbd9f5-5e90-4f6e-9abc-52f01d8498dc", version = "1744248866106")
    @AutoGenerated(locked = false, uuid = "3cdbd9f5-5e90-4f6e-9abc-52f01d8498dc")
    @RequestMapping(
            value = {"/api/dictionary-business/lab-class/merge-related-lab-item-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public Boolean mergeRelatedLabItemList(
            @NotNull String labClassId, @Valid @NotNull List<String> labItemIdList) {

        LabClassDictionaryBaseDto labClass =
                labClassDictionaryBaseDtoService.getByLabClassCode(labClassId);
        if (labClass == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未查询到检验分类编码对应的检验分类记录");
        }

        List<LabItemBaseDto> labItemBaseDtoList = labItemBaseDtoService.getByIds(labItemIdList);

        if (CollUtil.isEmpty(labItemBaseDtoList)) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未查询到检验项目ID列表对应的检验项目记录");
        }

        for (LabItemBaseDto labItemBaseDto : labItemBaseDtoList) {
            UpdateLabItemBto updateLabItemBto = new UpdateLabItemBto();
            updateLabItemBto.setId(labItemBaseDto.getId());
            updateLabItemBto.setLabClassId(labClassId);
            labItemBOService.updateLabItem(updateLabItemBto);
        }

        return Boolean.TRUE;
    }

    /** 启用检验项目 */
    @PublicInterface(id = "1e4a65f7-3920-4b0e-9fbc-312deb9b6f1f", version = "1744596921928")
    @AutoGenerated(locked = false, uuid = "1e4a65f7-3920-4b0e-9fbc-312deb9b6f1f")
    @RequestMapping(
            value = {"/api/dictionary-business/enable-lab-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Boolean enableLabItem(@NotNull String labItemId, @NotNull Boolean enableFlag) {

        LabItemBaseDto labItem = labItemBaseDtoService.getById(labItemId);
        if (labItem == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未查询到检验项目ID对应的检验项目记录");
        }

        EnableClinicItemBto enableClinicItemBto = new EnableClinicItemBto();
        enableClinicItemBto.setClinicItemId(labItem.getClinicItemId());
        enableClinicItemBto.setEnableFlag(enableFlag);

        clinicItemDictionaryBOService.enableClinicItem(enableClinicItemBto);

        return Boolean.TRUE;
    }

    /** 创建检验项目导医说明 */
    @PublicInterface(id = "5b554c63-b99e-4283-a372-b5ff546ce768", version = "1744964122592")
    @AutoGenerated(locked = false, uuid = "5b554c63-b99e-4283-a372-b5ff546ce768")
    @RequestMapping(
            value = {"/api/dictionary-business/create-lab-item-guide-description"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createLabItemGuideDescription(
            String labItemId,
            @Valid
                    CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                            labItemGuideDescription) {
        LabItemBaseDto labItemBaseDto = labItemBaseDtoService.getById(labItemId);
        if (labItemBaseDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未查询到检验项目ID对应的检验项目记录");
        }

        labItemGuideDescription.setId(null);
        labItemGuideDescription.setClinicItemId(labItemGuideDescription.getClinicItemId());

        return clinicItemDictionaryBOService.createClinicItemGuideDescription(
                labItemGuideDescription);
    }

    /** 保存检验项目规则 */
    @PublicInterface(id = "6c1d3465-fe48-470d-9ae8-f8ad759ab03e", version = "1744620660033")
    @AutoGenerated(locked = false, uuid = "6c1d3465-fe48-470d-9ae8-f8ad759ab03e")
    @RequestMapping(
            value = {"/api/dictionary-business/merge-lab-item-rule"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeLabItemRule(
            @Valid @NotNull MergeLabItemRuleBto.LabItemRuleBto labItemRuleBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = labItemBOService.mergeLabItemRule(labItemRuleBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存检验项目关联收费项目列表 */
    @PublicInterface(id = "87c8dcd8-efe3-4658-9f89-b69b3a95ddf9", version = "1744619824588")
    @AutoGenerated(locked = false, uuid = "87c8dcd8-efe3-4658-9f89-b69b3a95ddf9")
    @RequestMapping(
            value = {"/api/dictionary-business/merge-lab-item-charge-item-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public Boolean mergeLabItemChargeItemList(
            @Valid @NotNull SaveClinicItemChargeItemBto mergeLabItemChargeItem, String labItemId) {
        LabItemBaseDto labItem = labItemBaseDtoService.getById(labItemId);
        if (labItem == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未查询到检验项目ID对应的检验项目记录");
        }
        if (labItem.getClinicItemId() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未查询到检验项目对应的诊疗项目记录");
        }
        mergeLabItemChargeItem.setClinicItemId(labItem.getClinicItemId());
        clinicItemDictionaryBOService.saveClinicItemChargeItem(mergeLabItemChargeItem);
        return Boolean.TRUE;
    }

    /** 更新检验项目导医说明 */
    @PublicInterface(id = "e3653acf-1679-4e88-b662-a2dd44df8915", version = "1744620057830")
    @AutoGenerated(locked = false, uuid = "e3653acf-1679-4e88-b662-a2dd44df8915")
    @RequestMapping(
            value = {"/api/business/update-lab-item-guide-description"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Boolean updateLabItemGuideDescription(
            @Valid @NotNull
                    UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                            labItemGuideDescription) {
        clinicItemDictionaryBOService.updateClinicItemGuideDescription(labItemGuideDescription);
        return Boolean.TRUE;
    }
}
