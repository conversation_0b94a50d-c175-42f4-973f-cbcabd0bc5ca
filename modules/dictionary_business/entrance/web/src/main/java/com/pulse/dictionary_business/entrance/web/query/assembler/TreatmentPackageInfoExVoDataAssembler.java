package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageInfoExVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemDetailVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TreatmentPackageInfoExVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "725f9fca-9199-3b66-a43d-ce07da7cd56a")
public class TreatmentPackageInfoExVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    /** 组装treatmentPackageItemDetailList数据 */
    @AutoGenerated(locked = true, uuid = "35a5d740-2cdf-38cc-8116-950049b8af9f")
    private void assembleTreatmentPackageItemDetailListData(
            TreatmentPackageInfoExVoDataAssembler.TreatmentPackageInfoExVoDataHolder dataHolder) {
        Map<String, Pair<TreatItemBaseDto, TreatItemBaseVo>>
                treatmentPackageItemDetailList2ItemInfo =
                        dataHolder.treatmentPackageItemDetailList2ItemInfo.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .treatmentPackageItemDetailList2ItemInfo
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<TreatmentPackageItemDto, TreatmentPackageItemDetailVo>
                treatmentPackageItemDetailList :
                        dataHolder.treatmentPackageItemDetailList.entrySet()) {
            TreatmentPackageItemDto baseDto = treatmentPackageItemDetailList.getKey();
            TreatmentPackageItemDetailVo vo = treatmentPackageItemDetailList.getValue();
            vo.setItemInfo(
                    Optional.ofNullable(
                                    treatmentPackageItemDetailList2ItemInfo.get(
                                            baseDto.getItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 批量自定义组装TreatmentPackageInfoExVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b4664f1e-6af0-3d40-971f-cb46c1c9a27c")
    public void assembleDataCustomized(List<TreatmentPackageInfoExVo> dataList) {
        // 自定义数据组装

    }

    /** 组装TreatmentPackageInfoExVo数据 */
    @AutoGenerated(locked = true, uuid = "d47891b0-6c16-3a2f-acfc-4d302a33fdab")
    public void assembleData(
            Map<String, TreatmentPackageInfoExVo> voMap,
            TreatmentPackageInfoExVoDataAssembler.TreatmentPackageInfoExVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<TreatmentPackageInfoDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, List<Pair<TreatmentPackageItemDto, TreatmentPackageItemDetailVo>>>
                treatmentPackageItemDetailList =
                        dataHolder.treatmentPackageItemDetailList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getTreatmentPackageId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .treatmentPackageItemDetailList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (TreatmentPackageInfoDto baseDto : baseDtoList) {
            TreatmentPackageInfoExVo vo = voMap.get(baseDto.getId());
            vo.setTreatmentPackageItemDetailList(
                    Optional.ofNullable(treatmentPackageItemDetailList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleTreatmentPackageItemDetailListData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class TreatmentPackageInfoExVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatmentPackageInfoDto> rootBaseDtoList;

        /** 持有字段treatmentPackageItemDetailList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TreatmentPackageItemDto, TreatmentPackageItemDetailVo>
                treatmentPackageItemDetailList;

        /** 持有字段treatmentPackageItemDetailList.itemInfo的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TreatItemBaseDto, TreatItemBaseVo> treatmentPackageItemDetailList2ItemInfo;
    }
}
