package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.dictionary_business.common.enums.DefaultPerformDepartmentTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "077ed158-d72f-44c3-b0fc-5b80fc3c8abb|VO|DEFINITION")
public class ClinicItemDictionaryListVo {
    /** 主键 */
    @AutoGenerated(locked = true, uuid = "d534481d-8675-48f6-a1c7-cff2a45c61fe")
    private String clinicItemId;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "20b71d44-d372-4c99-864d-923a6d576ce5")
    private String clinicItemName;

    /** 延迟天数 */
    @AutoGenerated(locked = true, uuid = "b195f980-d8a5-4f41-8c4b-54ed61de5cd1")
    private Long delayDays;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "7926e4d3-0830-428c-8dee-ab3a63bb069d")
    private Boolean enableFlag;

    /** 排斥时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "9d9b61b2-41c3-45bd-86ae-63617f9a6705")
    private TimeEo exclusionTime;

    /** 排斥类型 */
    @AutoGenerated(locked = true, uuid = "4db63699-6c63-44fb-89ca-e3c26a9c376c")
    private String exclusionType;

    /** 频次 */
    @AutoGenerated(locked = true, uuid = "f5638adc-c3ee-43da-8e2a-4a3e59fd9982")
    private String frequency;

    /** 住院默认执行科室类型 */
    @AutoGenerated(locked = true, uuid = "c277eff8-aa8c-417d-9678-f180c7cb8c5c")
    private DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType;

    /** 机构代码 */
    @AutoGenerated(locked = true, uuid = "1bfdfdc4-dcd8-4ca1-bc9b-62640cc9ca88")
    private String institutionId;

    /** 项目规格 */
    @AutoGenerated(locked = true, uuid = "81a9dacf-f39b-4706-893e-87014b88dc14")
    private String itemSpecification;

    /** 医嘱项目类型 */
    @AutoGenerated(locked = true, uuid = "5d3d8e61-6537-4674-acea-e0a0379a449d")
    private String itemType;

    /** 门诊默认执行科室ID */
    @AutoGenerated(locked = true, uuid = "b502298b-42f3-4cb7-8001-2e214b047e71")
    private String outpDefaultPerformDepartmentId;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "1735d9d0-0aab-4841-a397-48117181b78a")
    private Long sortNumber;

    /** 标准代码 */
    @AutoGenerated(locked = true, uuid = "8f7868f5-7fbe-4cfc-be7e-4fe467a9f3e8")
    private String standardCode;
}
