package com.pulse.dictionary_business.entrance.web.controller;

import com.pulse.dictionary_business.entrance.web.converter.ExamTypeChargeItemDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeDictionaryDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeDictionaryVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeDocumentTemplateVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeOrderLimitVoConverter;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeChargeItemDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDocumentTemplateVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeOrderLimitVo;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemDetailDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryDetailDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDocumentTemplateBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeOrderLimitBaseDto;
import com.pulse.dictionary_business.persist.qto.ListExamTypeDictionaryByKeyWordQto;
import com.pulse.dictionary_business.service.ExamTypeChargeItemDetailDtoService;
import com.pulse.dictionary_business.service.ExamTypeDictionaryDetailDtoService;
import com.pulse.dictionary_business.service.ExamTypeDocumentTemplateBaseDtoService;
import com.pulse.dictionary_business.service.ExamTypeOrderLimitBaseDtoService;
import com.pulse.dictionary_business.service.query.ExamTypeDictionaryBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "643d5190-9372-3910-a774-af27f92f3f41")
public class ExamTypeDictionaryController {
    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemDetailDtoService examTypeChargeItemDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemDetailVoConverter examTypeChargeItemDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryBaseDtoQueryService examTypeDictionaryBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryDetailDtoService examTypeDictionaryDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryDetailVoConverter examTypeDictionaryDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryVoConverter examTypeDictionaryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDocumentTemplateBaseDtoService examTypeDocumentTemplateBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDocumentTemplateVoConverter examTypeDocumentTemplateVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeOrderLimitBaseDtoService examTypeOrderLimitBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeOrderLimitVoConverter examTypeOrderLimitVoConverter;

    /** 根据主键获取检查类型字典详情 */
    @PublicInterface(id = "26bdad8f-86b2-43da-acc5-a673a6240c94", version = "1741917537741")
    @AutoGenerated(locked = false, uuid = "26bdad8f-86b2-43da-acc5-a673a6240c94")
    @RequestMapping(
            value = {"/api/dictionary-business/get-exam-type-detail-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public ExamTypeDictionaryDetailVo getExamTypeDetailById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamTypeDictionaryDetailDto rpcResult = examTypeDictionaryDetailDtoService.getById(id);
        ExamTypeDictionaryDetailVo result =
                examTypeDictionaryDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 通过关键字获取检查类型列表 */
    @PublicInterface(id = "44843138-e7aa-412b-bc94-4b4522931491", version = "1741854923115")
    @AutoGenerated(locked = false, uuid = "44843138-e7aa-412b-bc94-4b4522931491")
    @RequestMapping(
            value = {"/api/dictionary-business/query-list-exam-type-dictionary-by-key-word"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ExamTypeDictionaryVo> queryListExamTypeDictionaryByKeyWord(
            @Valid ListExamTypeDictionaryByKeyWordQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamTypeDictionaryBaseDto> rpcResult =
                examTypeDictionaryBaseDtoQueryService.queryListExamTypeDictionaryByKeyWord(qto);
        List<ExamTypeDictionaryVo> result =
                examTypeDictionaryVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据检查类型id获取检查类型开单限制列表 */
    @PublicInterface(id = "57c385f2-1021-4971-8630-470bf3eb9ea6", version = "1741930412411")
    @AutoGenerated(locked = false, uuid = "57c385f2-1021-4971-8630-470bf3eb9ea6")
    @RequestMapping(
            value = {"/api/dictionary-business/exam-type-order-limit/get-by-exam-type-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ExamTypeOrderLimitVo> getOrderLimitByExamTypeId(@NotNull String examTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamTypeOrderLimitBaseDto> rpcResult =
                examTypeOrderLimitBaseDtoService.getByExamTypeId(examTypeId);
        List<ExamTypeOrderLimitVo> result =
                examTypeOrderLimitVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据检查类型id获取检查类型收费项目列表 */
    @PublicInterface(id = "c4ddd493-1c0a-4296-bb3c-439a37fbc73e", version = "1741924525148")
    @AutoGenerated(locked = false, uuid = "c4ddd493-1c0a-4296-bb3c-439a37fbc73e")
    @RequestMapping(
            value = {"/api/dictionary-business/exam-type-charge-item/get-by-exam-type-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ExamTypeChargeItemDetailVo> getChargeItemByExamTypeId(String examTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamTypeChargeItemDetailDto> rpcResult =
                examTypeChargeItemDetailDtoService.getByExamTypeId(examTypeId);
        List<ExamTypeChargeItemDetailVo> result =
                examTypeChargeItemDetailVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据检查类型编码获取检查类型文书模板 */
    @PublicInterface(id = "fd5ad9ea-6d7f-4042-a0d2-3448ce0e4c16", version = "1741921115480")
    @AutoGenerated(locked = false, uuid = "fd5ad9ea-6d7f-4042-a0d2-3448ce0e4c16")
    @RequestMapping(
            value = {"/api/dictionary-business/exam-type-document-template/get-by-exam-type-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public ExamTypeDocumentTemplateVo getByExamTypeId(String examTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamTypeDocumentTemplateBaseDto rpcResult =
                examTypeDocumentTemplateBaseDtoService.getByExamTypeId(examTypeId);
        ExamTypeDocumentTemplateVo result =
                examTypeDocumentTemplateVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
