package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemDetailVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatmentPackageItemDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TreatmentPackageItemDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "67be1137-4372-350e-bc1a-54ed7be37c67")
public class TreatmentPackageItemDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoService treatmentPackageItemDtoService;

    /** 批量自定义组装TreatmentPackageItemDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "ba6f0031-33c2-3bc2-99d1-083d41aac294")
    public void assembleDataCustomized(List<TreatmentPackageItemDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 组装TreatmentPackageItemDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "d36974cc-3e6c-3908-9d4e-0e6275d9aa77")
    public void assembleData(
            Map<String, TreatmentPackageItemDetailVo> voMap,
            TreatmentPackageItemDetailVoDataAssembler.TreatmentPackageItemDetailVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<TreatmentPackageItemDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<TreatItemBaseDto, TreatItemBaseVo>> itemInfo =
                dataHolder.itemInfo.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.itemInfo.get(dto)),
                                        (o1, o2) -> o1));

        for (TreatmentPackageItemDto baseDto : baseDtoList) {
            TreatmentPackageItemDetailVo vo = voMap.get(baseDto.getId());
            vo.setItemInfo(
                    Optional.ofNullable(itemInfo.get(baseDto.getItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class TreatmentPackageItemDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatmentPackageItemDto> rootBaseDtoList;

        /** 持有字段itemInfo的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TreatItemBaseDto, TreatItemBaseVo> itemInfo;
    }
}
