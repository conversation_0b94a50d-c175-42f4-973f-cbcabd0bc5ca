package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_business.common.enums.TreatScopeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "be8bfa04-d161-4986-a6f3-d357231fbea5|VO|DEFINITION")
public class TreatItemBaseVo {
    /** 适用范围 */
    @AutoGenerated(locked = true, uuid = "1e300a3c-9169-4b4a-ac74-30805892c51e")
    private TreatScopeEnum applicableScope;

    /** 院区 */
    @AutoGenerated(locked = true, uuid = "e2416a01-0e93-4d78-8df6-4f2471828b6c")
    private String branchInstitutionId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "d2cc7678-9eca-4d44-8191-5ea7b91259cb")
    private Date createdAt;

    /** 默认次数 */
    @AutoGenerated(locked = true, uuid = "001b2306-159a-4632-9b06-312dc53f2c6c")
    private Long defaultTimes;

    /** 设备ID */
    @AutoGenerated(locked = true, uuid = "c66f4b77-4b93-4ed1-b888-a5702dc8cbbd")
    private String deviceId;

    /** 是否高危 */
    @AutoGenerated(locked = true, uuid = "34f915ed-f508-4d4b-b25e-2214c20f3b54")
    private Boolean highRiskFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "e634e335-6c30-430b-8d3b-7142ab5b03d9")
    private String id;

    /** 最大治疗次数 */
    @AutoGenerated(locked = true, uuid = "e6299942-223a-4c04-8c89-41ce3fde87a7")
    private Long maxTreatTimes;

    /** 执行科室ID */
    @AutoGenerated(locked = true, uuid = "316c0bb7-43ec-4385-8c49-9252bb9122fa")
    private String performDeptId;

    /** 参考执行时长(分钟) */
    @AutoGenerated(locked = true, uuid = "febb55e3-9cff-4eee-9ee8-dd4ab30cb67a")
    private Long referenceExecutionTime;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "112a8666-cc22-428e-ad68-3bcd93e62cfa")
    private String remark;

    /** 治疗类型ID */
    @AutoGenerated(locked = true, uuid = "887b76ee-feff-4860-9347-5c153966a062")
    private String treatClassId;

    /** 治疗项目名称 */
    @AutoGenerated(locked = true, uuid = "f944f2be-7a18-4dfd-a8dc-df5c279efcba")
    private String treatItemName;

    /** 治疗室ID */
    @AutoGenerated(locked = true, uuid = "e4e6dd99-5292-44d8-8706-2d7ab15998fc")
    private String treatRoomId;

    /** 治疗方式ID */
    @AutoGenerated(locked = true, uuid = "27a0b0a5-62ed-4ace-9192-76b5fe232dcd")
    private String treatWayId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "3faa0433-3f7d-4631-9076-06ba7dfa8b82")
    private Date updatedAt;
}
