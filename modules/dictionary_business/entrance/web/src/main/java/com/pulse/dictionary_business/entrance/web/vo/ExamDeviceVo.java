package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.common.enums.ExamTypeBillingModeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "18be5a5e-1b77-4af7-aeda-3786f268d020|VO|DEFINITION")
public class ExamDeviceVo {
    /** 计费模式 */
    @AutoGenerated(locked = true, uuid = "95c36a95-f4e1-4b13-a287-4a8550efeee6")
    private ExamTypeBillingModeEnum billingMode;

    /** 院区id */
    @Valid
    @AutoGenerated(locked = true, uuid = "08b842ce-f6a7-4c2b-b2ed-9a271e0bc2cb")
    private List<String> campusId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "391ea9f3-a850-4c7f-8bed-7a7048d44a51")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "66d40a06-92e2-416a-84f0-45b08a1fa4c5")
    private String createdBy;

    /** 设备ID */
    @AutoGenerated(locked = true, uuid = "5a2bd4af-fee7-4102-818d-b857b28f6285")
    private String deviceId;

    /** 设备名称 */
    @AutoGenerated(locked = true, uuid = "30409d7a-372e-4a09-800a-627b05eec939")
    private String deviceName;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "f0cda960-099f-438d-915c-36c46d718b52")
    private Boolean enableFlag;

    /** 检查类型id */
    @Valid
    @AutoGenerated(locked = true, uuid = "bd2efb24-2dd7-453d-be22-d939d2e5f1aa")
    private ExamTypeDictionaryVo examType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "fb1abd14-b005-46c6-af33-274c04b219cd")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "62630e5c-9573-416a-b8c4-26fb2c0659ca")
    private InputCodeEo inputCode;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "857be357-1cf6-48f6-adb3-4eac2b8152b8")
    private Long sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "90fddcea-a485-43b8-893f-422f9b5ab55f")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "69f038d1-ec18-4e6d-9dc7-bd77dc7ed507")
    private String updatedBy;
}
