package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.ExamTypeDictionaryDetailVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamTypeDictionaryDetailVoDataAssembler.ExamTypeDictionaryDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ExamTypeDictionaryDetailVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDeviceVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDocumentTemplateVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeOrderLimitVo;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryDetailDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDocumentTemplateBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeOrderLimitBaseDto;
import com.pulse.dictionary_business.service.ExamTypeDictionaryBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ExamTypeDictionaryDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "19abdd9f-a616-4100-b6f7-e60ac2ccd97c|VO|CONVERTER")
public class ExamTypeDictionaryDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDeviceVoConverter examTypeDeviceVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryBaseDtoService examTypeDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryDetailVoDataAssembler examTypeDictionaryDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryDetailVoDataCollector examTypeDictionaryDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDocumentTemplateVoConverter examTypeDocumentTemplateVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeOrderLimitVoConverter examTypeOrderLimitVoConverter;

    /** 把ExamTypeDictionaryDetailDto转换成ExamTypeDictionaryDetailVo */
    @AutoGenerated(locked = true, uuid = "04939db3-48e2-3d60-8d2b-0840513b984d")
    public ExamTypeDictionaryDetailVo convertToExamTypeDictionaryDetailVo(
            ExamTypeDictionaryDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToExamTypeDictionaryDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把ExamTypeDictionaryDetailDto转换成ExamTypeDictionaryDetailVo */
    @AutoGenerated(locked = false, uuid = "19abdd9f-a616-4100-b6f7-e60ac2ccd97c-converter-Map")
    public Map<ExamTypeDictionaryDetailDto, ExamTypeDictionaryDetailVo>
            convertToExamTypeDictionaryDetailVoMap(List<ExamTypeDictionaryDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ExamDeviceBaseDto, ExamTypeDeviceVo> examTypeDeviceListMap =
                examTypeDeviceVoConverter.convertToExamTypeDeviceVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getExamTypeDeviceList()))
                                .flatMap(dto -> dto.getExamTypeDeviceList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamTypeOrderLimitBaseDto, ExamTypeOrderLimitVo> examTypeOrderLimitListMap =
                examTypeOrderLimitVoConverter.convertToExamTypeOrderLimitVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getExamTypeOrderLimitList()))
                                .flatMap(dto -> dto.getExamTypeOrderLimitList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamTypeDocumentTemplateBaseDto, ExamTypeDocumentTemplateVo>
                examTypeDocumentTemplateMap =
                        examTypeDocumentTemplateVoConverter.convertToExamTypeDocumentTemplateVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(
                                                ExamTypeDictionaryDetailDto
                                                        ::getExamTypeDocumentTemplate)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<ExamTypeDictionaryDetailDto, ExamTypeDictionaryDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ExamTypeDictionaryDetailVo vo =
                                                    new ExamTypeDictionaryDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setExamTypeCode(dto.getExamTypeCode());
                                            vo.setExamTypeName(dto.getExamTypeName());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setBillingMode(dto.getBillingMode());
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setUseScope(dto.getUseScopeList());
                                            vo.setExamTypeDeviceList(
                                                    dto.getExamTypeDeviceList() == null
                                                            ? null
                                                            : dto.getExamTypeDeviceList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    examTypeDeviceListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setExamTypeOrderLimitList(
                                                    dto.getExamTypeOrderLimitList() == null
                                                            ? null
                                                            : dto
                                                                    .getExamTypeOrderLimitList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    examTypeOrderLimitListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setExamTypeDocumentTemplate(
                                                    dto.getExamTypeDocumentTemplate() == null
                                                            ? null
                                                            : examTypeDocumentTemplateMap.get(
                                                                    dto
                                                                            .getExamTypeDocumentTemplate()));
                                            vo.setPartSplitFlag(dto.getPartSplitFlag());
                                            vo.setParentId(dto.getParentId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ExamTypeDictionaryDetailDto转换成ExamTypeDictionaryDetailVo */
    @AutoGenerated(locked = true, uuid = "19abdd9f-a616-4100-b6f7-e60ac2ccd97c-converter-list")
    public List<ExamTypeDictionaryDetailVo> convertToExamTypeDictionaryDetailVoList(
            List<ExamTypeDictionaryDetailDto> dtoList) {
        return new ArrayList<>(convertToExamTypeDictionaryDetailVoMap(dtoList).values());
    }

    /** 使用默认方式组装ExamTypeDictionaryDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "1c80b67e-197d-32b2-ab0f-e5481525423c")
    public ExamTypeDictionaryDetailVo convertAndAssembleData(ExamTypeDictionaryDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ExamTypeDictionaryDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d801259c-8421-3464-a012-0ff90b1c7489")
    public List<ExamTypeDictionaryDetailVo> convertAndAssembleDataList(
            List<ExamTypeDictionaryDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ExamTypeDictionaryDetailVoDataHolder dataHolder =
                new ExamTypeDictionaryDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                examTypeDictionaryBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ExamTypeDictionaryDetailDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ExamTypeDictionaryDetailVo> voMap =
                convertToExamTypeDictionaryDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        examTypeDictionaryDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        examTypeDictionaryDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
