package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemDictionaryDetailVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemDictionaryDetailVoDataAssembler.ClinicItemDictionaryDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ClinicItemDictionaryDetailVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemChargeItemExtVo;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemPerformDepartmentVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemChargeItemExtDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryDetailDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemPerformDepartmentDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ClinicItemDictionaryDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "7eb08ece-db5b-4157-82d1-73e71b40bba6|VO|CONVERTER")
public class ClinicItemDictionaryDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemChargeItemExtVoConverter
            clinicItemChargeItemChargeItemExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryDetailVoDataAssembler clinicItemDictionaryDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryDetailVoDataCollector clinicItemDictionaryDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemPerformDepartmentVoConverter clinicItemPerformDepartmentVoConverter;

    /** 使用默认方式组装ClinicItemDictionaryDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "0bb02312-35c3-3592-8a95-a2ddf9ec7a1d")
    public ClinicItemDictionaryDetailVo convertAndAssembleData(ClinicItemDictionaryDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ClinicItemDictionaryDetailDto转换成ClinicItemDictionaryDetailVo */
    @AutoGenerated(locked = false, uuid = "7eb08ece-db5b-4157-82d1-73e71b40bba6-converter-Map")
    public Map<ClinicItemDictionaryDetailDto, ClinicItemDictionaryDetailVo>
            convertToClinicItemDictionaryDetailVoMap(List<ClinicItemDictionaryDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ClinicItemPerformDepartmentDto, ClinicItemPerformDepartmentVo>
                clinicItemPerformDepartmentListMap =
                        clinicItemPerformDepartmentVoConverter
                                .convertToClinicItemPerformDepartmentVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getClinicItemPerformDepartmentList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getClinicItemPerformDepartmentList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<ClinicItemChargeItemChargeItemExtDto, ClinicItemChargeItemChargeItemExtVo>
                clinicItemChargeItemListMap =
                        clinicItemChargeItemChargeItemExtVoConverter
                                .convertToClinicItemChargeItemChargeItemExtVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getClinicItemChargeItemList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getClinicItemChargeItemList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<ClinicItemDictionaryDetailDto, ClinicItemDictionaryDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ClinicItemDictionaryDetailVo vo =
                                                    new ClinicItemDictionaryDetailVo();
                                            vo.setClinicItemId(dto.getClinicItemId());
                                            vo.setItemType(dto.getItemType());
                                            vo.setClinicItemName(dto.getClinicItemName());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setRemark(dto.getRemark());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setAuditFlag(dto.getAuditFlag());
                                            vo.setAuditDate(dto.getAuditDate());
                                            vo.setAuditOperatorId(dto.getAuditOperatorId());
                                            vo.setDescription(dto.getDescription());
                                            vo.setLimitGender(dto.getLimitGender());
                                            vo.setBillingAttribute(dto.getBillingAttribute());
                                            vo.setOnlySelectSettingDepartmentFlag(
                                                    dto.getOnlySelectSettingDepartmentFlag());
                                            vo.setOperationCode(dto.getOperationCode());
                                            vo.setUseScopeList(dto.getUseScopeList());
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setAgeMinLimit(dto.getAgeMinLimit());
                                            vo.setAgeMaxLimit(dto.getAgeMaxLimit());
                                            vo.setClinicItemCatalogId(dto.getClinicItemCatalogId());
                                            vo.setStandardCode(dto.getStandardCode());
                                            vo.setFrequency(dto.getFrequency());
                                            vo.setUnit(dto.getUnit());
                                            vo.setPrintFlag(dto.getPrintFlag());
                                            vo.setExclusionType(dto.getExclusionType());
                                            vo.setExclusionTime(dto.getExclusionTime());
                                            vo.setDelayDays(dto.getDelayDays());
                                            vo.setOutpDefaultPerformDepartmentId(
                                                    dto.getOutpDefaultPerformDepartmentId());
                                            vo.setInpDefaultPerformDepartmentType(
                                                    dto.getInpDefaultPerformDepartmentType());
                                            vo.setFrequencyNotAllowedModifyFlag(
                                                    dto.getFrequencyNotAllowedModifyFlag());
                                            vo.setDoubleSignatureFlag(dto.getDoubleSignatureFlag());
                                            vo.setLimitWardIdList(dto.getLimitWardIdList());
                                            vo.setPdaPerformFlag(dto.getPdaPerformFlag());
                                            vo.setCardPrintType(dto.getCardPrintTypeList());
                                            vo.setOrderFrequencyBillingType(
                                                    dto.getOrderFrequencyBillingType());
                                            vo.setBillingInterval(dto.getBillingInterval());
                                            vo.setAlias(dto.getAlias());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setClinicItemPerformDepartmentList(
                                                    dto.getClinicItemPerformDepartmentList() == null
                                                            ? null
                                                            : dto
                                                                    .getClinicItemPerformDepartmentList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    clinicItemPerformDepartmentListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setItemSpecification(dto.getItemSpecification());
                                            vo.setClinicItemChargeItemList(
                                                    dto.getClinicItemChargeItemList() == null
                                                            ? null
                                                            : dto
                                                                    .getClinicItemChargeItemList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    clinicItemChargeItemListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setSortNumber(dto.getSortNumber());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ClinicItemDictionaryDetailDto转换成ClinicItemDictionaryDetailVo */
    @AutoGenerated(locked = true, uuid = "7eb08ece-db5b-4157-82d1-73e71b40bba6-converter-list")
    public List<ClinicItemDictionaryDetailVo> convertToClinicItemDictionaryDetailVoList(
            List<ClinicItemDictionaryDetailDto> dtoList) {
        return new ArrayList<>(convertToClinicItemDictionaryDetailVoMap(dtoList).values());
    }

    /** 把ClinicItemDictionaryDetailDto转换成ClinicItemDictionaryDetailVo */
    @AutoGenerated(locked = true, uuid = "a5486181-73b5-3c22-8906-b343adf933ef")
    public ClinicItemDictionaryDetailVo convertToClinicItemDictionaryDetailVo(
            ClinicItemDictionaryDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToClinicItemDictionaryDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装ClinicItemDictionaryDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fa4cc671-5373-3cb4-b6c7-89adc6c3611c")
    public List<ClinicItemDictionaryDetailVo> convertAndAssembleDataList(
            List<ClinicItemDictionaryDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ClinicItemDictionaryDetailVoDataHolder dataHolder =
                new ClinicItemDictionaryDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                clinicItemDictionaryBaseDtoService.getByClinicItemIds(
                        dtoList.stream()
                                .map(ClinicItemDictionaryDetailDto::getClinicItemId)
                                .collect(Collectors.toList())));
        Map<String, ClinicItemDictionaryDetailVo> voMap =
                convertToClinicItemDictionaryDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getClinicItemId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        clinicItemDictionaryDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        clinicItemDictionaryDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getClinicItemId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
