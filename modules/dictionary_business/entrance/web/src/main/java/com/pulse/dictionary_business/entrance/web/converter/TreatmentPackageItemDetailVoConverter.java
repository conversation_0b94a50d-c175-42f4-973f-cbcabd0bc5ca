package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageItemDetailVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageItemDetailVoDataAssembler.TreatmentPackageItemDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.TreatmentPackageItemDetailVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemDetailVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.service.TreatmentPackageItemDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TreatmentPackageItemDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3e3ba1e8-803d-43e8-8351-1144799b737d|VO|CONVERTER")
public class TreatmentPackageItemDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseVoConverter treatItemBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailVoDataAssembler treatmentPackageItemDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailVoDataCollector treatmentPackageItemDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoService treatmentPackageItemDtoService;

    /** 使用默认方式组装TreatmentPackageItemDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "0065babc-8e3e-32c9-85c1-c3f38dffe804")
    public TreatmentPackageItemDetailVo convertAndAssembleData(TreatmentPackageItemDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把TreatmentPackageItemDetailDto转换成TreatmentPackageItemDetailVo */
    @AutoGenerated(locked = false, uuid = "3e3ba1e8-803d-43e8-8351-1144799b737d-converter-Map")
    public Map<TreatmentPackageItemDetailDto, TreatmentPackageItemDetailVo>
            convertToTreatmentPackageItemDetailVoMap(List<TreatmentPackageItemDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<TreatItemBaseDto, TreatItemBaseVo> itemInfoMap =
                treatItemBaseVoConverter.convertToTreatItemBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(TreatmentPackageItemDetailDto::getItemInfo)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TreatmentPackageItemDetailDto, TreatmentPackageItemDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TreatmentPackageItemDetailVo vo =
                                                    new TreatmentPackageItemDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setItemInfo(
                                                    dto.getItemInfo() == null
                                                            ? null
                                                            : itemInfoMap.get(dto.getItemInfo()));
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setAmount(dto.getAmount());
                                            vo.setFrequencyId(dto.getFrequencyId());
                                            vo.setInstruction(dto.getInstruction());
                                            vo.setPayBySelf(dto.getPayBySelf());
                                            vo.setTreatmentPackageId(dto.getTreatmentPackageId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TreatmentPackageItemDetailDto转换成TreatmentPackageItemDetailVo */
    @AutoGenerated(locked = true, uuid = "3e3ba1e8-803d-43e8-8351-1144799b737d-converter-list")
    public List<TreatmentPackageItemDetailVo> convertToTreatmentPackageItemDetailVoList(
            List<TreatmentPackageItemDetailDto> dtoList) {
        return new ArrayList<>(convertToTreatmentPackageItemDetailVoMap(dtoList).values());
    }

    /** 使用默认方式组装TreatmentPackageItemDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "83fb336e-49ae-34ab-a14c-d8301637b841")
    public List<TreatmentPackageItemDetailVo> convertAndAssembleDataList(
            List<TreatmentPackageItemDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TreatmentPackageItemDetailVoDataHolder dataHolder =
                new TreatmentPackageItemDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                treatmentPackageItemDtoService.getByIds(
                        dtoList.stream()
                                .map(TreatmentPackageItemDetailDto::getId)
                                .collect(Collectors.toList())));
        Map<String, TreatmentPackageItemDetailVo> voMap =
                convertToTreatmentPackageItemDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        treatmentPackageItemDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        treatmentPackageItemDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把TreatmentPackageItemDetailDto转换成TreatmentPackageItemDetailVo */
    @AutoGenerated(locked = true, uuid = "85678542-b042-38c0-b6d7-04c0b91333bf")
    public TreatmentPackageItemDetailVo convertToTreatmentPackageItemDetailVo(
            TreatmentPackageItemDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTreatmentPackageItemDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
