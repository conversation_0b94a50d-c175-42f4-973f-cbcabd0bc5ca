package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_business.common.enums.PersonHospitalEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "1e6b3953-568e-4992-aa5a-5f5b4f29e425|VO|DEFINITION")
public class TreatmentPackageInfoExVo {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "5c601bce-7980-4764-9d86-b910dd8a206f")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "cb9e789e-8607-42b9-b9a8-2f48d5a61c2f")
    private String id;

    /** 个人或医院 */
    @AutoGenerated(locked = true, uuid = "e600552a-dc15-401d-82a6-71147252d434")
    private PersonHospitalEnum personOrHospital;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "e8adca18-0699-4485-8261-c17ae14012d6")
    private Long sortNumber;

    /** 治疗套餐名称 */
    @AutoGenerated(locked = true, uuid = "5eb45057-22a0-444c-ab7f-b53aa6b32664")
    private String treatPackageName;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2dd66d17-c1fd-4296-b457-8c6bf409be28")
    private List<TreatmentPackageItemDetailVo> treatmentPackageItemDetailList;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "cb3bc0f5-b883-455e-a190-91d2f6f2fc64")
    private Date updatedAt;
}
