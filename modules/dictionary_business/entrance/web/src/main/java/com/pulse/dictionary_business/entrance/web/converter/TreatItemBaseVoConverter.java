package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.TreatItemBaseVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TreatItemBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "be8bfa04-d161-4986-a6f3-d357231fbea5|VO|CONVERTER")
public class TreatItemBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseVoDataAssembler treatItemBaseVoDataAssembler;

    /** 使用默认方式组装TreatItemBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "530d2303-dba7-3828-9261-6db90fc31676")
    public List<TreatItemBaseVo> convertAndAssembleDataList(List<TreatItemBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, TreatItemBaseVo> voMap =
                convertToTreatItemBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        treatItemBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把TreatItemBaseDto转换成TreatItemBaseVo */
    @AutoGenerated(locked = true, uuid = "5cdd340a-dfa3-3b1e-bafa-110547c969d5")
    public TreatItemBaseVo convertToTreatItemBaseVo(TreatItemBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTreatItemBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TreatItemBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "6220b57b-3998-3ebd-bf82-7229b4705253")
    public TreatItemBaseVo convertAndAssembleData(TreatItemBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把TreatItemBaseDto转换成TreatItemBaseVo */
    @AutoGenerated(locked = false, uuid = "be8bfa04-d161-4986-a6f3-d357231fbea5-converter-Map")
    public Map<TreatItemBaseDto, TreatItemBaseVo> convertToTreatItemBaseVoMap(
            List<TreatItemBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<TreatItemBaseDto, TreatItemBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TreatItemBaseVo vo = new TreatItemBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setTreatClassId(dto.getTreatClassId());
                                            vo.setTreatWayId(dto.getTreatWayId());
                                            vo.setHighRiskFlag(dto.getHighRiskFlag());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setBranchInstitutionId(dto.getBranchInstitutionId());
                                            vo.setDefaultTimes(dto.getDefaultTimes());
                                            vo.setMaxTreatTimes(dto.getMaxTreatTimes());
                                            vo.setTreatRoomId(dto.getTreatRoomId());
                                            vo.setDeviceId(dto.getDeviceId());
                                            vo.setPerformDeptId(dto.getPerformDeptId());
                                            vo.setApplicableScope(dto.getApplicableScope());
                                            vo.setReferenceExecutionTime(
                                                    dto.getReferenceExecutionTime());
                                            vo.setRemark(dto.getRemark());
                                            vo.setTreatItemName(dto.getTreatItemName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TreatItemBaseDto转换成TreatItemBaseVo */
    @AutoGenerated(locked = true, uuid = "be8bfa04-d161-4986-a6f3-d357231fbea5-converter-list")
    public List<TreatItemBaseVo> convertToTreatItemBaseVoList(List<TreatItemBaseDto> dtoList) {
        return new ArrayList<>(convertToTreatItemBaseVoMap(dtoList).values());
    }
}
