package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "b86e55fa-6de1-46d1-bc22-01da92b63e59|VO|DEFINITION")
public class HighRiskDrugBaseVo {
    /** 药品代码 */
    @AutoGenerated(locked = true, uuid = "23e8a35a-fb40-4637-a410-562e025cf25e")
    private String drugCode;

    /** 高风险因素ID */
    @AutoGenerated(locked = true, uuid = "bda0acfa-b922-4ebb-810c-11874b8e760f")
    private String highRiskFactorId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f8bb0287-7e01-4ed3-ab0e-dbbc2fdc51aa")
    private String id;
}
