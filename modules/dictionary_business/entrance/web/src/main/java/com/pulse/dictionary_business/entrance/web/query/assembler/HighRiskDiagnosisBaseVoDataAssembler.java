package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.HighRiskDiagnosisBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** HighRiskDiagnosisBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "ee0eb66a-18a6-3d0a-8568-8b9bfdae48d6")
public class HighRiskDiagnosisBaseVoDataAssembler {

    /** 组装HighRiskDiagnosisBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "5b881080-ac7c-3e50-9bf7-435dd13e0606")
    public void assembleData(Map<String, HighRiskDiagnosisBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装HighRiskDiagnosisBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f0e34e9c-96dd-327c-911e-c1e694161c37")
    public void assembleDataCustomized(List<HighRiskDiagnosisBaseVo> dataList) {
        // 自定义数据组装

    }
}
