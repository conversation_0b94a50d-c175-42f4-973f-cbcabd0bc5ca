package com.pulse.dictionary_business.entrance.web.controller;

import com.pulse.dictionary_business.entrance.web.converter.TreatItemVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageInfoExVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageInfoVoConverter;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageInfoExVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageInfoVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.persist.qto.SearchTreatItemQto;
import com.pulse.dictionary_business.persist.qto.SearchTreatmentPackageExQto;
import com.pulse.dictionary_business.persist.qto.SearchTreatmentPackageInfoQto;
import com.pulse.dictionary_business.service.TreatClassDictionaryBOService;
import com.pulse.dictionary_business.service.TreatItemBOService;
import com.pulse.dictionary_business.service.TreatmentPackageInfoBOService;
import com.pulse.dictionary_business.service.bto.ChangeTreatClassDisabledFlagBto;
import com.pulse.dictionary_business.service.bto.DeleteTreatmentPackageInfoBto;
import com.pulse.dictionary_business.service.bto.MergeTreatItemBto;
import com.pulse.dictionary_business.service.bto.MergeTreatmentPackageInfoBto;
import com.pulse.dictionary_business.service.bto.UpdateTreatItemBto;
import com.pulse.dictionary_business.service.query.TreatItemBaseDtoQueryService;
import com.pulse.dictionary_business.service.query.TreatmentPackageInfoDtoQueryService;
import com.pulse.dictionary_business.service.query.TreatmentPackageInfoExDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "34a1dd17-5ef4-34eb-8618-793ba342851f")
public class TreatItemController {
    @AutoGenerated(locked = true)
    @Resource
    private TreatClassDictionaryBOService treatClassDictionaryBOService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBOService treatItemBOService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoQueryService treatItemBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemVoConverter treatItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoBOService treatmentPackageInfoBOService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoQueryService treatmentPackageInfoDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExDtoQueryService treatmentPackageInfoExDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExVoConverter treatmentPackageInfoExVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoVoConverter treatmentPackageInfoVoConverter;

    /** 查找治疗项目 */
    @PublicInterface(id = "06517cc4-0436-4f50-a652-8984eb4b01d7", version = "1748590344102")
    @AutoGenerated(locked = false, uuid = "06517cc4-0436-4f50-a652-8984eb4b01d7")
    @RequestMapping(
            value = {"/api/dictionary-business/search-treat-item-page"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<TreatItemVo> searchTreatItemPage(@Valid SearchTreatItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<TreatItemBaseDto> dtoResult =
                treatItemBaseDtoQueryService.searchTreatItemPaged(qto);
        VSQueryResult<TreatItemVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(treatItemVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找治疗项目 */
    @PublicInterface(id = "0d2dc73b-ff4a-48bf-8ab3-9aa5f060a029", version = "1748589987310")
    @AutoGenerated(locked = false, uuid = "0d2dc73b-ff4a-48bf-8ab3-9aa5f060a029")
    @RequestMapping(
            value = {"/api/dictionary-business/search-treat-item-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<TreatItemVo> searchTreatItemPaged(@Valid SearchTreatItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<TreatItemBaseDto> dtoResult =
                treatItemBaseDtoQueryService.searchTreatItemPaged(qto);
        VSQueryResult<TreatItemVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(treatItemVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改治疗项目 */
    @PublicInterface(id = "24dc91b4-4f26-4467-ac16-572a36913de6", version = "1748240724734")
    @AutoGenerated(locked = false, uuid = "24dc91b4-4f26-4467-ac16-572a36913de6")
    @RequestMapping(
            value = {"/api/dictionary-business/update-treat-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateTreatItem(@Valid UpdateTreatItemBto updateTreatItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = treatItemBOService.updateTreatItem(updateTreatItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 检索治疗套餐 */
    @PublicInterface(id = "2f8f7a43-c566-4475-86d6-89b3f5860dea", version = "1748941776399")
    @AutoGenerated(locked = false, uuid = "2f8f7a43-c566-4475-86d6-89b3f5860dea")
    @RequestMapping(
            value = {"/api/dictionary-business/search-treatment-package-ex"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<TreatmentPackageInfoExVo> searchTreatmentPackageEx(
            @Valid SearchTreatmentPackageExQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageInfoExDto> rpcResult =
                treatmentPackageInfoExDtoQueryService.searchTreatmentPackageEx(qto);
        List<TreatmentPackageInfoExVo> result =
                treatmentPackageInfoExVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找治疗套餐 */
    @PublicInterface(id = "380aa156-e62e-45b1-a132-9bd037bd2cb0", version = "1748339920226")
    @AutoGenerated(locked = false, uuid = "380aa156-e62e-45b1-a132-9bd037bd2cb0")
    @RequestMapping(
            value = {"/api/dictionary-business/search-treatment-package-info"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<TreatmentPackageInfoVo> searchTreatmentPackageInfo(
            @Valid SearchTreatmentPackageInfoQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageInfoDto> rpcResult =
                treatmentPackageInfoDtoQueryService.searchTreatmentPackageInfo(qto);
        List<TreatmentPackageInfoVo> result =
                treatmentPackageInfoVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除治疗套餐项目 */
    @PublicInterface(id = "48b11651-0df9-402c-80da-7592ff471a74", version = "1748326253931")
    @AutoGenerated(locked = false, uuid = "48b11651-0df9-402c-80da-7592ff471a74")
    @RequestMapping(
            value = {"/api/dictionary-business/delete-treatment-package-info"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteTreatmentPackageInfo(
            @Valid DeleteTreatmentPackageInfoBto deleteTreatmentPackageInfoBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                treatmentPackageInfoBOService.deleteTreatmentPackageInfo(
                        deleteTreatmentPackageInfoBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存治疗项目 */
    @PublicInterface(id = "66cfd924-85ce-4053-b826-035dc8ba77ea", version = "1748240446110")
    @AutoGenerated(locked = false, uuid = "66cfd924-85ce-4053-b826-035dc8ba77ea")
    @RequestMapping(
            value = {"/api/dictionary-business/merge-treat-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeTreatItem(@Valid MergeTreatItemBto mergeTreatItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = treatItemBOService.mergeTreatItem(mergeTreatItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找治疗项目行数 */
    @PublicInterface(id = "9a52698b-dfec-40f8-87d1-c8cd9f81c82f", version = "1748240148640")
    @AutoGenerated(locked = false, uuid = "9a52698b-dfec-40f8-87d1-c8cd9f81c82f")
    @RequestMapping(
            value = {"/api/dictionary-business/search-treat-item-count"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Integer searchTreatItemCount(@Valid SearchTreatItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        Integer result = treatItemBaseDtoQueryService.searchTreatItemCount(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存治疗套餐 */
    @PublicInterface(id = "a742961b-b063-496a-8387-bc89fbc8b2d8", version = "1748335089187")
    @AutoGenerated(locked = false, uuid = "a742961b-b063-496a-8387-bc89fbc8b2d8")
    @RequestMapping(
            value = {"/api/dictionary-business/merge-treatment-package-info"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeTreatmentPackageInfo(
            @Valid MergeTreatmentPackageInfoBto mergeTreatmentPackageInfoBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                treatmentPackageInfoBOService.mergeTreatmentPackageInfo(
                        mergeTreatmentPackageInfoBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更改治疗类别状态 */
    @PublicInterface(id = "d819af5c-8bda-47ca-929d-165499f06c6e", version = "1748591356502")
    @AutoGenerated(locked = false, uuid = "d819af5c-8bda-47ca-929d-165499f06c6e")
    @RequestMapping(
            value = {"/api/dictionary-business/change-treat-class-disabled-flag"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String changeTreatClassDisabledFlag(
            @Valid ChangeTreatClassDisabledFlagBto changeTreatClassDisabledFlagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                treatClassDictionaryBOService.changeTreatClassDisabledFlag(
                        changeTreatClassDisabledFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
