package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.TreatItemBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** TreatItemBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "0d30c5b6-d30d-3d6e-91dc-a1488cfaa43c")
public class TreatItemBaseVoDataAssembler {

    /** 批量自定义组装TreatItemBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "32e20c8b-dcad-38c0-b309-75e7d4dab31c")
    public void assembleDataCustomized(List<TreatItemBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装TreatItemBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "81f94214-4059-3d51-ae94-e60615a67418")
    public void assembleData(Map<String, TreatItemBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
