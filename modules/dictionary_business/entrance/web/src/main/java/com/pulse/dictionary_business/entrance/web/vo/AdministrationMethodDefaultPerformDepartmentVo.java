package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_business.common.enums.DefaultPerformDepartmentTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "b4f226cb-8f40-459c-8600-1197b6185550|VO|DEFINITION")
public class AdministrationMethodDefaultPerformDepartmentVo {
    /** 给药方式ID */
    @AutoGenerated(locked = true, uuid = "d618405b-a65e-4a84-a58f-fcabde47d611")
    private String administrationMethodId;

    /** 适用对象ID */
    @AutoGenerated(locked = true, uuid = "e964f9a0-4fcf-49a0-8830-3aec741defae")
    private String applicableObjectId;

    /** 适用对象类型 */
    @AutoGenerated(locked = true, uuid = "f4254c68-03ab-45bb-97ce-db67bfbbc364")
    private String applicableObjectType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "e7af8521-f179-4ea9-8acc-5ca0ccf36d39")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "46aff119-e57e-4f7b-8df6-75edab1099a2")
    private String createdBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c66cebb3-231a-4461-9d1d-18d610d8676e")
    private String id;

    /** 住院默认执行科室类型 */
    @AutoGenerated(locked = true, uuid = "1921f96b-aa3d-4273-bb67-bfec867f88ac")
    private DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType;

    /** 执行科室ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "94627eb3-8eab-4667-8b8e-fc26ef66bacc")
    private DictionaryBusinessRefOrganizationVo performDepartment;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "bf2a7735-322f-4c85-933f-cf59420ec666")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "cf1cc0ef-c86c-4d93-a8c2-608d49209280")
    private String updatedBy;

    /** 使用范围列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "5f4f073e-2399-434c-9dc1-f6c32e0add17")
    private List<String> useScopeList;
}
