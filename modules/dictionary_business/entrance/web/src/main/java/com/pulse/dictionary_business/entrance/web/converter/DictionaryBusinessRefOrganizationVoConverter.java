package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.DictionaryBusinessRefOrganizationVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.vo.DictionaryBusinessRefOrganizationVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DictionaryBusinessRefOrganizationVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3de3a192-c21b-4094-b991-dcfb264ac158|VO|CONVERTER")
public class DictionaryBusinessRefOrganizationVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DictionaryBusinessRefOrganizationVoDataAssembler
            dictionaryBusinessRefOrganizationVoDataAssembler;

    /** 使用默认方式组装DictionaryBusinessRefOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "20f3d5ea-1b2f-32b3-8885-1b666d7c96c6")
    public DictionaryBusinessRefOrganizationVo convertAndAssembleData(OrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationBaseDto转换成DictionaryBusinessRefOrganizationVo */
    @AutoGenerated(locked = false, uuid = "3de3a192-c21b-4094-b991-dcfb264ac158-converter-Map")
    public Map<OrganizationBaseDto, DictionaryBusinessRefOrganizationVo>
            convertToDictionaryBusinessRefOrganizationVoMap(List<OrganizationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<OrganizationBaseDto, DictionaryBusinessRefOrganizationVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DictionaryBusinessRefOrganizationVo vo =
                                                    new DictionaryBusinessRefOrganizationVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setType(dto.getType());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setAlias(dto.getAlias());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationBaseDto转换成DictionaryBusinessRefOrganizationVo */
    @AutoGenerated(locked = true, uuid = "3de3a192-c21b-4094-b991-dcfb264ac158-converter-list")
    public List<DictionaryBusinessRefOrganizationVo>
            convertToDictionaryBusinessRefOrganizationVoList(List<OrganizationBaseDto> dtoList) {
        return new ArrayList<>(convertToDictionaryBusinessRefOrganizationVoMap(dtoList).values());
    }

    /** 把OrganizationBaseDto转换成DictionaryBusinessRefOrganizationVo */
    @AutoGenerated(locked = true, uuid = "4358fb35-a772-33ed-977d-13b77639f42d")
    public DictionaryBusinessRefOrganizationVo convertToDictionaryBusinessRefOrganizationVo(
            OrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDictionaryBusinessRefOrganizationVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DictionaryBusinessRefOrganizationVo列表数据 */
    @AutoGenerated(locked = true, uuid = "b933bac9-e047-3a7f-83f1-4357b9ea017d")
    public List<DictionaryBusinessRefOrganizationVo> convertAndAssembleDataList(
            List<OrganizationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DictionaryBusinessRefOrganizationVo> voMap =
                convertToDictionaryBusinessRefOrganizationVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        dictionaryBusinessRefOrganizationVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
