package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemChargeItemChargeItemExtVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemChargeItemChargeItemExtVoDataAssembler.ClinicItemChargeItemChargeItemExtVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ClinicItemChargeItemChargeItemExtVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemChargeItemExtVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemChargeItemExtDto;
import com.pulse.dictionary_business.service.ClinicItemChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ClinicItemChargeItemChargeItemExtVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9e122299-d6a1-4b24-b60f-5368dae3b742|VO|CONVERTER")
public class ClinicItemChargeItemChargeItemExtVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemVoConverter chargeItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoService clinicItemChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemChargeItemExtVoDataAssembler
            clinicItemChargeItemChargeItemExtVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemChargeItemExtVoDataCollector
            clinicItemChargeItemChargeItemExtVoDataCollector;

    /** 把ClinicItemChargeItemChargeItemExtDto转换成ClinicItemChargeItemChargeItemExtVo */
    @AutoGenerated(locked = true, uuid = "754babd0-ea84-36b2-8303-0df16cc522d9")
    public ClinicItemChargeItemChargeItemExtVo convertToClinicItemChargeItemChargeItemExtVo(
            ClinicItemChargeItemChargeItemExtDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToClinicItemChargeItemChargeItemExtVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装ClinicItemChargeItemChargeItemExtVo数据 */
    @AutoGenerated(locked = true, uuid = "9986cd68-3d02-3a58-9225-8ac0a51186d4")
    public ClinicItemChargeItemChargeItemExtVo convertAndAssembleData(
            ClinicItemChargeItemChargeItemExtDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ClinicItemChargeItemChargeItemExtDto转换成ClinicItemChargeItemChargeItemExtVo */
    @AutoGenerated(locked = false, uuid = "9e122299-d6a1-4b24-b60f-5368dae3b742-converter-Map")
    public Map<ClinicItemChargeItemChargeItemExtDto, ClinicItemChargeItemChargeItemExtVo>
            convertToClinicItemChargeItemChargeItemExtVoMap(
                    List<ClinicItemChargeItemChargeItemExtDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ChargeItemDto, ChargeItemVo> chargeItemMap =
                chargeItemVoConverter.convertToChargeItemVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ClinicItemChargeItemChargeItemExtDto::getChargeItem)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ClinicItemChargeItemChargeItemExtDto, ClinicItemChargeItemChargeItemExtVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ClinicItemChargeItemChargeItemExtVo vo =
                                                    new ClinicItemChargeItemChargeItemExtVo();
                                            vo.setId(dto.getId());
                                            vo.setClinicItemId(dto.getClinicItemId());
                                            vo.setChargeItem(
                                                    dto.getChargeItem() == null
                                                            ? null
                                                            : chargeItemMap.get(
                                                                    dto.getChargeItem()));
                                            vo.setCampusId(dto.getCampusId());
                                            vo.setChargeItemCount(dto.getChargeItemCount());
                                            vo.setFilmFeeType(dto.getFilmFeeType());
                                            vo.setGraphicFeeFlag(dto.getGraphicFeeFlag());
                                            vo.setDigitalImagingFeeFlag(
                                                    dto.getDigitalImagingFeeFlag());
                                            vo.setUseScopeList(dto.getUseScopeList());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setFirstTimeBillingFlag(
                                                    dto.getFirstTimeBillingFlag());
                                            vo.setPerformDepartmentId(dto.getPerformDepartmentId());
                                            vo.setAllowModifyCountFlag(
                                                    dto.getAllowModifyCountFlag());
                                            vo.setClinicItemBillingType(
                                                    dto.getClinicItemBillingType());
                                            vo.setClinicInsuranceCode(dto.getClinicInsuranceCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ClinicItemChargeItemChargeItemExtDto转换成ClinicItemChargeItemChargeItemExtVo */
    @AutoGenerated(locked = true, uuid = "9e122299-d6a1-4b24-b60f-5368dae3b742-converter-list")
    public List<ClinicItemChargeItemChargeItemExtVo>
            convertToClinicItemChargeItemChargeItemExtVoList(
                    List<ClinicItemChargeItemChargeItemExtDto> dtoList) {
        return new ArrayList<>(convertToClinicItemChargeItemChargeItemExtVoMap(dtoList).values());
    }

    /** 使用默认方式组装ClinicItemChargeItemChargeItemExtVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d296f3e9-7058-3dfd-815b-ee23d8faf8d3")
    public List<ClinicItemChargeItemChargeItemExtVo> convertAndAssembleDataList(
            List<ClinicItemChargeItemChargeItemExtDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ClinicItemChargeItemChargeItemExtVoDataHolder dataHolder =
                new ClinicItemChargeItemChargeItemExtVoDataHolder();
        dataHolder.setRootBaseDtoList(
                clinicItemChargeItemBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ClinicItemChargeItemChargeItemExtDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ClinicItemChargeItemChargeItemExtVo> voMap =
                convertToClinicItemChargeItemChargeItemExtVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        clinicItemChargeItemChargeItemExtVoDataCollector.collectDataWithDtoData(
                dtoList, dataHolder);
        clinicItemChargeItemChargeItemExtVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
