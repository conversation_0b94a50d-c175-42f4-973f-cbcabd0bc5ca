package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "3e3ba1e8-803d-43e8-8351-1144799b737d|VO|DEFINITION")
public class TreatmentPackageItemDetailVo {
    /** 数量 */
    @AutoGenerated(locked = true, uuid = "5e642cf2-15e8-4ee7-8d1a-d440823dc7cf")
    private Integer amount;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "5953f4be-c98d-4b84-8514-b3b0cfa89e6a")
    private Date createdAt;

    /** 频率 */
    @AutoGenerated(locked = true, uuid = "a273dc73-6613-4a9e-bacf-d7d3ad5c322b")
    private String frequencyId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "02da0b73-2ff3-42c5-b228-ebb5135f714e")
    private String id;

    /** 嘱托 */
    @AutoGenerated(locked = true, uuid = "a4f8826c-fbd7-496e-afe1-ff45c9517f5c")
    private String instruction;

    /** 项目信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d562a047-e429-4762-ad17-f86175649c57")
    private TreatItemBaseVo itemInfo;

    /** 自费 */
    @AutoGenerated(locked = true, uuid = "410a022c-befd-4cca-a51f-815952cc7cca")
    private Boolean payBySelf;

    /** 治疗套餐ID */
    @AutoGenerated(locked = true, uuid = "9da9dd28-8117-420b-84db-6ff086c25b05")
    private String treatmentPackageId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "a8c746e9-d07d-4477-8e2b-d5fadbb68b6c")
    private Date updatedAt;
}
