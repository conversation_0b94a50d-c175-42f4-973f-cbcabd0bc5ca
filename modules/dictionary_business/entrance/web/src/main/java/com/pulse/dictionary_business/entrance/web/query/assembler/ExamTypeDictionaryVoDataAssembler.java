package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamTypeDictionaryVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "becda5d5-bf00-3033-be34-25b17625fe44")
public class ExamTypeDictionaryVoDataAssembler {

    /** 批量自定义组装ExamTypeDictionaryVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b58912ff-4a54-3ae6-b393-893325ee1ece")
    public void assembleDataCustomized(List<ExamTypeDictionaryVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ExamTypeDictionaryVo数据 */
    @AutoGenerated(locked = true, uuid = "d547b747-eb17-3af8-add3-e0f1d8db21ba")
    public void assembleData(Map<String, ExamTypeDictionaryVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
