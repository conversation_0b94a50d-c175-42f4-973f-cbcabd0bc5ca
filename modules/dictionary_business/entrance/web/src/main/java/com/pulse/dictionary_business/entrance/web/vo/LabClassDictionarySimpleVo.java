package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "2db29387-a578-4a35-be41-374473b0fe54|VO|DEFINITION")
public class LabClassDictionarySimpleVo {
    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4fc4f2b3-f576-49c4-bcd2-9cefc894d811")
    private InputCodeEo inputCode;

    /** 检验类别代码 */
    @AutoGenerated(locked = true, uuid = "068aa6f6-1176-42ae-bdea-5ea9ced5570b")
    private String labClassCode;

    /** 检验类别名称 */
    @AutoGenerated(locked = true, uuid = "1b43def5-3812-49ec-a297-367ce23d9e5b")
    private String labClassName;

    /** 叶子标志 */
    @AutoGenerated(locked = true, uuid = "f31bd3ef-f1ba-44c3-a205-2c9630ac7fdf")
    private Boolean leafFlag;

    /** 父类代码 */
    @AutoGenerated(locked = true, uuid = "87ccbfbf-fcb7-4300-93b3-184668931618")
    private String parentClassCode;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "275ec5a3-4c31-42dd-a5aa-9e1b427ea0ed")
    private Long sortNumber;
}
