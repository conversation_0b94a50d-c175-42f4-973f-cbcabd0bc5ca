package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageInfoExVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageInfoExVoDataAssembler.TreatmentPackageInfoExVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.TreatmentPackageInfoExVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageInfoExVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemDetailVo;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TreatmentPackageInfoExVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "1e6b3953-568e-4992-aa5a-5f5b4f29e425|VO|CONVERTER")
public class TreatmentPackageInfoExVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExVoDataAssembler treatmentPackageInfoExVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoExVoDataCollector treatmentPackageInfoExVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDetailVoConverter treatmentPackageItemDetailVoConverter;

    /** 把TreatmentPackageInfoExDto转换成TreatmentPackageInfoExVo */
    @AutoGenerated(locked = false, uuid = "1e6b3953-568e-4992-aa5a-5f5b4f29e425-converter-Map")
    public Map<TreatmentPackageInfoExDto, TreatmentPackageInfoExVo>
            convertToTreatmentPackageInfoExVoMap(List<TreatmentPackageInfoExDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<TreatmentPackageItemDetailDto, TreatmentPackageItemDetailVo>
                treatmentPackageItemDetailListMap =
                        treatmentPackageItemDetailVoConverter
                                .convertToTreatmentPackageItemDetailVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getTreatmentPackageItemDetailList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getTreatmentPackageItemDetailList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<TreatmentPackageInfoExDto, TreatmentPackageInfoExVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TreatmentPackageInfoExVo vo =
                                                    new TreatmentPackageInfoExVo();
                                            vo.setId(dto.getId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setPersonOrHospital(dto.getPersonOrHospital());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setTreatPackageName(dto.getTreatPackageName());
                                            vo.setTreatmentPackageItemDetailList(
                                                    dto.getTreatmentPackageItemDetailList() == null
                                                            ? null
                                                            : dto
                                                                    .getTreatmentPackageItemDetailList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    treatmentPackageItemDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TreatmentPackageInfoExDto转换成TreatmentPackageInfoExVo */
    @AutoGenerated(locked = true, uuid = "1e6b3953-568e-4992-aa5a-5f5b4f29e425-converter-list")
    public List<TreatmentPackageInfoExVo> convertToTreatmentPackageInfoExVoList(
            List<TreatmentPackageInfoExDto> dtoList) {
        return new ArrayList<>(convertToTreatmentPackageInfoExVoMap(dtoList).values());
    }

    /** 使用默认方式组装TreatmentPackageInfoExVo数据 */
    @AutoGenerated(locked = true, uuid = "85cd9fcf-8e90-3924-9dfb-5ed91c0193c2")
    public TreatmentPackageInfoExVo convertAndAssembleData(TreatmentPackageInfoExDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把TreatmentPackageInfoExDto转换成TreatmentPackageInfoExVo */
    @AutoGenerated(locked = true, uuid = "9198bdae-5f81-3a61-a175-26b8122b33b6")
    public TreatmentPackageInfoExVo convertToTreatmentPackageInfoExVo(
            TreatmentPackageInfoExDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTreatmentPackageInfoExVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TreatmentPackageInfoExVo列表数据 */
    @AutoGenerated(locked = true, uuid = "a62510ad-6cf0-3242-900e-7bae046555c5")
    public List<TreatmentPackageInfoExVo> convertAndAssembleDataList(
            List<TreatmentPackageInfoExDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TreatmentPackageInfoExVoDataHolder dataHolder = new TreatmentPackageInfoExVoDataHolder();
        dataHolder.setRootBaseDtoList(
                treatmentPackageInfoDtoService.getByIds(
                        dtoList.stream()
                                .map(TreatmentPackageInfoExDto::getId)
                                .collect(Collectors.toList())));
        Map<String, TreatmentPackageInfoExVo> voMap =
                convertToTreatmentPackageInfoExVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        treatmentPackageInfoExVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        treatmentPackageInfoExVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
