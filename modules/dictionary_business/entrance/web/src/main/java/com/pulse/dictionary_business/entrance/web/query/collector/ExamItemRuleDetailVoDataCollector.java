package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.ClinicItemDictionaryBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamItemDocumentTemplateVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamItemRuleDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemRuleDetailVoDataAssembler.ExamItemRuleDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemDocumentTemplateVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemDocumentTemplateBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemRuleDetailDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.pulse.dictionary_business.service.ExamItemDocumentTemplateBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ExamItemRuleDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "d871fe1a-ff3d-35bf-946f-475b4bf70ee9")
public class ExamItemRuleDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseVoConverter clinicItemDictionaryBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemBaseDtoService examItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemDocumentTemplateBaseDtoService examItemDocumentTemplateBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemDocumentTemplateVoConverter examItemDocumentTemplateVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemRuleDetailVoConverter examItemRuleDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemRuleDetailVoDataCollector examItemRuleDetailVoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "08b70a5e-4255-3e27-9508-de57b8d9fdcd")
    private void fillDataWhenNecessary(ExamItemRuleDetailVoDataHolder dataHolder) {
        List<ExamItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.clinicItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getClinicItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ClinicItemDictionaryBaseDto> baseDtoList =
                    clinicItemDictionaryBaseDtoService
                            .getByClinicItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            ClinicItemDictionaryBaseDto::getClinicItemId))
                            .collect(Collectors.toList());
            Map<String, ClinicItemDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ClinicItemDictionaryBaseDto::getClinicItemId,
                                            Function.identity()));
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> dtoVoMap =
                    clinicItemDictionaryBaseVoConverter.convertToClinicItemDictionaryBaseVoMap(
                            baseDtoList);
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.clinicItem =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getClinicItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.examItemDocumentTemplate == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ExamItemDocumentTemplateBaseDto> baseDtoList =
                    examItemDocumentTemplateBaseDtoService
                            .getByExamItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ExamItemDocumentTemplateBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<ExamItemDocumentTemplateBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ExamItemDocumentTemplateBaseDto::getExamItemId));
            Map<ExamItemDocumentTemplateBaseDto, ExamItemDocumentTemplateVo> dtoVoMap =
                    examItemDocumentTemplateVoConverter.convertToExamItemDocumentTemplateVoMap(
                            baseDtoList);
            Map<ExamItemDocumentTemplateBaseDto, ExamItemDocumentTemplateVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.examItemDocumentTemplate =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "42e7c515-15ae-394e-8c08-3dd047b6909a")
    public void collectDataDefault(ExamItemRuleDetailVoDataHolder dataHolder) {
        examItemRuleDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取ExamItemRuleDetailDto数据填充ExamItemRuleDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "f051cbcd-9ac0-3f3a-88f6-05cfc677b0e0")
    public void collectDataWithDtoData(
            List<ExamItemRuleDetailDto> dtoList, ExamItemRuleDetailVoDataHolder dataHolder) {
        List<ClinicItemDictionaryBaseDto> clinicItemList = new ArrayList<>();
        List<ExamItemDocumentTemplateBaseDto> examItemDocumentTemplateList = new ArrayList<>();

        for (ExamItemRuleDetailDto rootDto : dtoList) {
            ClinicItemDictionaryBaseDto clinicItemDto = rootDto.getClinicItem();
            if (clinicItemDto != null) {
                clinicItemList.add(clinicItemDto);
            }
            ExamItemDocumentTemplateBaseDto examItemDocumentTemplateDto =
                    rootDto.getExamItemDocumentTemplate();
            if (examItemDocumentTemplateDto != null) {
                examItemDocumentTemplateList.add(examItemDocumentTemplateDto);
            }
        }

        // access clinicItem
        Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> clinicItemVoMap =
                clinicItemDictionaryBaseVoConverter.convertToClinicItemDictionaryBaseVoMap(
                        clinicItemList);
        dataHolder.clinicItem =
                clinicItemList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> clinicItemVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access examItemDocumentTemplate
        Map<ExamItemDocumentTemplateBaseDto, ExamItemDocumentTemplateVo>
                examItemDocumentTemplateVoMap =
                        examItemDocumentTemplateVoConverter.convertToExamItemDocumentTemplateVoMap(
                                examItemDocumentTemplateList);
        dataHolder.examItemDocumentTemplate =
                examItemDocumentTemplateList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> examItemDocumentTemplateVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
