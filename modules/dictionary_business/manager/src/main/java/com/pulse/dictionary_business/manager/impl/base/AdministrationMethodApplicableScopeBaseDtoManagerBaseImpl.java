package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.AdministrationMethodApplicableScopeBaseDtoManager;
import com.pulse.dictionary_business.manager.converter.AdministrationMethodApplicableScopeBaseDtoConverter;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodApplicableScopeBaseDto;
import com.pulse.dictionary_business.persist.dos.AdministrationMethodApplicableScope;
import com.pulse.dictionary_business.persist.mapper.AdministrationMethodApplicableScopeDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "6a26edf4-a48a-4f7e-86bb-59d3e5dc3f80|DTO|BASE_MANAGER_IMPL")
public abstract class AdministrationMethodApplicableScopeBaseDtoManagerBaseImpl
        implements AdministrationMethodApplicableScopeBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodApplicableScopeBaseDtoConverter
            administrationMethodApplicableScopeBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodApplicableScopeDao administrationMethodApplicableScopeDao;

    @AutoGenerated(locked = true, uuid = "04686579-6709-3d48-a57d-ea09284b5693")
    public List<AdministrationMethodApplicableScopeBaseDto>
            doConvertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                    List<AdministrationMethodApplicableScope>
                            administrationMethodApplicableScopeList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(administrationMethodApplicableScopeList)) {
            return Collections.emptyList();
        }

        Map<String, AdministrationMethodApplicableScopeBaseDto> dtoMap =
                administrationMethodApplicableScopeBaseDtoConverter
                        .convertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                                administrationMethodApplicableScopeList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        AdministrationMethodApplicableScopeBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<AdministrationMethodApplicableScopeBaseDto>
                administrationMethodApplicableScopeBaseDtoList = new ArrayList<>();
        for (AdministrationMethodApplicableScope i : administrationMethodApplicableScopeList) {
            AdministrationMethodApplicableScopeBaseDto administrationMethodApplicableScopeBaseDto =
                    dtoMap.get(i.getId());
            if (administrationMethodApplicableScopeBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            administrationMethodApplicableScopeBaseDtoList.add(
                    administrationMethodApplicableScopeBaseDto);
        }
        return administrationMethodApplicableScopeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "106bffc3-9b9e-342c-99ca-d1484012e3bf")
    @Override
    public AdministrationMethodApplicableScopeBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodApplicableScopeBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        AdministrationMethodApplicableScopeBaseDto administrationMethodApplicableScopeBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return administrationMethodApplicableScopeBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "52481862-f3da-3a60-852f-37165de25fd6")
    @Override
    public List<AdministrationMethodApplicableScopeBaseDto> getByAdministrationMethodIds(
            List<String> administrationMethodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(administrationMethodId)) {
            return Collections.emptyList();
        }

        List<AdministrationMethodApplicableScope> administrationMethodApplicableScopeList =
                administrationMethodApplicableScopeDao.getByAdministrationMethodIds(
                        administrationMethodId);
        if (CollectionUtil.isEmpty(administrationMethodApplicableScopeList)) {
            return Collections.emptyList();
        }

        return doConvertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                administrationMethodApplicableScopeList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9924ea4f-059c-3aba-95e4-50062e5465c3")
    @Override
    public List<AdministrationMethodApplicableScopeBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<AdministrationMethodApplicableScope> administrationMethodApplicableScopeList =
                administrationMethodApplicableScopeDao.getByIds(id);
        if (CollectionUtil.isEmpty(administrationMethodApplicableScopeList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, AdministrationMethodApplicableScope> administrationMethodApplicableScopeMap =
                administrationMethodApplicableScopeList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        administrationMethodApplicableScopeList =
                id.stream()
                        .map(i -> administrationMethodApplicableScopeMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                administrationMethodApplicableScopeList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d499a738-d690-38bd-9f14-bfdc8334de3c")
    @Override
    public List<AdministrationMethodApplicableScopeBaseDto> getByAdministrationMethodId(
            String administrationMethodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodApplicableScopeBaseDto>
                administrationMethodApplicableScopeBaseDtoList =
                        getByAdministrationMethodIds(Arrays.asList(administrationMethodId));
        return administrationMethodApplicableScopeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
