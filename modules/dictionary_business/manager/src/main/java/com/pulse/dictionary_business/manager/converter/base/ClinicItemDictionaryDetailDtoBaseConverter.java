package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ClinicItemDictionaryBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryDetailDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "d5d29fb1-6edc-4824-9c0a-14eaa6042332|DTO|BASE_CONVERTER")
public class ClinicItemDictionaryDetailDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoManager clinicItemDictionaryBaseDtoManager;

    @AutoGenerated(locked = true)
    public ClinicItemDictionaryDetailDto
            convertFromClinicItemDictionaryBaseDtoToClinicItemDictionaryDetailDto(
                    ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto) {
        return convertFromClinicItemDictionaryBaseDtoToClinicItemDictionaryDetailDto(
                        List.of(clinicItemDictionaryBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ClinicItemDictionaryDetailDto>
            convertFromClinicItemDictionaryBaseDtoToClinicItemDictionaryDetailDto(
                    List<ClinicItemDictionaryBaseDto> clinicItemDictionaryBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicItemDictionaryBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ClinicItemDictionaryDetailDto> clinicItemDictionaryDetailDtoList = new ArrayList<>();
        for (ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto :
                clinicItemDictionaryBaseDtoList) {
            if (clinicItemDictionaryBaseDto == null) {
                continue;
            }
            ClinicItemDictionaryDetailDto clinicItemDictionaryDetailDto =
                    new ClinicItemDictionaryDetailDto();
            clinicItemDictionaryDetailDto.setClinicItemId(
                    clinicItemDictionaryBaseDto.getClinicItemId());
            clinicItemDictionaryDetailDto.setItemType(clinicItemDictionaryBaseDto.getItemType());
            clinicItemDictionaryDetailDto.setClinicItemName(
                    clinicItemDictionaryBaseDto.getClinicItemName());
            clinicItemDictionaryDetailDto.setInputCode(clinicItemDictionaryBaseDto.getInputCode());
            clinicItemDictionaryDetailDto.setRemark(clinicItemDictionaryBaseDto.getRemark());
            clinicItemDictionaryDetailDto.setEnableFlag(
                    clinicItemDictionaryBaseDto.getEnableFlag());
            clinicItemDictionaryDetailDto.setInstitutionId(
                    clinicItemDictionaryBaseDto.getInstitutionId());
            clinicItemDictionaryDetailDto.setAuditFlag(clinicItemDictionaryBaseDto.getAuditFlag());
            clinicItemDictionaryDetailDto.setAuditDate(clinicItemDictionaryBaseDto.getAuditDate());
            clinicItemDictionaryDetailDto.setAuditOperatorId(
                    clinicItemDictionaryBaseDto.getAuditOperatorId());
            clinicItemDictionaryDetailDto.setDescription(
                    clinicItemDictionaryBaseDto.getDescription());
            clinicItemDictionaryDetailDto.setLimitGender(
                    clinicItemDictionaryBaseDto.getLimitGender());
            clinicItemDictionaryDetailDto.setIncludeCurrentDepartmentFlag(
                    clinicItemDictionaryBaseDto.getIncludeCurrentDepartmentFlag());
            clinicItemDictionaryDetailDto.setRescueFlag(
                    clinicItemDictionaryBaseDto.getRescueFlag());
            clinicItemDictionaryDetailDto.setBillingAttribute(
                    clinicItemDictionaryBaseDto.getBillingAttribute());
            clinicItemDictionaryDetailDto.setSpecialNeedFlag(
                    clinicItemDictionaryBaseDto.getSpecialNeedFlag());
            clinicItemDictionaryDetailDto.setOnlySelectSettingDepartmentFlag(
                    clinicItemDictionaryBaseDto.getOnlySelectSettingDepartmentFlag());
            clinicItemDictionaryDetailDto.setOperationCode(
                    clinicItemDictionaryBaseDto.getOperationCode());
            clinicItemDictionaryDetailDto.setCreatedAt(clinicItemDictionaryBaseDto.getCreatedAt());
            clinicItemDictionaryDetailDto.setUpdatedAt(clinicItemDictionaryBaseDto.getUpdatedAt());
            clinicItemDictionaryDetailDto.setDisabledReason(
                    clinicItemDictionaryBaseDto.getDisabledReason());
            clinicItemDictionaryDetailDto.setUseScopeList(
                    clinicItemDictionaryBaseDto.getUseScopeList());
            clinicItemDictionaryDetailDto.setCampusIdList(
                    clinicItemDictionaryBaseDto.getCampusIdList());
            clinicItemDictionaryDetailDto.setAgeMinLimit(
                    clinicItemDictionaryBaseDto.getAgeMinLimit());
            clinicItemDictionaryDetailDto.setAgeMaxLimit(
                    clinicItemDictionaryBaseDto.getAgeMaxLimit());
            clinicItemDictionaryDetailDto.setClinicItemCatalogId(
                    clinicItemDictionaryBaseDto.getClinicItemCatalogId());
            clinicItemDictionaryDetailDto.setStandardCode(
                    clinicItemDictionaryBaseDto.getStandardCode());
            clinicItemDictionaryDetailDto.setFrequency(clinicItemDictionaryBaseDto.getFrequency());
            clinicItemDictionaryDetailDto.setUnit(clinicItemDictionaryBaseDto.getUnit());
            clinicItemDictionaryDetailDto.setPrintFlag(clinicItemDictionaryBaseDto.getPrintFlag());
            clinicItemDictionaryDetailDto.setExclusionType(
                    clinicItemDictionaryBaseDto.getExclusionType());
            clinicItemDictionaryDetailDto.setExclusionTime(
                    clinicItemDictionaryBaseDto.getExclusionTime());
            clinicItemDictionaryDetailDto.setDelayDays(clinicItemDictionaryBaseDto.getDelayDays());
            clinicItemDictionaryDetailDto.setOutpDefaultPerformDepartmentId(
                    clinicItemDictionaryBaseDto.getOutpDefaultPerformDepartmentId());
            clinicItemDictionaryDetailDto.setInpDefaultPerformDepartmentType(
                    clinicItemDictionaryBaseDto.getInpDefaultPerformDepartmentType());
            clinicItemDictionaryDetailDto.setFrequencyNotAllowedModifyFlag(
                    clinicItemDictionaryBaseDto.getFrequencyNotAllowedModifyFlag());
            clinicItemDictionaryDetailDto.setDoubleSignatureFlag(
                    clinicItemDictionaryBaseDto.getDoubleSignatureFlag());
            clinicItemDictionaryDetailDto.setLimitWardIdList(
                    clinicItemDictionaryBaseDto.getLimitWardIdList());
            clinicItemDictionaryDetailDto.setPdaPerformFlag(
                    clinicItemDictionaryBaseDto.getPdaPerformFlag());
            clinicItemDictionaryDetailDto.setCardPrintTypeList(
                    clinicItemDictionaryBaseDto.getCardPrintTypeList());
            clinicItemDictionaryDetailDto.setOrderFrequencyBillingType(
                    clinicItemDictionaryBaseDto.getOrderFrequencyBillingType());
            clinicItemDictionaryDetailDto.setBillingInterval(
                    clinicItemDictionaryBaseDto.getBillingInterval());
            clinicItemDictionaryDetailDto.setAlias(clinicItemDictionaryBaseDto.getAlias());
            clinicItemDictionaryDetailDto.setCreatedBy(clinicItemDictionaryBaseDto.getCreatedBy());
            clinicItemDictionaryDetailDto.setUpdatedBy(clinicItemDictionaryBaseDto.getUpdatedBy());
            clinicItemDictionaryDetailDto.setItemSpecification(
                    clinicItemDictionaryBaseDto.getItemSpecification());
            clinicItemDictionaryDetailDto.setSortNumber(
                    clinicItemDictionaryBaseDto.getSortNumber());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            clinicItemDictionaryDetailDtoList.add(clinicItemDictionaryDetailDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return clinicItemDictionaryDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ClinicItemDictionaryBaseDto
            convertFromClinicItemDictionaryDetailDtoToClinicItemDictionaryBaseDto(
                    ClinicItemDictionaryDetailDto clinicItemDictionaryDetailDto) {
        return convertFromClinicItemDictionaryDetailDtoToClinicItemDictionaryBaseDto(
                        List.of(clinicItemDictionaryDetailDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ClinicItemDictionaryBaseDto>
            convertFromClinicItemDictionaryDetailDtoToClinicItemDictionaryBaseDto(
                    List<ClinicItemDictionaryDetailDto> clinicItemDictionaryDetailDtoList) {
        if (CollectionUtil.isEmpty(clinicItemDictionaryDetailDtoList)) {
            return new ArrayList<>();
        }
        return clinicItemDictionaryBaseDtoManager.getByClinicItemIds(
                clinicItemDictionaryDetailDtoList.stream()
                        .map(ClinicItemDictionaryDetailDto::getClinicItemId)
                        .collect(Collectors.toList()));
    }
}
