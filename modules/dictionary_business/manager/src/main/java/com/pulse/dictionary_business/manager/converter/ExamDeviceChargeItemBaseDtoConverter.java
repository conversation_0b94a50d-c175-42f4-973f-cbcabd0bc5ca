package com.pulse.dictionary_business.manager.converter;

import com.pulse.dictionary_business.manager.converter.base.ExamDeviceChargeItemBaseDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "0d3601d9-d9e8-4073-af38-308a9546f750|DTO|CONVERTER")
public class ExamDeviceChargeItemBaseDtoConverter
        extends ExamDeviceChargeItemBaseDtoBaseConverter {}
