package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.TreatmentPackageItemDtoManager;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "135755c3-4f90-4408-9d86-87116330d49c|DTO|BASE_CONVERTER")
public class TreatmentPackageItemDetailDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoManager treatmentPackageItemDtoManager;

    @AutoGenerated(locked = true)
    public TreatmentPackageItemDto
            convertFromTreatmentPackageItemDetailDtoToTreatmentPackageItemDto(
                    TreatmentPackageItemDetailDto treatmentPackageItemDetailDto) {
        return convertFromTreatmentPackageItemDetailDtoToTreatmentPackageItemDto(
                        List.of(treatmentPackageItemDetailDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<TreatmentPackageItemDto>
            convertFromTreatmentPackageItemDetailDtoToTreatmentPackageItemDto(
                    List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList) {
        if (CollectionUtil.isEmpty(treatmentPackageItemDetailDtoList)) {
            return new ArrayList<>();
        }
        return treatmentPackageItemDtoManager.getByIds(
                treatmentPackageItemDetailDtoList.stream()
                        .map(TreatmentPackageItemDetailDto::getId)
                        .collect(Collectors.toList()));
    }

    @AutoGenerated(locked = true)
    public TreatmentPackageItemDetailDto
            convertFromTreatmentPackageItemDtoToTreatmentPackageItemDetailDto(
                    TreatmentPackageItemDto treatmentPackageItemDto) {
        return convertFromTreatmentPackageItemDtoToTreatmentPackageItemDetailDto(
                        List.of(treatmentPackageItemDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<TreatmentPackageItemDetailDto>
            convertFromTreatmentPackageItemDtoToTreatmentPackageItemDetailDto(
                    List<TreatmentPackageItemDto> treatmentPackageItemDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(treatmentPackageItemDtoList)) {
            return new ArrayList<>();
        }
        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList = new ArrayList<>();
        for (TreatmentPackageItemDto treatmentPackageItemDto : treatmentPackageItemDtoList) {
            if (treatmentPackageItemDto == null) {
                continue;
            }
            TreatmentPackageItemDetailDto treatmentPackageItemDetailDto =
                    new TreatmentPackageItemDetailDto();
            treatmentPackageItemDetailDto.setId(treatmentPackageItemDto.getId());
            treatmentPackageItemDetailDto.setCreatedAt(treatmentPackageItemDto.getCreatedAt());
            treatmentPackageItemDetailDto.setUpdatedAt(treatmentPackageItemDto.getUpdatedAt());
            treatmentPackageItemDetailDto.setAmount(treatmentPackageItemDto.getAmount());
            treatmentPackageItemDetailDto.setFrequencyId(treatmentPackageItemDto.getFrequencyId());
            treatmentPackageItemDetailDto.setInstruction(treatmentPackageItemDto.getInstruction());
            treatmentPackageItemDetailDto.setPayBySelf(treatmentPackageItemDto.getPayBySelf());
            treatmentPackageItemDetailDto.setTreatmentPackageId(
                    treatmentPackageItemDto.getTreatmentPackageId());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            treatmentPackageItemDetailDtoList.add(treatmentPackageItemDetailDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return treatmentPackageItemDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
