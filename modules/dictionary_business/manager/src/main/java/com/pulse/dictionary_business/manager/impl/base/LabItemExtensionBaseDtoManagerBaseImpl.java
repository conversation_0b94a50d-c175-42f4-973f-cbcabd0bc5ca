package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.LabItemExtensionBaseDtoManager;
import com.pulse.dictionary_business.manager.converter.LabItemExtensionBaseDtoConverter;
import com.pulse.dictionary_business.manager.dto.LabItemExtensionBaseDto;
import com.pulse.dictionary_business.persist.dos.LabItemExtension;
import com.pulse.dictionary_business.persist.mapper.LabItemExtensionDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "b2d0bb66-b571-40ce-adc8-81223e6e9593|DTO|BASE_MANAGER_IMPL")
public abstract class LabItemExtensionBaseDtoManagerBaseImpl
        implements LabItemExtensionBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private LabItemExtensionBaseDtoConverter labItemExtensionBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private LabItemExtensionDao labItemExtensionDao;

    @AutoGenerated(locked = true, uuid = "5326d66a-0c0d-3e70-b142-7cd7a12639c6")
    @Override
    public List<LabItemExtensionBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<LabItemExtension> labItemExtensionList = labItemExtensionDao.getByIds(id);
        if (CollectionUtil.isEmpty(labItemExtensionList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, LabItemExtension> labItemExtensionMap =
                labItemExtensionList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        labItemExtensionList =
                id.stream()
                        .map(i -> labItemExtensionMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromLabItemExtensionToLabItemExtensionBaseDto(labItemExtensionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8574e399-fb67-3a67-bfa7-c4eea8dd6c15")
    @Override
    public List<LabItemExtensionBaseDto> getByLabItemIds(List<String> labItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(labItemId)) {
            return Collections.emptyList();
        }

        List<LabItemExtension> labItemExtensionList =
                labItemExtensionDao.getByLabItemIds(labItemId);
        if (CollectionUtil.isEmpty(labItemExtensionList)) {
            return Collections.emptyList();
        }

        return doConvertFromLabItemExtensionToLabItemExtensionBaseDto(labItemExtensionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bc1353fa-7954-3181-805b-4bece2cdb009")
    @Override
    public LabItemExtensionBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<LabItemExtensionBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        LabItemExtensionBaseDto labItemExtensionBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return labItemExtensionBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d53a9b19-2cb3-37cc-a921-ce1375f7a741")
    @Override
    public List<LabItemExtensionBaseDto> getByLabItemId(String labItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<LabItemExtensionBaseDto> labItemExtensionBaseDtoList =
                getByLabItemIds(Arrays.asList(labItemId));
        return labItemExtensionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "faf9b27d-a8eb-3373-9e37-2a2e300f8585")
    public List<LabItemExtensionBaseDto> doConvertFromLabItemExtensionToLabItemExtensionBaseDto(
            List<LabItemExtension> labItemExtensionList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(labItemExtensionList)) {
            return Collections.emptyList();
        }

        Map<String, LabItemExtensionBaseDto> dtoMap =
                labItemExtensionBaseDtoConverter
                        .convertFromLabItemExtensionToLabItemExtensionBaseDto(labItemExtensionList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        LabItemExtensionBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<LabItemExtensionBaseDto> labItemExtensionBaseDtoList = new ArrayList<>();
        for (LabItemExtension i : labItemExtensionList) {
            LabItemExtensionBaseDto labItemExtensionBaseDto = dtoMap.get(i.getId());
            if (labItemExtensionBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            labItemExtensionBaseDtoList.add(labItemExtensionBaseDto);
        }
        return labItemExtensionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
