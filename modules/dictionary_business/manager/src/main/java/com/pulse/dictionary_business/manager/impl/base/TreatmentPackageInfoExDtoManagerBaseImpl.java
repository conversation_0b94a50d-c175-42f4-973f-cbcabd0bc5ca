package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.dictionary_business.manager.TreatmentPackageInfoDtoManager;
import com.pulse.dictionary_business.manager.TreatmentPackageInfoExDtoManager;
import com.pulse.dictionary_business.manager.TreatmentPackageItemDetailDtoManager;
import com.pulse.dictionary_business.manager.converter.TreatmentPackageInfoDtoConverter;
import com.pulse.dictionary_business.manager.converter.TreatmentPackageInfoExDtoConverter;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.persist.dos.TreatmentPackageInfo;
import com.pulse.dictionary_business.persist.mapper.TreatmentPackageInfoDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "e7ab6558-9f07-411e-83bd-2c20a8eacf5d|DTO|BASE_MANAGER_IMPL")
public abstract class TreatmentPackageInfoExDtoManagerBaseImpl
        implements TreatmentPackageInfoExDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageInfoDao treatmentPackageInfoDao;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageInfoDtoConverter treatmentPackageInfoDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageInfoDtoManager treatmentPackageInfoDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageInfoExDtoConverter treatmentPackageInfoExDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDetailDtoManager treatmentPackageItemDetailDtoManager;

    @AutoGenerated(locked = true, uuid = "1293ef62-f2ce-30dd-bb1a-10fb1e2ae50c")
    @Override
    public TreatmentPackageInfoExDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageInfoExDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        TreatmentPackageInfoExDto treatmentPackageInfoExDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return treatmentPackageInfoExDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a791ea14-d770-3c83-a33f-fddb8a3f4ffa")
    @Override
    public List<TreatmentPackageInfoExDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<TreatmentPackageInfo> treatmentPackageInfoList = treatmentPackageInfoDao.getByIds(id);
        if (CollectionUtil.isEmpty(treatmentPackageInfoList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, TreatmentPackageInfo> treatmentPackageInfoMap =
                treatmentPackageInfoList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        treatmentPackageInfoList =
                id.stream()
                        .map(i -> treatmentPackageInfoMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromTreatmentPackageInfoToTreatmentPackageInfoExDto(
                treatmentPackageInfoList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d4bc16e4-1cbf-3e7f-9482-310ca7bd142a")
    public List<TreatmentPackageInfoExDto>
            doConvertFromTreatmentPackageInfoToTreatmentPackageInfoExDto(
                    List<TreatmentPackageInfo> treatmentPackageInfoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(treatmentPackageInfoList)) {
            return Collections.emptyList();
        }

        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList =
                treatmentPackageItemDetailDtoManager.getByTreatmentPackageIds(
                        treatmentPackageInfoList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<TreatmentPackageItemDetailDto>> idTreatmentPackageItemDetailDtoListMap =
                treatmentPackageItemDetailDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getTreatmentPackageId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<TreatmentPackageInfoDto> baseDtoList =
                treatmentPackageInfoDtoConverter
                        .convertFromTreatmentPackageInfoToTreatmentPackageInfoDto(
                                treatmentPackageInfoList);
        Map<String, TreatmentPackageInfoExDto> dtoMap =
                treatmentPackageInfoExDtoConverter
                        .convertFromTreatmentPackageInfoDtoToTreatmentPackageInfoExDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        TreatmentPackageInfoExDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<TreatmentPackageInfoExDto> treatmentPackageInfoExDtoList = new ArrayList<>();
        for (TreatmentPackageInfo i : treatmentPackageInfoList) {
            TreatmentPackageInfoExDto treatmentPackageInfoExDto = dtoMap.get(i.getId());
            if (treatmentPackageInfoExDto == null) {
                continue;
            }

            if (null != i.getId()) {
                treatmentPackageInfoExDto.setTreatmentPackageItemDetailList(
                        idTreatmentPackageItemDetailDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            treatmentPackageInfoExDtoList.add(treatmentPackageInfoExDto);
        }
        return treatmentPackageInfoExDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
