package com.pulse.dictionary_business.manager.bo.base;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.converter.InputCodeEoConverter;
import com.pulse.dictionary_business.manager.bo.ExclusionOrderTypeBO;
import com.pulse.dictionary_business.manager.bo.ExclusionOrderTypeItemDetailBO;
import com.pulse.dictionary_business.persist.dos.ExclusionOrderType;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.Valid;

@DoNotModify
@Table(name = "exclusion_order_type")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "67d40b44-8072-35fd-ba5e-4d17db77c385")
public abstract class BaseExclusionOrderTypeBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "45bf85e4-dc71-5927-a00d-768eb249e140")
    private Date createdAt;

    /** 创建者 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "f8c53430-3d8e-44e4-8fdd-30de98bba8a1")
    private String createdBy;

    @JoinColumn(name = "exclusion_order_type_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ExclusionOrderTypeItemDetailBO> exclusionOrderTypeItemDetailBOSet = new HashSet<>();

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "2c99527e-3984-4ea2-bf1b-f4d5ba69b5b0")
    @Id
    private String id;

    /** 输入代码 */
    @Column(name = "input_code")
    @Valid
    @AutoGenerated(locked = true, uuid = "3275cc24-c3b2-4bc5-ab3b-576820b14768")
    @Convert(converter = InputCodeEoConverter.class)
    private InputCodeEo inputCode;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "aaaf337f-2f99-4e42-9fa5-bdc55a07d00e")
    @Version
    private Long lockVersion;

    /** 医嘱类型名称 */
    @Column(name = "order_type_name")
    @AutoGenerated(locked = true, uuid = "b561703d-8a02-400a-809c-f55b74874464")
    private String orderTypeName;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "9cac1362-be29-5a6a-b56e-ad2adaf03f19")
    private Date updatedAt;

    /** 更新者 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "fa839d75-6ce3-4fa5-afd0-1ea22f7ae2a1")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public ExclusionOrderType convertToExclusionOrderType() {
        ExclusionOrderType entity = new ExclusionOrderType();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "orderTypeName",
                "inputCode",
                "updatedBy",
                "createdBy",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static ExclusionOrderTypeBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        ExclusionOrderTypeBO exclusionOrderType =
                (ExclusionOrderTypeBO)
                        session.createQuery("from ExclusionOrderTypeBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return exclusionOrderType;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Set<ExclusionOrderTypeItemDetailBO> getExclusionOrderTypeItemDetailBOSet() {
        return this.exclusionOrderTypeItemDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public InputCodeEo getInputCode() {
        return this.inputCode;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getOrderTypeName() {
        return this.orderTypeName;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    private void setExclusionOrderTypeItemDetailBOSet(
            Set<ExclusionOrderTypeItemDetailBO> exclusionOrderTypeItemDetailBOSet) {
        this.exclusionOrderTypeItemDetailBOSet = exclusionOrderTypeItemDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setId(String id) {
        this.id = id;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setInputCode(InputCodeEo inputCode) {
        this.inputCode = inputCode;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (ExclusionOrderTypeBO) this;
    }

    @AutoGenerated(locked = true)
    public ExclusionOrderTypeBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (ExclusionOrderTypeBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
