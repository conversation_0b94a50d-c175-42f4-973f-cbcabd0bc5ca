package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.common.enums.ExamTypeBillingModeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "d0e2ef61-c841-4b82-ad48-2aa076ad20c6|DTO|DEFINITION")
public class ExamDeviceDto {
    /** 计费模式 */
    @AutoGenerated(locked = true, uuid = "375bd60b-d9db-4362-955d-d0e31537f61d")
    private ExamTypeBillingModeEnum billingMode;

    /** 院区id */
    @Valid
    @AutoGenerated(locked = true, uuid = "c99f60e8-**************-05972167abd6")
    private List<String> campusIdList;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "302f5c60-4193-4d8a-a120-297a70d600b4")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "c0f83136-6630-420c-92c2-42d9eb963b2d")
    private String createdBy;

    /** 设备ID */
    @AutoGenerated(locked = true, uuid = "0f4bcc6c-9df1-409b-bf58-c414c5221f51")
    private String deviceId;

    /** 设备名称 */
    @AutoGenerated(locked = true, uuid = "b478ecce-b8e3-47c6-914a-8c47278cedf4")
    private String deviceName;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "f42ab26b-95ee-42df-90bd-ca2130bacf74")
    private Boolean enableFlag;

    /** 检查类型id */
    @Valid
    @AutoGenerated(locked = true, uuid = "b8a1a138-fdec-48cb-8898-e3b6d80343fe")
    private ExamTypeDictionaryBaseDto examType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "91b939bf-59c4-474c-be0f-f70d45dd9904")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d2b9f007-eb53-4f44-891c-2dfe9306d97e")
    private InputCodeEo inputCode;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "72e61387-faaa-4e5e-8e82-c0d43b533378")
    private Long sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "a9107d49-ae15-4944-be3c-25cc9d4943ab")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "dd8b0c53-949c-4a5b-a248-afea707fe05e")
    private String updatedBy;
}
