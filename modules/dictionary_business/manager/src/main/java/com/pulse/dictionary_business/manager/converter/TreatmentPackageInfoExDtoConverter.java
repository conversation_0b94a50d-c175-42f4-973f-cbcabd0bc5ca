package com.pulse.dictionary_business.manager.converter;

import com.pulse.dictionary_business.manager.converter.base.TreatmentPackageInfoExDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "e7ab6558-9f07-411e-83bd-2c20a8eacf5d|DTO|CONVERTER")
public class TreatmentPackageInfoExDtoConverter extends TreatmentPackageInfoExDtoBaseConverter {}
