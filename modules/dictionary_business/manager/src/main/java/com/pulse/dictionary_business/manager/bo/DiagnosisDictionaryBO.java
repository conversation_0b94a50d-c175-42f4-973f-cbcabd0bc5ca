package com.pulse.dictionary_business.manager.bo;

import com.pulse.dictionary_business.manager.bo.base.BaseDiagnosisDictionaryBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "diagnosis_dictionary")
@Entity
@AutoGenerated(locked = false, uuid = "e084dee5-7629-4b58-9d61-b880e376ff1c|BO|DEFINITION")
public class DiagnosisDictionaryBO extends BaseDiagnosisDictionaryBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "e084dee5-7629-4b58-9d61-b880e376ff1c|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
