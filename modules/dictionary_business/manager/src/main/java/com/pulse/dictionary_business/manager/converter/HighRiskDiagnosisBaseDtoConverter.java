package com.pulse.dictionary_business.manager.converter;

import com.pulse.dictionary_business.manager.converter.base.HighRiskDiagnosisBaseDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "6305594c-c10e-4838-b8f5-9b663edc17bc|DTO|CONVERTER")
public class HighRiskDiagnosisBaseDtoConverter extends HighRiskDiagnosisBaseDtoBaseConverter {}
