package com.pulse.dictionary_business.manager;

import com.pulse.dictionary_business.manager.dto.ExamItemBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "1467e317-533d-4325-8716-46ec0afb8f7f|DTO|MANAGER")
public interface ExamItemBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "1d10bfa6-4959-3fac-996f-b3888b8b5c93")
    List<ExamItemBaseDto> getByClinicItemIds(List<String> clinicItemId);

    @AutoGenerated(locked = true, uuid = "2d071d2e-aef9-39fe-a2eb-737bbf29df8d")
    List<ExamItemBaseDto> getByExamCatalogId(String examCatalogId);

    @AutoGenerated(locked = true, uuid = "67b27b38-2c3f-3f0f-bfe8-c6d6cfba139f")
    ExamItemBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "6c41b665-e578-3674-bd5c-233f827e6421")
    ExamItemBaseDto getByClinicItemId(String clinicItemId);

    @AutoGenerated(locked = true, uuid = "7ffb2ad1-3efe-3e3d-a9fd-36edf430cbe6")
    List<ExamItemBaseDto> getByExamTypeId(String examTypeId);

    @AutoGenerated(locked = true, uuid = "82c0aad7-29db-3f40-87d2-daded3b4e48c")
    List<ExamItemBaseDto> getByExamCatalogIds(List<String> examCatalogId);

    @AutoGenerated(locked = true, uuid = "cfde78be-067e-3124-b009-02868891e8d1")
    List<ExamItemBaseDto> getByExamTypeIds(List<String> examTypeId);

    @AutoGenerated(locked = true, uuid = "fcd3fc7b-801a-3e79-aa5b-22d9902c61ed")
    List<ExamItemBaseDto> getByIds(List<String> id);
}
