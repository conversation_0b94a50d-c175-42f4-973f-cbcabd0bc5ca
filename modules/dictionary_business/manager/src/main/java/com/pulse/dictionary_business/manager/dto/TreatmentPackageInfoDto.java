package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_business.common.enums.PersonHospitalEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "d52d7e16-1581-457b-b89e-a52863f057b6|DTO|DEFINITION")
public class TreatmentPackageInfoDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "c2a6260b-4b64-4b05-8ea5-84532c19cba5")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "1c0bc0fb-be2b-4dfc-806c-2f8485deeeb3")
    private String id;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "d81aeccc-2b4b-4aa6-b22f-7dcdead21ff5")
    private Long lockVersion;

    /** 个人或医院 */
    @AutoGenerated(locked = true, uuid = "7076f7bf-af92-45c0-a4cd-be1c953ce910")
    private PersonHospitalEnum personOrHospital;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "aff781fa-afe9-4934-857f-e8acb88f9a28")
    private Long sortNumber;

    /** 治疗套餐名称 */
    @AutoGenerated(locked = true, uuid = "18a748bb-c318-4c54-8889-e6b0c92ce030")
    private String treatPackageName;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "6eda4b04-7591-4535-a6d7-71807ec4cfb2")
    private List<TreatmentPackageItemDto> treatmentPackageItemList;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "8d4a469d-bab3-47da-bb06-221e43620a8b")
    private Date updatedAt;
}
