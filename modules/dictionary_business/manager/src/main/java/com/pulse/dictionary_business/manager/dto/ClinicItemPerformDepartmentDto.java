package com.pulse.dictionary_business.manager.dto;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "1ecb6340-13c6-416e-9354-2bdbef0d83a5|DTO|DEFINITION")
public class ClinicItemPerformDepartmentDto {
    /** 诊疗项目id */
    @AutoGenerated(locked = true, uuid = "6ebaab9b-e5b6-4ffa-a664-925bec91d725")
    private String clinicItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "bd3373cc-7ae0-440b-b30c-b5e85b9028c4")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c39a5f52-30e4-43f9-9135-89f43ce7f04b")
    private Long id;

    /** 开单科室id */
    @Valid
    @AutoGenerated(locked = true, uuid = "2ee048f1-865c-47ec-acbf-57e4c2461c0a")
    private OrganizationBaseDto orderDepartment;

    /** 组织ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "585fd586-f0d9-419f-b9e4-cf8cc4b470d0")
    private OrganizationBaseDto organization;

    /** 执行科室id */
    @Valid
    @AutoGenerated(locked = true, uuid = "55baae29-8741-4935-aa8e-2e67dc569ae3")
    private OrganizationBaseDto performDepartment;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "70c2457d-64b3-438f-8eb6-ad09878b2b7f")
    private Date updatedAt;

    /** 使用组织类型 */
    @AutoGenerated(locked = true, uuid = "68c27dfd-d84b-4d81-a653-3902f0ea183b")
    private String useOrganizationType;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "df854bae-8047-47fc-9732-512988adbcaf")
    private List<String> useScopeList;
}
