package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.FrequencyBaseDtoManager;
import com.pulse.dictionary_business.manager.converter.FrequencyBaseDtoConverter;
import com.pulse.dictionary_business.manager.dto.FrequencyBaseDto;
import com.pulse.dictionary_business.persist.dos.Frequency;
import com.pulse.dictionary_business.persist.mapper.FrequencyDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "1df3bc0b-5c94-40d6-af22-39f87d2f31f8|DTO|BASE_MANAGER_IMPL")
public abstract class FrequencyBaseDtoManagerBaseImpl implements FrequencyBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private FrequencyBaseDtoConverter frequencyBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private FrequencyDao frequencyDao;

    @AutoGenerated(locked = true, uuid = "07c3eadd-2429-3930-a345-5ff434edfc36")
    @Override
    public List<FrequencyBaseDto> getByCodes(List<String> code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(code)) {
            return Collections.emptyList();
        }

        List<Frequency> frequencyList = frequencyDao.getByCodes(code);
        if (CollectionUtil.isEmpty(frequencyList)) {
            return Collections.emptyList();
        }

        return doConvertFromFrequencyToFrequencyBaseDto(frequencyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1721a8b3-ec46-30cd-977c-aed5efbf0304")
    @Override
    public List<FrequencyBaseDto> getByDisplayNames(List<String> displayName) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(displayName)) {
            return Collections.emptyList();
        }

        List<Frequency> frequencyList = frequencyDao.getByDisplayNames(displayName);
        if (CollectionUtil.isEmpty(frequencyList)) {
            return Collections.emptyList();
        }

        return doConvertFromFrequencyToFrequencyBaseDto(frequencyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "663c3bc5-7959-392f-948e-3e47673e3420")
    public List<FrequencyBaseDto> doConvertFromFrequencyToFrequencyBaseDto(
            List<Frequency> frequencyList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(frequencyList)) {
            return Collections.emptyList();
        }

        Map<String, FrequencyBaseDto> dtoMap =
                frequencyBaseDtoConverter
                        .convertFromFrequencyToFrequencyBaseDto(frequencyList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        FrequencyBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<FrequencyBaseDto> frequencyBaseDtoList = new ArrayList<>();
        for (Frequency i : frequencyList) {
            FrequencyBaseDto frequencyBaseDto = dtoMap.get(i.getId());
            if (frequencyBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            frequencyBaseDtoList.add(frequencyBaseDto);
        }
        return frequencyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "72c3bef2-7fb3-3300-98ce-9803e2db1a44")
    @Override
    public FrequencyBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<FrequencyBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        FrequencyBaseDto frequencyBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return frequencyBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "983c78d6-3e90-319d-8d93-01d25fbf26d9")
    @Override
    public FrequencyBaseDto getByDisplayName(String displayName) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<FrequencyBaseDto> ret = getByDisplayNames(Arrays.asList(displayName));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        FrequencyBaseDto frequencyBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return frequencyBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b72c198b-1230-360c-858d-3e301c77aa04")
    @Override
    public FrequencyBaseDto getByCode(String code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<FrequencyBaseDto> ret = getByCodes(Arrays.asList(code));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        FrequencyBaseDto frequencyBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return frequencyBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f2ee8227-1a01-3487-8702-6900da6d5600")
    @Override
    public List<FrequencyBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Frequency> frequencyList = frequencyDao.getByIds(id);
        if (CollectionUtil.isEmpty(frequencyList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Frequency> frequencyMap =
                frequencyList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        frequencyList =
                id.stream()
                        .map(i -> frequencyMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromFrequencyToFrequencyBaseDto(frequencyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
