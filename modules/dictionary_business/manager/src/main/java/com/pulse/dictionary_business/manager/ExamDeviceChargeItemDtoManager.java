package com.pulse.dictionary_business.manager;

import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "d09aae3d-4d0c-4865-a77d-576e42ed7822|DTO|MANAGER")
public interface ExamDeviceChargeItemDtoManager {

    @AutoGenerated(locked = true, uuid = "0310f356-25b4-33de-813b-d598bfb8d173")
    List<ExamDeviceChargeItemDto> getByChargeItemIds(List<String> chargeItemId);

    @AutoGenerated(locked = true, uuid = "1029173d-78e9-3624-b91d-359d2fc389d7")
    ExamDeviceChargeItemDto getById(String id);

    @AutoGenerated(locked = true, uuid = "3b1c8c6b-1c46-3d6d-8a2a-810e25471b9a")
    List<ExamDeviceChargeItemDto> getByChargeItemId(String chargeItemId);

    @AutoGenerated(locked = true, uuid = "839b43d3-18dd-3725-a850-85735f7dc37a")
    List<ExamDeviceChargeItemDto> getByExamDeviceId(String examDeviceId);

    @AutoGenerated(locked = true, uuid = "d3a1d032-d594-369e-8929-849e393c7370")
    List<ExamDeviceChargeItemDto> getByExamDeviceIds(List<String> examDeviceId);

    @AutoGenerated(locked = true, uuid = "fd7abeff-8c7a-34e4-87e3-63ec2d4986b0")
    List<ExamDeviceChargeItemDto> getByIds(List<String> id);
}
