package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.AdministrationMethodBaseDtoManager;
import com.pulse.dictionary_business.manager.converter.AdministrationMethodBaseDtoConverter;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodBaseDto;
import com.pulse.dictionary_business.persist.dos.AdministrationMethod;
import com.pulse.dictionary_business.persist.mapper.AdministrationMethodDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "3e8a4ca1-bcff-4478-95cb-03e46f1778b5|DTO|BASE_MANAGER_IMPL")
public abstract class AdministrationMethodBaseDtoManagerBaseImpl
        implements AdministrationMethodBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodBaseDtoConverter administrationMethodBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodDao administrationMethodDao;

    @AutoGenerated(locked = true, uuid = "83cc15b6-c0f2-3412-b9df-5356b52c3fdc")
    @Override
    public AdministrationMethodBaseDto getByCode(String code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodBaseDto> ret = getByCodes(Arrays.asList(code));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        AdministrationMethodBaseDto administrationMethodBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return administrationMethodBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c1ec1ecf-5ba9-3ea1-8645-9b5abd27b621")
    @Override
    public List<AdministrationMethodBaseDto> getByCodes(List<String> code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(code)) {
            return Collections.emptyList();
        }

        List<AdministrationMethod> administrationMethodList =
                administrationMethodDao.getByCodes(code);
        if (CollectionUtil.isEmpty(administrationMethodList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, AdministrationMethod> administrationMethodMap =
                administrationMethodList.stream()
                        .collect(Collectors.toMap(i -> i.getCode(), i -> i));
        administrationMethodList =
                code.stream()
                        .map(i -> administrationMethodMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromAdministrationMethodToAdministrationMethodBaseDto(
                administrationMethodList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e4f82067-5503-3720-854f-336205feeaa6")
    public List<AdministrationMethodBaseDto>
            doConvertFromAdministrationMethodToAdministrationMethodBaseDto(
                    List<AdministrationMethod> administrationMethodList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(administrationMethodList)) {
            return Collections.emptyList();
        }

        Map<String, AdministrationMethodBaseDto> dtoMap =
                administrationMethodBaseDtoConverter
                        .convertFromAdministrationMethodToAdministrationMethodBaseDto(
                                administrationMethodList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        AdministrationMethodBaseDto::getCode,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<AdministrationMethodBaseDto> administrationMethodBaseDtoList = new ArrayList<>();
        for (AdministrationMethod i : administrationMethodList) {
            AdministrationMethodBaseDto administrationMethodBaseDto = dtoMap.get(i.getCode());
            if (administrationMethodBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            administrationMethodBaseDtoList.add(administrationMethodBaseDto);
        }
        return administrationMethodBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
