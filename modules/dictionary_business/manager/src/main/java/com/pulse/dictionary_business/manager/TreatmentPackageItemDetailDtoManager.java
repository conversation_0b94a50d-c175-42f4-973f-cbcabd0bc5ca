package com.pulse.dictionary_business.manager;

import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "135755c3-4f90-4408-9d86-87116330d49c|DTO|MANAGER")
public interface TreatmentPackageItemDetailDtoManager {

    @AutoGenerated(locked = true, uuid = "1c637f6c-1d37-32b6-b026-696224ce37c3")
    List<TreatmentPackageItemDetailDto> getByItemIds(List<String> itemId);

    @AutoGenerated(locked = true, uuid = "83e6a8e3-08cc-3cf6-a7b3-878848490bde")
    List<TreatmentPackageItemDetailDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "bf7a8de9-b3d0-31f5-a437-1149c06fc6e5")
    TreatmentPackageItemDetailDto getById(String id);

    @AutoGenerated(locked = true, uuid = "c5ea1b5b-e4be-3b0c-ac9c-8c57fd5f3c61")
    List<TreatmentPackageItemDetailDto> getByTreatmentPackageId(String treatmentPackageId);

    @AutoGenerated(locked = true, uuid = "ceb5bceb-a94c-3cd8-af10-4ff463b138a9")
    List<TreatmentPackageItemDetailDto> getByTreatmentPackageIds(List<String> treatmentPackageId);

    @AutoGenerated(locked = true, uuid = "f4c06a0e-fd5c-388f-b22c-757077acf282")
    List<TreatmentPackageItemDetailDto> getByItemId(String itemId);
}
