package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ClinicItemChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemClinicItemExtDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "b048b5df-0362-4fda-be8a-fa9bee3b89fc|DTO|BASE_CONVERTER")
public class ClinicItemChargeItemClinicItemExtDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoManager clinicItemChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemClinicItemExtDto
            convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemClinicItemExtDto(
                    ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto) {
        return convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemClinicItemExtDto(
                        List.of(clinicItemChargeItemBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ClinicItemChargeItemClinicItemExtDto>
            convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemClinicItemExtDto(
                    List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicItemChargeItemBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ClinicItemChargeItemClinicItemExtDto> clinicItemChargeItemClinicItemExtDtoList =
                new ArrayList<>();
        for (ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto :
                clinicItemChargeItemBaseDtoList) {
            if (clinicItemChargeItemBaseDto == null) {
                continue;
            }
            ClinicItemChargeItemClinicItemExtDto clinicItemChargeItemClinicItemExtDto =
                    new ClinicItemChargeItemClinicItemExtDto();
            clinicItemChargeItemClinicItemExtDto.setId(clinicItemChargeItemBaseDto.getId());
            clinicItemChargeItemClinicItemExtDto.setChargeItemId(
                    clinicItemChargeItemBaseDto.getChargeItemId());
            clinicItemChargeItemClinicItemExtDto.setCampusId(
                    clinicItemChargeItemBaseDto.getCampusId());
            clinicItemChargeItemClinicItemExtDto.setChargeItemCount(
                    clinicItemChargeItemBaseDto.getChargeItemCount());
            clinicItemChargeItemClinicItemExtDto.setFilmFeeType(
                    clinicItemChargeItemBaseDto.getFilmFeeType());
            clinicItemChargeItemClinicItemExtDto.setGraphicFeeFlag(
                    clinicItemChargeItemBaseDto.getGraphicFeeFlag());
            clinicItemChargeItemClinicItemExtDto.setDigitalImagingFeeFlag(
                    clinicItemChargeItemBaseDto.getDigitalImagingFeeFlag());
            clinicItemChargeItemClinicItemExtDto.setUseScopeList(
                    clinicItemChargeItemBaseDto.getUseScopeList());
            clinicItemChargeItemClinicItemExtDto.setFirstTimeBillingFlag(
                    clinicItemChargeItemBaseDto.getFirstTimeBillingFlag());
            clinicItemChargeItemClinicItemExtDto.setPerformDepartmentId(
                    clinicItemChargeItemBaseDto.getPerformDepartmentId());
            clinicItemChargeItemClinicItemExtDto.setAllowModifyCountFlag(
                    clinicItemChargeItemBaseDto.getAllowModifyCountFlag());
            clinicItemChargeItemClinicItemExtDto.setClinicItemBillingType(
                    clinicItemChargeItemBaseDto.getClinicItemBillingType());
            clinicItemChargeItemClinicItemExtDto.setClinicInsuranceCode(
                    clinicItemChargeItemBaseDto.getClinicInsuranceCode());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            clinicItemChargeItemClinicItemExtDtoList.add(clinicItemChargeItemClinicItemExtDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return clinicItemChargeItemClinicItemExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBaseDto
            convertFromClinicItemChargeItemClinicItemExtDtoToClinicItemChargeItemBaseDto(
                    ClinicItemChargeItemClinicItemExtDto clinicItemChargeItemClinicItemExtDto) {
        return convertFromClinicItemChargeItemClinicItemExtDtoToClinicItemChargeItemBaseDto(
                        List.of(clinicItemChargeItemClinicItemExtDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ClinicItemChargeItemBaseDto>
            convertFromClinicItemChargeItemClinicItemExtDtoToClinicItemChargeItemBaseDto(
                    List<ClinicItemChargeItemClinicItemExtDto>
                            clinicItemChargeItemClinicItemExtDtoList) {
        if (CollectionUtil.isEmpty(clinicItemChargeItemClinicItemExtDtoList)) {
            return new ArrayList<>();
        }
        return clinicItemChargeItemBaseDtoManager.getByIds(
                clinicItemChargeItemClinicItemExtDtoList.stream()
                        .map(ClinicItemChargeItemClinicItemExtDto::getId)
                        .collect(Collectors.toList()));
    }
}
