package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "c90ef6f0-fc60-44b9-a0fa-ce936bb738d2|DTO|DEFINITION")
public class LabItemPackageDetailBaseDto {
    /** 数量 */
    @AutoGenerated(locked = true, uuid = "61e9e5b9-bbfe-4221-a8e6-4eabbb49e00d")
    private Long count;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "d28a5b07-599f-4d01-ac1e-cb6560e90e85")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "bf5d750e-10e5-4b7d-9225-f084ce5a7386")
    private Long id;

    /** 检验项目ID */
    @AutoGenerated(locked = true, uuid = "e0eefb26-e8f7-4f6c-bc60-74cc10ab98f3")
    private String labItemId;

    /** 套餐ID */
    @AutoGenerated(locked = true, uuid = "247ad958-d0ad-4a5d-821e-0c568ff21cc7")
    private String packageId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "bae72519-7c9d-4470-971c-9bec54927b22")
    private Date updatedAt;
}
