package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.TreatChargeItemBaseDto;
import com.pulse.dictionary_business.persist.dos.TreatChargeItem;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "863e24be-e7b6-4591-8b47-f29ab42f5f22|DTO|BASE_CONVERTER")
public class TreatChargeItemBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public TreatChargeItemBaseDto convertFromTreatChargeItemToTreatChargeItemBaseDto(
            TreatChargeItem treatChargeItem) {
        return convertFromTreatChargeItemToTreatChargeItemBaseDto(List.of(treatChargeItem)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<TreatChargeItemBaseDto> convertFromTreatChargeItemToTreatChargeItemBaseDto(
            List<TreatChargeItem> treatChargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(treatChargeItemList)) {
            return new ArrayList<>();
        }
        List<TreatChargeItemBaseDto> treatChargeItemBaseDtoList = new ArrayList<>();
        for (TreatChargeItem treatChargeItem : treatChargeItemList) {
            if (treatChargeItem == null) {
                continue;
            }
            TreatChargeItemBaseDto treatChargeItemBaseDto = new TreatChargeItemBaseDto();
            treatChargeItemBaseDto.setId(treatChargeItem.getId());
            treatChargeItemBaseDto.setTreatItemId(treatChargeItem.getTreatItemId());
            treatChargeItemBaseDto.setChargeItemCode(treatChargeItem.getChargeItemCode());
            treatChargeItemBaseDto.setCreatedAt(treatChargeItem.getCreatedAt());
            treatChargeItemBaseDto.setUpdatedAt(treatChargeItem.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            treatChargeItemBaseDtoList.add(treatChargeItemBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return treatChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
