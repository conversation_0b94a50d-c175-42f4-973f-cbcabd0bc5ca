package com.pulse.dictionary_business.manager.facade.dictionary_basic.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.service.bto.MergeBusinessUseScopeBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "94d14303-3d78-3680-a43f-c13863e3e58d")
public class BusinessUseScopeBOServiceInDictionaryBusinessBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "14575446-32db-4437-a45a-c5529d87b5d9|RPC|BASE_ADAPTER")
    public Long mergeBusinessUseScope(MergeBusinessUseScopeBto mergeBusinessUseScopeBto) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("merge_business_use_scope_bto", mergeBusinessUseScopeBto);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("merge_business_use_scope_bto", MergeBusinessUseScopeBto.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/dictionary_basic/14575446-32db-4437-a45a-c5529d87b5d9/BusinessUseScopeBOService-mergeBusinessUseScope",
                        "com.pulse.dictionary_basic.service.BusinessUseScopeBOService",
                        "mergeBusinessUseScope",
                        paramMap,
                        paramTypeMap,
                        "c87071a8-7ca8-438e-998f-eaf79a9cffee",
                        "003f087c-177d-4a69-81e6-c6650b5f6080"),
                new TypeReference<>() {});
    }
}
