package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "863e24be-e7b6-4591-8b47-f29ab42f5f22|DTO|DEFINITION")
public class TreatChargeItemBaseDto {
    /** 收费项目代码 */
    @AutoGenerated(locked = true, uuid = "4ef071cc-ba85-40a9-8cfd-3f109bb82232")
    private String chargeItemCode;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "32bf172e-5753-45b7-a33a-ca3028de0220")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "da9416dc-6ef2-4fbc-bb47-e411a10f76d6")
    private Long id;

    /** 治疗项目ID */
    @AutoGenerated(locked = true, uuid = "b4ac6b18-2d9d-4ae5-8cb2-24562f2f0622")
    private String treatItemId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "cab1fd9f-2267-44ad-86bc-45da8d5573b3")
    private Date updatedAt;
}
