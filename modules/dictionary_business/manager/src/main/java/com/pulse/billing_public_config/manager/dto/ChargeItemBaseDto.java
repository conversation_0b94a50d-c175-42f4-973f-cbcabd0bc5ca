package com.pulse.billing_public_config.manager.dto;

import com.pulse.billing_public_config.common.enums.ChargeItemSourceEnum;
import com.pulse.billing_public_config.common.enums.ChargePackageTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "f4b808b7-872b-427b-b077-4538d0cb65fd|DTO|DEFINITION")
public class ChargeItemBaseDto {
    /** 会计科目分类 */
    @AutoGenerated(locked = true, uuid = "da4b68cc-d1f5-4900-9e05-25acaa5251dc")
    private String accountingSubjectCategory;

    /** 中标代码 */
    @AutoGenerated(locked = true, uuid = "3f0d3a2f-b117-415d-a05b-f76115d9f5c3")
    private String biddingCode;

    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "cdf63acb-0f63-46ac-b0a8-329d5f87b04b")
    private List<String> campusIdList;

    /** 收费项目目录 */
    @AutoGenerated(locked = true, uuid = "0b89fc9f-e8d5-4b95-8bcc-017f8d80a395")
    private String chargeItemCategory;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "730745cb-5250-4d90-bf5d-13c7a6b20303")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "5afa0446-7d43-4631-b3ac-828882ac2057")
    private String createdBy;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "7a0ecefd-f84c-4080-998e-5dadf16f54de")
    private Boolean enableFlag;

    /** 扩展字段 */
    @AutoGenerated(locked = true, uuid = "4f1beda7-aee1-480d-a871-090c488db296")
    private String extensionField;

    /** 厂商编码 */
    @AutoGenerated(locked = true, uuid = "d618d24e-a381-48f2-90b6-767a9eff502e")
    private String firmCode;

    /** 自主定价标志 */
    @AutoGenerated(locked = true, uuid = "*************-47e3-9719-a090249e9508")
    private Boolean independentPricingFlag;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "e16b463c-c61f-48cd-b24a-8fd1facdf534")
    private InputCodeEo inputCode;

    /** 医保项目等级 */
    @AutoGenerated(locked = true, uuid = "09d05b0c-137c-4b08-bfb7-f9ad73c21903")
    private String insuranceItemLevel;

    /** 医保国家代码 */
    @AutoGenerated(locked = true, uuid = "e809119d-5f76-4d6c-96cb-ed5974620afe")
    private String insuranceNationCode;

    /** 医保国家名称 */
    @AutoGenerated(locked = true, uuid = "25bb6c61-718f-4403-a94a-1f9cadb13895")
    private String insuranceNationName;

    /** 医保省码 */
    @AutoGenerated(locked = true, uuid = "635c2d92-fe3a-4b76-a897-a31bc77b611b")
    private String insuranceProvinceCode;

    /** 项目分类 */
    @AutoGenerated(locked = true, uuid = "87165096-0a4e-4878-ba28-739eda72c98e")
    private String itemCategory;

    /** 项目代码 */
    @AutoGenerated(locked = true, uuid = "e9e113a9-bbea-44e5-9201-cf7d9f8ed49b")
    private String itemCode;

    /** 项目内涵 */
    @AutoGenerated(locked = true, uuid = "ea8efb4d-5f21-4ee1-9bff-bd875095428e")
    private String itemConnotation;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "08f737a3-73f1-4444-83a1-2be0184cc49e")
    private String itemName;

    /** 项目规格 */
    @AutoGenerated(locked = true, uuid = "fd8a33e9-f3ce-4cd7-9da5-00c40c7c9e42")
    private String itemSpecification;

    /** 叶子标志 */
    @AutoGenerated(locked = true, uuid = "603552f7-d65a-4d53-81cd-7dceeca3e012")
    private Boolean leafFlag;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "e4d1c46f-5dff-4840-8e40-a39b049a4c88")
    private Long lockVersion;

    /** 病案首页分类 */
    @AutoGenerated(locked = true, uuid = "202920fe-3d2b-4c47-b6ab-4cd65f39f7ff")
    private String medicalRecordCategory;

    /** 病案首页小类 */
    @AutoGenerated(locked = true, uuid = "9f24a52d-d207-42f5-934d-e1511a3e67ce")
    private String medicalRecordSubCategory;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "dbb8ff2f-5c83-4403-80e7-29a9c275fe5c")
    private String organizationId;

    /** 套餐类型 */
    @AutoGenerated(locked = true, uuid = "f6e6c1bc-12f8-48b7-9a29-95d36c1dee96")
    private ChargePackageTypeEnum packageType;

    /** 父项目代码 */
    @AutoGenerated(locked = true, uuid = "a8dc0145-85fa-4ba1-afc7-677ff408b940")
    private String parentItemCode;

    /** 核算分类 */
    @AutoGenerated(locked = true, uuid = "c3a9f768-deed-4d3e-8777-7fdd23dbba96")
    private String reckoningCategory;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "41b53756-f6c5-45a4-a9fe-e5c5f5670bfd")
    private String remark;

    /** 独立结算标识 */
    @AutoGenerated(locked = true, uuid = "86476712-f549-4d68-a6ae-bcdf20369630")
    private Boolean singleSettleFlag;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "d9562e55-dfda-4792-819f-85505647af03")
    private Long sortNumber;

    /** 数据来源 */
    @AutoGenerated(locked = true, uuid = "0171d841-e1ab-4605-b61e-3454cf996bb8")
    private ChargeItemSourceEnum source;

    /** 标准代码 */
    @AutoGenerated(locked = true, uuid = "1b630fc4-00f0-40da-968a-7d17b300954e")
    private String standardCode;

    /** 是否同步过诊疗项目标记 */
    @AutoGenerated(locked = true, uuid = "382bd6b6-b0b5-488f-85e9-3494b8a8d0f1")
    private Boolean synchronizedFlag;

    /** 中医病案首页分类 */
    @AutoGenerated(locked = true, uuid = "5406e82c-ad60-44cd-b3b9-26db4bf81ba8")
    private String tcmMedicalRecordCategory;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "70f87f8a-2e24-4b63-a116-d3e650f6ce0e")
    private String unit;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "d4370018-9190-4a21-ad1d-13ddce09d391")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "ba8b5e73-893e-405b-a87a-18bb44ff3998")
    private String updatedBy;

    /** 使用范围列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2ee10501-553f-47cc-97a1-435c1021bbf5")
    private List<String> useScopeList;
}
