package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_business.common.enums.DefaultPerformDepartmentTypeEnum;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "d09e3b59-a91e-4a3f-8882-23804d8da7e5|DTO|DEFINITION")
public class AdministrationMethodDefaultPerformDepartmentDto {
    /** 给药方式ID */
    @AutoGenerated(locked = true, uuid = "7b0d2b90-6005-4aac-b9d5-694f15d1f9dd")
    private String administrationMethodId;

    /** 适用对象ID */
    @AutoGenerated(locked = true, uuid = "b47051a3-2ae9-4620-87bb-cf2a4c2014c2")
    private String applicableObjectId;

    /** 适用对象类型 */
    @AutoGenerated(locked = true, uuid = "839807b7-ae76-4ae8-9190-87d19025532b")
    private String applicableObjectType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "6cb49088-e02d-44f2-914f-6f54a9694517")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "1de487ec-549a-43d1-803e-95e50b88a1dc")
    private String createdBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "bdff78c4-68e0-4a3e-b07d-3058ca8687f5")
    private String id;

    /** 住院默认执行科室类型 */
    @AutoGenerated(locked = true, uuid = "ac5969e9-9dc5-4718-acea-6e7443cf0f7a")
    private DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType;

    /** 执行科室ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "30dda80b-4e9a-4108-bca7-3c5ac54cd137")
    private OrganizationBaseDto performDepartment;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "1903fea7-7195-4117-8f22-d36772393f7b")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "a0de173c-3817-4c94-a2d4-6ea5e0ffa8ba")
    private String updatedBy;

    /** 使用范围列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "46c85631-6243-46a5-9575-1cee0f0f6240")
    private List<String> useScopeList;
}
