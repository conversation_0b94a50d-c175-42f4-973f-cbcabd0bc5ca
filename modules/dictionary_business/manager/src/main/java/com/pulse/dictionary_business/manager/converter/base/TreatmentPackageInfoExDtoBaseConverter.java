package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.TreatmentPackageInfoDtoManager;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "e7ab6558-9f07-411e-83bd-2c20a8eacf5d|DTO|BASE_CONVERTER")
public class TreatmentPackageInfoExDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoManager treatmentPackageInfoDtoManager;

    @AutoGenerated(locked = true)
    public TreatmentPackageInfoExDto convertFromTreatmentPackageInfoDtoToTreatmentPackageInfoExDto(
            TreatmentPackageInfoDto treatmentPackageInfoDto) {
        return convertFromTreatmentPackageInfoDtoToTreatmentPackageInfoExDto(
                        List.of(treatmentPackageInfoDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<TreatmentPackageInfoExDto>
            convertFromTreatmentPackageInfoDtoToTreatmentPackageInfoExDto(
                    List<TreatmentPackageInfoDto> treatmentPackageInfoDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(treatmentPackageInfoDtoList)) {
            return new ArrayList<>();
        }
        List<TreatmentPackageInfoExDto> treatmentPackageInfoExDtoList = new ArrayList<>();
        for (TreatmentPackageInfoDto treatmentPackageInfoDto : treatmentPackageInfoDtoList) {
            if (treatmentPackageInfoDto == null) {
                continue;
            }
            TreatmentPackageInfoExDto treatmentPackageInfoExDto = new TreatmentPackageInfoExDto();
            treatmentPackageInfoExDto.setId(treatmentPackageInfoDto.getId());
            treatmentPackageInfoExDto.setCreatedAt(treatmentPackageInfoDto.getCreatedAt());
            treatmentPackageInfoExDto.setUpdatedAt(treatmentPackageInfoDto.getUpdatedAt());
            treatmentPackageInfoExDto.setPersonOrHospital(
                    treatmentPackageInfoDto.getPersonOrHospital());
            treatmentPackageInfoExDto.setSortNumber(treatmentPackageInfoDto.getSortNumber());
            treatmentPackageInfoExDto.setTreatPackageName(
                    treatmentPackageInfoDto.getTreatPackageName());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            treatmentPackageInfoExDtoList.add(treatmentPackageInfoExDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return treatmentPackageInfoExDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public TreatmentPackageInfoDto convertFromTreatmentPackageInfoExDtoToTreatmentPackageInfoDto(
            TreatmentPackageInfoExDto treatmentPackageInfoExDto) {
        return convertFromTreatmentPackageInfoExDtoToTreatmentPackageInfoDto(
                        List.of(treatmentPackageInfoExDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<TreatmentPackageInfoDto>
            convertFromTreatmentPackageInfoExDtoToTreatmentPackageInfoDto(
                    List<TreatmentPackageInfoExDto> treatmentPackageInfoExDtoList) {
        if (CollectionUtil.isEmpty(treatmentPackageInfoExDtoList)) {
            return new ArrayList<>();
        }
        return treatmentPackageInfoDtoManager.getByIds(
                treatmentPackageInfoExDtoList.stream()
                        .map(TreatmentPackageInfoExDto::getId)
                        .collect(Collectors.toList()));
    }
}
