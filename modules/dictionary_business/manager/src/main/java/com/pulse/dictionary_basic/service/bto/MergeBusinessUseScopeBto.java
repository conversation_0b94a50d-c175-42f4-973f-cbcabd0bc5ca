package com.pulse.dictionary_basic.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> BusinessUseScope
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "2fcc273d-5036-4a7a-8b4b-c01aa2cc1d1d|BTO|DEFINITION")
public class MergeBusinessUseScopeBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 范围编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2b752c87-7dc0-469e-b4d6-1a96b6987d69")
    private List<String> codeList;

    /** 实体ID */
    @AutoGenerated(locked = true, uuid = "cb21084d-890b-4e92-b69d-0a85758ebc94")
    private String entityId;

    /** 实体类型 */
    @AutoGenerated(locked = true, uuid = "a3529fa7-6fa2-4995-8310-a078bd5fc8bf")
    private String entityType;

    @AutoGenerated(locked = true)
    public void setCode(List<String> code) {
        this.__$validPropertySet.add("codeList");
        this.codeList = code;
    }

    @AutoGenerated(locked = true)
    public void setCodeList(List<String> codeList) {
        this.__$validPropertySet.add("codeList");
        this.codeList = codeList;
    }

    @AutoGenerated(locked = true)
    public void setEntityId(String entityId) {
        this.__$validPropertySet.add("entityId");
        this.entityId = entityId;
    }

    @AutoGenerated(locked = true)
    public void setEntityType(String entityType) {
        this.__$validPropertySet.add("entityType");
        this.entityType = entityType;
    }
}
