package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.TreatItemBaseDtoManager;
import com.pulse.dictionary_business.manager.TreatmentPackageItemDetailDtoManager;
import com.pulse.dictionary_business.manager.TreatmentPackageItemDtoManager;
import com.pulse.dictionary_business.manager.converter.TreatmentPackageItemDetailDtoConverter;
import com.pulse.dictionary_business.manager.converter.TreatmentPackageItemDtoConverter;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDetailDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.persist.dos.TreatmentPackageItem;
import com.pulse.dictionary_business.persist.mapper.TreatmentPackageItemDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "135755c3-4f90-4408-9d86-87116330d49c|DTO|BASE_MANAGER_IMPL")
public abstract class TreatmentPackageItemDetailDtoManagerBaseImpl
        implements TreatmentPackageItemDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private TreatItemBaseDtoManager treatItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDao treatmentPackageItemDao;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDetailDtoConverter treatmentPackageItemDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDtoConverter treatmentPackageItemDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatmentPackageItemDtoManager treatmentPackageItemDtoManager;

    @AutoGenerated(locked = true, uuid = "010c3c9b-843b-3dc0-84ce-d54788b79451")
    @Override
    public List<TreatmentPackageItemDetailDto> getByItemIds(List<String> itemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(itemId)) {
            return Collections.emptyList();
        }

        List<TreatmentPackageItem> treatmentPackageItemList =
                treatmentPackageItemDao.getByItemIds(itemId);
        if (CollectionUtil.isEmpty(treatmentPackageItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromTreatmentPackageItemToTreatmentPackageItemDetailDto(
                treatmentPackageItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3a0d5c56-ac89-3920-b5a9-b45b0d7ea68d")
    @Override
    public List<TreatmentPackageItemDetailDto> getByTreatmentPackageIds(
            List<String> treatmentPackageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(treatmentPackageId)) {
            return Collections.emptyList();
        }

        List<TreatmentPackageItem> treatmentPackageItemList =
                treatmentPackageItemDao.getByTreatmentPackageIds(treatmentPackageId);
        if (CollectionUtil.isEmpty(treatmentPackageItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromTreatmentPackageItemToTreatmentPackageItemDetailDto(
                treatmentPackageItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5554576d-12c0-3a52-b168-ae2a6f70c46c")
    @Override
    public List<TreatmentPackageItemDetailDto> getByItemId(String itemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList =
                getByItemIds(Arrays.asList(itemId));
        return treatmentPackageItemDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "75b86e5e-260f-3065-9293-253babfa0208")
    public List<TreatmentPackageItemDetailDto>
            doConvertFromTreatmentPackageItemToTreatmentPackageItemDetailDto(
                    List<TreatmentPackageItem> treatmentPackageItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(treatmentPackageItemList)) {
            return Collections.emptyList();
        }

        Map<String, String> itemIdMap =
                treatmentPackageItemList.stream()
                        .filter(i -> i.getItemId() != null)
                        .collect(
                                Collectors.toMap(
                                        TreatmentPackageItem::getId,
                                        TreatmentPackageItem::getItemId));
        List<TreatItemBaseDto> itemIdTreatItemBaseDtoList =
                treatItemBaseDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(itemIdMap.values())));
        Map<String, TreatItemBaseDto> itemIdTreatItemBaseDtoMapRaw =
                itemIdTreatItemBaseDtoList.stream()
                        .collect(Collectors.toMap(TreatItemBaseDto::getId, i -> i));
        Map<String, TreatItemBaseDto> itemIdTreatItemBaseDtoMap =
                itemIdMap.entrySet().stream()
                        .filter(i -> itemIdTreatItemBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> itemIdTreatItemBaseDtoMapRaw.get(i.getValue())));

        List<TreatmentPackageItemDto> baseDtoList =
                treatmentPackageItemDtoConverter
                        .convertFromTreatmentPackageItemToTreatmentPackageItemDto(
                                treatmentPackageItemList);
        Map<String, TreatmentPackageItemDetailDto> dtoMap =
                treatmentPackageItemDetailDtoConverter
                        .convertFromTreatmentPackageItemDtoToTreatmentPackageItemDetailDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        TreatmentPackageItemDetailDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList = new ArrayList<>();
        for (TreatmentPackageItem i : treatmentPackageItemList) {
            TreatmentPackageItemDetailDto treatmentPackageItemDetailDto = dtoMap.get(i.getId());
            if (treatmentPackageItemDetailDto == null) {
                continue;
            }

            if (null != i.getItemId()) {
                treatmentPackageItemDetailDto.setItemInfo(
                        itemIdTreatItemBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            treatmentPackageItemDetailDtoList.add(treatmentPackageItemDetailDto);
        }
        return treatmentPackageItemDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "775525ce-3d2c-3e8a-ad52-8a412673b181")
    @Override
    public TreatmentPackageItemDetailDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageItemDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        TreatmentPackageItemDetailDto treatmentPackageItemDetailDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return treatmentPackageItemDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9067704d-2e17-3a5c-ad34-34378d84a2c6")
    @Override
    public List<TreatmentPackageItemDetailDto> getByTreatmentPackageId(String treatmentPackageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailDtoList =
                getByTreatmentPackageIds(Arrays.asList(treatmentPackageId));
        return treatmentPackageItemDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b3fd558f-ad64-3ecd-8ab4-0468f3414642")
    @Override
    public List<TreatmentPackageItemDetailDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<TreatmentPackageItem> treatmentPackageItemList = treatmentPackageItemDao.getByIds(id);
        if (CollectionUtil.isEmpty(treatmentPackageItemList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, TreatmentPackageItem> treatmentPackageItemMap =
                treatmentPackageItemList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        treatmentPackageItemList =
                id.stream()
                        .map(i -> treatmentPackageItemMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromTreatmentPackageItemToTreatmentPackageItemDetailDto(
                treatmentPackageItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
