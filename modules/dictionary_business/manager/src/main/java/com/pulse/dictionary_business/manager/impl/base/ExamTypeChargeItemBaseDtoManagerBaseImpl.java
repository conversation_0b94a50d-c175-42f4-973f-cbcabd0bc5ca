package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ExamTypeChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.converter.ExamTypeChargeItemBaseDtoConverter;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamTypeChargeItem;
import com.pulse.dictionary_business.persist.mapper.ExamTypeChargeItemDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "d489e16a-eded-48e3-9302-864c7505b033|DTO|BASE_MANAGER_IMPL")
public abstract class ExamTypeChargeItemBaseDtoManagerBaseImpl
        implements ExamTypeChargeItemBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private ExamTypeChargeItemBaseDtoConverter examTypeChargeItemBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ExamTypeChargeItemDao examTypeChargeItemDao;

    @AutoGenerated(locked = true, uuid = "14536072-a77c-3f45-8d55-93a7fb7667c5")
    @Override
    public List<ExamTypeChargeItemBaseDto> getByExamTypeIds(List<String> examTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examTypeId)) {
            return Collections.emptyList();
        }

        List<ExamTypeChargeItem> examTypeChargeItemList =
                examTypeChargeItemDao.getByExamTypeIds(examTypeId);
        if (CollectionUtil.isEmpty(examTypeChargeItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(examTypeChargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "20037c5f-059a-3ac0-9312-c7efcc13abf1")
    public List<ExamTypeChargeItemBaseDto>
            doConvertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(
                    List<ExamTypeChargeItem> examTypeChargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examTypeChargeItemList)) {
            return Collections.emptyList();
        }

        Map<String, ExamTypeChargeItemBaseDto> dtoMap =
                examTypeChargeItemBaseDtoConverter
                        .convertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(
                                examTypeChargeItemList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ExamTypeChargeItemBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<ExamTypeChargeItemBaseDto> examTypeChargeItemBaseDtoList = new ArrayList<>();
        for (ExamTypeChargeItem i : examTypeChargeItemList) {
            ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto = dtoMap.get(i.getId());
            if (examTypeChargeItemBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            examTypeChargeItemBaseDtoList.add(examTypeChargeItemBaseDto);
        }
        return examTypeChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "7d65a6cb-f25a-3e23-9ba9-a3cfc91f6c4a")
    @Override
    public List<ExamTypeChargeItemBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<ExamTypeChargeItem> examTypeChargeItemList = examTypeChargeItemDao.getByIds(id);
        if (CollectionUtil.isEmpty(examTypeChargeItemList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, ExamTypeChargeItem> examTypeChargeItemMap =
                examTypeChargeItemList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        examTypeChargeItemList =
                id.stream()
                        .map(i -> examTypeChargeItemMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(examTypeChargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "97a5502e-1780-30ff-8eb2-b3fddc6bd5a2")
    @Override
    public ExamTypeChargeItemBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamTypeChargeItemBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return examTypeChargeItemBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9ad70065-d222-37e7-9469-9ea7b1a16095")
    @Override
    public List<ExamTypeChargeItemBaseDto> getByChargeItemId(String chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamTypeChargeItemBaseDto> examTypeChargeItemBaseDtoList =
                getByChargeItemIds(Arrays.asList(chargeItemId));
        return examTypeChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "acb977d9-865e-3bef-a8a6-5f96e257c9a1")
    @Override
    public List<ExamTypeChargeItemBaseDto> getByExamTypeId(String examTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamTypeChargeItemBaseDto> examTypeChargeItemBaseDtoList =
                getByExamTypeIds(Arrays.asList(examTypeId));
        return examTypeChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d0f1c23f-98c9-3e1d-92d3-6833633c4483")
    @Override
    public List<ExamTypeChargeItemBaseDto> getByChargeItemIds(List<String> chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemId)) {
            return Collections.emptyList();
        }

        List<ExamTypeChargeItem> examTypeChargeItemList =
                examTypeChargeItemDao.getByChargeItemIds(chargeItemId);
        if (CollectionUtil.isEmpty(examTypeChargeItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(examTypeChargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
