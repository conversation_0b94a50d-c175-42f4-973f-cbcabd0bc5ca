package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.ExamItemDrugBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamItemDrug;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "206e3ac5-f6d1-4339-beb0-a6e5c738bae4|DTO|BASE_CONVERTER")
public class ExamItemDrugBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ExamItemDrugBaseDto convertFromExamItemDrugToExamItemDrugBaseDto(
            ExamItemDrug examItemDrug) {
        return convertFromExamItemDrugToExamItemDrugBaseDto(List.of(examItemDrug)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ExamItemDrugBaseDto> convertFromExamItemDrugToExamItemDrugBaseDto(
            List<ExamItemDrug> examItemDrugList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examItemDrugList)) {
            return new ArrayList<>();
        }
        List<ExamItemDrugBaseDto> examItemDrugBaseDtoList = new ArrayList<>();
        for (ExamItemDrug examItemDrug : examItemDrugList) {
            if (examItemDrug == null) {
                continue;
            }
            ExamItemDrugBaseDto examItemDrugBaseDto = new ExamItemDrugBaseDto();
            examItemDrugBaseDto.setId(examItemDrug.getId());
            examItemDrugBaseDto.setExamItemId(examItemDrug.getExamItemId());
            examItemDrugBaseDto.setPriceItemId(examItemDrug.getPriceItemId());
            examItemDrugBaseDto.setPriceItemType(examItemDrug.getPriceItemType());
            examItemDrugBaseDto.setCount(examItemDrug.getCount());
            examItemDrugBaseDto.setCampusIdList(examItemDrug.getCampusIdList());
            examItemDrugBaseDto.setAdministration(examItemDrug.getAdministration());
            examItemDrugBaseDto.setDepartmentDrugFlag(examItemDrug.getDepartmentDrugFlag());
            examItemDrugBaseDto.setUseScopeList(examItemDrug.getUseScopeList());
            examItemDrugBaseDto.setContrastAgentType(examItemDrug.getContrastAgentType());
            examItemDrugBaseDto.setUpdatedBy(examItemDrug.getUpdatedBy());
            examItemDrugBaseDto.setCreatedBy(examItemDrug.getCreatedBy());
            examItemDrugBaseDto.setCreatedAt(examItemDrug.getCreatedAt());
            examItemDrugBaseDto.setUpdatedAt(examItemDrug.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            examItemDrugBaseDtoList.add(examItemDrugBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return examItemDrugBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
