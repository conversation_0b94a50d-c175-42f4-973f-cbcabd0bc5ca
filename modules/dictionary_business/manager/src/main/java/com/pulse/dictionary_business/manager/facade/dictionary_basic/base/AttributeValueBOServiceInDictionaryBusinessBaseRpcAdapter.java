package com.pulse.dictionary_business.manager.facade.dictionary_basic.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.service.bto.MergeAttributeValueBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "ec41d6bd-464d-33e3-a24e-f100217d0e67")
public class AttributeValueBOServiceInDictionaryBusinessBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    /** 保存扩展属性值列表 */
    @AutoGenerated(locked = true, uuid = "da35a7db-a77b-4c42-9b10-1209c4455de1|RPC|BASE_ADAPTER")
    public List<String> mergeAttributeValueList(
            List<MergeAttributeValueBto> mergeAttributeValueList,
            String entityType,
            String entityId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("merge_attribute_value_list", mergeAttributeValueList);
        paramMap.put("entity_type", entityType);
        paramMap.put("entity_id", entityId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("merge_attribute_value_list", List.class);
        paramTypeMap.put("entity_type", String.class);
        paramTypeMap.put("entity_id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/dictionary_basic/da35a7db-a77b-4c42-9b10-1209c4455de1/AttributeValueBOService-mergeAttributeValueList",
                        "com.pulse.dictionary_basic.service.AttributeValueBOService",
                        "mergeAttributeValueList",
                        paramMap,
                        paramTypeMap,
                        "c87071a8-7ca8-438e-998f-eaf79a9cffee",
                        "003f087c-177d-4a69-81e6-c6650b5f6080"),
                new TypeReference<>() {});
    }
}
