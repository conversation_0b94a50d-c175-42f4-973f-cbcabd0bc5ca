package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "b24ca398-c846-4c3e-99f3-b8184be6e70a|DTO|DEFINITION")
public class ExamItemExtensionBaseDto {
    /** 预约方法 */
    @AutoGenerated(locked = true, uuid = "0cf0c5bb-1855-4e25-8f91-fbb862114051")
    private String appointMethod;

    /** 预约类型 */
    @AutoGenerated(locked = true, uuid = "cf552a12-28b7-48a9-b0cd-d3dba0ba23bd")
    private String appointType;

    /** 院区 */
    @AutoGenerated(locked = true, uuid = "a2cbf933-fb7e-4fdf-ad3c-ff9cf1bb4dd4")
    private String branchInstitutionId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "b7621b5a-0529-41b1-a08c-fce9a701e38a")
    private Date createdAt;

    /** 检查项目ID */
    @AutoGenerated(locked = true, uuid = "d0f638fb-3b03-42b9-ab8c-300b8a014c85")
    private String examItemId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "1da3fc10-1992-4ee9-89f7-41515a5855cf")
    private String id;

    /** 默认胶片选项 */
    @AutoGenerated(locked = true, uuid = "4b48c2ad-86d0-4c67-83ac-360cbaa64997")
    private String radiationFilmOption;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b7291aa7-07d1-4fb3-a127-48e1b718f707")
    private Date updatedAt;

    /** 使用范围 */
    @AutoGenerated(locked = true, uuid = "4b6849cd-db46-4369-8fb3-26b07c0d31f5")
    private String usageScope;
}
