package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_business.common.enums.PersonHospitalEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "e7ab6558-9f07-411e-83bd-2c20a8eacf5d|DTO|DEFINITION")
public class TreatmentPackageInfoExDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "e5f96b52-36ee-4c2b-b3cc-fb61a24841f1")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c31d6e28-c962-4544-8a34-59b376922454")
    private String id;

    /** 个人或医院 */
    @AutoGenerated(locked = true, uuid = "5cbcbee1-08df-44da-bcd1-686539d49df2")
    private PersonHospitalEnum personOrHospital;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "2bbd75cf-42fb-4cbb-9875-6ab17b44fc61")
    private Long sortNumber;

    /** 治疗套餐名称 */
    @AutoGenerated(locked = true, uuid = "703d5819-3b33-4f42-868a-714627d9599f")
    private String treatPackageName;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "eb395bfb-4e0d-4b7a-8862-70f5ec65c29f")
    private List<TreatmentPackageItemDetailDto> treatmentPackageItemDetailList;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "75c7234d-2e2e-4271-bd74-ab0a7e4e1f4f")
    private Date updatedAt;
}
