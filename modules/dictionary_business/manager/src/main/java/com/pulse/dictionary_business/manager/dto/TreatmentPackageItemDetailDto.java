package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "135755c3-4f90-4408-9d86-87116330d49c|DTO|DEFINITION")
public class TreatmentPackageItemDetailDto {
    /** 数量 */
    @AutoGenerated(locked = true, uuid = "820882e2-0cd8-43cf-8981-1c39cb97f02e")
    private Integer amount;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "fd251fe7-49b5-4e35-a99c-f36e2a5981f8")
    private Date createdAt;

    /** 频率 */
    @AutoGenerated(locked = true, uuid = "98d35c7e-dc34-47b1-b886-3a9644a32031")
    private String frequencyId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f1f903e5-8f14-4684-9acd-3f994fa3e5e1")
    private String id;

    /** 嘱托 */
    @AutoGenerated(locked = true, uuid = "212ebd12-e8db-4402-9bd3-b8a1a11c0049")
    private String instruction;

    /** 项目信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ed92269b-24d8-4185-a203-74c8054dc86a")
    private TreatItemBaseDto itemInfo;

    /** 自费 */
    @AutoGenerated(locked = true, uuid = "341d0638-e07c-44f5-a1cd-427ec63ebc01")
    private Boolean payBySelf;

    /** 治疗套餐ID */
    @AutoGenerated(locked = true, uuid = "8c3327e3-20b3-4361-a368-6dd9db29ab5f")
    private String treatmentPackageId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "0bc349f3-1bda-482d-9c0b-b413ac0b1e4e")
    private Date updatedAt;
}
