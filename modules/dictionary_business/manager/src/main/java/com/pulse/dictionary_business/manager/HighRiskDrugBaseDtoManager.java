package com.pulse.dictionary_business.manager;

import com.pulse.dictionary_business.manager.dto.HighRiskDrugBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "db9d7f7c-f2ac-459e-8e37-49811f6a97e0|DTO|MANAGER")
public interface HighRiskDrugBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "27d874a3-e9fc-31b9-b2a9-17a46e04d574")
    List<HighRiskDrugBaseDto> getByHighRiskFactorIds(List<String> highRiskFactorId);

    @AutoGenerated(locked = true, uuid = "731e45b8-37dc-3a89-a025-619f2d153491")
    HighRiskDrugBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "f4d45b63-9e96-3880-8a4d-e01b96dbf3aa")
    List<HighRiskDrugBaseDto> getByHighRiskFactorId(String highRiskFactorId);

    @AutoGenerated(locked = true, uuid = "fd0cb6f0-4913-3453-88e7-e5e1905c8a1f")
    List<HighRiskDrugBaseDto> getByIds(List<String> id);
}
