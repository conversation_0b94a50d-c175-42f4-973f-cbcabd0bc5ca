package com.pulse.dictionary_business.manager;

import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoExDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "e7ab6558-9f07-411e-83bd-2c20a8eacf5d|DTO|MANAGER")
public interface TreatmentPackageInfoExDtoManager {

    @AutoGenerated(locked = true, uuid = "0c19ea90-f17f-3737-b8e7-dcda9c725a1b")
    TreatmentPackageInfoExDto getById(String id);

    @AutoGenerated(locked = true, uuid = "fc4eae71-d6ed-3da9-aea4-6c695d87489f")
    List<TreatmentPackageInfoExDto> getByIds(List<String> id);
}
