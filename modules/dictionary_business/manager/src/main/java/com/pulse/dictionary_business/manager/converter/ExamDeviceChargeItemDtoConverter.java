package com.pulse.dictionary_business.manager.converter;

import com.pulse.dictionary_business.manager.converter.base.ExamDeviceChargeItemDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "d09aae3d-4d0c-4865-a77d-576e42ed7822|DTO|CONVERTER")
public class ExamDeviceChargeItemDtoConverter extends ExamDeviceChargeItemDtoBaseConverter {}
