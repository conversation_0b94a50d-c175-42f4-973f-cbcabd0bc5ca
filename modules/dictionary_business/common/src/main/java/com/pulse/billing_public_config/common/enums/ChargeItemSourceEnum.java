package com.pulse.billing_public_config.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "37dcf456-9b2d-4b18-a69b-d6d5dd206d28|ENUM|DEFINITION")
public enum ChargeItemSourceEnum {

    /** 导入 */
    IMPORT(),

    /** spd录入 */
    SPD(),

    /** 接口 */
    INTERFACE(),

    /** 录入 */
    TYPE_IN();

    @AutoGenerated(locked = true)
    ChargeItemSourceEnum() {}
}
