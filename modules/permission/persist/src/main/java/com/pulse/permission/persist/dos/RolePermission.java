package com.pulse.permission.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "role_permission", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "7698d6ee-9915-4cb2-8c24-d510c6c9c746|ENTITY|DEFINITION")
public class RolePermission {
    @AutoGenerated(locked = true, uuid = "f0d3887a-fc1a-5fcb-b7ec-845f546c3bdd")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "f51cf378-51a7-4bb0-8e7a-b9e123bedee1")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "668f23d0-2ae9-482c-a3aa-27d7db0d42f2")
    @TableField(value = "disable_reason")
    private String disableReason;

    /** 启用状态:1-启用,0-停用 */
    @AutoGenerated(locked = true, uuid = "9085f862-8316-4e1a-ac98-acd95f885589")
    @TableField(value = "enable_flag")
    private Boolean enableFlag;

    @AutoGenerated(locked = true, uuid = "fdb874ac-d7c3-43c0-9803-5b073b05f8ad")
    @TableId(value = "id")
    private String id;

    /** 权限点ID（关联permission表） */
    @AutoGenerated(locked = true, uuid = "7f6efead-bff6-4165-a272-6dbe70532c19")
    @TableField(value = "permission_id")
    private String permissionId;

    /** 角色ID（关联role表） */
    @AutoGenerated(locked = true, uuid = "450e661a-0e37-484a-99d8-4e3edb5fe969")
    @TableField(value = "role_id")
    private String roleId;

    @AutoGenerated(locked = true, uuid = "e50c583a-3da0-5e5f-bade-a3191ec1ab47")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "b2794385-dffa-4f50-8e14-fb12f802ce1e")
    @TableField(value = "updated_by")
    private String updatedBy;

    @Data
    public static class PermissionIdAndRoleId {
        @AutoGenerated(locked = true)
        private String roleId;

        @AutoGenerated(locked = true)
        private String permissionId;
    }
}
