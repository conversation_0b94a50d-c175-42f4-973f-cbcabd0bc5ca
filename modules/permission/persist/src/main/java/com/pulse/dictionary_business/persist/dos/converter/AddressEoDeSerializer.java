package com.pulse.dictionary_business.persist.dos.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_business.persist.eo.AddressEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;
import com.vs.sqlmapper.convert.Convert;

@AutoGenerated(locked = true, uuid = "064db172-dc46-3b4f-ab22-3fcdfc63613a")
public class AddressEoDeSerializer implements Convert<AddressEo> {

    @AutoGenerated(locked = true)
    @Override
    public AddressEo convert(Object src) {
        if (src == null) {
            return null;
        } else {
            return JsonUtils.readObject((String) src, new TypeReference<AddressEo>() {});
        }
    }

    @AutoGenerated(locked = true)
    @Override
    public boolean support(Object src) {
        return src instanceof String;
    }
}
