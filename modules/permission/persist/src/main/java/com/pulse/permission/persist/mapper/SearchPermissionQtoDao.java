package com.pulse.permission.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.permission.persist.qto.SearchPermissionQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "27478bfe-7af4-4bc7-a5c2-199d68ff1eae|QTO|DAO")
public class SearchPermissionQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询权限列表 */
    @AutoGenerated(locked = false, uuid = "27478bfe-7af4-4bc7-a5c2-199d68ff1eae-count")
    public Integer count(SearchPermissionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(permission.id) FROM permission WHERE ( permission.code like"
                    + " #searchKey OR permission.name like #searchKey OR"
                    + " JSON_VALUE(permission.input_code, '$.pinyin') like #inputCodePinyinLike OR"
                    + " JSON_VALUE(permission.input_code, '$.wubi') like #inputCodeWubiLike OR"
                    + " JSON_VALUE(permission.input_code, '$.custom') like #inputCodeCustomLike )"
                    + " AND permission.category_id = #categoryIdIs AND permission.enable_flag ="
                    + " #enableFlagIs AND permission.resource_type in #resourceTypeIn AND"
                    + " permission.resource_type not in #resourceTypeNotIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (CollectionUtil.isEmpty(qto.getResourceTypeNotIn())) {
            conditionToRemove.add("#resourceTypeNotIn");
        }
        if (CollectionUtil.isEmpty(qto.getResourceTypeIn())) {
            conditionToRemove.add("#resourceTypeIn");
        }
        if (qto.getCategoryIdIs() == null) {
            conditionToRemove.add("#categoryIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getSearchKey() == null) {
            conditionToRemove.add("#searchKey");
        }
        if (qto.getSearchKey() == null) {
            conditionToRemove.add("#searchKey");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("permission");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace(
                                "#resourceTypeNotIn",
                                CollectionUtil.isEmpty(qto.getResourceTypeNotIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getResourceTypeNotIn().size()))
                        .replace(
                                "#resourceTypeIn",
                                CollectionUtil.isEmpty(qto.getResourceTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getResourceTypeIn().size()))
                        .replace("#categoryIdIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#searchKey", "?")
                        .replace("#searchKey", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#resourceTypeNotIn")) {
                sqlParams.addAll(
                        qto.getResourceTypeNotIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#resourceTypeIn")) {
                sqlParams.addAll(
                        qto.getResourceTypeIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#categoryIdIs")) {
                sqlParams.add(qto.getCategoryIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#searchKey")) {
                sqlParams.add("%" + qto.getSearchKey() + "%");
            } else if (paramName.equalsIgnoreCase("#searchKey")) {
                sqlParams.add("%" + qto.getSearchKey() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询权限列表 */
    @AutoGenerated(locked = false, uuid = "27478bfe-7af4-4bc7-a5c2-199d68ff1eae-query-all")
    public List<String> query(SearchPermissionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT permission.id FROM permission WHERE ( permission.code like #searchKey OR"
                    + " permission.name like #searchKey OR JSON_VALUE(permission.input_code,"
                    + " '$.pinyin') like #inputCodePinyinLike OR JSON_VALUE(permission.input_code,"
                    + " '$.wubi') like #inputCodeWubiLike OR JSON_VALUE(permission.input_code,"
                    + " '$.custom') like #inputCodeCustomLike ) AND permission.category_id ="
                    + " #categoryIdIs AND permission.enable_flag = #enableFlagIs AND"
                    + " permission.resource_type in #resourceTypeIn AND permission.resource_type"
                    + " not in #resourceTypeNotIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (CollectionUtil.isEmpty(qto.getResourceTypeNotIn())) {
            conditionToRemove.add("#resourceTypeNotIn");
        }
        if (CollectionUtil.isEmpty(qto.getResourceTypeIn())) {
            conditionToRemove.add("#resourceTypeIn");
        }
        if (qto.getCategoryIdIs() == null) {
            conditionToRemove.add("#categoryIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getSearchKey() == null) {
            conditionToRemove.add("#searchKey");
        }
        if (qto.getSearchKey() == null) {
            conditionToRemove.add("#searchKey");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("permission");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace(
                                "#resourceTypeNotIn",
                                CollectionUtil.isEmpty(qto.getResourceTypeNotIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getResourceTypeNotIn().size()))
                        .replace(
                                "#resourceTypeIn",
                                CollectionUtil.isEmpty(qto.getResourceTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getResourceTypeIn().size()))
                        .replace("#categoryIdIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#searchKey", "?")
                        .replace("#searchKey", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#resourceTypeNotIn")) {
                sqlParams.addAll(
                        qto.getResourceTypeNotIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#resourceTypeIn")) {
                sqlParams.addAll(
                        qto.getResourceTypeIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#categoryIdIs")) {
                sqlParams.add(qto.getCategoryIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#searchKey")) {
                sqlParams.add("%" + qto.getSearchKey() + "%");
            } else if (paramName.equalsIgnoreCase("#searchKey")) {
                sqlParams.add("%" + qto.getSearchKey() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  permission.sort_number asc , permission.created_at desc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询权限列表 */
    @AutoGenerated(locked = false, uuid = "27478bfe-7af4-4bc7-a5c2-199d68ff1eae-query-paginate")
    public List<String> queryPaged(SearchPermissionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT permission.id FROM permission WHERE ( permission.code like #searchKey OR"
                    + " permission.name like #searchKey OR JSON_VALUE(permission.input_code,"
                    + " '$.pinyin') like #inputCodePinyinLike OR JSON_VALUE(permission.input_code,"
                    + " '$.wubi') like #inputCodeWubiLike OR JSON_VALUE(permission.input_code,"
                    + " '$.custom') like #inputCodeCustomLike ) AND permission.category_id ="
                    + " #categoryIdIs AND permission.enable_flag = #enableFlagIs AND"
                    + " permission.resource_type in #resourceTypeIn AND permission.resource_type"
                    + " not in #resourceTypeNotIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (CollectionUtil.isEmpty(qto.getResourceTypeNotIn())) {
            conditionToRemove.add("#resourceTypeNotIn");
        }
        if (CollectionUtil.isEmpty(qto.getResourceTypeIn())) {
            conditionToRemove.add("#resourceTypeIn");
        }
        if (qto.getCategoryIdIs() == null) {
            conditionToRemove.add("#categoryIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getSearchKey() == null) {
            conditionToRemove.add("#searchKey");
        }
        if (qto.getSearchKey() == null) {
            conditionToRemove.add("#searchKey");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("permission");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace(
                                "#resourceTypeNotIn",
                                CollectionUtil.isEmpty(qto.getResourceTypeNotIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getResourceTypeNotIn().size()))
                        .replace(
                                "#resourceTypeIn",
                                CollectionUtil.isEmpty(qto.getResourceTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getResourceTypeIn().size()))
                        .replace("#categoryIdIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#searchKey", "?")
                        .replace("#searchKey", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#resourceTypeNotIn")) {
                sqlParams.addAll(
                        qto.getResourceTypeNotIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#resourceTypeIn")) {
                sqlParams.addAll(
                        qto.getResourceTypeIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#categoryIdIs")) {
                sqlParams.add(qto.getCategoryIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#searchKey")) {
                sqlParams.add("%" + qto.getSearchKey() + "%");
            } else if (paramName.equalsIgnoreCase("#searchKey")) {
                sqlParams.add("%" + qto.getSearchKey() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  permission.sort_number asc , permission.created_at desc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
