package com.pulse.permission.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> Role
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "70f56e15-54b6-49dc-8ced-6c782aafcbf8|BTO|DEFINITION")
public class MergeRoleBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 角色编码 角色编码，需符合命名规范：全大写字母+下划线，如ADMIN、DOCTOR_CHIEF */
    @AutoGenerated(locked = true, uuid = "2e5aec30-b5b3-4857-895c-50ce9a3c9165")
    private String code;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "1f9f2b4d-f37d-4382-bcb4-a02ee5e7ea55")
    private String createdBy;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "9ce96862-589f-41a7-8cee-96dc1b327fe9")
    private String deletedBy;

    /** 角色描述 角色描述，详细说明角色权限范围 */
    @AutoGenerated(locked = true, uuid = "269838d7-9c3e-4221-9fb4-67b500f08b0c")
    private String description;

    /** 停用原因 */
    @AutoGenerated(locked = true, uuid = "77c8ff5f-2eb4-41f1-acdc-64fcd0261b8e")
    private String disableReason;

    /** 启用标志 启用状态:1-启用,0-停用 */
    @AutoGenerated(locked = true, uuid = "abb6319d-adc2-40e3-abc8-ad53d042cff7")
    private Boolean enableFlag;

    /** Primary Key */
    @AutoGenerated(locked = true, uuid = "b5586904-3d30-46f4-aa6a-1a11f95c114e")
    private String id;

    /** 角色名称 角色名称（中文展示） */
    @AutoGenerated(locked = true, uuid = "2ae86787-9a40-4db8-a590-7c7b24dda291")
    private String name;

    /** 是否系统预置 是否系统预置:1-是（不可删除）,0-否 */
    @AutoGenerated(locked = true, uuid = "6e53eb49-1925-47ee-92ad-3ac162d022d8")
    private Boolean systemFlag;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "76c45d14-3979-4ae9-9db6-712d21dfbe10")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDeletedBy(String deletedBy) {
        this.__$validPropertySet.add("deletedBy");
        this.deletedBy = deletedBy;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setDisableReason(String disableReason) {
        this.__$validPropertySet.add("disableReason");
        this.disableReason = disableReason;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setName(String name) {
        this.__$validPropertySet.add("name");
        this.name = name;
    }

    @AutoGenerated(locked = true)
    public void setSystemFlag(Boolean systemFlag) {
        this.__$validPropertySet.add("systemFlag");
        this.systemFlag = systemFlag;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
