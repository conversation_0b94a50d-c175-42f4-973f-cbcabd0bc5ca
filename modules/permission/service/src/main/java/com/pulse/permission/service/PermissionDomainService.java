package com.pulse.permission.service;

import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/** 模块领域服务，负责模块的BoService的包装 */
@Service
@Slf4j
@AutoGenerated(locked = false, uuid = "46c10c6c-56e4-4f14-b672-e6441a0ecc52")
public class PermissionDomainService {
    @AutoGenerated(locked = true)
    @Resource
    private PermissionBOService permissionBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RoleBOService roleBOService;
}
