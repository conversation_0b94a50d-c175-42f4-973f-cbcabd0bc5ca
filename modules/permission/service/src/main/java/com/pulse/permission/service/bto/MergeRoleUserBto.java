package com.pulse.permission.service.bto;

import com.vs.code.AutoGenerated;
import com.vs.common.util.validator.anno.ListLength;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Role
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "131fe82f-2351-4ef1-abe4-0b33ab87d6fd|BTO|DEFINITION")
public class MergeRoleUserBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** Primary Key */
    @AutoGenerated(locked = true, uuid = "7c45e745-467b-4eaf-a5f8-7855272879c5")
    private String id;

    @Valid
    @ListLength(min = 1, message = "至少选一个用户")
    @AutoGenerated(locked = true, uuid = "40cfb26d-f172-48bf-b690-6c75852e829f")
    private List<MergeRoleUserBto.UserRoleBto> userRoleBtoList;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setUserRoleBtoList(List<MergeRoleUserBto.UserRoleBto> userRoleBtoList) {
        this.__$validPropertySet.add("userRoleBtoList");
        this.userRoleBtoList = userRoleBtoList;
    }

    /**
     * <b>[源自]</b> UserRole
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_IGNORE
     */
    @Getter
    @NoArgsConstructor
    public static class UserRoleBto {
        /** Primary Key */
        @AutoGenerated(locked = true, uuid = "77736322-7eee-4d3d-85e5-afd78f7027f4")
        private String id;

        /** 用户ID 用户ID（关联user表） */
        @AutoGenerated(locked = true, uuid = "84b33597-a390-471b-b14c-38f745f5ecb2")
        private String userId;

        /** 启用标志 启用状态:1-启用,0-停用 */
        @AutoGenerated(locked = true, uuid = "b2ceaa46-476a-46f3-a85b-10d60f47bbca")
        private Boolean enableFlag;

        /** 停用原因 */
        @AutoGenerated(locked = true, uuid = "b5446164-37a2-4678-9585-0e97d4f2f65d")
        private String disableReason;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "113e13bb-6798-44d4-adde-e4f36ff9c995")
        private String createdBy;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "b19bfd5c-c779-4b49-b2cb-633ac8960483")
        private String updatedBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setUserId(String userId) {
            this.__$validPropertySet.add("userId");
            this.userId = userId;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setDisableReason(String disableReason) {
            this.__$validPropertySet.add("disableReason");
            this.disableReason = disableReason;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }
    }
}
