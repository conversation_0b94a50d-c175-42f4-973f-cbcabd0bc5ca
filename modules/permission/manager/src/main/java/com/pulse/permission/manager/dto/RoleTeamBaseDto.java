package com.pulse.permission.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "62a96aa2-bd7b-4979-983d-b435ebfefab6|DTO|DEFINITION")
public class RoleTeamBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "c1fba81f-d19d-41fd-b037-7cbd5cc73a13")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "37b30f1b-0c05-453c-a7fe-5395c5a14bec")
    private String createdBy;

    /** 停用原因 */
    @AutoGenerated(locked = true, uuid = "9c67eab6-985a-4bb2-974c-19915b0e449b")
    private String disableReason;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "9a256c9d-b83c-41d8-9bcd-0bdc1a872492")
    private Boolean enableFlag;

    /** Primary Key */
    @AutoGenerated(locked = true, uuid = "67378f7d-dd54-4e52-bffc-bd6801faf71d")
    private String id;

    /** 角色ID */
    @AutoGenerated(locked = true, uuid = "fee1591e-66de-48e4-beb3-f47e56726ac9")
    private String roleId;

    /** 团队ID */
    @AutoGenerated(locked = true, uuid = "0d2ab380-2aa0-4cc0-b949-ca2eb96525e3")
    private String teamId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "07d49964-423f-48d5-a6b6-e1c1faee8430")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "0a429be5-f1f6-4f6e-a172-492259e5eced")
    private String updatedBy;
}
