package com.pulse.permission.manager.facade.application;

import com.pulse.application.manager.dto.ApplicationMenuBaseDto;
import com.pulse.permission.manager.facade.application.base.ApplicationMenuBaseDtoServiceInPermissionBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "46c10c6c-56e4-4f14-b672-e6441a0ecc52")
@AutoGenerated(locked = false, uuid = "6886459a-d67d-3880-b1ff-c3fffd700bf3")
public class ApplicationMenuBaseDtoServiceInPermissionRpcAdapter
        extends ApplicationMenuBaseDtoServiceInPermissionBaseRpcAdapter {

    @RpcRefer(id = "ccebe00c-0faf-4fec-b691-2472b612afda", version = "1746683234853")
    @AutoGenerated(locked = false, uuid = "ccebe00c-0faf-4fec-b691-2472b612afda|RPC|ADAPTER")
    public List<ApplicationMenuBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }

    @RpcRefer(id = "cf854ca5-e60f-4e38-9cbf-96b7d5346f33", version = "1746683234850")
    @AutoGenerated(locked = false, uuid = "cf854ca5-e60f-4e38-9cbf-96b7d5346f33|RPC|ADAPTER")
    public ApplicationMenuBaseDto getById(String id) {
        return super.getById(id);
    }
}
