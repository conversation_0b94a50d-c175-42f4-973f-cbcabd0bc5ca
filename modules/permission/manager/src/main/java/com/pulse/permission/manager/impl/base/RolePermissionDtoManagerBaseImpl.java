package com.pulse.permission.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.permission.manager.PermissionAggDtoManager;
import com.pulse.permission.manager.RolePermissionBaseDtoManager;
import com.pulse.permission.manager.RolePermissionDtoManager;
import com.pulse.permission.manager.converter.RolePermissionBaseDtoConverter;
import com.pulse.permission.manager.converter.RolePermissionDtoConverter;
import com.pulse.permission.manager.dto.PermissionAggDto;
import com.pulse.permission.manager.dto.RolePermissionBaseDto;
import com.pulse.permission.manager.dto.RolePermissionDto;
import com.pulse.permission.persist.dos.RolePermission;
import com.pulse.permission.persist.mapper.RolePermissionDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "3fe2a27e-cad0-4e59-b57e-0ae45adb28fb|DTO|BASE_MANAGER_IMPL")
public abstract class RolePermissionDtoManagerBaseImpl implements RolePermissionDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private PermissionAggDtoManager permissionAggDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RolePermissionBaseDtoConverter rolePermissionBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RolePermissionBaseDtoManager rolePermissionBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RolePermissionDao rolePermissionDao;

    @AutoGenerated(locked = true)
    @Autowired
    private RolePermissionDtoConverter rolePermissionDtoConverter;

    @AutoGenerated(locked = true, uuid = "0ec0d659-0b86-33bf-a966-3bdc9560675d")
    @Override
    public List<RolePermissionDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RolePermission> rolePermissionList = rolePermissionDao.getByIds(id);
        if (CollectionUtil.isEmpty(rolePermissionList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RolePermission> rolePermissionMap =
                rolePermissionList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        rolePermissionList =
                id.stream()
                        .map(i -> rolePermissionMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRolePermissionToRolePermissionDto(rolePermissionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "108b1d9d-3454-3ada-8b18-af22a6d557c4")
    @Override
    public List<RolePermissionDto> getByPermissionIds(List<String> permissionId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(permissionId)) {
            return Collections.emptyList();
        }

        List<RolePermission> rolePermissionList =
                rolePermissionDao.getByPermissionIds(permissionId);
        if (CollectionUtil.isEmpty(rolePermissionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRolePermissionToRolePermissionDto(rolePermissionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2ba23439-d01e-3836-9915-1c99e1e2fcd9")
    @Override
    public List<RolePermissionDto> getByRoleIdsAndPermissionIds(
            List<RolePermission.PermissionIdAndRoleId> var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<RolePermission> rolePermissionList =
                rolePermissionDao.getByRoleIdsAndPermissionIds(var);
        if (CollectionUtil.isEmpty(rolePermissionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRolePermissionToRolePermissionDto(rolePermissionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2c7d325c-138f-30b6-a9e4-af4ef54842b5")
    @Override
    public RolePermissionDto getByRoleIdAndPermissionId(RolePermission.PermissionIdAndRoleId var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RolePermissionDto> ret = getByRoleIdsAndPermissionIds(Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RolePermissionDto rolePermissionDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return rolePermissionDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3176108f-0874-31f9-b9a4-e8dbd883c23c")
    @Override
    public List<RolePermissionDto> getByEnableFlag(Boolean enableFlag) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RolePermissionDto> rolePermissionDtoList = getByEnableFlags(Arrays.asList(enableFlag));
        return rolePermissionDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "43651a75-b6f8-3500-98be-0328c675a2aa")
    @Override
    public List<RolePermissionDto> getByRoleId(String roleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RolePermissionDto> rolePermissionDtoList = getByRoleIds(Arrays.asList(roleId));
        return rolePermissionDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "59e71175-9a4c-3733-82c7-4f324450490c")
    public List<RolePermissionDto> doConvertFromRolePermissionToRolePermissionDto(
            List<RolePermission> rolePermissionList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(rolePermissionList)) {
            return Collections.emptyList();
        }

        Map<String, String> permissionIdMap =
                rolePermissionList.stream()
                        .filter(i -> i.getPermissionId() != null)
                        .collect(
                                Collectors.toMap(
                                        RolePermission::getId, RolePermission::getPermissionId));
        List<PermissionAggDto> permissionIdPermissionAggDtoList =
                permissionAggDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(permissionIdMap.values())));
        Map<String, PermissionAggDto> permissionIdPermissionAggDtoMapRaw =
                permissionIdPermissionAggDtoList.stream()
                        .collect(Collectors.toMap(PermissionAggDto::getId, i -> i));
        Map<String, PermissionAggDto> permissionIdPermissionAggDtoMap =
                permissionIdMap.entrySet().stream()
                        .filter(i -> permissionIdPermissionAggDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> permissionIdPermissionAggDtoMapRaw.get(i.getValue())));

        List<RolePermissionBaseDto> baseDtoList =
                rolePermissionBaseDtoConverter.convertFromRolePermissionToRolePermissionBaseDto(
                        rolePermissionList);
        Map<String, RolePermissionDto> dtoMap =
                rolePermissionDtoConverter
                        .convertFromRolePermissionBaseDtoToRolePermissionDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RolePermissionDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RolePermissionDto> rolePermissionDtoList = new ArrayList<>();
        for (RolePermission i : rolePermissionList) {
            RolePermissionDto rolePermissionDto = dtoMap.get(i.getId());
            if (rolePermissionDto == null) {
                continue;
            }

            if (null != i.getPermissionId()) {
                rolePermissionDto.setPermission(
                        permissionIdPermissionAggDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            rolePermissionDtoList.add(rolePermissionDto);
        }
        return rolePermissionDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "774fd94d-48cd-3e97-af55-8aabbd4fe37d")
    @Override
    public List<RolePermissionDto> getByEnableFlags(List<Boolean> enableFlag) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(enableFlag)) {
            return Collections.emptyList();
        }

        List<RolePermission> rolePermissionList = rolePermissionDao.getByEnableFlags(enableFlag);
        if (CollectionUtil.isEmpty(rolePermissionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRolePermissionToRolePermissionDto(rolePermissionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "78749a08-7f9b-38db-8bd2-5c2b36aab8fd")
    @Override
    public List<RolePermissionDto> getByRoleIds(List<String> roleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(roleId)) {
            return Collections.emptyList();
        }

        List<RolePermission> rolePermissionList = rolePermissionDao.getByRoleIds(roleId);
        if (CollectionUtil.isEmpty(rolePermissionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRolePermissionToRolePermissionDto(rolePermissionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8220b09e-275b-305d-a6fb-52904aeb1689")
    @Override
    public RolePermissionDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RolePermissionDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RolePermissionDto rolePermissionDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return rolePermissionDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f3039c4c-6835-3d61-b664-d525013066f1")
    @Override
    public List<RolePermissionDto> getByPermissionId(String permissionId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RolePermissionDto> rolePermissionDtoList =
                getByPermissionIds(Arrays.asList(permissionId));
        return rolePermissionDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
