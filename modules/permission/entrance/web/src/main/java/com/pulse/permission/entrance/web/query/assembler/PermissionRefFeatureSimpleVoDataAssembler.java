package com.pulse.permission.entrance.web.query.assembler;

import com.pulse.permission.entrance.web.vo.PermissionRefFeatureSimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** PermissionRefFeatureSimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "9a5ab7cf-d050-3348-a405-d21236c14e04")
public class PermissionRefFeatureSimpleVoDataAssembler {

    /** 批量自定义组装PermissionRefFeatureSimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "3062bb39-f49a-3a93-ad09-f7f53d42e4fb")
    public void assembleDataCustomized(List<PermissionRefFeatureSimpleVo> dataList) {
        // 自定义数据组装

    }

    /** 组装PermissionRefFeatureSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "6458da54-ee5c-3c7c-8083-3def654fb8a4")
    public void assembleData(Map<String, PermissionRefFeatureSimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
