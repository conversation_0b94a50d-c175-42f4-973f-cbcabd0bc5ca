package com.pulse.permission.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.permission.entrance.web.query.assembler.PermissionRefFeatureSimpleVoDataAssembler;
import com.pulse.permission.entrance.web.vo.PermissionRefFeatureSimpleVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到PermissionRefFeatureSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "2d68bf4c-81c6-40d7-8558-b27f5e70465c|VO|CONVERTER")
public class PermissionRefFeatureSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private PermissionRefFeatureSimpleVoDataAssembler permissionRefFeatureSimpleVoDataAssembler;

    /** 使用默认方式组装PermissionRefFeatureSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "1360fc10-039d-3a47-b5b8-38756962ec75")
    public PermissionRefFeatureSimpleVo convertAndAssembleData(FeatureBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把FeatureBaseDto转换成PermissionRefFeatureSimpleVo */
    @AutoGenerated(locked = false, uuid = "2d68bf4c-81c6-40d7-8558-b27f5e70465c-converter-Map")
    public Map<FeatureBaseDto, PermissionRefFeatureSimpleVo>
            convertToPermissionRefFeatureSimpleVoMap(List<FeatureBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<FeatureBaseDto, PermissionRefFeatureSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            PermissionRefFeatureSimpleVo vo =
                                                    new PermissionRefFeatureSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setCode(dto.getCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把FeatureBaseDto转换成PermissionRefFeatureSimpleVo */
    @AutoGenerated(locked = true, uuid = "2d68bf4c-81c6-40d7-8558-b27f5e70465c-converter-list")
    public List<PermissionRefFeatureSimpleVo> convertToPermissionRefFeatureSimpleVoList(
            List<FeatureBaseDto> dtoList) {
        return new ArrayList<>(convertToPermissionRefFeatureSimpleVoMap(dtoList).values());
    }

    /** 把FeatureBaseDto转换成PermissionRefFeatureSimpleVo */
    @AutoGenerated(locked = true, uuid = "49b00373-d732-3c28-b511-74f8e0cc7bda")
    public PermissionRefFeatureSimpleVo convertToPermissionRefFeatureSimpleVo(FeatureBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToPermissionRefFeatureSimpleVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装PermissionRefFeatureSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "60fdde19-f0be-3db3-9f73-4b198f97c537")
    public List<PermissionRefFeatureSimpleVo> convertAndAssembleDataList(
            List<FeatureBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, PermissionRefFeatureSimpleVo> voMap =
                convertToPermissionRefFeatureSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        permissionRefFeatureSimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
