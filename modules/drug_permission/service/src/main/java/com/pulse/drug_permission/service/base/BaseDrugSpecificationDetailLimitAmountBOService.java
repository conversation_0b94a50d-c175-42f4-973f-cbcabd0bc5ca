package com.pulse.drug_permission.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_permission.manager.bo.*;
import com.pulse.drug_permission.manager.bo.DrugSpecificationDetailLimitAmountBO;
import com.pulse.drug_permission.persist.dos.DrugSpecificationDetailLimitAmount;
import com.pulse.drug_permission.service.base.BaseDrugSpecificationDetailLimitAmountBOService.DeleteDrugSpecificationDetailLimitAmountBoResult;
import com.pulse.drug_permission.service.base.BaseDrugSpecificationDetailLimitAmountBOService.MergeDrugSpecificationDetailLimitAmountBoResult;
import com.pulse.drug_permission.service.bto.DeleteDrugSpecificationDetailLimitAmountBto;
import com.pulse.drug_permission.service.bto.MergeDrugSpecificationDetailLimitAmountBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "388dad0a-bc84-3e5f-b3b9-ea5cf4bf5539")
public class BaseDrugSpecificationDetailLimitAmountBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugSpecificationDetailLimitAmountBO
            createMergeDrugSpecificationDetailLimitAmountOnDuplicateUpdate(
                    BaseDrugSpecificationDetailLimitAmountBOService
                                    .MergeDrugSpecificationDetailLimitAmountBoResult
                            boResult,
                    MergeDrugSpecificationDetailLimitAmountBto
                            mergeDrugSpecificationDetailLimitAmountBto) {
        DrugSpecificationDetailLimitAmountBO drugSpecificationDetailLimitAmountBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeDrugSpecificationDetailLimitAmountBto.getId() == null);
        if (!allNull && !found) {
            drugSpecificationDetailLimitAmountBO =
                    DrugSpecificationDetailLimitAmountBO.getById(
                            mergeDrugSpecificationDetailLimitAmountBto.getId());
            if (drugSpecificationDetailLimitAmountBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull =
                (mergeDrugSpecificationDetailLimitAmountBto.getDrugSpecificationDetailId() == null);
        if (!allNull && !found) {
            drugSpecificationDetailLimitAmountBO =
                    DrugSpecificationDetailLimitAmountBO.getByDrugSpecificationDetailId(
                            mergeDrugSpecificationDetailLimitAmountBto
                                    .getDrugSpecificationDetailId());
            if (drugSpecificationDetailLimitAmountBO != null) {
                matchedUkName += "(";
                matchedUkName += "'drug_specification_detail_id'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (drugSpecificationDetailLimitAmountBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        drugSpecificationDetailLimitAmountBO
                                .convertToDrugSpecificationDetailLimitAmount());
                updatedBto.setBto(mergeDrugSpecificationDetailLimitAmountBto);
                updatedBto.setBo(drugSpecificationDetailLimitAmountBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "drugOriginCode")) {
                    drugSpecificationDetailLimitAmountBO.setDrugOriginCode(
                            mergeDrugSpecificationDetailLimitAmountBto.getDrugOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "drugSpecificationDetailId")) {
                    drugSpecificationDetailLimitAmountBO.setDrugSpecificationDetailId(
                            mergeDrugSpecificationDetailLimitAmountBto
                                    .getDrugSpecificationDetailId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "institutionId")) {
                    drugSpecificationDetailLimitAmountBO.setInstitutionId(
                            mergeDrugSpecificationDetailLimitAmountBto.getInstitutionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "limitUnit")) {
                    drugSpecificationDetailLimitAmountBO.setLimitUnit(
                            mergeDrugSpecificationDetailLimitAmountBto.getLimitUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "outpMaxDosage")) {
                    drugSpecificationDetailLimitAmountBO.setOutpMaxDosage(
                            mergeDrugSpecificationDetailLimitAmountBto.getOutpMaxDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "outpDayMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setOutpDayMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getOutpDayMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "outpMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setOutpMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getOutpMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "erpMaxDosage")) {
                    drugSpecificationDetailLimitAmountBO.setErpMaxDosage(
                            mergeDrugSpecificationDetailLimitAmountBto.getErpMaxDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "erpDayMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setErpDayMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getErpDayMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "erpMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setErpMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getErpMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "inpMaxDosage")) {
                    drugSpecificationDetailLimitAmountBO.setInpMaxDosage(
                            mergeDrugSpecificationDetailLimitAmountBto.getInpMaxDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "inpDayMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setInpDayMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getInpDayMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "inpMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setInpMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getInpMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "dripSpeedUpperLimit")) {
                    drugSpecificationDetailLimitAmountBO.setDripSpeedUpperLimit(
                            mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedUpperLimit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "dripSpeedLowerLimit")) {
                    drugSpecificationDetailLimitAmountBO.setDripSpeedLowerLimit(
                            mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedLowerLimit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "dripSpeedUnit")) {
                    drugSpecificationDetailLimitAmountBO.setDripSpeedUnit(
                            mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "updatedBy")) {
                    drugSpecificationDetailLimitAmountBO.setUpdatedBy(
                            mergeDrugSpecificationDetailLimitAmountBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "createdBy")) {
                    drugSpecificationDetailLimitAmountBO.setCreatedBy(
                            mergeDrugSpecificationDetailLimitAmountBto.getCreatedBy());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        drugSpecificationDetailLimitAmountBO
                                .convertToDrugSpecificationDetailLimitAmount());
                updatedBto.setBto(mergeDrugSpecificationDetailLimitAmountBto);
                updatedBto.setBo(drugSpecificationDetailLimitAmountBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "drugOriginCode")) {
                    drugSpecificationDetailLimitAmountBO.setDrugOriginCode(
                            mergeDrugSpecificationDetailLimitAmountBto.getDrugOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "drugSpecificationDetailId")) {
                    drugSpecificationDetailLimitAmountBO.setDrugSpecificationDetailId(
                            mergeDrugSpecificationDetailLimitAmountBto
                                    .getDrugSpecificationDetailId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "institutionId")) {
                    drugSpecificationDetailLimitAmountBO.setInstitutionId(
                            mergeDrugSpecificationDetailLimitAmountBto.getInstitutionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "limitUnit")) {
                    drugSpecificationDetailLimitAmountBO.setLimitUnit(
                            mergeDrugSpecificationDetailLimitAmountBto.getLimitUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "outpMaxDosage")) {
                    drugSpecificationDetailLimitAmountBO.setOutpMaxDosage(
                            mergeDrugSpecificationDetailLimitAmountBto.getOutpMaxDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "outpDayMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setOutpDayMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getOutpDayMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "outpMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setOutpMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getOutpMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "erpMaxDosage")) {
                    drugSpecificationDetailLimitAmountBO.setErpMaxDosage(
                            mergeDrugSpecificationDetailLimitAmountBto.getErpMaxDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "erpDayMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setErpDayMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getErpDayMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "erpMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setErpMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getErpMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "inpMaxDosage")) {
                    drugSpecificationDetailLimitAmountBO.setInpMaxDosage(
                            mergeDrugSpecificationDetailLimitAmountBto.getInpMaxDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "inpDayMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setInpDayMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getInpDayMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "inpMaxAmount")) {
                    drugSpecificationDetailLimitAmountBO.setInpMaxAmount(
                            mergeDrugSpecificationDetailLimitAmountBto.getInpMaxAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "dripSpeedUpperLimit")) {
                    drugSpecificationDetailLimitAmountBO.setDripSpeedUpperLimit(
                            mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedUpperLimit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "dripSpeedLowerLimit")) {
                    drugSpecificationDetailLimitAmountBO.setDripSpeedLowerLimit(
                            mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedLowerLimit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "dripSpeedUnit")) {
                    drugSpecificationDetailLimitAmountBO.setDripSpeedUnit(
                            mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "updatedBy")) {
                    drugSpecificationDetailLimitAmountBO.setUpdatedBy(
                            mergeDrugSpecificationDetailLimitAmountBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugSpecificationDetailLimitAmountBto,
                                        "__$validPropertySet"),
                        "createdBy")) {
                    drugSpecificationDetailLimitAmountBO.setCreatedBy(
                            mergeDrugSpecificationDetailLimitAmountBto.getCreatedBy());
                }
            }
        } else {
            drugSpecificationDetailLimitAmountBO = new DrugSpecificationDetailLimitAmountBO();
            if (pkExist) {
                drugSpecificationDetailLimitAmountBO.setId(
                        mergeDrugSpecificationDetailLimitAmountBto.getId());
            } else {
                drugSpecificationDetailLimitAmountBO.setId(
                        String.valueOf(
                                this.idGenerator.allocateId(
                                        "drug_specification_detail_limit_amount")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "drugOriginCode")) {
                drugSpecificationDetailLimitAmountBO.setDrugOriginCode(
                        mergeDrugSpecificationDetailLimitAmountBto.getDrugOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "drugSpecificationDetailId")) {
                drugSpecificationDetailLimitAmountBO.setDrugSpecificationDetailId(
                        mergeDrugSpecificationDetailLimitAmountBto.getDrugSpecificationDetailId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "institutionId")) {
                drugSpecificationDetailLimitAmountBO.setInstitutionId(
                        mergeDrugSpecificationDetailLimitAmountBto.getInstitutionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "limitUnit")) {
                drugSpecificationDetailLimitAmountBO.setLimitUnit(
                        mergeDrugSpecificationDetailLimitAmountBto.getLimitUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "outpMaxDosage")) {
                drugSpecificationDetailLimitAmountBO.setOutpMaxDosage(
                        mergeDrugSpecificationDetailLimitAmountBto.getOutpMaxDosage());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "outpDayMaxAmount")) {
                drugSpecificationDetailLimitAmountBO.setOutpDayMaxAmount(
                        mergeDrugSpecificationDetailLimitAmountBto.getOutpDayMaxAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "outpMaxAmount")) {
                drugSpecificationDetailLimitAmountBO.setOutpMaxAmount(
                        mergeDrugSpecificationDetailLimitAmountBto.getOutpMaxAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "erpMaxDosage")) {
                drugSpecificationDetailLimitAmountBO.setErpMaxDosage(
                        mergeDrugSpecificationDetailLimitAmountBto.getErpMaxDosage());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "erpDayMaxAmount")) {
                drugSpecificationDetailLimitAmountBO.setErpDayMaxAmount(
                        mergeDrugSpecificationDetailLimitAmountBto.getErpDayMaxAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "erpMaxAmount")) {
                drugSpecificationDetailLimitAmountBO.setErpMaxAmount(
                        mergeDrugSpecificationDetailLimitAmountBto.getErpMaxAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "inpMaxDosage")) {
                drugSpecificationDetailLimitAmountBO.setInpMaxDosage(
                        mergeDrugSpecificationDetailLimitAmountBto.getInpMaxDosage());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "inpDayMaxAmount")) {
                drugSpecificationDetailLimitAmountBO.setInpDayMaxAmount(
                        mergeDrugSpecificationDetailLimitAmountBto.getInpDayMaxAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "inpMaxAmount")) {
                drugSpecificationDetailLimitAmountBO.setInpMaxAmount(
                        mergeDrugSpecificationDetailLimitAmountBto.getInpMaxAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "dripSpeedUpperLimit")) {
                drugSpecificationDetailLimitAmountBO.setDripSpeedUpperLimit(
                        mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedUpperLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "dripSpeedLowerLimit")) {
                drugSpecificationDetailLimitAmountBO.setDripSpeedLowerLimit(
                        mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedLowerLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "dripSpeedUnit")) {
                drugSpecificationDetailLimitAmountBO.setDripSpeedUnit(
                        mergeDrugSpecificationDetailLimitAmountBto.getDripSpeedUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "updatedBy")) {
                drugSpecificationDetailLimitAmountBO.setUpdatedBy(
                        mergeDrugSpecificationDetailLimitAmountBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugSpecificationDetailLimitAmountBto,
                                    "__$validPropertySet"),
                    "createdBy")) {
                drugSpecificationDetailLimitAmountBO.setCreatedBy(
                        mergeDrugSpecificationDetailLimitAmountBto.getCreatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeDrugSpecificationDetailLimitAmountBto);
            addedBto.setBo(drugSpecificationDetailLimitAmountBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugSpecificationDetailLimitAmountBO;
    }

    /** 删除对象:deleteDrugSpecificationDetailLimitAmount,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugSpecificationDetailLimitAmountBO
            deleteDeleteDrugSpecificationDetailLimitAmountOnMissThrowEx(
                    BaseDrugSpecificationDetailLimitAmountBOService
                                    .DeleteDrugSpecificationDetailLimitAmountBoResult
                            boResult,
                    DeleteDrugSpecificationDetailLimitAmountBto
                            deleteDrugSpecificationDetailLimitAmountBto) {
        DrugSpecificationDetailLimitAmountBO drugSpecificationDetailLimitAmountBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteDrugSpecificationDetailLimitAmountBto.getId() == null);
        if (!allNull && !found) {
            drugSpecificationDetailLimitAmountBO =
                    DrugSpecificationDetailLimitAmountBO.getById(
                            deleteDrugSpecificationDetailLimitAmountBto.getId());
            found = true;
        }
        if (drugSpecificationDetailLimitAmountBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugSpecificationDetailLimitAmountBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteDrugSpecificationDetailLimitAmountBto);
            deletedBto.setEntity(
                    drugSpecificationDetailLimitAmountBO
                            .convertToDrugSpecificationDetailLimitAmount());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugSpecificationDetailLimitAmountBO;
        }
    }

    /** 功能：删除药品限量 */
    @AutoGenerated(locked = true)
    protected DeleteDrugSpecificationDetailLimitAmountBoResult
            deleteDrugSpecificationDetailLimitAmountBase(
                    DeleteDrugSpecificationDetailLimitAmountBto
                            deleteDrugSpecificationDetailLimitAmountBto) {
        if (deleteDrugSpecificationDetailLimitAmountBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteDrugSpecificationDetailLimitAmountBoResult boResult =
                new DeleteDrugSpecificationDetailLimitAmountBoResult();
        DrugSpecificationDetailLimitAmountBO drugSpecificationDetailLimitAmountBO =
                deleteDeleteDrugSpecificationDetailLimitAmountOnMissThrowEx(
                        boResult, deleteDrugSpecificationDetailLimitAmountBto);
        boResult.setRootBo(drugSpecificationDetailLimitAmountBO);
        return boResult;
    }

    /** 功能：保存药品限量 */
    @AutoGenerated(locked = true)
    protected MergeDrugSpecificationDetailLimitAmountBoResult
            mergeDrugSpecificationDetailLimitAmountBase(
                    MergeDrugSpecificationDetailLimitAmountBto
                            mergeDrugSpecificationDetailLimitAmountBto) {
        if (mergeDrugSpecificationDetailLimitAmountBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDrugSpecificationDetailLimitAmountBoResult boResult =
                new MergeDrugSpecificationDetailLimitAmountBoResult();
        DrugSpecificationDetailLimitAmountBO drugSpecificationDetailLimitAmountBO =
                createMergeDrugSpecificationDetailLimitAmountOnDuplicateUpdate(
                        boResult, mergeDrugSpecificationDetailLimitAmountBto);
        boResult.setRootBo(drugSpecificationDetailLimitAmountBO);
        return boResult;
    }

    public static class MergeDrugSpecificationDetailLimitAmountBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailLimitAmountBO getRootBo() {
            return (DrugSpecificationDetailLimitAmountBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        MergeDrugSpecificationDetailLimitAmountBto,
                        DrugSpecificationDetailLimitAmountBO>
                getCreatedBto(
                        MergeDrugSpecificationDetailLimitAmountBto
                                mergeDrugSpecificationDetailLimitAmountBto) {
            return this.getAddedResult(mergeDrugSpecificationDetailLimitAmountBto);
        }

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailLimitAmount getDeleted_DrugSpecificationDetailLimitAmount() {
            return (DrugSpecificationDetailLimitAmount)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugSpecificationDetailLimitAmount.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDrugSpecificationDetailLimitAmountBto,
                        DrugSpecificationDetailLimitAmount,
                        DrugSpecificationDetailLimitAmountBO>
                getUpdatedBto(
                        MergeDrugSpecificationDetailLimitAmountBto
                                mergeDrugSpecificationDetailLimitAmountBto) {
            return super.getUpdatedResult(mergeDrugSpecificationDetailLimitAmountBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        MergeDrugSpecificationDetailLimitAmountBto,
                        DrugSpecificationDetailLimitAmountBO>
                getUnmodifiedBto(
                        MergeDrugSpecificationDetailLimitAmountBto
                                mergeDrugSpecificationDetailLimitAmountBto) {
            return super.getUnmodifiedResult(mergeDrugSpecificationDetailLimitAmountBto);
        }
    }

    public static class DeleteDrugSpecificationDetailLimitAmountBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailLimitAmountBO getRootBo() {
            return (DrugSpecificationDetailLimitAmountBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        DeleteDrugSpecificationDetailLimitAmountBto,
                        DrugSpecificationDetailLimitAmountBO>
                getCreatedBto(
                        DeleteDrugSpecificationDetailLimitAmountBto
                                deleteDrugSpecificationDetailLimitAmountBto) {
            return this.getAddedResult(deleteDrugSpecificationDetailLimitAmountBto);
        }

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailLimitAmount getDeleted_DrugSpecificationDetailLimitAmount() {
            return (DrugSpecificationDetailLimitAmount)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugSpecificationDetailLimitAmount.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteDrugSpecificationDetailLimitAmountBto,
                        DrugSpecificationDetailLimitAmount,
                        DrugSpecificationDetailLimitAmountBO>
                getUpdatedBto(
                        DeleteDrugSpecificationDetailLimitAmountBto
                                deleteDrugSpecificationDetailLimitAmountBto) {
            return super.getUpdatedResult(deleteDrugSpecificationDetailLimitAmountBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        DeleteDrugSpecificationDetailLimitAmountBto,
                        DrugSpecificationDetailLimitAmountBO>
                getUnmodifiedBto(
                        DeleteDrugSpecificationDetailLimitAmountBto
                                deleteDrugSpecificationDetailLimitAmountBto) {
            return super.getUnmodifiedResult(deleteDrugSpecificationDetailLimitAmountBto);
        }
    }
}
