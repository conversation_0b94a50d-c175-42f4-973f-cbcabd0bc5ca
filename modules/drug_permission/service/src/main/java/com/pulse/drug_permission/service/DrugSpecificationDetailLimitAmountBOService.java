package com.pulse.drug_permission.service;

import com.pulse.drug_permission.manager.bo.*;
import com.pulse.drug_permission.manager.dto.DrugSpecificationDetailLimitAmountBaseDto;
import com.pulse.drug_permission.persist.dos.DrugSpecificationDetailLimitAmount;
import com.pulse.drug_permission.service.base.BaseDrugSpecificationDetailLimitAmountBOService;
import com.pulse.drug_permission.service.bto.DeleteDrugSpecificationDetailLimitAmountBto;
import com.pulse.drug_permission.service.bto.MergeDrugSpecificationDetailLimitAmountBto;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "066e2f77-1e73-4d86-abe5-5e9ab9b3c719|BO|SERVICE")
public class DrugSpecificationDetailLimitAmountBOService
        extends BaseDrugSpecificationDetailLimitAmountBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDetailLimitAmountBaseDtoService
            drugSpecificationDetailLimitAmountBaseDtoService;

    /** 功能：删除药品限量 */
    @PublicInterface(id = "4b5b981b-92a6-4ce5-972a-0905b0107d7b", module = "drug_permission")
    @Transactional
    @AutoGenerated(locked = false, uuid = "014d952e-e019-4180-b7f4-af09cc25a3eb")
    public String deleteDrugSpecificationDetailLimitAmount(
            @Valid @NotNull
                    DeleteDrugSpecificationDetailLimitAmountBto
                            deleteDrugSpecificationDetailLimitAmountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugSpecificationDetailLimitAmountBaseDto drugSpecificationDetailLimitAmountBaseDto =
                drugSpecificationDetailLimitAmountBaseDtoService.getById(
                        deleteDrugSpecificationDetailLimitAmountBto.getId());
        DeleteDrugSpecificationDetailLimitAmountBoResult boResult =
                super.deleteDrugSpecificationDetailLimitAmountBase(
                        deleteDrugSpecificationDetailLimitAmountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteDrugSpecificationDetailLimitAmountBto */
        {
            DeleteDrugSpecificationDetailLimitAmountBto bto =
                    boResult
                            .<DeleteDrugSpecificationDetailLimitAmountBto>getBtoOfType(
                                    DeleteDrugSpecificationDetailLimitAmountBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<
                            DeleteDrugSpecificationDetailLimitAmountBto,
                            DrugSpecificationDetailLimitAmount>
                    deletedBto = boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 功能：保存药品限量 */
    @PublicInterface(id = "8de83450-0ef4-4936-af3d-e5336e1b2b3f", module = "drug_permission")
    @Transactional
    @AutoGenerated(locked = false, uuid = "52beee55-9aa0-48d5-a8d7-5f4e93910142")
    public String mergeDrugSpecificationDetailLimitAmount(
            @Valid @NotNull
                    MergeDrugSpecificationDetailLimitAmountBto
                            mergeDrugSpecificationDetailLimitAmountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugSpecificationDetailLimitAmountBaseDto drugSpecificationDetailLimitAmountBaseDto = null;
        if (mergeDrugSpecificationDetailLimitAmountBto.getId() != null) {
            drugSpecificationDetailLimitAmountBaseDto =
                    drugSpecificationDetailLimitAmountBaseDtoService.getById(
                            mergeDrugSpecificationDetailLimitAmountBto.getId());
        }
        MergeDrugSpecificationDetailLimitAmountBoResult boResult =
                super.mergeDrugSpecificationDetailLimitAmountBase(
                        mergeDrugSpecificationDetailLimitAmountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeDrugSpecificationDetailLimitAmountBto */
        {
            MergeDrugSpecificationDetailLimitAmountBto bto =
                    boResult
                            .<MergeDrugSpecificationDetailLimitAmountBto>getBtoOfType(
                                    MergeDrugSpecificationDetailLimitAmountBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            MergeDrugSpecificationDetailLimitAmountBto,
                            DrugSpecificationDetailLimitAmount,
                            DrugSpecificationDetailLimitAmountBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<
                            MergeDrugSpecificationDetailLimitAmountBto,
                            DrugSpecificationDetailLimitAmountBO>
                    addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugSpecificationDetailLimitAmountBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugSpecificationDetailLimitAmount entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DrugSpecificationDetailLimitAmountBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
