package com.pulse.drug_permission.service.converter;

import com.pulse.drug_permission.manager.dto.DrugOriginSpecificationSupplyBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "6a9abb53-01eb-3fb7-bc6e-f8fe620766bd")
public class DrugOriginSpecificationSupplyBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugOriginSpecificationSupplyBaseDto> DrugOriginSpecificationSupplyBaseDtoConverter(
            List<DrugOriginSpecificationSupplyBaseDto> drugOriginSpecificationSupplyBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugOriginSpecificationSupplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
