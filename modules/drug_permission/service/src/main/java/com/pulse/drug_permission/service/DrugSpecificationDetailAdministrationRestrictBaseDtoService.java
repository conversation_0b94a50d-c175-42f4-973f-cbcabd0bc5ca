package com.pulse.drug_permission.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_permission.manager.DrugSpecificationDetailAdministrationRestrictBaseDtoManager;
import com.pulse.drug_permission.manager.dto.DrugSpecificationDetailAdministrationRestrictBaseDto;
import com.pulse.drug_permission.service.converter.DrugSpecificationDetailAdministrationRestrictBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "7d00b5b4-ebc9-4bdb-bf86-2d74cf0981a7|DTO|SERVICE")
public class DrugSpecificationDetailAdministrationRestrictBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugSpecificationDetailAdministrationRestrictBaseDtoManager
            drugSpecificationDetailAdministrationRestrictBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugSpecificationDetailAdministrationRestrictBaseDtoServiceConverter
            drugSpecificationDetailAdministrationRestrictBaseDtoServiceConverter;

    @PublicInterface(
            id = "6d97aa0a-f29c-4bb5-984c-c892d4a2735d",
            module = "drug_permission",
            moduleId = "bfd4add9-5d8b-4865-b283-7f479fba6d7e",
            pubRpc = true,
            version = "1748503946835")
    @AutoGenerated(locked = false, uuid = "3b0bf39e-82f1-32fa-afd0-cca354df40b2")
    public List<DrugSpecificationDetailAdministrationRestrictBaseDto>
            getByDrugSpecificationDetailIds(
                    @Valid @NotNull(message = "药品规格明细id不能为空")
                            List<String> drugSpecificationDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugSpecificationDetailId = new ArrayList<>(new HashSet<>(drugSpecificationDetailId));
        List<DrugSpecificationDetailAdministrationRestrictBaseDto>
                drugSpecificationDetailAdministrationRestrictBaseDtoList =
                        drugSpecificationDetailAdministrationRestrictBaseDtoManager
                                .getByDrugSpecificationDetailIds(drugSpecificationDetailId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugSpecificationDetailAdministrationRestrictBaseDtoServiceConverter
                .DrugSpecificationDetailAdministrationRestrictBaseDtoConverter(
                        drugSpecificationDetailAdministrationRestrictBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "737102e4-b4c0-4ea4-84af-c2084b950989",
            module = "drug_permission",
            moduleId = "bfd4add9-5d8b-4865-b283-7f479fba6d7e",
            pubRpc = true,
            version = "1748503946833")
    @AutoGenerated(locked = false, uuid = "5a195b1c-e54a-35f4-a605-cc20a7c92583")
    public List<DrugSpecificationDetailAdministrationRestrictBaseDto>
            getByDrugSpecificationDetailId(
                    @NotNull(message = "药品规格明细id不能为空") String drugSpecificationDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugSpecificationDetailIds(Arrays.asList(drugSpecificationDetailId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "ebff0eca-205d-4bf4-9492-c47a9f4c80a7",
            module = "drug_permission",
            moduleId = "bfd4add9-5d8b-4865-b283-7f479fba6d7e",
            pubRpc = true,
            version = "1748503946830")
    @AutoGenerated(locked = false, uuid = "7a5c4e7b-b5c8-3fe8-b871-6761635dc8d2")
    public DrugSpecificationDetailAdministrationRestrictBaseDto getById(
            @NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugSpecificationDetailAdministrationRestrictBaseDto> ret =
                getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "db0c5bee-5413-4dc2-b45f-916f9ef75427",
            module = "drug_permission",
            moduleId = "bfd4add9-5d8b-4865-b283-7f479fba6d7e",
            pubRpc = true,
            version = "1748503946832")
    @AutoGenerated(locked = false, uuid = "a4ce465c-b8f4-3b0d-9fc8-753229952da2")
    public List<DrugSpecificationDetailAdministrationRestrictBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugSpecificationDetailAdministrationRestrictBaseDto>
                drugSpecificationDetailAdministrationRestrictBaseDtoList =
                        drugSpecificationDetailAdministrationRestrictBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugSpecificationDetailAdministrationRestrictBaseDtoServiceConverter
                .DrugSpecificationDetailAdministrationRestrictBaseDtoConverter(
                        drugSpecificationDetailAdministrationRestrictBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
