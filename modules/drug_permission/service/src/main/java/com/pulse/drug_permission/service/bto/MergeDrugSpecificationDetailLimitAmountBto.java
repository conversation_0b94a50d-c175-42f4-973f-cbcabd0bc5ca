package com.pulse.drug_permission.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashSet;

/**
 * <b>[源自]</b> DrugSpecificationDetailLimitAmount
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "52beee55-9aa0-48d5-a8d7-5f4e93910142|BTO|DEFINITION")
public class MergeDrugSpecificationDetailLimitAmountBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 创建人id */
    @AutoGenerated(locked = true, uuid = "18526041-36c3-4d4d-a8e2-1a4aca7e4a7d")
    private String createdBy;

    /** 滴速下限 */
    @AutoGenerated(locked = true, uuid = "6447fd98-0f2a-452c-b5d0-868e5d6c1297")
    private Long dripSpeedLowerLimit;

    /** 滴速单位 */
    @AutoGenerated(locked = true, uuid = "4ab01567-a6cd-4185-b8a6-72d1288df0b1")
    private String dripSpeedUnit;

    /** 滴速上限 */
    @AutoGenerated(locked = true, uuid = "65b5399e-18fb-4c59-8987-00abfb2694fa")
    private Long dripSpeedUpperLimit;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "537c5538-8e5b-4728-ba8d-3de9667b3d1e")
    private String drugOriginCode;

    /** 药品规格明细id */
    @AutoGenerated(locked = true, uuid = "28ee9766-c7ae-4207-bba5-244514fc6a89")
    private String drugSpecificationDetailId;

    /** 急诊最大使用天数 */
    @AutoGenerated(locked = true, uuid = "ca07dac2-f68e-4097-83a4-278cdb3b3a95")
    private Long erpDayMaxAmount;

    /** 急诊限量数量 */
    @AutoGenerated(locked = true, uuid = "3763f2ff-9ff6-4ca6-8bbe-d3da1cd20e35")
    private BigDecimal erpMaxAmount;

    /** 急诊单次最大剂量 */
    @AutoGenerated(locked = true, uuid = "8a1c68ea-ab7b-46b5-b24b-707b4a036169")
    private BigDecimal erpMaxDosage;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "6e4780c1-f267-4f6c-9693-f65f59c4fce6")
    private String id;

    /** 住院最大使用天数 */
    @AutoGenerated(locked = true, uuid = "aaeea320-39ad-442a-b0bf-74daba7c83be")
    private Long inpDayMaxAmount;

    /** 住院限量数量 */
    @AutoGenerated(locked = true, uuid = "c990681e-b2d8-4007-8042-2d82027e392e")
    private BigDecimal inpMaxAmount;

    /** 住院单次最大剂量 */
    @AutoGenerated(locked = true, uuid = "7cdaf006-fed1-4596-a363-7e42c42d7aa5")
    private BigDecimal inpMaxDosage;

    /** 机构编码 */
    @AutoGenerated(locked = true, uuid = "00c9bd6f-30d2-4c5f-beeb-6b450be956d7")
    private String institutionId;

    /** 限量单位 */
    @AutoGenerated(locked = true, uuid = "15901343-b6a7-4600-94ee-d99a94e817b2")
    private String limitUnit;

    /** 门诊最大使用天数 */
    @AutoGenerated(locked = true, uuid = "ca44780f-d688-4258-b931-97ae59701fa6")
    private Long outpDayMaxAmount;

    /** 门诊限量数量 */
    @AutoGenerated(locked = true, uuid = "373a338e-52b9-462d-9060-a38246cac047")
    private BigDecimal outpMaxAmount;

    /** 门诊单次最大剂量 */
    @AutoGenerated(locked = true, uuid = "69ca8180-331a-47a7-a4a2-e6d3c3e04f0a")
    private BigDecimal outpMaxDosage;

    /** 修改人id */
    @AutoGenerated(locked = true, uuid = "e34c5190-7dd8-4a45-a5a0-2167430d7b91")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDripSpeedLowerLimit(Long dripSpeedLowerLimit) {
        this.__$validPropertySet.add("dripSpeedLowerLimit");
        this.dripSpeedLowerLimit = dripSpeedLowerLimit;
    }

    @AutoGenerated(locked = true)
    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.__$validPropertySet.add("dripSpeedUnit");
        this.dripSpeedUnit = dripSpeedUnit;
    }

    @AutoGenerated(locked = true)
    public void setDripSpeedUpperLimit(Long dripSpeedUpperLimit) {
        this.__$validPropertySet.add("dripSpeedUpperLimit");
        this.dripSpeedUpperLimit = dripSpeedUpperLimit;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginCode(String drugOriginCode) {
        this.__$validPropertySet.add("drugOriginCode");
        this.drugOriginCode = drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugSpecificationDetailId(String drugSpecificationDetailId) {
        this.__$validPropertySet.add("drugSpecificationDetailId");
        this.drugSpecificationDetailId = drugSpecificationDetailId;
    }

    @AutoGenerated(locked = true)
    public void setErpDayMaxAmount(Long erpDayMaxAmount) {
        this.__$validPropertySet.add("erpDayMaxAmount");
        this.erpDayMaxAmount = erpDayMaxAmount;
    }

    @AutoGenerated(locked = true)
    public void setErpMaxAmount(BigDecimal erpMaxAmount) {
        this.__$validPropertySet.add("erpMaxAmount");
        this.erpMaxAmount = erpMaxAmount;
    }

    @AutoGenerated(locked = true)
    public void setErpMaxDosage(BigDecimal erpMaxDosage) {
        this.__$validPropertySet.add("erpMaxDosage");
        this.erpMaxDosage = erpMaxDosage;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInpDayMaxAmount(Long inpDayMaxAmount) {
        this.__$validPropertySet.add("inpDayMaxAmount");
        this.inpDayMaxAmount = inpDayMaxAmount;
    }

    @AutoGenerated(locked = true)
    public void setInpMaxAmount(BigDecimal inpMaxAmount) {
        this.__$validPropertySet.add("inpMaxAmount");
        this.inpMaxAmount = inpMaxAmount;
    }

    @AutoGenerated(locked = true)
    public void setInpMaxDosage(BigDecimal inpMaxDosage) {
        this.__$validPropertySet.add("inpMaxDosage");
        this.inpMaxDosage = inpMaxDosage;
    }

    @AutoGenerated(locked = true)
    public void setInstitutionId(String institutionId) {
        this.__$validPropertySet.add("institutionId");
        this.institutionId = institutionId;
    }

    @AutoGenerated(locked = true)
    public void setLimitUnit(String limitUnit) {
        this.__$validPropertySet.add("limitUnit");
        this.limitUnit = limitUnit;
    }

    @AutoGenerated(locked = true)
    public void setOutpDayMaxAmount(Long outpDayMaxAmount) {
        this.__$validPropertySet.add("outpDayMaxAmount");
        this.outpDayMaxAmount = outpDayMaxAmount;
    }

    @AutoGenerated(locked = true)
    public void setOutpMaxAmount(BigDecimal outpMaxAmount) {
        this.__$validPropertySet.add("outpMaxAmount");
        this.outpMaxAmount = outpMaxAmount;
    }

    @AutoGenerated(locked = true)
    public void setOutpMaxDosage(BigDecimal outpMaxDosage) {
        this.__$validPropertySet.add("outpMaxDosage");
        this.outpMaxDosage = outpMaxDosage;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
