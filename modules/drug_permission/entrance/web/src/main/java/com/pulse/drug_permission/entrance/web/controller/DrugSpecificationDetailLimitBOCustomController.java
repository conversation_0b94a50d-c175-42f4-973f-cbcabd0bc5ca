package com.pulse.drug_permission.entrance.web.controller;

import cn.hutool.core.util.StrUtil;

import com.pulse.drug_permission.service.DrugSpecificationDetailLimitAmountBOService;
import com.pulse.drug_permission.service.bto.MergeDrugSpecificationDetailLimitAmountBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "4f9445be-f153-3a9d-b42f-6972658b3206")
@Slf4j
public class DrugSpecificationDetailLimitBOCustomController {

    @Resource
    private DrugSpecificationDetailLimitAmountBOService drugSpecificationDetailLimitAmountBOService;

    /** 保存药品规格限量信息列表 */
    @PublicInterface(id = "114eec08-297f-496e-b18f-eb050834eda4", version = "1748938085956")
    @AutoGenerated(locked = false, uuid = "114eec08-297f-496e-b18f-eb050834eda4")
    @RequestMapping(
            value = {"/api/drug-permission/merge-drug-specification-detail-limit-amount-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeSpecificationDetailLimitAmountList(
            @Valid @NotNull
                    List<MergeDrugSpecificationDetailLimitAmountBto>
                            mergeSpecificationDetailLimitAmountBtoList) {

        int successCount = 0;
        log.info("保存药品规格限量信息列表开始，数量[{}]", mergeSpecificationDetailLimitAmountBtoList.size());
        int failCount = 0;
        String errorMessage = "";
        for (MergeDrugSpecificationDetailLimitAmountBto mergeDrugSpecificationDetailLimitAmountBto :
                mergeSpecificationDetailLimitAmountBtoList) {
            try {
                drugSpecificationDetailLimitAmountBOService.mergeDrugSpecificationDetailLimitAmount(
                        mergeDrugSpecificationDetailLimitAmountBto);
                successCount++;
            } catch (Exception e) {
                log.warn(
                        "保存药品规格限量信息失败: 药品产地编码[{}]，药品产地规格[{}]，错误信息: {}",
                        mergeDrugSpecificationDetailLimitAmountBto.getDrugOriginCode(),
                        mergeDrugSpecificationDetailLimitAmountBto.getDrugSpecificationDetailId(),
                        e.getMessage());
                failCount++;
                errorMessage += e.getMessage() + "；";
            }
        }

        log.info("保存药品规格限量信息列表结束，成功[{}]，失败[{}]", successCount, failCount);

        if (failCount > 0) {
            return StrUtil.format(
                    "保存{}条药品数据成功，保存{}条药品数据失败，失败原因: 【{}】", successCount, failCount, errorMessage);
        }
        return StrUtil.format("保存{}条药品数据成功", successCount);
    }
}
