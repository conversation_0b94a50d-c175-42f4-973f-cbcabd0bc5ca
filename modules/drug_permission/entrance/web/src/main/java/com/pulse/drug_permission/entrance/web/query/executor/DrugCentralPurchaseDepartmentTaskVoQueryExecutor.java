package com.pulse.drug_permission.entrance.web.query.executor;

import com.pulse.drug_permission.entrance.web.converter.DrugCentralPurchaseDepartmentTaskVoConverter;
import com.pulse.drug_permission.entrance.web.query.assembler.DrugCentralPurchaseDepartmentTaskVoDataAssembler;
import com.pulse.drug_permission.entrance.web.vo.DrugCentralPurchaseDepartmentTaskVo;
import com.pulse.drug_permission.manager.dto.DrugCentralPurchaseDepartmentTaskBaseDto;
import com.pulse.drug_permission.persist.qto.ListDepartmentTaskByTaskIdQto;
import com.pulse.drug_permission.service.DrugCentralPurchaseDepartmentTaskBaseDtoService;
import com.pulse.drug_permission.service.index.entity.ListDepartmentTaskByTaskIdQtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** DrugCentralPurchaseDepartmentTaskVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "00651f05-74ee-3462-98f7-b4ba0dc7d987")
public class DrugCentralPurchaseDepartmentTaskVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskBaseDtoService
            drugCentralPurchaseDepartmentTaskBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskVoConverter
            drugCentralPurchaseDepartmentTaskVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskVoDataAssembler
            drugCentralPurchaseDepartmentTaskVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ListDepartmentTaskByTaskIdQtoService listDepartmentTaskByTaskIdQtoService;

    /** 根据ListDepartmentTaskByTaskIdQto查询DrugCentralPurchaseDepartmentTaskVo列表,上限500 */
    @AutoGenerated(locked = false, uuid = "ca03f711-cf1a-3039-af90-8758b1c33836")
    public List<DrugCentralPurchaseDepartmentTaskVo> queryListDepartmentTaskByTaskId(
            @NotNull ListDepartmentTaskByTaskIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listDepartmentTaskByTaskIdQtoService.query(qto);
        Map<String, DrugCentralPurchaseDepartmentTaskVo> idVoMap = toIdVoMap(ids);
        drugCentralPurchaseDepartmentTaskVoDataAssembler.assembleData(idVoMap);
        List<DrugCentralPurchaseDepartmentTaskVo> result =
                ids.stream()
                        .map(id -> idVoMap.get(id))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "d62470b2-b38a-3d69-bf37-8aa82924de79")
    private Map<String, DrugCentralPurchaseDepartmentTaskVo> toIdVoMap(List<String> ids) {
        List<DrugCentralPurchaseDepartmentTaskBaseDto> rootBaseDtoList =
                drugCentralPurchaseDepartmentTaskBaseDtoService.getByIds(ids);
        Map<String, DrugCentralPurchaseDepartmentTaskBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCentralPurchaseDepartmentTaskBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<DrugCentralPurchaseDepartmentTaskBaseDto, DrugCentralPurchaseDepartmentTaskVo> voMap =
                drugCentralPurchaseDepartmentTaskVoConverter
                        .convertToDrugCentralPurchaseDepartmentTaskVoMap(
                                new ArrayList<>(baseDtoMap.values()));
        Map<String, DrugCentralPurchaseDepartmentTaskVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCentralPurchaseDepartmentTaskBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }
}
