package com.pulse.drug_permission.entrance.web.controller;

import com.pulse.drug_permission.service.DrugSpecificationDetailLimitAmountBOService;
import com.pulse.drug_permission.service.bto.MergeDrugSpecificationDetailLimitAmountBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "30fde61e-4038-3e6f-bcef-ee16867c62e7")
public class DrugSpecificationDetailLimitBOController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDetailLimitAmountBOService drugSpecificationDetailLimitAmountBOService;

    /** 功能：保存药品限量 保存药品限量 */
    @PublicInterface(id = "0ddf790f-5d22-47b6-ad88-84689f97b6af", version = "1748937033106")
    @AutoGenerated(locked = false, uuid = "0ddf790f-5d22-47b6-ad88-84689f97b6af")
    @RequestMapping(
            value = {"/api/drug-permission/merge-drug-specification-detail-limit-amount"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeDrugSpecificationDetailLimitAmount(
            @Valid
                    MergeDrugSpecificationDetailLimitAmountBto
                            mergeDrugSpecificationDetailLimitAmountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                drugSpecificationDetailLimitAmountBOService.mergeDrugSpecificationDetailLimitAmount(
                        mergeDrugSpecificationDetailLimitAmountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
