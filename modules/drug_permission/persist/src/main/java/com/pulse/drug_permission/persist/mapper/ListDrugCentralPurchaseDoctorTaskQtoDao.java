package com.pulse.drug_permission.persist.mapper;

import com.pulse.drug_permission.persist.qto.ListDrugCentralPurchaseDoctorTaskQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "ed387996-62d9-44eb-bf28-bd676f6e5f10|QTO|DAO")
public class ListDrugCentralPurchaseDoctorTaskQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 功能：获取集采医生任务 */
    @AutoGenerated(locked = false, uuid = "ed387996-62d9-44eb-bf28-bd676f6e5f10-count")
    public Integer count(ListDrugCentralPurchaseDoctorTaskQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_central_purchase_doctor_task.id) FROM"
                    + " drug_central_purchase_doctor_task WHERE"
                    + " drug_central_purchase_doctor_task.department_task_id = #departmentTaskIdIs"
                    + " ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDepartmentTaskIdIs() == null) {
            conditionToRemove.add("#departmentTaskIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"doctor\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql = sql.replace("#departmentTaskIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#departmentTaskIdIs")) {
                sqlParams.add(qto.getDepartmentTaskIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 功能：获取集采医生任务 */
    @AutoGenerated(locked = false, uuid = "ed387996-62d9-44eb-bf28-bd676f6e5f10-query-all")
    public List<String> query(ListDrugCentralPurchaseDoctorTaskQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_central_purchase_doctor_task.id FROM drug_central_purchase_doctor_task"
                    + " WHERE drug_central_purchase_doctor_task.department_task_id ="
                    + " #departmentTaskIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDepartmentTaskIdIs() == null) {
            conditionToRemove.add("#departmentTaskIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"doctor\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql = sql.replace("#departmentTaskIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#departmentTaskIdIs")) {
                sqlParams.add(qto.getDepartmentTaskIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_central_purchase_doctor_task.created_at desc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
