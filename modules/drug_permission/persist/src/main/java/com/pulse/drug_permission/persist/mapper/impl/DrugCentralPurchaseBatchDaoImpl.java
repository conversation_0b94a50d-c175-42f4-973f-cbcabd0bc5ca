package com.pulse.drug_permission.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.drug_permission.persist.dos.DrugCentralPurchaseBatch;
import com.pulse.drug_permission.persist.mapper.DrugCentralPurchaseBatchDao;
import com.pulse.drug_permission.persist.mapper.mybatis.DrugCentralPurchaseBatchMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "53eeed10-7391-322d-926c-199818c8cf67|ENTITY|DAO")
public class DrugCentralPurchaseBatchDaoImpl implements DrugCentralPurchaseBatchDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseBatchMapper drugCentralPurchaseBatchMapper;

    @AutoGenerated(locked = true, uuid = "3bd378d2-4a0c-3d49-9f87-82b51c49c01c")
    @Override
    public DrugCentralPurchaseBatch getById(String id) {
        QueryWrapper<DrugCentralPurchaseBatch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugCentralPurchaseBatchMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "7ed6261f-a392-3141-a355-e4df1c06f96c")
    @Override
    public List<DrugCentralPurchaseBatch> getByIds(List<String> id) {
        QueryWrapper<DrugCentralPurchaseBatch> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugCentralPurchaseBatchMapper.selectList(queryWrapper);
    }
}
