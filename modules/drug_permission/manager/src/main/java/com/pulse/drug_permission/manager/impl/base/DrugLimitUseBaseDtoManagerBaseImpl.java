package com.pulse.drug_permission.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_permission.manager.DrugLimitUseBaseDtoManager;
import com.pulse.drug_permission.manager.converter.DrugLimitUseBaseDtoConverter;
import com.pulse.drug_permission.manager.dto.DrugLimitUseBaseDto;
import com.pulse.drug_permission.persist.dos.DrugLimitUse;
import com.pulse.drug_permission.persist.mapper.DrugLimitUseDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "887b1d71-91ac-4ad2-bf34-672f1e387c17|DTO|BASE_MANAGER_IMPL")
public abstract class DrugLimitUseBaseDtoManagerBaseImpl implements DrugLimitUseBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugLimitUseBaseDtoConverter drugLimitUseBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugLimitUseDao drugLimitUseDao;

    @AutoGenerated(locked = true, uuid = "013c9c61-04e2-3652-bc62-4d19b6d76cc9")
    @Override
    public List<DrugLimitUseBaseDto> getByDoctorIds(List<String> doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(doctorId)) {
            return Collections.emptyList();
        }

        List<DrugLimitUse> drugLimitUseList = drugLimitUseDao.getByDoctorIds(doctorId);
        if (CollectionUtil.isEmpty(drugLimitUseList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugLimitUseToDrugLimitUseBaseDto(drugLimitUseList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "08441ab9-0c10-3ceb-8032-5548138cd4c6")
    @Override
    public List<DrugLimitUseBaseDto> getByDoctorId(String doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLimitUseBaseDto> drugLimitUseBaseDtoList = getByDoctorIds(Arrays.asList(doctorId));
        return drugLimitUseBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "34869b60-fd05-3907-9e75-cee143ac5fd6")
    @Override
    public List<DrugLimitUseBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugLimitUse> drugLimitUseList = drugLimitUseDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugLimitUseList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugLimitUse> drugLimitUseMap =
                drugLimitUseList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugLimitUseList =
                id.stream()
                        .map(i -> drugLimitUseMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugLimitUseToDrugLimitUseBaseDto(drugLimitUseList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "55196004-707c-3771-a6cf-82076887d41a")
    @Override
    public List<DrugLimitUseBaseDto> getByDrugOriginCodes(List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginCode)) {
            return Collections.emptyList();
        }

        List<DrugLimitUse> drugLimitUseList = drugLimitUseDao.getByDrugOriginCodes(drugOriginCode);
        if (CollectionUtil.isEmpty(drugLimitUseList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugLimitUseToDrugLimitUseBaseDto(drugLimitUseList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9a31f375-d700-303c-9b95-26943a6d17da")
    @Override
    public List<DrugLimitUseBaseDto> getByDrugOriginCode(String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLimitUseBaseDto> drugLimitUseBaseDtoList =
                getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        return drugLimitUseBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d14a1dec-042b-33f2-9a10-e27853d57256")
    public List<DrugLimitUseBaseDto> doConvertFromDrugLimitUseToDrugLimitUseBaseDto(
            List<DrugLimitUse> drugLimitUseList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugLimitUseList)) {
            return Collections.emptyList();
        }

        Map<String, DrugLimitUseBaseDto> dtoMap =
                drugLimitUseBaseDtoConverter
                        .convertFromDrugLimitUseToDrugLimitUseBaseDto(drugLimitUseList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugLimitUseBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugLimitUseBaseDto> drugLimitUseBaseDtoList = new ArrayList<>();
        for (DrugLimitUse i : drugLimitUseList) {
            DrugLimitUseBaseDto drugLimitUseBaseDto = dtoMap.get(i.getId());
            if (drugLimitUseBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugLimitUseBaseDtoList.add(drugLimitUseBaseDto);
        }
        return drugLimitUseBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "f4399c7c-a153-3926-8c64-4c1b2aaaee57")
    @Override
    public DrugLimitUseBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLimitUseBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugLimitUseBaseDto drugLimitUseBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugLimitUseBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
