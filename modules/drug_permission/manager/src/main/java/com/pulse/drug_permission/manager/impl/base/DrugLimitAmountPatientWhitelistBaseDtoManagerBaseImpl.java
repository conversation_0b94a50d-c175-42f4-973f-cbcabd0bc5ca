package com.pulse.drug_permission.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_permission.manager.DrugLimitAmountPatientWhitelistBaseDtoManager;
import com.pulse.drug_permission.manager.converter.DrugLimitAmountPatientWhitelistBaseDtoConverter;
import com.pulse.drug_permission.manager.dto.DrugLimitAmountPatientWhitelistBaseDto;
import com.pulse.drug_permission.persist.dos.DrugLimitAmountPatientWhitelist;
import com.pulse.drug_permission.persist.mapper.DrugLimitAmountPatientWhitelistDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "0ec41cb8-d659-458a-8bb7-d2143884745a|DTO|BASE_MANAGER_IMPL")
public abstract class DrugLimitAmountPatientWhitelistBaseDtoManagerBaseImpl
        implements DrugLimitAmountPatientWhitelistBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugLimitAmountPatientWhitelistBaseDtoConverter
            drugLimitAmountPatientWhitelistBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugLimitAmountPatientWhitelistDao drugLimitAmountPatientWhitelistDao;

    @AutoGenerated(locked = true, uuid = "02b2c528-ca2a-35da-a010-b5d5d8196950")
    public List<DrugLimitAmountPatientWhitelistBaseDto>
            doConvertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                    List<DrugLimitAmountPatientWhitelist> drugLimitAmountPatientWhitelistList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugLimitAmountPatientWhitelistList)) {
            return Collections.emptyList();
        }

        Map<String, DrugLimitAmountPatientWhitelistBaseDto> dtoMap =
                drugLimitAmountPatientWhitelistBaseDtoConverter
                        .convertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                                drugLimitAmountPatientWhitelistList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugLimitAmountPatientWhitelistBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugLimitAmountPatientWhitelistBaseDto> drugLimitAmountPatientWhitelistBaseDtoList =
                new ArrayList<>();
        for (DrugLimitAmountPatientWhitelist i : drugLimitAmountPatientWhitelistList) {
            DrugLimitAmountPatientWhitelistBaseDto drugLimitAmountPatientWhitelistBaseDto =
                    dtoMap.get(i.getId());
            if (drugLimitAmountPatientWhitelistBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugLimitAmountPatientWhitelistBaseDtoList.add(drugLimitAmountPatientWhitelistBaseDto);
        }
        return drugLimitAmountPatientWhitelistBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "8a3ebd57-a9a2-3a4e-9c90-05926b708533")
    @Override
    public List<DrugLimitAmountPatientWhitelistBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugLimitAmountPatientWhitelist> drugLimitAmountPatientWhitelistList =
                drugLimitAmountPatientWhitelistDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugLimitAmountPatientWhitelistList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugLimitAmountPatientWhitelist> drugLimitAmountPatientWhitelistMap =
                drugLimitAmountPatientWhitelistList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugLimitAmountPatientWhitelistList =
                id.stream()
                        .map(i -> drugLimitAmountPatientWhitelistMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                drugLimitAmountPatientWhitelistList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ac9b41c1-7c01-3dc5-9cc1-5fdfb22e656a")
    @Override
    public List<DrugLimitAmountPatientWhitelistBaseDto> getByProductCode(String productCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLimitAmountPatientWhitelistBaseDto> drugLimitAmountPatientWhitelistBaseDtoList =
                getByProductCodes(Arrays.asList(productCode));
        return drugLimitAmountPatientWhitelistBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ead9b2da-f57b-3ad4-8bb2-1a4a01000c00")
    @Override
    public List<DrugLimitAmountPatientWhitelistBaseDto> getByProductCodes(
            List<String> productCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(productCode)) {
            return Collections.emptyList();
        }

        List<DrugLimitAmountPatientWhitelist> drugLimitAmountPatientWhitelistList =
                drugLimitAmountPatientWhitelistDao.getByProductCodes(productCode);
        if (CollectionUtil.isEmpty(drugLimitAmountPatientWhitelistList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                drugLimitAmountPatientWhitelistList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "fe99dcd2-e145-3472-8a61-c6acd1dc4f08")
    @Override
    public DrugLimitAmountPatientWhitelistBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLimitAmountPatientWhitelistBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugLimitAmountPatientWhitelistBaseDto drugLimitAmountPatientWhitelistBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugLimitAmountPatientWhitelistBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
