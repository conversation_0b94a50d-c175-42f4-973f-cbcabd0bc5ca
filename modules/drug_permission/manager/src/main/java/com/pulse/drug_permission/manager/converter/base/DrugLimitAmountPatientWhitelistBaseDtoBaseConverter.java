package com.pulse.drug_permission.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_permission.manager.dto.DrugLimitAmountPatientWhitelistBaseDto;
import com.pulse.drug_permission.persist.dos.DrugLimitAmountPatientWhitelist;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "0ec41cb8-d659-458a-8bb7-d2143884745a|DTO|BASE_CONVERTER")
public class DrugLimitAmountPatientWhitelistBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugLimitAmountPatientWhitelistBaseDto
            convertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                    DrugLimitAmountPatientWhitelist drugLimitAmountPatientWhitelist) {
        return convertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                        List.of(drugLimitAmountPatientWhitelist))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugLimitAmountPatientWhitelistBaseDto>
            convertFromDrugLimitAmountPatientWhitelistToDrugLimitAmountPatientWhitelistBaseDto(
                    List<DrugLimitAmountPatientWhitelist> drugLimitAmountPatientWhitelistList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugLimitAmountPatientWhitelistList)) {
            return new ArrayList<>();
        }
        List<DrugLimitAmountPatientWhitelistBaseDto> drugLimitAmountPatientWhitelistBaseDtoList =
                new ArrayList<>();
        for (DrugLimitAmountPatientWhitelist drugLimitAmountPatientWhitelist :
                drugLimitAmountPatientWhitelistList) {
            if (drugLimitAmountPatientWhitelist == null) {
                continue;
            }
            DrugLimitAmountPatientWhitelistBaseDto drugLimitAmountPatientWhitelistBaseDto =
                    new DrugLimitAmountPatientWhitelistBaseDto();
            drugLimitAmountPatientWhitelistBaseDto.setId(drugLimitAmountPatientWhitelist.getId());
            drugLimitAmountPatientWhitelistBaseDto.setProductCode(
                    drugLimitAmountPatientWhitelist.getProductCode());
            drugLimitAmountPatientWhitelistBaseDto.setPatientId(
                    drugLimitAmountPatientWhitelist.getPatientId());
            drugLimitAmountPatientWhitelistBaseDto.setUpdatedBy(
                    drugLimitAmountPatientWhitelist.getUpdatedBy());
            drugLimitAmountPatientWhitelistBaseDto.setCreatedBy(
                    drugLimitAmountPatientWhitelist.getCreatedBy());
            drugLimitAmountPatientWhitelistBaseDto.setCreatedAt(
                    drugLimitAmountPatientWhitelist.getCreatedAt());
            drugLimitAmountPatientWhitelistBaseDto.setUpdatedAt(
                    drugLimitAmountPatientWhitelist.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugLimitAmountPatientWhitelistBaseDtoList.add(drugLimitAmountPatientWhitelistBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugLimitAmountPatientWhitelistBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
