package com.pulse.drug_permission.manager.bo.base;

import com.pulse.drug_permission.manager.bo.DrugCentralPurchaseBatchBO;
import com.pulse.drug_permission.manager.bo.DrugCentralPurchaseBatchProductDetailBO;
import com.pulse.drug_permission.persist.dos.DrugCentralPurchaseBatch;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_central_purchase_batch")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "1e429bc9-833d-3787-b38e-e2317d2a84cd")
public abstract class BaseDrugCentralPurchaseBatchBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "6a47f909-2716-3c3d-a455-ca2d8f6b1beb")
    private Date createdAt;

    /** 创建人id */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "c1b9a2cf-8d2e-3db8-852c-9f6f0a546d75")
    private String createdBy;

    /** 描述 集采药品批次名称 */
    @Column(name = "description")
    @AutoGenerated(locked = true, uuid = "28d8ac5b-f47d-3180-b8bf-69f1a8b63154")
    private String description;

    @JoinColumn(name = "batch_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugCentralPurchaseBatchProductDetailBO>
            drugCentralPurchaseBatchProductDetailBOSet = new HashSet<>();

    /** 任务结束时间 批次结束时间 */
    @Column(name = "end_date_time")
    @AutoGenerated(locked = true, uuid = "05403998-7c96-3d75-bba1-0d50b2545ada")
    private Date endDateTime;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "93617ce7-104b-32cf-a942-27bb0be2a5ac")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "d8fc286d-00da-4963-a549-f7b7a1154dff")
    @Version
    private Long lockVersion;

    /** 任务开始时间 批次发布时间 */
    @Column(name = "start_date_time")
    @AutoGenerated(locked = true, uuid = "c7b65515-2411-3168-a354-d81746e4c0fc")
    private Date startDateTime;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "386532dd-6fb6-3a5d-82d3-316a80dd76ad")
    private Date updatedAt;

    /** 修改人id */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "b4ba6749-c1b0-33fe-9ffe-8362ea57157d")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatch convertToDrugCentralPurchaseBatch() {
        DrugCentralPurchaseBatch entity = new DrugCentralPurchaseBatch();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "description",
                "startDateTime",
                "endDateTime",
                "createdBy",
                "updatedBy",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static DrugCentralPurchaseBatchBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugCentralPurchaseBatchBO drugCentralPurchaseBatch =
                (DrugCentralPurchaseBatchBO)
                        session.createQuery("from DrugCentralPurchaseBatchBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugCentralPurchaseBatch;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public String getDescription() {
        return this.description;
    }

    @AutoGenerated(locked = true)
    public Set<DrugCentralPurchaseBatchProductDetailBO>
            getDrugCentralPurchaseBatchProductDetailBOSet() {
        return this.drugCentralPurchaseBatchProductDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public Date getEndDateTime() {
        return this.endDateTime;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public Date getStartDateTime() {
        return this.startDateTime;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setDescription(String description) {
        this.description = description;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugCentralPurchaseBatchProductDetailBOSet(
            Set<DrugCentralPurchaseBatchProductDetailBO>
                    drugCentralPurchaseBatchProductDetailBOSet) {
        this.drugCentralPurchaseBatchProductDetailBOSet =
                drugCentralPurchaseBatchProductDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setEndDateTime(Date endDateTime) {
        this.endDateTime = endDateTime;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setId(String id) {
        this.id = id;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setStartDateTime(Date startDateTime) {
        this.startDateTime = startDateTime;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugCentralPurchaseBatchBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseBatchBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (DrugCentralPurchaseBatchBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
