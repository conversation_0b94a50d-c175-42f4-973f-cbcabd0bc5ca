package com.pulse.parameter.service.query;

import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.persist.qto.SearchParameterConfigQto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.pulse.parameter.service.index.entity.SearchParameterConfigQtoService;
import com.pulse.parameter.service.query.assembler.ParameterConfigBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** ParameterConfigBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "2aef63ee-7e6f-32fd-a2da-87b8b4e9db35")
public class ParameterConfigBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoDataAssembler parameterConfigBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SearchParameterConfigQtoService searchParameterConfigQtoService;

    /** 根据SearchParameterConfigQto查询ParameterConfigBaseDto列表,分页 */
    @PublicInterface(id = "a9de2f42-60d3-4ad8-80a1-45f651fe6ec9", module = "parameter")
    @AutoGenerated(locked = false, uuid = "049fbaea-df18-3530-90e0-738a68afc73d")
    public VSQueryResult<ParameterConfigBaseDto> searchParameterConfigPaged(
            @Valid @NotNull SearchParameterConfigQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchParameterConfigQtoService.queryPaged(qto);
        List<ParameterConfigBaseDto> dtoList = toDtoList(ids);
        parameterConfigBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchParameterConfigQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "25f91419-77b8-3907-89b8-e76c9860abd1")
    private List<ParameterConfigBaseDto> toDtoList(List<String> ids) {
        List<ParameterConfigBaseDto> baseDtoList = parameterConfigBaseDtoService.getByIds(ids);
        Map<String, ParameterConfigBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        ParameterConfigBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchParameterConfigQto查询ParameterConfigBaseDto列表,上限500 */
    @PublicInterface(id = "62e2d3e6-02d6-4575-8196-d527862d0e9b", module = "parameter")
    @AutoGenerated(locked = false, uuid = "3341d869-a540-3fb2-8661-09a96d45711f")
    public List<ParameterConfigBaseDto> searchParameterConfig(
            @Valid @NotNull SearchParameterConfigQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchParameterConfigQtoService.query(qto);
        List<ParameterConfigBaseDto> result = toDtoList(ids);
        parameterConfigBaseDtoDataAssembler.assembleData(result);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
