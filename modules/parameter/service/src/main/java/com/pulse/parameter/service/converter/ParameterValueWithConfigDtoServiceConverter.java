package com.pulse.parameter.service.converter;

import com.pulse.parameter.manager.dto.ParameterValueWithConfigDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "5c3f1842-0b45-35c2-8b22-0b640010de29")
public class ParameterValueWithConfigDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ParameterValueWithConfigDto> ParameterValueWithConfigDtoConverter(
            List<ParameterValueWithConfigDto> parameterValueWithConfigDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return parameterValueWithConfigDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
