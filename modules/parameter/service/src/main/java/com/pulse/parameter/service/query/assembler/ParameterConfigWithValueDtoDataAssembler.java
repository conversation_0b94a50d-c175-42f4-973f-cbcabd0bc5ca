package com.pulse.parameter.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ParameterConfigWithValueDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "6d11d0f1-a8c4-3c06-aaa2-f24869394521")
public class ParameterConfigWithValueDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    /** 批量自定义组装ParameterConfigWithValueDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "23e67917-3f0b-3feb-8abe-0e16b4b9c565")
    public void assembleDataCustomized(List<ParameterConfigWithValueDto> dataList) {
        // 自定义数据组装

    }

    /** 组装ParameterConfigWithValueDto数据 */
    @AutoGenerated(locked = true, uuid = "5cbcf0ad-f1bb-3b60-b662-4065253f25ac")
    public void assembleData(
            List<ParameterConfigWithValueDto> dtoList,
            ParameterConfigWithValueDtoDataAssembler.ParameterConfigWithValueDtoDataHolder
                    dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, ParameterConfigBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(
                                Collectors.toMap(
                                        ParameterConfigBaseDto::getId, Function.identity()));

        Map<String, List<ParameterValueBaseDto>> parameterValueList =
                dataHolder.parameterValueList.stream()
                        .collect(Collectors.groupingBy(ParameterValueBaseDto::getConfigId));

        for (ParameterConfigWithValueDto dto : dtoList) {
            dto.setParameterValueList(
                    Optional.ofNullable(parameterValueList.get(dto.getId()))
                            .orElse(new ArrayList<>()));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class ParameterConfigWithValueDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ParameterConfigBaseDto> rootBaseDtoList;

        /** 持有dto字段parameterValueList的Dto数据 */
        @AutoGenerated(locked = true)
        public List<ParameterValueBaseDto> parameterValueList;
    }
}
