package com.pulse.parameter.manager.converter;

import com.pulse.parameter.manager.converter.base.ParameterValueWithConfigDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "6528d574-a410-43c8-bd8b-aa9b802a133a|DTO|CONVERTER")
public class ParameterValueWithConfigDtoConverter
        extends ParameterValueWithConfigDtoBaseConverter {}
