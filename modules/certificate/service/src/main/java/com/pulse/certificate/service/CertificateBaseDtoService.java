package com.pulse.certificate.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.certificate.manager.CertificateBaseDtoManager;
import com.pulse.certificate.manager.dto.CertificateBaseDto;
import com.pulse.certificate.service.converter.CertificateBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "803d280d-7655-47ae-9deb-8d3bfe06769b|DTO|SERVICE")
public class CertificateBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private CertificateBaseDtoManager certificateBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private CertificateBaseDtoServiceConverter certificateBaseDtoServiceConverter;

    @PublicInterface(id = "81f6a590-a66c-4f1c-9241-a758c9ecf98e", module = "certificate")
    @AutoGenerated(locked = false, uuid = "03149f7a-9f2e-37c1-92bf-538453545ecb")
    public List<CertificateBaseDto> getByPatientId(
            @NotNull(message = "患者ID不能为空") String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPatientIds(Arrays.asList(patientId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1cd344a7-b9ba-4db8-a68f-f550fac0c1bb", module = "certificate")
    @AutoGenerated(locked = false, uuid = "249a3723-7887-36e4-b84c-505c15987083")
    public List<CertificateBaseDto> getByOutpVisitIds(
            @Valid @NotNull(message = "门诊就诊ID不能为空") List<String> outpVisitId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        outpVisitId = new ArrayList<>(new HashSet<>(outpVisitId));
        List<CertificateBaseDto> certificateBaseDtoList =
                certificateBaseDtoManager.getByOutpVisitIds(outpVisitId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return certificateBaseDtoServiceConverter.CertificateBaseDtoConverter(
                certificateBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "55a61f23-6e0b-4e9b-bad0-32da47d9181f", module = "certificate")
    @AutoGenerated(locked = false, uuid = "45720ca6-6b21-3fb0-8691-1e39a13ecad3")
    public List<CertificateBaseDto> getByPatientIds(
            @Valid @NotNull(message = "患者ID不能为空") List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        patientId = new ArrayList<>(new HashSet<>(patientId));
        List<CertificateBaseDto> certificateBaseDtoList =
                certificateBaseDtoManager.getByPatientIds(patientId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return certificateBaseDtoServiceConverter.CertificateBaseDtoConverter(
                certificateBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "5fd4368c-f100-49f7-9940-735274e4edc4", module = "certificate")
    @AutoGenerated(locked = false, uuid = "73c969c8-56fe-3f11-b053-efa24db8d445")
    public List<CertificateBaseDto> getByOutpVisitId(
            @NotNull(message = "门诊就诊ID不能为空") String outpVisitId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOutpVisitIds(Arrays.asList(outpVisitId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "2a85957a-e071-4921-9bd1-23b7dd807e59", module = "certificate")
    @AutoGenerated(locked = false, uuid = "7ea0a45f-d533-3f0d-8714-7a87f12d4134")
    public CertificateBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "70f355f9-0b77-4504-8d6d-2e1f80f6d3ec", module = "certificate")
    @AutoGenerated(locked = false, uuid = "f3c22be2-9b8c-3a0c-a45b-ed84ed3b9881")
    public List<CertificateBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<CertificateBaseDto> certificateBaseDtoList = certificateBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return certificateBaseDtoServiceConverter.CertificateBaseDtoConverter(
                certificateBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
