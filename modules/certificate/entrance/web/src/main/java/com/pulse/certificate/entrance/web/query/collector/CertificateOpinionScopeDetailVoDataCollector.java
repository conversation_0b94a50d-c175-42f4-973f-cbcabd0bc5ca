package com.pulse.certificate.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.certificate.entrance.web.converter.CertificateOpinionBaseVoConverter;
import com.pulse.certificate.entrance.web.converter.CertificateOpinionScopeDetailVoConverter;
import com.pulse.certificate.entrance.web.query.assembler.CertificateOpinionScopeDetailVoDataAssembler.CertificateOpinionScopeDetailVoDataHolder;
import com.pulse.certificate.entrance.web.vo.CertificateOpinionBaseVo;
import com.pulse.certificate.manager.dto.CertificateOpinionBaseDto;
import com.pulse.certificate.manager.dto.CertificateOpinionScopeDetailDto;
import com.pulse.certificate.manager.dto.CertificateProcessingOpinionBaseDto;
import com.pulse.certificate.service.CertificateOpinionBaseDtoService;
import com.pulse.certificate.service.CertificateProcessingOpinionBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装CertificateOpinionScopeDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "e173d25d-b344-3c7b-8573-343b1e0fcbd6")
public class CertificateOpinionScopeDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private CertificateOpinionBaseDtoService certificateOpinionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private CertificateOpinionBaseVoConverter certificateOpinionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private CertificateOpinionScopeDetailVoConverter certificateOpinionScopeDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private CertificateOpinionScopeDetailVoDataCollector
            certificateOpinionScopeDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private CertificateProcessingOpinionBaseDtoService certificateProcessingOpinionBaseDtoService;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "d1f62141-ee8c-3cc7-9980-5a8adb1264f0")
    private void fillDataWhenNecessary(CertificateOpinionScopeDetailVoDataHolder dataHolder) {
        List<CertificateProcessingOpinionBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.certificateOpinionList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(CertificateProcessingOpinionBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<CertificateOpinionBaseDto> baseDtoList =
                    certificateOpinionBaseDtoService
                            .getByOpinionScopeIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(CertificateOpinionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<CertificateOpinionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            CertificateOpinionBaseDto::getOpinionScopeId));
            Map<CertificateOpinionBaseDto, CertificateOpinionBaseVo> dtoVoMap =
                    certificateOpinionBaseVoConverter.convertToCertificateOpinionBaseVoMap(
                            baseDtoList);
            Map<CertificateOpinionBaseDto, CertificateOpinionBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.certificateOpinionList =
                    rootDtoList.stream()
                            .map(CertificateProcessingOpinionBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取CertificateOpinionScopeDetailDto数据填充CertificateOpinionScopeDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "d3146033-f355-3cd6-b041-43cef4d80d0a")
    public void collectDataWithDtoData(
            List<CertificateOpinionScopeDetailDto> dtoList,
            CertificateOpinionScopeDetailVoDataHolder dataHolder) {
        List<CertificateOpinionBaseDto> certificateOpinionListList = new ArrayList<>();

        for (CertificateOpinionScopeDetailDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getCertificateOpinionList())) {
                for (CertificateOpinionBaseDto certificateOpinionListDto :
                        rootDto.getCertificateOpinionList()) {
                    certificateOpinionListList.add(certificateOpinionListDto);
                }
            }
        }

        // access certificateOpinionList
        Map<CertificateOpinionBaseDto, CertificateOpinionBaseVo> certificateOpinionListVoMap =
                certificateOpinionBaseVoConverter.convertToCertificateOpinionBaseVoMap(
                        certificateOpinionListList);
        dataHolder.certificateOpinionList =
                certificateOpinionListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> certificateOpinionListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "fc55d68e-e811-30e2-a06c-482eeae61fe3")
    public void collectDataDefault(CertificateOpinionScopeDetailVoDataHolder dataHolder) {
        certificateOpinionScopeDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
