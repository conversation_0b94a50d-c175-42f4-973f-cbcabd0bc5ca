package com.pulse.certificate.persist.mapper;

import com.pulse.certificate.persist.dos.CertificateOpinionScope;
import com.pulse.dictionary_business.common.enums.UseScopeEnum;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "f51d4eb4-a806-4381-83b9-ce6f62b0d2e5|ENTITY|IDAO")
public interface CertificateOpinionScopeDao {

    @AutoGenerated(locked = true, uuid = "15885f7a-9f1b-3630-8f67-8e1d5e20bfc6")
    List<CertificateOpinionScope> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "22b11481-78c7-3ed8-ab2b-46844c6cc892")
    CertificateOpinionScope getById(String id);
}
