package com.pulse.certificate.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "d16b7fe7-87ab-41bc-affb-ebeca116ca5d|DTO|DEFINITION")
public class CertificateOpinionBaseDto {
    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "863c1abf-4c35-4be1-b66e-5e2723bbb315")
    private String createdBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "ac0a82be-ffe3-4538-beea-a87440b8b7e5")
    private String id;

    /** 意见内容 */
    @AutoGenerated(locked = true, uuid = "b77eb06d-fd02-40ad-9f50-6be9cea57353")
    private String opinionContent;

    /** 证明处理意见范围ID */
    @AutoGenerated(locked = true, uuid = "2d486fa5-48d1-4f23-a202-8703c4388e12")
    private String opinionScopeId;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "2183c9cf-9b08-497c-b51c-170b1c21bc88")
    private Long sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "3c0ac573-9ac5-4e9c-90c4-6cc7d91c5c11")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "a15ead94-f26b-4e2f-9f7b-fdff8521d7cb")
    private String updatedBy;
}
