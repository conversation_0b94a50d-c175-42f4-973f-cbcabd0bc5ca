package com.pulse.rule_engine.entrance.web.vo;

import com.pulse.rule_engine.common.enums.RuleExecutionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "32682123-b655-474b-b855-458a17c3923d|VO|DEFINITION")
public class RuleExecutionLogBaseVo {
    /** 业务ID 业务对象 ID，如患者 ID、药品 ID */
    @AutoGenerated(locked = true, uuid = "9f66a69f-17c2-4099-8e99-40f12e78d06f")
    private String businessId;

    /** 业务类型 业务类型，如 "PATIENT"、"DRUG" */
    @AutoGenerated(locked = true, uuid = "d4f8e3a9-fa2a-4fa5-a6e7-4847fdf88c29")
    private String businessType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "8a40134d-8a31-4fc2-9c51-30257f271771")
    private Date createdAt;

    /** 执行人 */
    @AutoGenerated(locked = true, uuid = "21ed894e-c52c-40c2-b65e-70ce703ddb5a")
    private String executedBy;

    /** 执行时长（毫秒数） 执行时长，单位毫秒 */
    @AutoGenerated(locked = true, uuid = "fdeb7954-0874-42a1-b310-839955f8d0ed")
    private Long executionDuration;

    /** 执行状态 执行状态，如"SUCCESS"、"FAIL" */
    @AutoGenerated(locked = true, uuid = "865bf586-349a-44c1-ae6e-de9fd89f9810")
    private RuleExecutionStatusEnum executionStatus;

    /** 执行时间 执行时间 */
    @AutoGenerated(locked = true, uuid = "a0fa10c5-4f01-4092-8ec4-e66c99477150")
    private Date executionTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "ce09f9bf-8ee5-479c-aef1-cd060c112d69")
    private String id;

    /** 输入数据 输入数据，JSON 格式，如 {"drugs": ["莫西沙星"]} */
    @AutoGenerated(locked = true, uuid = "dd14df5e-8060-4d63-9b17-ee29682d99bf")
    private String inputData;

    /** 输出数据 输出数据，JSON 格式，如 {"needApproval": true} */
    @AutoGenerated(locked = true, uuid = "59240acd-c63a-47ed-b179-17f734671d3f")
    private String outputData;

    /** 规则编码 规则编码，便于业务识别 */
    @AutoGenerated(locked = true, uuid = "f7226bcd-591a-451a-8bc1-d993e081fb47")
    private String ruleCode;

    /** 规则ID 规则ID，外键关联规则表 */
    @AutoGenerated(locked = true, uuid = "27f0c84e-cce2-4e0f-9eb0-72c82957b92e")
    private String ruleId;

    /** 触发点 触发点，如 "PRESCRIPTION_SUBMIT" */
    @AutoGenerated(locked = true, uuid = "1d4c7274-0b36-456e-b226-819d05632cad")
    private String triggerPoint;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "35f6ef20-f12c-4fcd-bdde-b03544f1eb69")
    private Date updatedAt;
}
