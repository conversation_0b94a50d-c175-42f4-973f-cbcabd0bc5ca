package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.rule_engine.entrance.web.query.assembler.RuleRefCategorySimpleVoDataAssembler;
import com.pulse.rule_engine.entrance.web.vo.RuleRefCategorySimpleVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleRefCategorySimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "fb486c65-3fe9-4429-a49d-6708d1092319|VO|CONVERTER")
public class RuleRefCategorySimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleRefCategorySimpleVoDataAssembler ruleRefCategorySimpleVoDataAssembler;

    /** 使用默认方式组装RuleRefCategorySimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "0394e3e4-f152-3231-aa28-1d58964412f7")
    public RuleRefCategorySimpleVo convertAndAssembleData(CategoryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleRefCategorySimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "62ffdd99-7d03-3ba7-b10f-83c2719ec6b7")
    public List<RuleRefCategorySimpleVo> convertAndAssembleDataList(List<CategoryBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, RuleRefCategorySimpleVo> voMap =
                convertToRuleRefCategorySimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleRefCategorySimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把CategoryBaseDto转换成RuleRefCategorySimpleVo */
    @AutoGenerated(locked = true, uuid = "860defb7-8386-31a5-bb07-ae4f5f6a9308")
    public RuleRefCategorySimpleVo convertToRuleRefCategorySimpleVo(CategoryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleRefCategorySimpleVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把CategoryBaseDto转换成RuleRefCategorySimpleVo */
    @AutoGenerated(locked = false, uuid = "fb486c65-3fe9-4429-a49d-6708d1092319-converter-Map")
    public Map<CategoryBaseDto, RuleRefCategorySimpleVo> convertToRuleRefCategorySimpleVoMap(
            List<CategoryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<CategoryBaseDto, RuleRefCategorySimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleRefCategorySimpleVo vo =
                                                    new RuleRefCategorySimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setCode(dto.getCode());
                                            vo.setName(dto.getName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把CategoryBaseDto转换成RuleRefCategorySimpleVo */
    @AutoGenerated(locked = true, uuid = "fb486c65-3fe9-4429-a49d-6708d1092319-converter-list")
    public List<RuleRefCategorySimpleVo> convertToRuleRefCategorySimpleVoList(
            List<CategoryBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleRefCategorySimpleVoMap(dtoList).values());
    }
}
