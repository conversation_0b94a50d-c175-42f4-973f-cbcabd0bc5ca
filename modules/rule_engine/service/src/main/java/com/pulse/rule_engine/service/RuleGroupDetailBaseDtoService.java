package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleGroupDetailBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.service.converter.RuleGroupDetailBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "4b738980-2d24-4fc1-b751-924770d01dbb|DTO|SERVICE")
public class RuleGroupDetailBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupDetailBaseDtoManager ruleGroupDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupDetailBaseDtoServiceConverter ruleGroupDetailBaseDtoServiceConverter;

    @PublicInterface(id = "85a501ae-d85d-400f-933c-368a169b3dbc", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "1f645d31-9ac4-383f-9c76-49ba59d43e57")
    public List<RuleGroupDetailBaseDto> getByRuleIds(
            @Valid @NotNull(message = "规则ID不能为空") List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        ruleId = new ArrayList<>(new HashSet<>(ruleId));
        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList =
                ruleGroupDetailBaseDtoManager.getByRuleIds(ruleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleGroupDetailBaseDtoServiceConverter.RuleGroupDetailBaseDtoConverter(
                ruleGroupDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "1bef18ba-d322-44a4-988c-3b4e71433330", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "3758fc1c-e295-3104-860a-bffa39fcfd0e")
    public List<RuleGroupDetailBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList =
                ruleGroupDetailBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleGroupDetailBaseDtoServiceConverter.RuleGroupDetailBaseDtoConverter(
                ruleGroupDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "070da436-c592-4ff6-949b-b2bbd404664b", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "3ab6547f-396c-3366-a1da-d83f87b6823b")
    public List<RuleGroupDetailBaseDto> getByGroupIds(
            @Valid @NotNull(message = "分组ID不能为空") List<String> groupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        groupId = new ArrayList<>(new HashSet<>(groupId));
        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList =
                ruleGroupDetailBaseDtoManager.getByGroupIds(groupId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleGroupDetailBaseDtoServiceConverter.RuleGroupDetailBaseDtoConverter(
                ruleGroupDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "59184382-3a3e-486d-9822-7ca51ef91267", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "6e4832d5-cf45-304b-a715-bf6ca6ea9ef7")
    public RuleGroupDetailBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "a3f9559f-1289-4bda-8150-419d2eefb0bd", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "7797ab31-36db-3f1a-b776-db8d3e58d2e7")
    public List<RuleGroupDetailBaseDto> getByGroupId(
            @NotNull(message = "分组ID不能为空") String groupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByGroupIds(Arrays.asList(groupId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1731a04f-ce50-4096-97ef-fdb462b0c55f", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "ae6ef7db-9ea1-3bce-8030-81fb024c7ad7")
    public List<RuleGroupDetailBaseDto> getByRuleId(@NotNull(message = "规则ID不能为空") String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleIds(Arrays.asList(ruleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
