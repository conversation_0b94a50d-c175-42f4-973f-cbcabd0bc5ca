package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "1b26add8-38b8-3e9f-8136-5949c97c4455")
public class RuleVersionBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleVersionBaseDto> RuleVersionBaseDtoConverter(
            List<RuleVersionBaseDto> ruleVersionBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleVersionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
