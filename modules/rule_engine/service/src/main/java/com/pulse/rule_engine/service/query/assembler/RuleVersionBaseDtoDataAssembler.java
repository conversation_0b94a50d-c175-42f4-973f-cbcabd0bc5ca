package com.pulse.rule_engine.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** RuleVersionBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "ceacea57-231e-3f40-a705-47fd9a7b429a")
public class RuleVersionBaseDtoDataAssembler {

    /** 组装RuleVersionBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "4625816c-72a7-395a-b6d4-a642c32cd134")
    public void assembleData(List<RuleVersionBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装RuleVersionBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a158cef0-4bf3-3116-97f4-9b61047c707d")
    public void assembleDataCustomized(List<RuleVersionBaseDto> dataList) {
        // 自定义数据组装

    }
}
