package com.pulse.rule_engine.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.rule_engine.persist.mapper.ListRuleVersionQtoDao;
import com.pulse.rule_engine.persist.qto.ListRuleVersionQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204|QTO|SERVICE")
public class ListRuleVersionQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleVersionQtoDao listRuleVersionMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204-query-paged")
    public List<String> queryPaged(ListRuleVersionQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listRuleVersionMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListRuleVersionQto qto) {
        return listRuleVersionMapper.count(qto);
    }
}
