package com.pulse.rule_engine.service;

import com.pulse.rule_engine.service.base.BaseRuleGroupBOService;
import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|SERVICE")
public class RuleGroupBOService extends BaseRuleGroupBOService {}
