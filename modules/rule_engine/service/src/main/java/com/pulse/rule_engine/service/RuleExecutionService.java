package com.pulse.rule_engine.service;

import com.pulse.rule_engine.manager.RuleVersionBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.persist.eo.RuleVersionEo;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapter;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapterFactory;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** Auto generated Service */
@Slf4j
@Controller
@Validated
@AutoGenerated(locked = false, uuid = "9ec75136-5fa2-3923-8f8d-4f434d2c8b75")
public class RuleExecutionService {

    @Autowired private RuleEngineAdapterFactory ruleEngineAdapterFactory;

    @Autowired private RuleVersionBaseDtoManager ruleVersionBaseDtoManager;

    /** 检查多个规则是否存在逻辑冲突：检查传入的规则版本列表的逻辑冲突，如只有规则ID，则取该规则最新的规则版本 */
    @PublicInterface(
            id = "285ec1c8-9a55-4e48-90b5-a19eea9d7cab",
            module = "rule_engine",
            moduleId = "e03ca7ad-ee08-468d-9c8a-1b7d14950192",
            version = "1748916606529",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "285ec1c8-9a55-4e48-90b5-a19eea9d7cab")
    public Boolean checkRuleConflict(
            @Valid @NotNull List<RuleVersionEo> ruleVersionList, @NotNull String businessData) {
        if (CollectionUtils.isEmpty(ruleVersionList) || !StringUtils.hasText(businessData)) {
            log.warn("规则版本列表为空或业务数据为空，无法进行冲突检查");
            return false;
        }

        try {
            log.info("开始检查规则冲突，规则版本数量: {}", ruleVersionList.size());

            // 获取完整的规则版本信息
            List<RuleVersionBaseDto> completeRuleVersionList =
                    getCompleteRuleVersionList(ruleVersionList);

            if (CollectionUtils.isEmpty(completeRuleVersionList)) {
                log.warn("未找到有效的规则版本信息");
                return false;
            }

            // 获取规则引擎适配器
            RuleEngineAdapter ruleEngineAdapter = ruleEngineAdapterFactory.getDefaultAdapter();

            // 执行冲突检查，传入业务数据
            Boolean hasConflict =
                    ruleEngineAdapter.checkRuleConflict(completeRuleVersionList, businessData);

            log.info("规则冲突检查完成，结果: {}", hasConflict ? "存在冲突" : "无冲突");
            return hasConflict;

        } catch (Exception e) {
            log.error("规则冲突检查失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则冲突检查失败: " + e.getMessage());
        }
    }

    /** 根据业务数据执行指定版本的规则：规则版本ID/版本号可选，如非空则使用指定版本的规则，如空则使用当前最新版的规则； */
    @PublicInterface(
            id = "3446b3f8-36f2-4288-b7e4-ce62e60a942a",
            module = "rule_engine",
            moduleId = "e03ca7ad-ee08-468d-9c8a-1b7d14950192",
            version = "1748916606529",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "3446b3f8-36f2-4288-b7e4-ce62e60a942a")
    public Boolean executeRule(
            @Valid @NotNull List<RuleVersionEo> ruleVersionList, @NotNull String businessData) {
        if (CollectionUtils.isEmpty(ruleVersionList) || !StringUtils.hasText(businessData)) {
            log.warn("规则版本列表为空或业务数据为空，无法执行规则");
            return false;
        }

        try {
            log.info("开始执行规则，规则版本数量: {}", ruleVersionList.size());

            // 获取完整的规则版本信息
            List<RuleVersionBaseDto> completeRuleVersionList =
                    getCompleteRuleVersionList(ruleVersionList);

            if (CollectionUtils.isEmpty(completeRuleVersionList)) {
                log.warn("未找到有效的规则版本信息");
                return false;
            }

            // 获取规则引擎适配器
            RuleEngineAdapter ruleEngineAdapter = ruleEngineAdapterFactory.getDefaultAdapter();

            // 执行规则
            Boolean result = ruleEngineAdapter.executeRule(completeRuleVersionList, businessData);

            log.info("规则执行完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("规则执行失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取完整的规则版本信息 如果只有规则ID，则获取该规则的最新版本
     *
     * @param ruleVersionList 规则版本EO列表
     * @return 完整的规则版本DTO列表
     */
    private List<RuleVersionBaseDto> getCompleteRuleVersionList(
            List<RuleVersionEo> ruleVersionList) {
        List<RuleVersionBaseDto> result = new ArrayList<>();

        for (RuleVersionEo ruleVersionEo : ruleVersionList) {
            RuleVersionBaseDto ruleVersionDto = null;

            // 如果有规则版本ID，直接根据ID查询
            if (StringUtils.hasText(ruleVersionEo.getRuleVersionId())) {
                ruleVersionDto =
                        ruleVersionBaseDtoManager.getById(ruleVersionEo.getRuleVersionId());
                if (ruleVersionDto != null) {
                    log.debug("根据规则版本ID获取规则版本: {}", ruleVersionEo.getRuleVersionId());
                }
            }

            // 如果没有规则版本ID，但有规则ID和版本号，根据规则ID和版本号查询
            if (ruleVersionDto == null
                    && StringUtils.hasText(ruleVersionEo.getRuleId())
                    && StringUtils.hasText(ruleVersionEo.getVersionNumber())) {
                RuleVersion.RuleIdAndVersionNumber query = new RuleVersion.RuleIdAndVersionNumber();
                query.setRuleId(ruleVersionEo.getRuleId());
                query.setVersionNumber(ruleVersionEo.getVersionNumber());

                ruleVersionDto = ruleVersionBaseDtoManager.getByRuleIdAndVersionNumber(query);
                if (ruleVersionDto != null) {
                    log.debug(
                            "根据规则ID和版本号获取规则版本: {} - {}",
                            ruleVersionEo.getRuleId(),
                            ruleVersionEo.getVersionNumber());
                }
            }

            // 如果只有规则ID，获取该规则的最新版本
            if (ruleVersionDto == null && StringUtils.hasText(ruleVersionEo.getRuleId())) {
                List<RuleVersionBaseDto> ruleVersions =
                        ruleVersionBaseDtoManager.getByRuleId(ruleVersionEo.getRuleId());
                if (!CollectionUtils.isEmpty(ruleVersions)) {
                    // 获取最新版本（假设按创建时间排序，取最后一个）
                    ruleVersionDto = ruleVersions.get(ruleVersions.size() - 1);
                    log.debug("根据规则ID获取最新规则版本: {}", ruleVersionEo.getRuleId());
                }
            }

            if (ruleVersionDto != null) {
                result.add(ruleVersionDto);
            } else {
                log.warn(
                        "未找到规则版本信息: ruleId={}, ruleVersionId={}, versionNumber={}",
                        ruleVersionEo.getRuleId(),
                        ruleVersionEo.getRuleVersionId(),
                        ruleVersionEo.getVersionNumber());
            }
        }

        return result;
    }
}
