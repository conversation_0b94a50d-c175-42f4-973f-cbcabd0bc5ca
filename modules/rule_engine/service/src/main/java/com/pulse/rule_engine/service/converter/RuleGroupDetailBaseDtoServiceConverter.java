package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "ca16d97c-90ea-3b7b-8c15-cee55b3ae19e")
public class RuleGroupDetailBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleGroupDetailBaseDto> RuleGroupDetailBaseDtoConverter(
            List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleGroupDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
