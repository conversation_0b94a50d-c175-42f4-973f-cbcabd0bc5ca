package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "894b9438-16dc-3c1a-b84e-54cbb75d8edf")
public class RuleGroupBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleGroupBaseDto> RuleGroupBaseDtoConverter(
            List<RuleGroupBaseDto> ruleGroupBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleGroupBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
