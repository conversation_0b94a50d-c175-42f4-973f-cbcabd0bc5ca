package com.pulse.rule_engine.service;

import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/** 模块领域服务，负责模块的BoService的包装 */
@Service
@Slf4j
@AutoGenerated(locked = false, uuid = "e03ca7ad-ee08-468d-9c8a-1b7d14950192")
public class RuleEngineDomainService {
    @AutoGenerated(locked = true)
    @Resource
    private RuleBOService ruleBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogBOService ruleExecutionLogBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBOService ruleGroupBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleTestDataBOService ruleTestDataBOService;
}
