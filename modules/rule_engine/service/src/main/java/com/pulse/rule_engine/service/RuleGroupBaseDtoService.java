package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleGroupBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.service.converter.RuleGroupBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "aa51881e-7090-432a-b4a4-846278819a34|DTO|SERVICE")
public class RuleGroupBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupBaseDtoManager ruleGroupBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupBaseDtoServiceConverter ruleGroupBaseDtoServiceConverter;

    @PublicInterface(id = "f5896b34-a4ce-4d13-8c0c-18c054519526", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "0ffa1cea-2ca2-34af-9a34-0d83714c5b33")
    public RuleGroupBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "67d08ffd-87fe-45df-b96a-7ddbb37dbf37", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "cade111f-159f-3056-b055-e583df421508")
    public List<RuleGroupBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleGroupBaseDto> ruleGroupBaseDtoList = ruleGroupBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleGroupBaseDtoServiceConverter.RuleGroupBaseDtoConverter(ruleGroupBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
