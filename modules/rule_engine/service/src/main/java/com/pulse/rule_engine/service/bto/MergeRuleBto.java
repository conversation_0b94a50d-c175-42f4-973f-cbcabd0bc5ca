package com.pulse.rule_engine.service.bto;

import com.pulse.rule_engine.common.enums.RuleOrgRelationTypeEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.common.enums.RuleVersionChangeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Rule
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "6166d994-64ba-4fcd-a88b-265827f89f67|BTO|DEFINITION")
public class MergeRuleBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 规则编码 规则编码，英文唯一标识，如 "ANTIBIOTIC_APPROVAL" */
    @AutoGenerated(locked = true, uuid = "186a1d34-826d-4662-b45d-2bd398280ade")
    private String code;

    /** 创建人 创建人ID，外键关联用户表 */
    @AutoGenerated(locked = true, uuid = "c0a3964d-b874-494c-a4df-3b5d07467d0a")
    private String createdBy;

    /** 当前生效版本 当前生效版本 */
    @AutoGenerated(locked = true, uuid = "81b58a57-a6f5-4900-b1f5-d85fb96ac014")
    private String currentVersion;

    /** 描述 描述 */
    @AutoGenerated(locked = true, uuid = "52342b8c-2310-48f3-ba0c-d7152c9aa8e9")
    private String description;

    /** 显示名称 显示名称，如 "抗生素分级使用审批规则" */
    @AutoGenerated(locked = true, uuid = "881a7efe-a6e6-493f-b65e-79bbef7ab627")
    private String displayName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "41ae4d1a-3569-4580-817f-5999dde515bb")
    private String id;

    /**
     * 规则分类ID 规则分类ID,关联分类表ID。用于分类规则。 - HIS 系统需适应医疗政策和业务变化，规则类型可能频繁调整，枚举扩展性不足。 -
     * 相比码表，分类表更专注于规则类型管理，避免通用码表的复杂性，维护成本更可控。 - 支持动态扩展和业务友好性，符合以人为本的设计原则。
     */
    @AutoGenerated(locked = true, uuid = "0f4f1f12-6a44-4d5c-ba49-7359cbf20c9e")
    private String ruleCategoryId;

    @Valid
    @AutoGenerated(locked = true, uuid = "d59f2743-8d95-4d32-a079-6a4b5dbf60e7")
    private List<MergeRuleBto.RuleOrganizationBto> ruleOrganizationBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "85d46083-d42f-4e30-bb16-63976e156b54")
    private List<MergeRuleBto.RuleVersionBto> ruleVersionBtoList;

    /** 规则的版本变更类型 */
    @AutoGenerated(locked = true, uuid = "ed27ee2f-96fd-4000-b1d9-8a679efd341e")
    private RuleVersionChangeTypeEnum ruleVersionChangeType;

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "2edc90c3-abf6-4313-8c8e-3a6fdcf54899")
    private RuleStatusEnum status;

    /** 更新人 更新人 */
    @AutoGenerated(locked = true, uuid = "574b67aa-3e82-43be-9abc-84c99812da1a")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setCurrentVersion(String currentVersion) {
        this.__$validPropertySet.add("currentVersion");
        this.currentVersion = currentVersion;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setDisplayName(String displayName) {
        this.__$validPropertySet.add("displayName");
        this.displayName = displayName;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setRuleCategoryId(String ruleCategoryId) {
        this.__$validPropertySet.add("ruleCategoryId");
        this.ruleCategoryId = ruleCategoryId;
    }

    @AutoGenerated(locked = true)
    public void setRuleOrganizationBtoList(
            List<MergeRuleBto.RuleOrganizationBto> ruleOrganizationBtoList) {
        this.__$validPropertySet.add("ruleOrganizationBtoList");
        this.ruleOrganizationBtoList = ruleOrganizationBtoList;
    }

    @AutoGenerated(locked = true)
    public void setRuleVersionBtoList(List<MergeRuleBto.RuleVersionBto> ruleVersionBtoList) {
        this.__$validPropertySet.add("ruleVersionBtoList");
        this.ruleVersionBtoList = ruleVersionBtoList;
    }

    @AutoGenerated(locked = true)
    public void setRuleVersionChangeType(RuleVersionChangeTypeEnum ruleVersionChangeType) {
        this.__$validPropertySet.add("ruleVersionChangeType");
        this.ruleVersionChangeType = ruleVersionChangeType;
    }

    @AutoGenerated(locked = true)
    public void setStatus(RuleStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    /**
     * <b>[源自]</b> RuleVersion
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_IGNORE
     */
    @Getter
    @NoArgsConstructor
    public static class RuleVersionBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "01cd481d-1fb9-4d70-aec1-acdfae2a3107")
        private String id;

        /**
         * 版本号 版本号,如 "2.1.3"。 - HIS 系统中的规则版本需反映变化的性质（如新功能、政策调整），时间戳和自增ID缺乏可读性。 -
         * 语义化版本便于开发、运维和业务人员理解版本演进，支持版本回滚和兼容性判断。
         */
        @AutoGenerated(locked = true, uuid = "6aace3c5-1ce8-406d-8135-c7a892ba0194")
        private String versionNumber;

        /** 决策规则内容 DRL 规则内容，如 "rule '抗生素特殊使用'..." */
        @AutoGenerated(locked = true, uuid = "65b53576-b7c7-4129-9897-4fd8d3fbc0a4")
        private String drlContent;

        /** 生效开始时间 生效开始时间 */
        @AutoGenerated(locked = true, uuid = "25318447-4f32-4392-ac12-a9c248609885")
        private Date effectiveStartTime;

        /** 有效结束时间 生效结束时间，可为空表示一直生效 */
        @AutoGenerated(locked = true, uuid = "4e4d1296-4d67-4e65-9142-35c2e108faed")
        private Date effectiveEndTime;

        /** 审批人ID 审批人ID，外键关联用户表 */
        @AutoGenerated(locked = true, uuid = "78c6da32-f147-41d8-800c-2803b8dfc96c")
        private String approverId;

        /** 创建人 创建人ID，关联用户表 */
        @AutoGenerated(locked = true, uuid = "bc7a74f7-1205-4b05-83f4-a782c4ef9eca")
        private String createdBy;

        /** 更新人 更新人ID，关联用户表 */
        @AutoGenerated(locked = true, uuid = "cff74674-d713-4ac3-92a2-6ed781ec5a1d")
        private String updatedBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setVersionNumber(String versionNumber) {
            this.__$validPropertySet.add("versionNumber");
            this.versionNumber = versionNumber;
        }

        @AutoGenerated(locked = true)
        public void setDrlContent(String drlContent) {
            this.__$validPropertySet.add("drlContent");
            this.drlContent = drlContent;
        }

        @AutoGenerated(locked = true)
        public void setEffectiveStartTime(Date effectiveStartTime) {
            this.__$validPropertySet.add("effectiveStartTime");
            this.effectiveStartTime = effectiveStartTime;
        }

        @AutoGenerated(locked = true)
        public void setEffectiveEndTime(Date effectiveEndTime) {
            this.__$validPropertySet.add("effectiveEndTime");
            this.effectiveEndTime = effectiveEndTime;
        }

        @AutoGenerated(locked = true)
        public void setApproverId(String approverId) {
            this.__$validPropertySet.add("approverId");
            this.approverId = approverId;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }
    }

    /**
     * <b>[源自]</b> RuleOrganization
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class RuleOrganizationBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "fe6ddc67-8b65-4687-a1fc-758a05562f21")
        private String id;

        /** 组织ID 组织ID */
        @AutoGenerated(locked = true, uuid = "ac73c9b6-395f-464b-b398-af62d232ac78")
        private String organizationId;

        /** 关系类型 关系类型 */
        @AutoGenerated(locked = true, uuid = "dca895b4-7311-48de-90c3-2da3a28e1bfe")
        private RuleOrgRelationTypeEnum relationType;

        /** 备注 说明具体关系或备注 */
        @AutoGenerated(locked = true, uuid = "dbb26344-a6e1-4807-bf21-6eda664d7451")
        private String remark;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setRelationType(RuleOrgRelationTypeEnum relationType) {
            this.__$validPropertySet.add("relationType");
            this.relationType = relationType;
        }

        @AutoGenerated(locked = true)
        public void setRemark(String remark) {
            this.__$validPropertySet.add("remark");
            this.remark = remark;
        }
    }
}
