package com.pulse.rule_engine.service.base;

import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "21d67aa3-cdd0-34e0-8d31-07c34f0ebe2e")
public class BaseRuleGroupBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;
}
