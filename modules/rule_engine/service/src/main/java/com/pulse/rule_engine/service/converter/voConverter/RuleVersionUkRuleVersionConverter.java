package com.pulse.rule_engine.service.converter.voConverter;

import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.persist.dos.RuleVersion.RuleIdAndVersionNumber;
import com.pulse.rule_engine.persist.eo.UkRuleVersionEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "5261a0e9-ee63-3722-b0d4-97d298ad8ed2")
public class RuleVersionUkRuleVersionConverter {

    @AutoGenerated(locked = true)
    public static RuleVersion.RuleIdAndVersionNumber convertFromUkRuleVersionToInner(
            UkRuleVersionEo ukRuleVersion) {
        if (null == ukRuleVersion) {
            return null;
        }

        RuleIdAndVersionNumber ruleIdAndVersionNumber = new RuleIdAndVersionNumber();
        ruleIdAndVersionNumber.setRuleId(ukRuleVersion.getRuleId());
        ruleIdAndVersionNumber.setVersionNumber(ukRuleVersion.getVersionNumber());
        return ruleIdAndVersionNumber;
    }
}
