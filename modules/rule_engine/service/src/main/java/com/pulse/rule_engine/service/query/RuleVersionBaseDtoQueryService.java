package com.pulse.rule_engine.service.query;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.qto.ListRuleVersionQto;
import com.pulse.rule_engine.service.RuleVersionBaseDtoService;
import com.pulse.rule_engine.service.index.entity.ListRuleVersionQtoService;
import com.pulse.rule_engine.service.query.assembler.RuleVersionBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** RuleVersionBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "ff0df2c4-78e9-3e59-892a-75149669452e")
public class RuleVersionBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleVersionQtoService listRuleVersionQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleVersionBaseDtoDataAssembler ruleVersionBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleVersionBaseDtoService ruleVersionBaseDtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "62c32906-24e3-363e-a78a-49cba34499de")
    private List<RuleVersionBaseDto> toDtoList(List<String> ids) {
        List<RuleVersionBaseDto> baseDtoList = ruleVersionBaseDtoService.getByIds(ids);
        Map<String, RuleVersionBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(Collectors.toMap(RuleVersionBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListRuleVersionQto查询RuleVersionBaseDto列表,分页 */
    @PublicInterface(id = "c05f716e-1eaf-4927-b514-cf2c0d726ccb", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "d9097440-a093-3b9e-a88a-32e86f264e21")
    public VSQueryResult<RuleVersionBaseDto> listRuleVersionPaged(
            @Valid @NotNull ListRuleVersionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listRuleVersionQtoService.queryPaged(qto);
        List<RuleVersionBaseDto> dtoList = toDtoList(ids);
        ruleVersionBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listRuleVersionQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
