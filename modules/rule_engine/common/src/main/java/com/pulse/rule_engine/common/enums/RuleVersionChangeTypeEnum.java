package com.pulse.rule_engine.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "6fcf9fab-83e1-40e4-9909-0f6c4fb8af8b|ENUM|DEFINITION")
public enum RuleVersionChangeTypeEnum {

    /** 主版本号 用于重大变更或不兼容更新 */
    MAJOR(),

    /** 次版本号 用于添加新功能或小规模变更 */
    MINOR(),

    /** 修订号 用于bug修复或小调整 */
    PATCH();

    @AutoGenerated(locked = true)
    RuleVersionChangeTypeEnum() {}
}
