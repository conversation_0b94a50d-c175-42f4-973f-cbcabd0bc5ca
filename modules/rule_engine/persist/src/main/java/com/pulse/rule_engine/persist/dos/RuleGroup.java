package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule_group", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "84064161-cec6-4cb6-b280-aba4c92d5bda|ENTITY|DEFINITION")
public class RuleGroup {
    @AutoGenerated(locked = true, uuid = "ddd6debe-06a7-5206-b1d3-22f4da17593a")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "676bcd1e-8b44-4953-9e81-2e862a73e4f6")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "8c098c91-d5e8-4ea6-b854-bd09e4fe44ae")
    @TableField(value = "description")
    private String description;

    @AutoGenerated(locked = true, uuid = "64e9781c-3c03-481a-a4c3-b3e975dd81fc")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "11517a1d-a350-4afe-a24c-0dee4be4306b")
    @TableField(value = "lock_version")
    private Long lockVersion;

    /** 逻辑运算符（AND、OR等） */
    @AutoGenerated(locked = true, uuid = "b8853cd1-0141-458d-84a3-cd743a3699ea")
    @TableField(value = "logic_operator")
    private String logicOperator;

    @AutoGenerated(locked = true, uuid = "22ef17e6-c389-460e-b6b4-68cbaa857d56")
    @TableField(value = "name")
    private String name;

    /** 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "43b1de93-3377-43b6-a2c7-87fbfc8b972f")
    @TableField(value = "status")
    private RuleStatusEnum status;

    @AutoGenerated(locked = true, uuid = "f9abd6de-0d1b-5e7a-9d20-679fa721b1f8")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "3d79254f-35d9-4d01-b45f-8ca82d954262")
    @TableField(value = "updated_by")
    private String updatedBy;
}
