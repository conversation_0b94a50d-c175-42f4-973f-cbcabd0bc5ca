package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "31e7474e-d551-4130-a705-8d03825be286|ENTITY|DEFINITION")
public class Rule {
    /** 规则编码，英文唯一标识，如 "ANTIBIOTIC_APPROVAL" */
    @AutoGenerated(locked = true, uuid = "306f245a-e010-49f1-ba6e-65542ef9a9b3")
    @TableField(value = "code")
    private String code;

    @AutoGenerated(locked = true, uuid = "3d7e4c20-dc78-5005-9d39-af945da19a3d")
    @TableField(value = "created_at")
    private Date createdAt;

    /** 创建人ID，外键关联用户表 */
    @AutoGenerated(locked = true, uuid = "91e67375-ab26-4ceb-b70f-************")
    @TableField(value = "created_by")
    private String createdBy;

    /** 当前生效版本 */
    @AutoGenerated(locked = true, uuid = "d00c2de9-374b-4f22-acfa-4d4d33537bad")
    @TableField(value = "current_version")
    private String currentVersion;

    @AutoGenerated(locked = true, uuid = "39559144-bb97-588e-a5bf-4df7b23c73f9")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "975e4776-60b2-425d-bc86-d842e10cd24e")
    @TableField(value = "description")
    private String description;

    /** 显示名称，如 "抗生素分级使用审批规则" */
    @AutoGenerated(locked = true, uuid = "044d8b4b-4b96-4f09-8de4-a2a7afe425a7")
    @TableField(value = "display_name")
    private String displayName;

    @AutoGenerated(locked = true, uuid = "eb813806-5fed-416e-a479-7f17b6672eec")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "922beba0-3d74-42d6-a7b4-6991463e6fe4")
    @TableField(value = "lock_version")
    private Long lockVersion;

    /**
     * 规则分类ID,关联分类表ID。用于分类规则。 - HIS 系统需适应医疗政策和业务变化，规则类型可能频繁调整，枚举扩展性不足。 -
     * 相比码表，分类表更专注于规则类型管理，避免通用码表的复杂性，维护成本更可控。 - 支持动态扩展和业务友好性，符合以人为本的设计原则。
     */
    @AutoGenerated(locked = true, uuid = "9e1fb853-e7ba-4a3f-8f76-632972a5d8b5")
    @TableField(value = "rule_category_id")
    private String ruleCategoryId;

    /** 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "bd91e480-ae55-4a8a-9f10-fc4b50f00105")
    @TableField(value = "status")
    private RuleStatusEnum status;

    @AutoGenerated(locked = true, uuid = "f4c08c62-2b5d-531d-9ae9-5b26da431c2e")
    @TableField(value = "updated_at")
    private Date updatedAt;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "2e13aea7-5d6f-4797-add9-3f6c1c883399")
    @TableField(value = "updated_by")
    private String updatedBy;
}
