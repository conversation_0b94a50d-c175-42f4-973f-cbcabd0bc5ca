package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.pulse.rule_engine.persist.mapper.RuleGroupDetailDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleGroupDetailMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "c56b7cc6-731f-4629-a774-************|ENTITY|DAO")
public class RuleGroupDetailDaoImpl implements RuleGroupDetailDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailMapper ruleGroupDetailMapper;

    @AutoGenerated(locked = true, uuid = "00f1cf7b-5d76-3848-a1c0-35ad61ece74e")
    @Override
    public List<RuleGroupDetail> getByRuleId(String ruleId) {
        QueryWrapper<RuleGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_id", ruleId).orderByAsc("id");
        return ruleGroupDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a008b4a0-cf61-3c39-a7c6-a217eec476e2")
    @Override
    public RuleGroupDetail getById(String id) {
        QueryWrapper<RuleGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return ruleGroupDetailMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b27792eb-2325-3b28-b949-85f1af45d60a")
    @Override
    public List<RuleGroupDetail> getByRuleIds(List<String> ruleId) {
        QueryWrapper<RuleGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_id", ruleId).orderByAsc("id");
        return ruleGroupDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d578a5f0-503f-3acb-b1d0-d51b64f91472")
    @Override
    public List<RuleGroupDetail> getByIds(List<String> id) {
        QueryWrapper<RuleGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return ruleGroupDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f10a88d8-c5f4-3a1c-9eb6-78d5d9a8e262")
    @Override
    public List<RuleGroupDetail> getByGroupId(String groupId) {
        QueryWrapper<RuleGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", groupId).orderByAsc("id");
        return ruleGroupDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "fa6cf6dc-6540-3dc6-9c4f-db3c38812d3d")
    @Override
    public List<RuleGroupDetail> getByGroupIds(List<String> groupId) {
        QueryWrapper<RuleGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("group_id", groupId).orderByAsc("id");
        return ruleGroupDetailMapper.selectList(queryWrapper);
    }
}
