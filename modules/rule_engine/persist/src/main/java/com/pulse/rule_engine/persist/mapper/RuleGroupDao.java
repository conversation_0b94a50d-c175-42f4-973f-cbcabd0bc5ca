package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "84064161-cec6-4cb6-b280-aba4c92d5bda|ENTITY|IDAO")
public interface RuleGroupDao {

    @AutoGenerated(locked = true, uuid = "248890df-3776-36f1-895d-6794ab911386")
    List<RuleGroup> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "e48bf86a-b6a2-391a-b272-ff2d7ca94ef8")
    RuleGroup getById(String id);
}
