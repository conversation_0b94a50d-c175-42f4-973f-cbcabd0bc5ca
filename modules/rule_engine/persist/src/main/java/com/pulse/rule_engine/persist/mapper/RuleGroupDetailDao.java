package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "c56b7cc6-731f-4629-a774-************|ENTITY|IDAO")
public interface RuleGroupDetailDao {

    @AutoGenerated(locked = true, uuid = "00f1cf7b-5d76-3848-a1c0-35ad61ece74e")
    List<RuleGroupDetail> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "a008b4a0-cf61-3c39-a7c6-a217eec476e2")
    RuleGroupDetail getById(String id);

    @AutoGenerated(locked = true, uuid = "b27792eb-2325-3b28-b949-85f1af45d60a")
    List<RuleGroupDetail> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "d578a5f0-503f-3acb-b1d0-d51b64f91472")
    List<RuleGroupDetail> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "f10a88d8-c5f4-3a1c-9eb6-78d5d9a8e262")
    List<RuleGroupDetail> getByGroupId(String groupId);

    @AutoGenerated(locked = true, uuid = "fa6cf6dc-6540-3dc6-9c4f-db3c38812d3d")
    List<RuleGroupDetail> getByGroupIds(List<String> groupId);
}
