package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule_group_detail", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "c56b7cc6-731f-4629-a774-************|ENTITY|DEFINITION")
public class RuleGroupDetail {
    @AutoGenerated(locked = true, uuid = "e1c3c052-0981-5b04-9d70-d8ce012b325a")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "a4cca469-aa28-4a63-b56f-5e417fe6e7ec")
    @TableField(value = "group_id")
    private String groupId;

    @AutoGenerated(locked = true, uuid = "b86a0c3a-acb3-4bb6-a771-bf148d58531a")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "9fe13ad6-3ec2-49e3-bdd9-da3f7dd7e8df")
    @TableField(value = "rule_id")
    private String ruleId;

    @AutoGenerated(locked = true, uuid = "db7e477c-ded0-5211-8800-6dd423418f99")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
