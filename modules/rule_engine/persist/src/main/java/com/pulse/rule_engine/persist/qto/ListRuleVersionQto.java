package com.pulse.rule_engine.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204|QTO|DEFINITION")
public class ListRuleVersionQto {
    /** 有效结束时间 rule_version.effective_end_time */
    @AutoGenerated(locked = true, uuid = "bdcad917-cc0f-4b64-bddc-cc81dc7b8f30")
    private Date effectiveEndTimeLessThanEqual;

    /** 生效开始时间 rule_version.effective_start_time */
    @AutoGenerated(locked = true, uuid = "6c585979-3489-4518-a6e6-b2ba5c93fde8")
    private Date effectiveStartTimeBiggerThanEqual;

    @AutoGenerated(locked = true, uuid = "2bf9e487-c55a-43a1-afe6-931fa765b327")
    private Integer from;

    /** 规则ID rule_version.rule_id */
    @AutoGenerated(locked = true, uuid = "b4c44c77-97d2-4fce-8383-9d0ec1159d08")
    private String ruleIdIs;

    @AutoGenerated(locked = true, uuid = "d7598811-c395-4b54-821e-e01893ad94cf")
    private Integer size;
}
