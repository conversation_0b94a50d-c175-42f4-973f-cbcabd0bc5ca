package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.qto.ListRuleVersionQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204|QTO|DAO")
public class ListRuleVersionQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询规则版本列表 */
    @AutoGenerated(locked = false, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204-count")
    public Integer count(ListRuleVersionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(rule_version.id) FROM rule_version WHERE rule_version.rule_id ="
                    + " #ruleIdIs AND rule_version.effective_start_time >="
                    + " #effectiveStartTimeBiggerThanEqual AND rule_version.effective_end_time <="
                    + " #effectiveEndTimeLessThanEqual ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getEffectiveStartTimeBiggerThanEqual() == null) {
            conditionToRemove.add("#effectiveStartTimeBiggerThanEqual");
        }
        if (qto.getEffectiveEndTimeLessThanEqual() == null) {
            conditionToRemove.add("#effectiveEndTimeLessThanEqual");
        }
        if (qto.getRuleIdIs() == null) {
            conditionToRemove.add("#ruleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule_version");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#effectiveStartTimeBiggerThanEqual", "?")
                        .replace("#effectiveEndTimeLessThanEqual", "?")
                        .replace("#ruleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#effectiveStartTimeBiggerThanEqual")) {
                sqlParams.add(qto.getEffectiveStartTimeBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#effectiveEndTimeLessThanEqual")) {
                sqlParams.add(qto.getEffectiveEndTimeLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#ruleIdIs")) {
                sqlParams.add(qto.getRuleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则版本列表 */
    @AutoGenerated(locked = false, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204-query-all")
    public List<String> query(ListRuleVersionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule_version.id FROM rule_version WHERE rule_version.rule_id = #ruleIdIs"
                    + " AND rule_version.effective_start_time >= #effectiveStartTimeBiggerThanEqual"
                    + " AND rule_version.effective_end_time <= #effectiveEndTimeLessThanEqual ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getEffectiveStartTimeBiggerThanEqual() == null) {
            conditionToRemove.add("#effectiveStartTimeBiggerThanEqual");
        }
        if (qto.getEffectiveEndTimeLessThanEqual() == null) {
            conditionToRemove.add("#effectiveEndTimeLessThanEqual");
        }
        if (qto.getRuleIdIs() == null) {
            conditionToRemove.add("#ruleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule_version");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#effectiveStartTimeBiggerThanEqual", "?")
                        .replace("#effectiveEndTimeLessThanEqual", "?")
                        .replace("#ruleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#effectiveStartTimeBiggerThanEqual")) {
                sqlParams.add(qto.getEffectiveStartTimeBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#effectiveEndTimeLessThanEqual")) {
                sqlParams.add(qto.getEffectiveEndTimeLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#ruleIdIs")) {
                sqlParams.add(qto.getRuleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule_version.version_number desc , rule_version.created_at desc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则版本列表 */
    @AutoGenerated(locked = false, uuid = "5368f7d2-05bd-44c9-86f5-40bec9125204-query-paginate")
    public List<String> queryPaged(ListRuleVersionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule_version.id FROM rule_version WHERE rule_version.rule_id = #ruleIdIs"
                    + " AND rule_version.effective_start_time >= #effectiveStartTimeBiggerThanEqual"
                    + " AND rule_version.effective_end_time <= #effectiveEndTimeLessThanEqual ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getEffectiveStartTimeBiggerThanEqual() == null) {
            conditionToRemove.add("#effectiveStartTimeBiggerThanEqual");
        }
        if (qto.getEffectiveEndTimeLessThanEqual() == null) {
            conditionToRemove.add("#effectiveEndTimeLessThanEqual");
        }
        if (qto.getRuleIdIs() == null) {
            conditionToRemove.add("#ruleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule_version");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#effectiveStartTimeBiggerThanEqual", "?")
                        .replace("#effectiveEndTimeLessThanEqual", "?")
                        .replace("#ruleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#effectiveStartTimeBiggerThanEqual")) {
                sqlParams.add(qto.getEffectiveStartTimeBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#effectiveEndTimeLessThanEqual")) {
                sqlParams.add(qto.getEffectiveEndTimeLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#ruleIdIs")) {
                sqlParams.add(qto.getRuleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule_version.version_number desc , rule_version.created_at desc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
