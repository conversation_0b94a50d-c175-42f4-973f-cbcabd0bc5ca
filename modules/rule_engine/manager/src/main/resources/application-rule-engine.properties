# 规则引擎配置
# 规则引擎类型，支持：drools、easy-rules等
rule.engine.type=drools

# 是否启用规则缓存
rule.engine.enable-cache=true

# 缓存过期时间（秒）
rule.engine.cache-expire-seconds=3600

# 规则执行超时时间（毫秒）
rule.engine.execution-timeout-ms=30000

# 是否启用规则执行日志
rule.engine.enable-execution-log=true

# Drools相关配置
# 是否启用增量编译
rule.engine.drools.enable-incremental-compilation=true

# 是否启用并行执行
rule.engine.drools.enable-parallel-execution=false

# 最大并行线程数
rule.engine.drools.max-parallel-threads=4

# 是否启用调试模式
rule.engine.drools.enable-debug-mode=false
