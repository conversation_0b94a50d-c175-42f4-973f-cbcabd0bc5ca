package com.pulse.rule_engine.manager.adapter;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;

import java.util.List;

/** 规则引擎适配器接口 提供统一的规则引擎操作接口，支持通过配置切换不同的规则引擎框架 */
public interface RuleEngineAdapter {

    /**
     * 检查多个规则版本是否存在逻辑冲突
     *
     * @param ruleVersionList 规则版本列表
     * @param businessData 业务数据（JSON格式），用于冲突检测的上下文
     * @return true表示存在冲突，false表示无冲突
     */
    Boolean checkRuleConflict(List<RuleVersionBaseDto> ruleVersionList, String businessData);

    /**
     * 根据业务数据执行指定版本的规则
     *
     * @param ruleVersionList 规则版本列表
     * @param businessData 业务数据（JSON格式）
     * @return 执行结果
     */
    Boolean executeRule(List<RuleVersionBaseDto> ruleVersionList, String businessData);

    /**
     * 验证规则语法是否正确
     *
     * @param drlContent DRL规则内容
     * @return true表示语法正确，false表示语法错误
     */
    Boolean validateRuleSyntax(String drlContent);

    /**
     * 获取适配器类型
     *
     * @return 适配器类型名称
     */
    String getAdapterType();
}
