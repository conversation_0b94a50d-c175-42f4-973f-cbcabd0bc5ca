package com.pulse.rule_engine.manager.facade.dictionary_basic;

import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.rule_engine.manager.facade.dictionary_basic.base.CategoryBaseDtoServiceInRuleEngineBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "e03ca7ad-ee08-468d-9c8a-1b7d14950192")
@AutoGenerated(locked = false, uuid = "d8774f76-48c4-3f28-9c59-76ca22682b1e")
public class CategoryBaseDtoServiceInRuleEngineRpcAdapter
        extends CategoryBaseDtoServiceInRuleEngineBaseRpcAdapter {

    @RpcRefer(id = "2a8a6595-c8cc-46e9-9e73-8455235dd586", version = "1744263535722")
    @AutoGenerated(locked = false, uuid = "2a8a6595-c8cc-46e9-9e73-8455235dd586|RPC|ADAPTER")
    public List<CategoryBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
