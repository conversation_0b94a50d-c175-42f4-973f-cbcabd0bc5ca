package com.pulse.rule_engine.manager.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/** 规则引擎配置类 支持通过配置文件切换不同的规则引擎框架 */
@Data
@Component
@ConfigurationProperties(prefix = "rule.engine")
public class RuleEngineConfig {

    /** 规则引擎类型，支持：drools、easy-rules等 默认使用drools */
    private String type = "drools";

    /** 是否启用规则缓存 */
    private Boolean enableCache = true;

    /** 缓存过期时间（秒） */
    private Integer cacheExpireSeconds = 3600;

    /** 规则执行超时时间（毫秒） */
    private Integer executionTimeoutMs = 30000;

    /** 是否启用规则执行日志 */
    private Boolean enableExecutionLog = true;

    /** Drools相关配置 */
    private DroolsConfig drools = new DroolsConfig();

    @Data
    public static class DroolsConfig {
        /** 是否启用增量编译 */
        private Boolean enableIncrementalCompilation = true;

        /** 是否启用并行执行 */
        private Boolean enableParallelExecution = false;

        /** 最大并行线程数 */
        private Integer maxParallelThreads = 4;

        /** 是否启用调试模式 */
        private Boolean enableDebugMode = false;
    }
}
