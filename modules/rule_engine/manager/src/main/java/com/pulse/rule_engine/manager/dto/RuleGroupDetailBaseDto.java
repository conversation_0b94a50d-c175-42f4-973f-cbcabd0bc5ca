package com.pulse.rule_engine.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "4b738980-2d24-4fc1-b751-924770d01dbb|DTO|DEFINITION")
public class RuleGroupDetailBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "aa085471-7567-46e6-9053-68e177be8330")
    private Date createdAt;

    /** 分组ID */
    @AutoGenerated(locked = true, uuid = "b6cf3718-0e6c-4e2f-94ac-e377c221a5de")
    private String groupId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "8fcd3ad8-6e10-4886-92b8-559b54e36039")
    private String id;

    /** 规则ID */
    @AutoGenerated(locked = true, uuid = "56ce5d1b-dbf9-4f54-a899-12ea14b1d199")
    private String ruleId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "6c2628a3-74f7-40a3-aca4-8563c1ed7269")
    private Date updatedAt;
}
