package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "aa51881e-7090-432a-b4a4-846278819a34|DTO|MANAGER")
public interface RuleGroupBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "b213d62a-4667-3162-80a0-6c2b1f09166a")
    RuleGroupBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "cdd6d4a3-dc9a-3ba4-a8c6-1b68c8157e89")
    List<RuleGroupBaseDto> getByIds(List<String> id);
}
