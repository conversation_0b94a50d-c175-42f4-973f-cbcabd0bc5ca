package com.pulse.rule_engine.manager.bo;

import cn.hutool.core.util.StrUtil;

import com.pulse.pulse.common.utils.SemanticVersionUtils;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapter;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapterFactory;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE rule_version  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ?")
@Getter
@Setter
@Slf4j
@Table(name = "rule_version")
@Entity
@AutoGenerated(locked = true, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|DEFINITION")
public class RuleVersionBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 规则引擎适配器工厂，用于获取规则引擎适配器进行DSL语法校验 */
    @Transient @Autowired private RuleEngineAdapterFactory ruleEngineAdapterFactory;

    /** 审批人ID 审批人ID，外键关联用户表 */
    @Column(name = "approver_id")
    @AutoGenerated(locked = true, uuid = "2116874c-2ce1-452c-bba3-cc44929b54fc")
    private String approverId;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "c66c0a24-09f7-5479-91c9-e318f85efa87")
    private Date createdAt;

    /** 创建人 创建人ID，关联用户表 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "65a19061-fc93-4461-a4b6-3b331b0c9db9")
    private String createdBy;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "728c1346-4675-551b-8a9e-c599097e5d6a")
    private Long deletedAt = 0L;

    /** 决策规则内容 DRL 规则内容，如 "rule '抗生素特殊使用'..." */
    @Column(name = "drl_content")
    @AutoGenerated(locked = true, uuid = "311af77a-ed86-4504-9e3b-9833677977b1")
    private String drlContent;

    /** 有效结束时间 生效结束时间，可为空表示一直生效 */
    @Column(name = "effective_end_time")
    @AutoGenerated(locked = true, uuid = "ac909e52-f578-4233-8ec1-4f81b472e12f")
    private Date effectiveEndTime;

    /** 生效开始时间 生效开始时间 */
    @Column(name = "effective_start_time")
    @AutoGenerated(locked = true, uuid = "604d4977-d9f2-4926-b184-45fead26dd86")
    private Date effectiveStartTime;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "c7a2e6e9-3d77-4bfb-896d-30a2df4909e3")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    @ManyToOne
    @JoinColumn(name = "rule_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private RuleBO ruleBO;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "c4d5cf4c-f8ad-57b9-b064-93acc38b8941")
    private Date updatedAt;

    /** 更新人 更新人ID，关联用户表 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "edc3e01d-8e42-4497-acf7-b69dcc98a7ae")
    private String updatedBy;

    /**
     * 版本号 版本号,如 "2.1.3"。 - HIS 系统中的规则版本需反映变化的性质（如新功能、政策调整），时间戳和自增ID缺乏可读性。 -
     * 语义化版本便于开发、运维和业务人员理解版本演进，支持版本回滚和兼容性判断。
     */
    @Column(name = "version_number")
    @AutoGenerated(locked = true, uuid = "800fe213-fdd4-4ea8-87fa-b1d62b32b27b")
    private String versionNumber;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "964940a5-694a-4c15-87a2-b422cc65fafe|BO|VALIDATOR")
    @Override
    public void validate() {
        log.debug("开始校验规则版本BO数据，版本号: {}", this.versionNumber);

        // 校验版本号格式（仅在版本号不为空时校验）
        validateVersionNumber();

        // 校验DRL内容语法（仅在DRL内容不为空时校验）
        validateDrlContent();

        // 校验生效时间逻辑（仅在时间字段不为空时校验）
        validateEffectiveTime();

        log.debug("规则版本BO数据校验完成");
    }

    @AutoGenerated(locked = true)
    public RuleVersion convertToRuleVersion() {
        RuleVersion entity = new RuleVersion();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "versionNumber",
                "drlContent",
                "effectiveStartTime",
                "effectiveEndTime",
                "approverId",
                "createdBy",
                "updatedBy",
                "createdAt",
                "updatedAt",
                "deletedAt");
        RuleBO ruleBO = this.getRuleBO();
        entity.setRuleId(ruleBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getApproverId() {
        return this.approverId;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDrlContent() {
        return this.drlContent;
    }

    @AutoGenerated(locked = true)
    public Date getEffectiveEndTime() {
        return this.effectiveEndTime;
    }

    @AutoGenerated(locked = true)
    public Date getEffectiveStartTime() {
        return this.effectiveStartTime;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public RuleBO getRuleBO() {
        return this.ruleBO;
    }

    @AutoGenerated(locked = true)
    public String getRuleId() {
        return this.getRuleBO().getId();
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public String getVersionNumber() {
        return this.versionNumber;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setApproverId(String approverId) {
        this.approverId = approverId;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setDrlContent(String drlContent) {
        this.drlContent = drlContent;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setEffectiveEndTime(Date effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setEffectiveStartTime(Date effectiveStartTime) {
        this.effectiveStartTime = effectiveStartTime;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setId(String id) {
        this.id = id;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setRuleBO(RuleBO ruleBO) {
        this.ruleBO = ruleBO;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
        return (RuleVersionBO) this;
    }

    /**
     * 校验版本号格式
     *
     * <p>仅在版本号不为空时进行校验，使用语义化版本工具类验证格式是否正确
     */
    private void validateVersionNumber() {
        if (StrUtil.isBlank(this.versionNumber)) {
            log.debug("版本号为空，跳过版本号格式校验");
            return;
        }

        try {
            // 使用语义化版本工具类验证版本号格式
            boolean isValid = SemanticVersionUtils.isValidVersion(this.versionNumber);
            if (!isValid) {
                String errorMessage = "版本号格式不正确，应符合语义化版本规范（如：1.0.0）: " + this.versionNumber;
                log.warn(errorMessage);
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
            }
            log.debug("版本号格式校验通过: {}", this.versionNumber);
        } catch (Exception e) {
            if (e instanceof IgnoredException) {
                throw e;
            }
            String errorMessage = "版本号格式校验失败: " + this.versionNumber + ", 错误信息: " + e.getMessage();
            log.error(errorMessage, e);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
    }

    /**
     * 校验DRL内容语法
     *
     * <p>仅在DRL内容不为空时进行校验，使用规则引擎适配器验证语法是否正确
     */
    private void validateDrlContent() {
        if (StrUtil.isBlank(this.drlContent)) {
            log.debug("DRL内容为空，跳过DRL语法校验");
            return;
        }

        try {
            // 获取规则引擎适配器进行语法校验
            if (ruleEngineAdapterFactory != null) {
                RuleEngineAdapter adapter = ruleEngineAdapterFactory.getDefaultAdapter();
                boolean isValid = adapter.validateRuleSyntax(this.drlContent);
                if (!isValid) {
                    String errorMessage = "DRL规则内容语法错误，请检查规则语法";
                    log.warn(errorMessage);
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                }
                log.debug("DRL内容语法校验通过");
            } else {
                log.warn("规则引擎适配器工厂为空，跳过DRL语法校验");
            }
        } catch (Exception e) {
            if (e instanceof IgnoredException) {
                throw e;
            }
            String errorMessage = "DRL内容语法校验失败: " + e.getMessage();
            log.error(errorMessage, e);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
    }

    /**
     * 校验生效时间逻辑
     *
     * <p>仅在时间字段不为空时进行校验，包括以下校验规则：
     *
     * <ul>
     *   <li>生效开始时间不能晚于结束时间
     *   <li>生效结束时间不能早于当前时间（避免创建已过期的规则版本）
     *   <li>生效开始时间不能过早（避免创建过于久远的历史规则版本）
     * </ul>
     */
    private void validateEffectiveTime() {
        // 如果开始时间和结束时间都为空，跳过校验
        if (this.effectiveStartTime == null && this.effectiveEndTime == null) {
            log.debug("生效时间字段均为空，跳过生效时间校验");
            return;
        }

        Date currentTime = new Date();

        // 校验生效开始时间（仅在不为空时校验）
        if (this.effectiveStartTime != null) {
            validateEffectiveStartTime(currentTime);
        }

        // 校验生效结束时间（仅在不为空时校验）
        if (this.effectiveEndTime != null) {
            validateEffectiveEndTime(currentTime);
        }

        // 校验开始时间和结束时间的逻辑关系（仅在两者都不为空时校验）
        if (this.effectiveStartTime != null && this.effectiveEndTime != null) {
            validateTimeRange();
        }

        log.debug("生效时间校验通过: 开始时间={}, 结束时间={}", this.effectiveStartTime, this.effectiveEndTime);
    }

    /**
     * 校验生效开始时间
     *
     * <p>确保开始时间不会过于久远，避免创建无意义的历史规则版本
     *
     * @param currentTime 当前时间
     */
    private void validateEffectiveStartTime(Date currentTime) {
        // 允许开始时间为过去时间，但不能超过1年前（避免创建过于久远的历史规则版本）
        long oneYearAgo = currentTime.getTime() - (365L * 24 * 60 * 60 * 1000);
        Date oneYearAgoDate = new Date(oneYearAgo);

        if (this.effectiveStartTime.before(oneYearAgoDate)) {
            String errorMessage = "生效开始时间不能早于一年前，请检查时间设置";
            log.warn("生效开始时间校验失败: 开始时间={}, 一年前时间={}", this.effectiveStartTime, oneYearAgoDate);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        log.debug("生效开始时间校验通过: {}", this.effectiveStartTime);
    }

    /**
     * 校验生效结束时间
     *
     * <p>确保结束时间不早于当前时间，避免创建已过期的规则版本
     *
     * @param currentTime 当前时间
     */
    private void validateEffectiveEndTime(Date currentTime) {
        // 结束时间不能早于当前时间（避免创建已过期的规则版本）
        if (this.effectiveEndTime.before(currentTime)) {
            String errorMessage = "生效结束时间不能早于当前时间，无法创建已过期的规则版本";
            log.warn("生效结束时间校验失败: 结束时间={}, 当前时间={}", this.effectiveEndTime, currentTime);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        log.debug("生效结束时间校验通过: {}", this.effectiveEndTime);
    }

    /**
     * 校验时间范围逻辑
     *
     * <p>确保开始时间不晚于结束时间
     */
    private void validateTimeRange() {
        if (this.effectiveStartTime.after(this.effectiveEndTime)) {
            String errorMessage = "生效开始时间不能晚于生效结束时间";
            log.warn("时间范围校验失败: 开始时间={}, 结束时间={}", this.effectiveStartTime, this.effectiveEndTime);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        log.debug("时间范围校验通过: 开始时间={}, 结束时间={}", this.effectiveStartTime, this.effectiveEndTime);
    }
}
