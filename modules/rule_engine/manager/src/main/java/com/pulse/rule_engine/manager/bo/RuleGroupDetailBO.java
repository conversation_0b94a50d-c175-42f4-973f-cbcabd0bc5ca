package com.pulse.rule_engine.manager.bo;

import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "rule_group_detail")
@Entity
@AutoGenerated(locked = true, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|DEFINITION")
public class RuleGroupDetailBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "e1c3c052-0981-5b04-9d70-d8ce012b325a")
    private Date createdAt;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "b86a0c3a-acb3-4bb6-a771-bf148d58531a")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    @ManyToOne
    @JoinColumn(name = "group_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private RuleGroupBO ruleGroupBO;

    /** 规则ID */
    @Column(name = "rule_id")
    @AutoGenerated(locked = true, uuid = "9fe13ad6-3ec2-49e3-bdd9-da3f7dd7e8df")
    private String ruleId;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "db7e477c-ded0-5211-8800-6dd423418f99")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "6fa3fcff-80ec-434f-ae89-6d4c48169b6f|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public RuleGroupDetail convertToRuleGroupDetail() {
        RuleGroupDetail entity = new RuleGroupDetail();
        BoUtil.copyProperties(this, entity, "id", "ruleId", "createdAt", "updatedAt");
        RuleGroupBO ruleGroupBO = this.getRuleGroupBO();
        entity.setGroupId(ruleGroupBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getGroupId() {
        return this.getRuleGroupBO().getId();
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO getRuleGroupBO() {
        return this.ruleGroupBO;
    }

    @AutoGenerated(locked = true)
    public String getRuleId() {
        return this.ruleId;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public RuleGroupDetailBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleGroupDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupDetailBO setId(String id) {
        this.id = id;
        return (RuleGroupDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupDetailBO setRuleGroupBO(RuleGroupBO ruleGroupBO) {
        this.ruleGroupBO = ruleGroupBO;
        return (RuleGroupDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupDetailBO setRuleId(String ruleId) {
        this.ruleId = ruleId;
        return (RuleGroupDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupDetailBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleGroupDetailBO) this;
    }
}
