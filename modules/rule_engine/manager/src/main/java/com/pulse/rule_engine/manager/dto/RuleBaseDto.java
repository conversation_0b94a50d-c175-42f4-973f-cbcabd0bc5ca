package com.pulse.rule_engine.manager.dto;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "a3f4913a-8dff-4672-8a3c-467df7fd4f38|DTO|DEFINITION")
public class RuleBaseDto {
    /** 规则编码 规则编码，英文唯一标识，如 "ANTIBIOTIC_APPROVAL" */
    @AutoGenerated(locked = true, uuid = "0c2f0919-706b-409a-890e-d0755e8ee941")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "a279d225-c34b-45e5-b20b-8e91b66698a2")
    private Date createdAt;

    /** 创建人 创建人ID，外键关联用户表 */
    @AutoGenerated(locked = true, uuid = "bb6decf0-3f1b-4345-a238-2e74d7735edc")
    private String createdBy;

    /** 当前生效版本 当前生效版本 */
    @AutoGenerated(locked = true, uuid = "87767bc5-4f00-41b7-bbdf-d6b4152331cc")
    private String currentVersion;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "eaf8d7b5-33c4-4905-b19d-dcd229a081b3")
    private Long deletedAt;

    /** 描述 描述 */
    @AutoGenerated(locked = true, uuid = "00fe7b5b-09bf-4700-8346-e115e0da7889")
    private String description;

    /** 显示名称 显示名称，如 "抗生素分级使用审批规则" */
    @AutoGenerated(locked = true, uuid = "ab55504c-2acf-4aef-88dd-e98f9532c692")
    private String displayName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "eb7d5ac2-6351-4fb0-b268-beccddfb48d5")
    private String id;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "384a1146-a38c-458c-af16-a47d90611906")
    private Long lockVersion;

    /**
     * 规则分类ID 规则分类ID,关联分类表ID。用于分类规则。 - HIS 系统需适应医疗政策和业务变化，规则类型可能频繁调整，枚举扩展性不足。 -
     * 相比码表，分类表更专注于规则类型管理，避免通用码表的复杂性，维护成本更可控。 - 支持动态扩展和业务友好性，符合以人为本的设计原则。
     */
    @AutoGenerated(locked = true, uuid = "e4732343-8f32-4d74-b6ad-707956c888e8")
    private String ruleCategoryId;

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "3b86f992-687a-47fb-94ff-42ae91629910")
    private RuleStatusEnum status;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "35a90351-5b03-47bd-8543-66603c014697")
    private Date updatedAt;

    /** 更新人 更新人 */
    @AutoGenerated(locked = true, uuid = "46e99af9-3067-4fb4-a7c9-b77f0cff3614")
    private String updatedBy;
}
