package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleGroupDetailBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleGroupDetailBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.pulse.rule_engine.persist.mapper.RuleGroupDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "4b738980-2d24-4fc1-b751-924770d01dbb|DTO|BASE_MANAGER_IMPL")
public abstract class RuleGroupDetailBaseDtoManagerBaseImpl
        implements RuleGroupDetailBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupDetailBaseDtoConverter ruleGroupDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupDetailDao ruleGroupDetailDao;

    @AutoGenerated(locked = true, uuid = "159ab296-f4c4-3e83-bc13-4c5b1e04cd09")
    @Override
    public List<RuleGroupDetailBaseDto> getByGroupIds(List<String> groupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(groupId)) {
            return Collections.emptyList();
        }

        List<RuleGroupDetail> ruleGroupDetailList = ruleGroupDetailDao.getByGroupIds(groupId);
        if (CollectionUtil.isEmpty(ruleGroupDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleGroupDetailToRuleGroupDetailBaseDto(ruleGroupDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "39925b9a-4b90-3565-82d7-758b3f481ee9")
    @Override
    public List<RuleGroupDetailBaseDto> getByRuleIds(List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleId)) {
            return Collections.emptyList();
        }

        List<RuleGroupDetail> ruleGroupDetailList = ruleGroupDetailDao.getByRuleIds(ruleId);
        if (CollectionUtil.isEmpty(ruleGroupDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleGroupDetailToRuleGroupDetailBaseDto(ruleGroupDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "644235f7-14c2-3771-8438-24bd135cae00")
    @Override
    public List<RuleGroupDetailBaseDto> getByGroupId(String groupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList =
                getByGroupIds(Arrays.asList(groupId));
        return ruleGroupDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a68f58a4-c6b1-389a-953c-edf11c2aec2d")
    @Override
    public List<RuleGroupDetailBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RuleGroupDetail> ruleGroupDetailList = ruleGroupDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleGroupDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RuleGroupDetail> ruleGroupDetailMap =
                ruleGroupDetailList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleGroupDetailList =
                id.stream()
                        .map(i -> ruleGroupDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleGroupDetailToRuleGroupDetailBaseDto(ruleGroupDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c195631c-849b-3c06-a3c0-55f5544355cc")
    public List<RuleGroupDetailBaseDto> doConvertFromRuleGroupDetailToRuleGroupDetailBaseDto(
            List<RuleGroupDetail> ruleGroupDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleGroupDetailList)) {
            return Collections.emptyList();
        }

        Map<String, RuleGroupDetailBaseDto> dtoMap =
                ruleGroupDetailBaseDtoConverter
                        .convertFromRuleGroupDetailToRuleGroupDetailBaseDto(ruleGroupDetailList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RuleGroupDetailBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList = new ArrayList<>();
        for (RuleGroupDetail i : ruleGroupDetailList) {
            RuleGroupDetailBaseDto ruleGroupDetailBaseDto = dtoMap.get(i.getId());
            if (ruleGroupDetailBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleGroupDetailBaseDtoList.add(ruleGroupDetailBaseDto);
        }
        return ruleGroupDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "cc8048eb-62ef-3c7f-a3a1-7f1d8f539bcc")
    @Override
    public List<RuleGroupDetailBaseDto> getByRuleId(String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList =
                getByRuleIds(Arrays.asList(ruleId));
        return ruleGroupDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ec4ce3cc-d2dd-3664-83d7-70583e4a12a4")
    @Override
    public RuleGroupDetailBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleGroupDetailBaseDto ruleGroupDetailBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleGroupDetailBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
