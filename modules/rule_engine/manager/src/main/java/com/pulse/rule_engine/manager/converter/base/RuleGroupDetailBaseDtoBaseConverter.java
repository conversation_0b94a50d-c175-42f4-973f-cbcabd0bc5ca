package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "4b738980-2d24-4fc1-b751-924770d01dbb|DTO|BASE_CONVERTER")
public class RuleGroupDetailBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleGroupDetailBaseDto convertFromRuleGroupDetailToRuleGroupDetailBaseDto(
            RuleGroupDetail ruleGroupDetail) {
        return convertFromRuleGroupDetailToRuleGroupDetailBaseDto(List.of(ruleGroupDetail)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleGroupDetailBaseDto> convertFromRuleGroupDetailToRuleGroupDetailBaseDto(
            List<RuleGroupDetail> ruleGroupDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleGroupDetailList)) {
            return new ArrayList<>();
        }
        List<RuleGroupDetailBaseDto> ruleGroupDetailBaseDtoList = new ArrayList<>();
        for (RuleGroupDetail ruleGroupDetail : ruleGroupDetailList) {
            if (ruleGroupDetail == null) {
                continue;
            }
            RuleGroupDetailBaseDto ruleGroupDetailBaseDto = new RuleGroupDetailBaseDto();
            ruleGroupDetailBaseDto.setId(ruleGroupDetail.getId());
            ruleGroupDetailBaseDto.setGroupId(ruleGroupDetail.getGroupId());
            ruleGroupDetailBaseDto.setRuleId(ruleGroupDetail.getRuleId());
            ruleGroupDetailBaseDto.setCreatedAt(ruleGroupDetail.getCreatedAt());
            ruleGroupDetailBaseDto.setUpdatedAt(ruleGroupDetail.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleGroupDetailBaseDtoList.add(ruleGroupDetailBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleGroupDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
