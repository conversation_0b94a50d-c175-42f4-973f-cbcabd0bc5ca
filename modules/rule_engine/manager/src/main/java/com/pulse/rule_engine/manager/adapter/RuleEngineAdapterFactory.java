package com.pulse.rule_engine.manager.adapter;

import com.pulse.rule_engine.manager.adapter.impl.DroolsRuleEngineAdapter;
import com.pulse.rule_engine.manager.config.RuleEngineConfig;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** 规则引擎适配器工厂类 根据配置创建对应的规则引擎适配器实例 */
@Slf4j
@Component
public class RuleEngineAdapterFactory {

    @Autowired private RuleEngineConfig ruleEngineConfig;

    @Autowired private DroolsRuleEngineAdapter droolsRuleEngineAdapter;

    /**
     * 获取默认的规则引擎适配器
     *
     * @return 规则引擎适配器实例
     */
    public RuleEngineAdapter getDefaultAdapter() {
        String engineType = ruleEngineConfig.getType();
        return getAdapter(engineType);
    }

    /**
     * 根据引擎类型获取对应的规则引擎适配器
     *
     * @param engineType 引擎类型
     * @return 规则引擎适配器实例
     */
    public RuleEngineAdapter getAdapter(String engineType) {
        if (engineType == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则引擎类型不能为空");
        }

        switch (engineType.toLowerCase()) {
            case "drools":
                log.debug("使用Drools规则引擎适配器");
                return droolsRuleEngineAdapter;

                // 可以在这里添加其他规则引擎的支持
                // case "easy-rules":
                //     return easyRulesRuleEngineAdapter;

            default:
                throw new IgnoredException(
                        ErrorCode.SYS_ERROR, "不支持的规则引擎类型: " + engineType + "，当前支持的类型: drools");
        }
    }

    /**
     * 获取所有支持的规则引擎类型
     *
     * @return 支持的引擎类型列表
     */
    public String[] getSupportedEngineTypes() {
        return new String[] {"drools"};
    }
}
