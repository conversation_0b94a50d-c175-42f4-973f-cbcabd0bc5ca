package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleGroupBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleGroupBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.pulse.rule_engine.persist.mapper.RuleGroupDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "aa51881e-7090-432a-b4a4-846278819a34|DTO|BASE_MANAGER_IMPL")
public abstract class RuleGroupBaseDtoManagerBaseImpl implements RuleGroupBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupBaseDtoConverter ruleGroupBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleGroupDao ruleGroupDao;

    @AutoGenerated(locked = true, uuid = "10997382-8804-30d8-aa24-d91f286a84ca")
    @Override
    public List<RuleGroupBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RuleGroup> ruleGroupList = ruleGroupDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleGroupList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RuleGroup> ruleGroupMap =
                ruleGroupList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleGroupList =
                id.stream()
                        .map(i -> ruleGroupMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleGroupToRuleGroupBaseDto(ruleGroupList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4f4a455d-0971-34f9-bedd-048e691e32f9")
    @Override
    public RuleGroupBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleGroupBaseDto ruleGroupBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleGroupBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f1437351-4b67-364a-9ab1-47b96d3605bb")
    public List<RuleGroupBaseDto> doConvertFromRuleGroupToRuleGroupBaseDto(
            List<RuleGroup> ruleGroupList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleGroupList)) {
            return Collections.emptyList();
        }

        Map<String, RuleGroupBaseDto> dtoMap =
                ruleGroupBaseDtoConverter
                        .convertFromRuleGroupToRuleGroupBaseDto(ruleGroupList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RuleGroupBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RuleGroupBaseDto> ruleGroupBaseDtoList = new ArrayList<>();
        for (RuleGroup i : ruleGroupList) {
            RuleGroupBaseDto ruleGroupBaseDto = dtoMap.get(i.getId());
            if (ruleGroupBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleGroupBaseDtoList.add(ruleGroupBaseDto);
        }
        return ruleGroupBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
