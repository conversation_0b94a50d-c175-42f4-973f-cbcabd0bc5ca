package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "4b738980-2d24-4fc1-b751-924770d01dbb|DTO|MANAGER")
public interface RuleGroupDetailBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "05644fcb-0fe1-3775-930d-04c0a87e3f21")
    List<RuleGroupDetailBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "1f80e192-cb63-3479-a01a-c44c8b9d9368")
    List<RuleGroupDetailBaseDto> getByGroupIds(List<String> groupId);

    @AutoGenerated(locked = true, uuid = "293342d0-27f5-3a2f-ab2b-e5c255459f51")
    List<RuleGroupDetailBaseDto> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "55131ba6-89f9-31ed-a639-7c9e02cb8c53")
    List<RuleGroupDetailBaseDto> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "605fc577-b06c-36e5-8cac-6118677db782")
    List<RuleGroupDetailBaseDto> getByGroupId(String groupId);

    @AutoGenerated(locked = true, uuid = "f3c2c4dd-78b6-3c9c-bb4a-47b20e862590")
    RuleGroupDetailBaseDto getById(String id);
}
