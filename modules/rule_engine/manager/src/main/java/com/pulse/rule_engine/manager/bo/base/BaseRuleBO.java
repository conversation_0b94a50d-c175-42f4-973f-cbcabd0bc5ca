package com.pulse.rule_engine.manager.bo.base;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.bo.RuleBO;
import com.pulse.rule_engine.manager.bo.RuleOrganizationBO;
import com.pulse.rule_engine.manager.bo.RuleVersionBO;
import com.pulse.rule_engine.persist.dos.Rule;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "rule")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "162fdbbc-ccf5-3d6c-ade8-3048e894418f")
public abstract class BaseRuleBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 规则编码 规则编码，英文唯一标识，如 "ANTIBIOTIC_APPROVAL" */
    @Column(name = "code")
    @AutoGenerated(locked = true, uuid = "306f245a-e010-49f1-ba6e-65542ef9a9b3")
    private String code;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "3d7e4c20-dc78-5005-9d39-af945da19a3d")
    private Date createdAt;

    /** 创建人 创建人ID，外键关联用户表 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "91e67375-ab26-4ceb-b70f-************")
    private String createdBy;

    /** 当前生效版本 当前生效版本 */
    @Column(name = "current_version")
    @AutoGenerated(locked = true, uuid = "d00c2de9-374b-4f22-acfa-4d4d33537bad")
    private String currentVersion;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "39559144-bb97-588e-a5bf-4df7b23c73f9")
    private Long deletedAt = 0L;

    /** 描述 描述 */
    @Column(name = "description")
    @AutoGenerated(locked = true, uuid = "975e4776-60b2-425d-bc86-d842e10cd24e")
    private String description;

    /** 显示名称 显示名称，如 "抗生素分级使用审批规则" */
    @Column(name = "display_name")
    @AutoGenerated(locked = true, uuid = "044d8b4b-4b96-4f09-8de4-a2a7afe425a7")
    private String displayName;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "eb813806-5fed-416e-a479-7f17b6672eec")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "922beba0-3d74-42d6-a7b4-6991463e6fe4")
    @Version
    private Long lockVersion;

    /**
     * 规则分类ID 规则分类ID,关联分类表ID。用于分类规则。 - HIS 系统需适应医疗政策和业务变化，规则类型可能频繁调整，枚举扩展性不足。 -
     * 相比码表，分类表更专注于规则类型管理，避免通用码表的复杂性，维护成本更可控。 - 支持动态扩展和业务友好性，符合以人为本的设计原则。
     */
    @Column(name = "rule_category_id")
    @AutoGenerated(locked = true, uuid = "9e1fb853-e7ba-4a3f-8f76-632972a5d8b5")
    private String ruleCategoryId;

    @JoinColumn(name = "rule_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<RuleOrganizationBO> ruleOrganizationBOSet = new HashSet<>();

    @JoinColumn(name = "rule_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<RuleVersionBO> ruleVersionBOSet = new HashSet<>();

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @Column(name = "status")
    @AutoGenerated(locked = true, uuid = "bd91e480-ae55-4a8a-9f10-fc4b50f00105")
    @Enumerated(EnumType.STRING)
    private RuleStatusEnum status;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "f4c08c62-2b5d-531d-9ae9-5b26da431c2e")
    private Date updatedAt;

    /** 更新人 更新人 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "2e13aea7-5d6f-4797-add9-3f6c1c883399")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public Rule convertToRule() {
        Rule entity = new Rule();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "code",
                "displayName",
                "ruleCategoryId",
                "status",
                "currentVersion",
                "description",
                "createdBy",
                "updatedBy",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static RuleBO getByCode(String code) {
        Session session = TransactionalSessionFactory.getSession();
        RuleBO rule =
                (RuleBO)
                        session.createQuery("from RuleBO where " + "code =: code ")
                                .setParameter("code", code)
                                .uniqueResult();
        return rule;
    }

    @AutoGenerated(locked = true)
    public static RuleBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        RuleBO rule =
                (RuleBO)
                        session.createQuery("from RuleBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return rule;
    }

    @AutoGenerated(locked = true)
    public String getCode() {
        return this.code;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public String getCurrentVersion() {
        return this.currentVersion;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDescription() {
        return this.description;
    }

    @AutoGenerated(locked = true)
    public String getDisplayName() {
        return this.displayName;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getRuleCategoryId() {
        return this.ruleCategoryId;
    }

    @AutoGenerated(locked = true)
    public Set<RuleOrganizationBO> getRuleOrganizationBOSet() {
        return this.ruleOrganizationBOSet;
    }

    @AutoGenerated(locked = true)
    public Set<RuleVersionBO> getRuleVersionBOSet() {
        return this.ruleVersionBOSet;
    }

    @AutoGenerated(locked = true)
    public RuleStatusEnum getStatus() {
        return this.status;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public RuleBO setCode(String code) {
        this.code = code;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setCurrentVersion(String currentVersion) {
        this.currentVersion = currentVersion;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setDescription(String description) {
        this.description = description;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setDisplayName(String displayName) {
        this.displayName = displayName;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setId(String id) {
        this.id = id;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setRuleCategoryId(String ruleCategoryId) {
        this.ruleCategoryId = ruleCategoryId;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    private void setRuleOrganizationBOSet(Set<RuleOrganizationBO> ruleOrganizationBOSet) {
        this.ruleOrganizationBOSet = ruleOrganizationBOSet;
    }

    @AutoGenerated(locked = true)
    private void setRuleVersionBOSet(Set<RuleVersionBO> ruleVersionBOSet) {
        this.ruleVersionBOSet = ruleVersionBOSet;
    }

    @AutoGenerated(locked = true)
    public RuleBO setStatus(RuleStatusEnum status) {
        this.status = status;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (RuleBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
