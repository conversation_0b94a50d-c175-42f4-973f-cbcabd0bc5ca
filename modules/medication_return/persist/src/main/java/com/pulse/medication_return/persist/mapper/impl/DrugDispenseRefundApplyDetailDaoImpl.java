package com.pulse.medication_return.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.medication_return.persist.dos.DrugDispenseRefundApplyDetail;
import com.pulse.medication_return.persist.mapper.DrugDispenseRefundApplyDetailDao;
import com.pulse.medication_return.persist.mapper.mybatis.DrugDispenseRefundApplyDetailMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "25a15eb1-bf38-3046-b184-5a6b76dcc59c|ENTITY|DAO")
public class DrugDispenseRefundApplyDetailDaoImpl implements DrugDispenseRefundApplyDetailDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugDispenseRefundApplyDetailMapper drugDispenseRefundApplyDetailMapper;

    @AutoGenerated(locked = true, uuid = "0237c919-939d-37dd-8fd2-71214941f217")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByApplyId(String applyId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("apply_id", applyId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "1c3a1760-ddc4-346c-bc55-1eb9d37a9870")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByApplyIds(List<String> applyId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("apply_id", applyId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "1feedb58-48da-39db-85e4-a5e9cbb03d42")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByDispensingDetailIds(
            List<String> dispensingDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("dispensing_detail_id", dispensingDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "36f9d376-0e41-361e-9697-bfe13d24886d")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByOrderDispenseDetailId(
            String orderDispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_dispense_detail_id", orderDispenseDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "394f5900-798b-36ae-a02e-94dc9ffe9895")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByDispenseDetailIds(
            List<String> dispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("dispense_detail_id", dispenseDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "3f9fc1c2-d949-3082-ab79-411d409731ee")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByRefundDispenseDetailIds(
            List<String> refundDispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("refund_dispense_detail_id", refundDispenseDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "6a81b837-64cf-394d-a461-15043005ad4f")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByIds(List<String> id) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "7a3186ad-b4cc-3b64-b73b-1c8dc7f6f6a4")
    @Override
    public DrugDispenseRefundApplyDetail getById(String id) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugDispenseRefundApplyDetailMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "7e83e293-ffdd-3fcd-993e-************")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "992fb631-a816-34f2-9a89-747b06344b6e")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "aaed7e73-3bc3-3302-8ed9-ac8537f99006")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByRefundOrderDispenseDetailIds(
            List<String> refundOrderDispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("refund_order_dispense_detail_id", refundOrderDispenseDetailId)
                .orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b3a8d991-5c03-3de2-9fea-7aa28800173b")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByRefundOrderDispenseDetailId(
            String refundOrderDispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("refund_order_dispense_detail_id", refundOrderDispenseDetailId)
                .orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b7402fb9-0ebe-3bc0-8042-d619d6698945")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByOrderDispenseDetailIds(
            List<String> orderDispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("order_dispense_detail_id", orderDispenseDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c2f9b700-acbd-3a45-92c6-31757ad98ecc")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByDispensingDetailId(String dispensingDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dispensing_detail_id", dispensingDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c6ef8c57-68e0-368d-8574-c416a22a9bd7")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByRefundDispenseDetailId(
            String refundDispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("refund_dispense_detail_id", refundDispenseDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "fbebc919-9ec0-3707-a12e-d71d3cd566cb")
    @Override
    public List<DrugDispenseRefundApplyDetail> getByDispenseDetailId(String dispenseDetailId) {
        QueryWrapper<DrugDispenseRefundApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dispense_detail_id", dispenseDetailId).orderByAsc("id");
        return drugDispenseRefundApplyDetailMapper.selectList(queryWrapper);
    }
}
