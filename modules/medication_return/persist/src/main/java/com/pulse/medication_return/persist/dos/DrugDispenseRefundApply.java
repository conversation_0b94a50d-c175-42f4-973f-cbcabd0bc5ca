package com.pulse.medication_return.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.medication_return.common.enums.RefundApplyStatusEnum;
import com.pulse.medication_return.common.enums.RefundOperatorTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "drug_dispense_refund_apply", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "64da2c85-215d-3a41-807c-c15a61e5a231|ENTITY|DEFINITION")
public class DrugDispenseRefundApply {
    @AutoGenerated(locked = true, uuid = "c720a1c9-54a5-36ae-9a03-4ccbd9113460")
    @TableField(value = "apply_date_time")
    private Date applyDateTime;

    @AutoGenerated(locked = true, uuid = "da2b9f47-666c-30e8-b225-6078e1485283")
    @TableField(value = "apply_number")
    private String applyNumber;

    @AutoGenerated(locked = true, uuid = "bf6940c9-66a4-30f1-8a05-9122cee1becf")
    @TableField(value = "apply_staff_id")
    private String applyStaffId;

    /** 待审核、审核通过、审核不通过、已作废、已退药 */
    @AutoGenerated(locked = true, uuid = "73dad89b-c410-3d0e-9806-b707184a43de")
    @TableField(value = "apply_status")
    private RefundApplyStatusEnum applyStatus;

    /** 医生申请、药房发起 */
    @AutoGenerated(locked = true, uuid = "4e9f52e2-b486-33bc-b875-dc8f2b244fbd")
    @TableField(value = "apply_type")
    private RefundOperatorTypeEnum applyType;

    @AutoGenerated(locked = true, uuid = "ab6f39ac-d082-3e5e-b8f0-a03aabc4bb59")
    @TableField(value = "audit_date_time")
    private Date auditDateTime;

    @AutoGenerated(locked = true, uuid = "9ffcfdb1-cf6d-3ef3-88e4-6c87769c1269")
    @TableField(value = "audit_staff_id")
    private String auditStaffId;

    @AutoGenerated(locked = true, uuid = "1b21463e-7854-3565-b8b2-3cc01665e97c")
    @TableField(value = "created_at")
    private Date createdAt;

    /** 住院医嘱退药，关联执行计划 */
    @AutoGenerated(locked = true, uuid = "f2f79a40-dfdc-395c-94a1-9651da359eb1")
    @TableField(value = "dispense_plan_id")
    private String dispensePlanId;

    /** 药房实际退药数量（药房可以修改退药数量） */
    @AutoGenerated(locked = true, uuid = "843acff3-68f3-3fef-9c1c-c0e4d58951db")
    @TableField(value = "herb_actual_regimen")
    private Long herbActualRegimen;

    /** 申请的退药数量 */
    @AutoGenerated(locked = true, uuid = "b72335fa-2efc-39cb-9390-1bd71d9050cb")
    @TableField(value = "herb_apply_regimen")
    private Long herbApplyRegimen;

    @AutoGenerated(locked = true, uuid = "f56d214d-439a-378b-ae38-9ece13186ebe")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "2c0a6bea-15b4-444a-808e-c56ed0f6b2ad")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "3d2daddd-b5ed-3fd4-8ac5-ec13bdb7711f")
    @TableField(value = "patient_id")
    private String patientId;

    /** 已发药退药，关联已发药id */
    @AutoGenerated(locked = true, uuid = "36ecf2cd-f920-30bb-882b-9c56ac4b05df")
    @TableField(value = "prescription_dispense_id")
    private String prescriptionDispenseId;

    /** 待发药退药，关联待发药记录id */
    @AutoGenerated(locked = true, uuid = "de836f9a-5d3a-3614-85e1-c1c7dd5f8dbe")
    @TableField(value = "prescription_dispensing_id")
    private String prescriptionDispensingId;

    /** 处方退药，关联处方 */
    @AutoGenerated(locked = true, uuid = "f3988ea8-4689-3610-9a40-5d11b0c46f40")
    @TableField(value = "prescription_id")
    private String prescriptionId;

    @AutoGenerated(locked = true, uuid = "e5ef1835-d378-34c8-966e-56a6e1762a94")
    @TableField(value = "reason")
    private String reason;

    /** 处方已发药退药，关联退药负记录 */
    @AutoGenerated(locked = true, uuid = "537eddcc-5359-32cb-a313-623ee289b8fe")
    @TableField(value = "refund_dispense_id")
    private String refundDispenseId;

    @AutoGenerated(locked = true, uuid = "3267eede-ec07-3765-823a-e7a227ea6fb5")
    @TableField(value = "storage_code")
    private String storageCode;

    @AutoGenerated(locked = true, uuid = "42dfd104-f4c6-3378-8e8d-4fa06f67460c")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
