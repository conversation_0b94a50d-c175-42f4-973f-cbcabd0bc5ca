package com.pulse.medication_return.manager.bo;

import com.pulse.medication_return.manager.bo.base.BaseDrugDispenseRefundApplyBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "drug_dispense_refund_apply")
@Entity
@AutoGenerated(locked = false, uuid = "59ed575b-7416-434d-b6be-0219f41dd7d0|BO|DEFINITION")
public class DrugDispenseRefundApplyBO extends BaseDrugDispenseRefundApplyBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "59ed575b-7416-434d-b6be-0219f41dd7d0|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
