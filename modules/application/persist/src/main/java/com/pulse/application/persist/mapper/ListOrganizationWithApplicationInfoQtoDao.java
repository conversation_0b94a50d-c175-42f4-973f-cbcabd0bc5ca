package com.pulse.application.persist.mapper;

import com.pulse.application.persist.qto.ListOrganizationWithApplicationInfoQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "a4b79d71-10d5-433e-a50f-1d9faa864416|QTO|DAO")
public class ListOrganizationWithApplicationInfoQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 获取组织应用关系 */
    @AutoGenerated(locked = false, uuid = "a4b79d71-10d5-433e-a50f-1d9faa864416-count")
    public Integer count(ListOrganizationWithApplicationInfoQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(application_organization.id) FROM application_organization LEFT JOIN"
                    + " application \"application\" on application_organization.application_id ="
                    + " \"application\".id WHERE application_organization.organization_id ="
                    + " #organizationIdIs AND ( JSON_VALUE(\"application\".input_code, '$.pinyin')"
                    + " like #input OR \"application\".name like #input ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getInput() == null) {
            conditionToRemove.add("#input");
        }
        if (qto.getInput() == null) {
            conditionToRemove.add("#input");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#organizationIdIs", "?").replace("#input", "?").replace("#input", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#input")) {
                sqlParams.add("%" + qto.getInput() + "%");
            } else if (paramName.equalsIgnoreCase("#input")) {
                sqlParams.add("%" + qto.getInput() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 获取组织应用关系 */
    @AutoGenerated(locked = false, uuid = "a4b79d71-10d5-433e-a50f-1d9faa864416-query-all")
    public List<String> query(ListOrganizationWithApplicationInfoQto qto) {
        qto.setSize(500);
        qto.setFrom(0);
        return this.queryPaged(qto);
    }

    /** 获取组织应用关系 */
    @AutoGenerated(locked = false, uuid = "a4b79d71-10d5-433e-a50f-1d9faa864416-query-paginate")
    public List<String> queryPaged(ListOrganizationWithApplicationInfoQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT application_organization.id FROM application_organization LEFT JOIN"
                    + " application \"application\" on application_organization.application_id ="
                    + " \"application\".id WHERE application_organization.organization_id ="
                    + " #organizationIdIs AND ( JSON_VALUE(\"application\".input_code, '$.pinyin')"
                    + " like #input OR \"application\".name like #input ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getInput() == null) {
            conditionToRemove.add("#input");
        }
        if (qto.getInput() == null) {
            conditionToRemove.add("#input");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#organizationIdIs", "?").replace("#input", "?").replace("#input", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#input")) {
                sqlParams.add("%" + qto.getInput() + "%");
            } else if (paramName.equalsIgnoreCase("#input")) {
                sqlParams.add("%" + qto.getInput() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  application_organization.sort_number asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
