package com.pulse.application.persist.mapper;

import com.pulse.application.persist.dos.Menu;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "37ead41e-429a-41ef-83c1-ce147a0f64da|ENTITY|IDAO")
public interface MenuDao {

    @AutoGenerated(locked = true, uuid = "44d977be-f810-382a-935e-a43b35328644")
    List<Menu> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "503f47b3-e05b-3f65-8234-fa9193fe36d1")
    Menu getById(String id);

    @AutoGenerated(locked = true, uuid = "63f3a118-67c0-3731-a564-20d0d76d05b6")
    List<Menu> getByRouterId(String routerId);

    @AutoGenerated(locked = true, uuid = "7e77912e-89f1-320a-80bf-44cc2db772b9")
    List<Menu> getByRouterIds(List<String> routerId);

    @AutoGenerated(locked = true, uuid = "bedefa9e-b900-341f-bcfc-4f6021780f69")
    List<Menu> getByParentIds(List<String> parentId);

    @AutoGenerated(locked = true, uuid = "c01a0fb0-7bf6-3ee4-ac7e-e72086afc41a")
    List<Menu> getByParentId(String parentId);
}
