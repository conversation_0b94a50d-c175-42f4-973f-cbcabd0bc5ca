package com.pulse.application.persist.mapper;

import com.pulse.application.persist.qto.ListFeatureRouterQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "9bd7d524-a5dc-4b56-a0d5-864189a166ef|QTO|DAO")
public class ListFeatureRouterQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 获取功能路由列表 */
    @AutoGenerated(locked = false, uuid = "9bd7d524-a5dc-4b56-a0d5-864189a166ef-count")
    public Integer count(ListFeatureRouterQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql = "SELECT count(feature.id) FROM feature WHERE feature.id = #idIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getIdIs() == null) {
            conditionToRemove.add("#idIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#idIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#idIs")) {
                sqlParams.add(qto.getIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 获取功能路由列表 */
    @AutoGenerated(locked = false, uuid = "9bd7d524-a5dc-4b56-a0d5-864189a166ef-query-all")
    public List<String> query(ListFeatureRouterQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql = "SELECT feature.id FROM feature WHERE feature.id = #idIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getIdIs() == null) {
            conditionToRemove.add("#idIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#idIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#idIs")) {
                sqlParams.add(qto.getIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  feature.sort_number asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
