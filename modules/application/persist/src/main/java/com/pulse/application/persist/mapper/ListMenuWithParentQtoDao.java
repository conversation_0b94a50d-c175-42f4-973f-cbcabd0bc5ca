package com.pulse.application.persist.mapper;

import com.pulse.application.persist.qto.ListMenuWithParentQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406|QTO|DAO")
public class ListMenuWithParentQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询菜单 */
    @AutoGenerated(locked = false, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406-count")
    public Integer count(ListMenuWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(menu.id) FROM menu WHERE menu.name like #searchLike OR ( menu.id like"
                    + " #searchLike AND ( menu.name like #searchLike OR JSON_VALUE(menu.input_code,"
                    + " '$.pinyin') like #searchLike OR JSON_VALUE(menu.input_code, '$.wubi') like"
                    + " #searchLike OR JSON_VALUE(menu.input_code, '$.custom') like #searchLike ) )"
                    + " ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询菜单 */
    @AutoGenerated(locked = false, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406-query-all")
    public List<String> query(ListMenuWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT menu.id FROM menu WHERE menu.name like #searchLike OR ( menu.id like"
                    + " #searchLike AND ( menu.name like #searchLike OR JSON_VALUE(menu.input_code,"
                    + " '$.pinyin') like #searchLike OR JSON_VALUE(menu.input_code, '$.wubi') like"
                    + " #searchLike OR JSON_VALUE(menu.input_code, '$.custom') like #searchLike ) )"
                    + " ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  menu.sort_number asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询菜单 */
    @AutoGenerated(locked = false, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406-query-paginate")
    public List<String> queryPaged(ListMenuWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT menu.id FROM menu WHERE menu.name like #searchLike OR ( menu.id like"
                    + " #searchLike AND ( menu.name like #searchLike OR JSON_VALUE(menu.input_code,"
                    + " '$.pinyin') like #searchLike OR JSON_VALUE(menu.input_code, '$.wubi') like"
                    + " #searchLike OR JSON_VALUE(menu.input_code, '$.custom') like #searchLike ) )"
                    + " ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  menu.sort_number asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
