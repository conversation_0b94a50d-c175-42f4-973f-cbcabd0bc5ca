package com.pulse.application.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.application.common.enums.ApplicationStatusEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@TableName(value = "application", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "b58e74be-9e51-4630-90ee-cd4bd9e396aa|ENTITY|DEFINITION")
public class Application {
    @AutoGenerated(locked = true, uuid = "cd7cb4bc-2505-45e3-a849-3215a1534177")
    @TableField(value = "abbreviation")
    private String abbreviation;

    @AutoGenerated(locked = true, uuid = "009a2f00-7f5f-4bcc-bc16-9b4d92c16b34")
    @TableField(value = "category_id")
    private String categoryId;

    @AutoGenerated(locked = true, uuid = "ba861175-9f2e-4db6-9e87-bc786820d49b")
    @TableField(value = "code")
    private String code;

    @AutoGenerated(locked = true, uuid = "9d334a52-f89b-57d1-b9ef-fbbc1ec8bb6d")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "da6c770b-9fb2-439f-8f00-04d65a875a15")
    @TableField(value = "description")
    private String description;

    @AutoGenerated(locked = true, uuid = "444b43d3-aea7-41a3-b16e-6df49d2774e9")
    @TableField(value = "enable_flag")
    private Boolean enableFlag;

    @AutoGenerated(locked = true, uuid = "4554ba79-8318-409a-8239-ee963f0c7604")
    @TableId(value = "id")
    private String id;

    @Valid
    @AutoGenerated(locked = true, uuid = "4fbb726a-94b0-4140-8aea-dde699170ff2")
    @TableField(value = "input_code", typeHandler = JacksonTypeHandler.class)
    private InputCodeEo inputCode;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "4eb945a0-b705-474c-a7e5-078d3b19cfa9")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "700258ba-1845-4d68-ba43-eccb2f060f5b")
    @TableField(value = "multiple_instance_flag")
    private Boolean multipleInstanceFlag;

    @AutoGenerated(locked = true, uuid = "8f2fab7d-4b37-457a-96cc-75aa6180d061")
    @TableField(value = "name")
    private String name;

    @AutoGenerated(locked = true, uuid = "84007533-4d38-4385-b525-f24fa7d8d231")
    @TableField(value = "organization_id")
    private String organizationId;

    @AutoGenerated(locked = true, uuid = "a122c623-1689-45e2-8c05-fe6d6d6c3ddd")
    @TableField(value = "parent_application_id")
    private String parentApplicationId;

    @AutoGenerated(locked = true, uuid = "e1ae4663-c1f0-45e9-81ef-9b27dc0b831a")
    @TableField(value = "router_id")
    private String routerId;

    @AutoGenerated(locked = true, uuid = "*************-4fe8-9e04-1a1e02857b35")
    @TableField(value = "status")
    private ApplicationStatusEnum status;

    @AutoGenerated(locked = true, uuid = "c951e5a5-fc6b-448d-9b8a-af6c40d1f723")
    @TableField(value = "template_application_flag")
    private Boolean templateApplicationFlag;

    @AutoGenerated(locked = true, uuid = "0e61673c-97a6-4d11-a175-b158428d4480")
    @TableField(value = "template_application_id")
    private String templateApplicationId;

    @AutoGenerated(locked = true, uuid = "de7943e9-32c0-5a7e-810a-8a2a7fb56333")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
