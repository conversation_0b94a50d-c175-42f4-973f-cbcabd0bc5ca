package com.pulse.application.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.application.persist.dos.Menu;
import com.pulse.application.persist.mapper.MenuDao;
import com.pulse.application.persist.mapper.mybatis.MenuMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "37ead41e-429a-41ef-83c1-ce147a0f64da|ENTITY|DAO")
public class MenuDaoImpl implements MenuDao {
    @AutoGenerated(locked = true)
    @Resource
    private MenuMapper menuMapper;

    @AutoGenerated(locked = true, uuid = "44d977be-f810-382a-935e-a43b35328644")
    @Override
    public List<Menu> getByIds(List<String> id) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return menuMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "503f47b3-e05b-3f65-8234-fa9193fe36d1")
    @Override
    public Menu getById(String id) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return menuMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "63f3a118-67c0-3731-a564-20d0d76d05b6")
    @Override
    public List<Menu> getByRouterId(String routerId) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("router_id", routerId).orderByAsc("id");
        return menuMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "7e77912e-89f1-320a-80bf-44cc2db772b9")
    @Override
    public List<Menu> getByRouterIds(List<String> routerId) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("router_id", routerId).orderByAsc("id");
        return menuMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "bedefa9e-b900-341f-bcfc-4f6021780f69")
    @Override
    public List<Menu> getByParentIds(List<String> parentId) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("parent_id", parentId).orderByAsc("id");
        return menuMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c01a0fb0-7bf6-3ee4-ac7e-e72086afc41a")
    @Override
    public List<Menu> getByParentId(String parentId) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId).orderByAsc("id");
        return menuMapper.selectList(queryWrapper);
    }
}
