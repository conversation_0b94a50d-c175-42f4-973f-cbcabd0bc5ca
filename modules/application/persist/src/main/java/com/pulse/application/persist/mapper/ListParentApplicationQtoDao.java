package com.pulse.application.persist.mapper;

import com.pulse.application.persist.qto.ListParentApplicationQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "c4f81fa1-4a7f-44ec-b3cd-d97910b1d7d9|QTO|DAO")
public class ListParentApplicationQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 获取父应用列表 */
    @AutoGenerated(locked = false, uuid = "c4f81fa1-4a7f-44ec-b3cd-d97910b1d7d9-count")
    public Integer count(ListParentApplicationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(application.id) FROM application WHERE application.name like"
                    + " #searchLike AND application.parent_application_id is null ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 获取父应用列表 */
    @AutoGenerated(locked = false, uuid = "c4f81fa1-4a7f-44ec-b3cd-d97910b1d7d9-query-all")
    public List<String> query(ListParentApplicationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT application.id FROM application WHERE application.name like #searchLike AND"
                    + " application.parent_application_id is null ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  application.id asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
