package com.pulse.application.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406|QTO|DEFINITION")
public class ListMenuWithParentQto {
    @AutoGenerated(locked = true, uuid = "01c211e7-f280-4a7b-bb86-d51cc498a024")
    private Integer from;

    /** 名称 menu.name */
    @AutoGenerated(locked = true, uuid = "dc5da1d9-1795-44a7-843d-529dae6e2f04")
    private String searchLike;

    @AutoGenerated(locked = true, uuid = "c8261e15-9d70-4abe-91ce-4de9adbd60ab")
    private Integer size;
}
