package com.pulse.application.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "c55bece1-523d-4558-a983-4d0b1a9ea663|QTO|DEFINITION")
public class ListRouterByNameLikeQto {
    /** 启用标识 router.enable_flag */
    @AutoGenerated(locked = true, uuid = "67523e1a-c907-4d4e-b8a8-38d1bfed3e20")
    private Boolean enableFlagIs;

    @AutoGenerated(locked = true, uuid = "d4374119-d837-4dda-bbba-750de8460c96")
    private Integer from;

    /** 名称 router.name */
    @AutoGenerated(locked = true, uuid = "0aafd987-c220-4690-a87f-7e56b8b2a31f")
    private String searchLike;

    @AutoGenerated(locked = true, uuid = "31c18b4d-192b-4a27-9fe7-68ec65f19c22")
    private Integer size;
}
