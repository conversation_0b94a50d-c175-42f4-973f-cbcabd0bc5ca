package com.pulse.application.persist.mapper;

import cn.hutool.core.util.StrUtil;

import com.pulse.application.persist.qto.GetGroupMenuQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "cecad086-c656-4ebb-b54a-3ef9e27dc20d|QTO|DAO")
public class GetGroupMenuQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 获取一个院区下所有菜单 */
    @AutoGenerated(locked = false, uuid = "cecad086-c656-4ebb-b54a-3ef9e27dc20d-count")
    public Integer count(GetGroupMenuQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(application_organization.id) FROM application_organization LEFT JOIN"
                    + " application \"application\" on application_organization.application_id ="
                    + " \"application\".id WHERE application_organization.organization_id ="
                    + " #organizationIdIs AND \"application\".enable_flag = #enableFlagIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#organizationIdIs", "?").replace("#enableFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = false, uuid = "cecad086-c656-4ebb-b54a-3ef9e27dc20d-filter")
    public List<Map> filter(List<String> idList, GetGroupMenuQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT application_organization.id AS"
                    + " \"application_organization.id\",\"application\".id AS"
                    + " \"application.id\",\"application_applicationMenuRouterList\".id AS"
                    + " \"application_applicationMenuRouterList.id\" FROM application_organization"
                    + " application_organization LEFT JOIN application \"application\" ON"
                    + " application_organization.application_id = \"application\".id LEFT JOIN ("
                    + " SELECT \"application_applicationMenuRouterList\".* FROM application_menu"
                    + " \"application_applicationMenuRouterList\" LEFT JOIN menu"
                    + " \"application_applicationMenuRouterList_menu\" on"
                    + " \"application_applicationMenuRouterList\".menu_id ="
                    + " \"application_applicationMenuRouterList_menu\".id LEFT JOIN router"
                    + " \"application_applicationMenuRouterList_menu_router\" on"
                    + " \"application_applicationMenuRouterList_menu\".router_id ="
                    + " \"application_applicationMenuRouterList_menu_router\".id LEFT JOIN feature"
                    + " \"application_applicationMenuRouterList_menu_router_feature\" on"
                    + " \"application_applicationMenuRouterList_menu_router\".feature_id ="
                    + " \"application_applicationMenuRouterList_menu_router_feature\".id WHERE"
                    + " \"application_applicationMenuRouterList_menu\".enable_flag ="
                    + " #menuEnableFlagIs AND"
                    + " \"application_applicationMenuRouterList_menu_router\".enable_flag ="
                    + " #menuEnableFlagIs AND"
                    + " \"application_applicationMenuRouterList_menu_router_feature\".enable_flag ="
                    + " #menuEnableFlagIs ) \"application_applicationMenuRouterList\" ON"
                    + " \"application\".id ="
                    + " \"application_applicationMenuRouterList\".application_id WHERE"
                    + " application_organization.id in ##mainId ";
        if (idList.isEmpty()) {
            return List.of();
        }
        sql = sql.replace("##mainId", SqlUtil.buildInSqlPram(idList.size()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getFilter().getMenuEnableFlagIs() == null) {
            conditionToRemove.add("#menuEnableFlagIs");
        }
        if (qto.getFilter().getMenuEnableFlagIs() == null) {
            conditionToRemove.add("#menuEnableFlagIs");
        }
        if (qto.getFilter().getMenuEnableFlagIs() == null) {
            conditionToRemove.add("#menuEnableFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#menuEnableFlagIs", "?")
                        .replace("#menuEnableFlagIs", "?")
                        .replace("#menuEnableFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#menuEnableFlagIs")) {
                sqlParams.add(qto.getFilter().getMenuEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#menuEnableFlagIs")) {
                sqlParams.add(qto.getFilter().getMenuEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#menuEnableFlagIs")) {
                sqlParams.add(qto.getFilter().getMenuEnableFlagIs());
            }
        }
        sqlParams.addAll(idList);
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        return this.sqlManager.getList(
                parsedSql,
                resultSet -> {
                    Map<String, Object> rowData = new HashMap<>();
                    if (StrUtil.isNotEmpty(resultSet.getString("application_organization.id"))) {
                        rowData.put(
                                "application_organization",
                                resultSet.getString("application_organization.id"));
                    }
                    if (StrUtil.isNotEmpty(resultSet.getString("application.id"))) {
                        rowData.put("application", resultSet.getString("application.id"));
                    }
                    if (StrUtil.isNotEmpty(
                            resultSet.getString("application_applicationMenuRouterList.id"))) {
                        rowData.put(
                                "application_applicationMenuRouterList",
                                resultSet.getString("application_applicationMenuRouterList.id"));
                    }
                    return rowData;
                },
                sqlParams);
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 获取一个院区下所有菜单 */
    @AutoGenerated(locked = false, uuid = "cecad086-c656-4ebb-b54a-3ef9e27dc20d-query-all")
    public List<String> query(GetGroupMenuQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT application_organization.id FROM application_organization LEFT JOIN"
                    + " application \"application\" on application_organization.application_id ="
                    + " \"application\".id WHERE application_organization.organization_id ="
                    + " #organizationIdIs AND \"application\".enable_flag = #enableFlagIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#organizationIdIs", "?").replace("#enableFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  application_organization.sort_number asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
