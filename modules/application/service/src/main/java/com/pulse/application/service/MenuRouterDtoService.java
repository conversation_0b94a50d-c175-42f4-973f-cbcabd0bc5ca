package com.pulse.application.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuRouterDtoManager;
import com.pulse.application.manager.dto.MenuRouterDto;
import com.pulse.application.service.converter.MenuRouterDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "f36e4344-2753-4659-9f5a-bd74f864015e|DTO|SERVICE")
public class MenuRouterDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private MenuRouterDtoManager menuRouterDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuRouterDtoServiceConverter menuRouterDtoServiceConverter;

    @PublicInterface(id = "d66c7f0a-355b-4afe-b285-c4fb76b99655", module = "application")
    @AutoGenerated(locked = false, uuid = "180f13e6-6464-3ea4-a884-ae2fff4782d7")
    public List<MenuRouterDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<MenuRouterDto> menuRouterDtoList = menuRouterDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuRouterDtoServiceConverter.MenuRouterDtoConverter(menuRouterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "498b9627-489a-429c-a9f7-2ccdc58acb2d", module = "application")
    @AutoGenerated(locked = false, uuid = "36764f4e-c50a-3a00-94b9-16084b80ecab")
    public List<MenuRouterDto> getByRouterIds(
            @Valid @NotNull(message = "关联路由ID不能为空") List<String> routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        routerId = new ArrayList<>(new HashSet<>(routerId));
        List<MenuRouterDto> menuRouterDtoList = menuRouterDtoManager.getByRouterIds(routerId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuRouterDtoServiceConverter.MenuRouterDtoConverter(menuRouterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0903bf71-07a2-4ad4-92ab-a5c0bbcfa5b1", module = "application")
    @AutoGenerated(locked = false, uuid = "94df3df0-7a6c-3bde-a930-0c1ace587188")
    public List<MenuRouterDto> getByRouterId(@NotNull(message = "关联路由ID不能为空") String routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRouterIds(Arrays.asList(routerId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "46b7f1bf-c399-478e-9b31-e57d92699472", module = "application")
    @AutoGenerated(locked = false, uuid = "dcff04d0-dfc4-3164-b214-6915e8d33741")
    public MenuRouterDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuRouterDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "12135183-f7d8-4a9e-8ad5-21b5991bd5e8", module = "application")
    @AutoGenerated(locked = false, uuid = "e27199a1-5faa-3c1d-a53a-2b867135d487")
    public List<MenuRouterDto> getByParentIds(
            @Valid @NotNull(message = "父菜单ID不能为空") List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        parentId = new ArrayList<>(new HashSet<>(parentId));
        List<MenuRouterDto> menuRouterDtoList = menuRouterDtoManager.getByParentIds(parentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuRouterDtoServiceConverter.MenuRouterDtoConverter(menuRouterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "05ed9737-1df4-42e7-981a-2c512c2192bd", module = "application")
    @AutoGenerated(locked = false, uuid = "f53ff31e-1a96-3b93-848b-68544a9d7128")
    public List<MenuRouterDto> getByParentId(@NotNull(message = "父菜单ID不能为空") String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByParentIds(Arrays.asList(parentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
