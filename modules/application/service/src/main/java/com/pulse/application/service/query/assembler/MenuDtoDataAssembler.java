package com.pulse.application.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.service.MenuBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** MenuDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "e28e2830-02c8-32d4-b8ca-d19047f2c63e")
public class MenuDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    /** 组装MenuDto数据 */
    @AutoGenerated(locked = true, uuid = "6f2ccd40-7826-3998-a662-9fff70a8af16")
    public void assembleData(
            List<MenuDto> dtoList, MenuDtoDataAssembler.MenuDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, MenuBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(MenuBaseDto::getId, Function.identity()));

        Map<String, MenuBaseDto> parent =
                dataHolder.parent.stream()
                        .collect(Collectors.toMap(MenuBaseDto::getId, Function.identity()));

        for (MenuDto dto : dtoList) {
            dto.setParent(
                    Optional.ofNullable(parent.get(baseDtoMap.get(dto.getId()).getParentId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装MenuDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "ff3a6a99-162b-33f7-8a54-a0734e05de3b")
    public void assembleDataCustomized(List<MenuDto> dataList) {
        // 自定义数据组装

    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class MenuDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<MenuBaseDto> rootBaseDtoList;

        /** 持有dto字段parent的Dto数据 */
        @AutoGenerated(locked = true)
        public List<MenuBaseDto> parent;
    }
}
