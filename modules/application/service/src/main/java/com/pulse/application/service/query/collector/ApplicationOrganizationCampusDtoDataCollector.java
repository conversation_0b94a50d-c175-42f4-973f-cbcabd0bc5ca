package com.pulse.application.service.query.collector;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationOrganizationDto;
import com.pulse.application.manager.facade.organization.CampusBaseDtoServiceInApplicationRpcAdapter;
import com.pulse.application.manager.facade.organization.OrganizationBaseDtoServiceInApplicationRpcAdapter;
import com.pulse.application.manager.facade.organization.OrganizationCampusDtoServiceInApplicationRpcAdapter;
import com.pulse.application.service.ApplicationBaseDtoService;
import com.pulse.application.service.ApplicationOrganizationDtoService;
import com.pulse.application.service.query.assembler.ApplicationOrganizationCampusDtoDataAssembler.ApplicationOrganizationCampusDtoDataHolder;
import com.pulse.organization.manager.dto.CampusBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationCampusDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ApplicationOrganizationCampusDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "645ceb52-d080-330c-ad17-73baf6d9055c")
public class ApplicationOrganizationCampusDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoService applicationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationCampusDtoDataCollector
            applicationOrganizationCampusDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationDtoService applicationOrganizationDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private CampusBaseDtoServiceInApplicationRpcAdapter campusBaseDtoServiceInApplicationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoServiceInApplicationRpcAdapter
            organizationBaseDtoServiceInApplicationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoServiceInApplicationRpcAdapter
            organizationCampusDtoServiceInApplicationRpcAdapter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "24db8258-2d4e-315e-88ae-30bbd2442eeb")
    public void collectDataDefault(ApplicationOrganizationCampusDtoDataHolder dataHolder) {
        applicationOrganizationCampusDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "66243a54-aa43-3256-992e-ed7fcc32c846")
    private void fillDataWhenNecessary(ApplicationOrganizationCampusDtoDataHolder dataHolder) {
        List<ApplicationOrganizationDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoServiceInApplicationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<String, OrganizationCampusDto> organizationCampusDtoMap =
                    organizationCampusDtoServiceInApplicationRpcAdapter
                            .getByIds(
                                    baseDtoList.stream()
                                            .map(OrganizationBaseDto::getId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationCampusDto::getId, Function.identity()));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    organizationCampusDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.application == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            dataHolder.application =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.organization2Campus == null) {
            Set<String> ids =
                    dataHolder.organization.keySet().stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<CampusBaseDto> baseDtoList =
                    campusBaseDtoServiceInApplicationRpcAdapter
                            .getByOrganizationIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(CampusBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<CampusBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(CampusBaseDto::getOrganizationId));
            dataHolder.organization2Campus =
                    dataHolder.organization.keySet().stream()
                            .map(OrganizationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
