package com.pulse.application.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.application.persist.mapper.ListMenuWithParentQtoDao;
import com.pulse.application.persist.qto.ListMenuWithParentQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406|QTO|SERVICE")
public class ListMenuWithParentQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListMenuWithParentQtoDao listMenuWithParentMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "8e9727de-3f05-4a84-9e4a-05542963c406-query-paged")
    public List<String> queryPaged(ListMenuWithParentQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listMenuWithParentMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListMenuWithParentQto qto) {
        return listMenuWithParentMapper.count(qto);
    }
}
