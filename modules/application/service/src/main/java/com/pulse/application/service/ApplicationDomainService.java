package com.pulse.application.service;

import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/** 模块领域服务，负责模块的BoService的包装 */
@Service
@Slf4j
@AutoGenerated(locked = false, uuid = "2490b347-7947-4b60-afc4-0054306a277c")
public class ApplicationDomainService {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBOService applicationBOService;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureBOService featureBOService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBOService menuBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBOService routerBOService;
}
