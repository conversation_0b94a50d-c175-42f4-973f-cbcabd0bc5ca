package com.pulse.application.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.application.manager.dto.FeatureDto;
import com.pulse.application.service.FeatureBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** FeatureDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "01c389dd-a6ae-364a-b97b-013c4e885605")
public class FeatureDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private FeatureBaseDtoService featureBaseDtoService;

    /** 组装FeatureDto数据 */
    @AutoGenerated(locked = true, uuid = "5d6918c9-81da-338d-a8bc-b69813635fe4")
    public void assembleData(
            List<FeatureDto> dtoList, FeatureDtoDataAssembler.FeatureDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, FeatureBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(FeatureBaseDto::getId, Function.identity()));

        Map<String, FeatureBaseDto> parent =
                dataHolder.parent.stream()
                        .collect(Collectors.toMap(FeatureBaseDto::getId, Function.identity()));

        for (FeatureDto dto : dtoList) {
            dto.setParent(
                    Optional.ofNullable(parent.get(baseDtoMap.get(dto.getId()).getParentId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装FeatureDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b6d347c2-32ca-3142-9280-a25f0e598266")
    public void assembleDataCustomized(List<FeatureDto> dataList) {
        // 自定义数据组装

    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class FeatureDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<FeatureBaseDto> rootBaseDtoList;

        /** 持有dto字段parent的Dto数据 */
        @AutoGenerated(locked = true)
        public List<FeatureBaseDto> parent;
    }
}
