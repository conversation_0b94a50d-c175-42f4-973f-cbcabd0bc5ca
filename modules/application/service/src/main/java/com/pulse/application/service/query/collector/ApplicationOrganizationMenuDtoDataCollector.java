package com.pulse.application.service.query.collector;

import com.pulse.application.manager.converter.ApplicationMenuFullDtoConverter;
import com.pulse.application.manager.converter.ApplicationMenuRouterDtoConverter;
import com.pulse.application.manager.converter.MenuRouterDtoConverter;
import com.pulse.application.manager.converter.RouterDtoConverter;
import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationMenuBaseDto;
import com.pulse.application.manager.dto.ApplicationMenuFullDto;
import com.pulse.application.manager.dto.ApplicationMenuRouterDto;
import com.pulse.application.manager.dto.ApplicationOrganizationDto;
import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuRouterDto;
import com.pulse.application.manager.dto.RouterBaseDto;
import com.pulse.application.manager.dto.RouterDto;
import com.pulse.application.persist.qto.GetGroupMenuQto;
import com.pulse.application.service.ApplicationBaseDtoService;
import com.pulse.application.service.ApplicationMenuBaseDtoService;
import com.pulse.application.service.ApplicationOrganizationDtoService;
import com.pulse.application.service.FeatureBaseDtoService;
import com.pulse.application.service.MenuBaseDtoService;
import com.pulse.application.service.RouterBaseDtoService;
import com.pulse.application.service.index.entity.GetGroupMenuQtoService;
import com.pulse.application.service.query.assembler.ApplicationOrganizationMenuDtoDataAssembler.ApplicationOrganizationMenuDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ApplicationOrganizationMenuDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "a38a71b2-53fb-3e91-98cb-2c8e08afa670")
public class ApplicationOrganizationMenuDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoService applicationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationMenuBaseDtoService applicationMenuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationMenuFullDtoConverter applicationMenuFullDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationMenuRouterDtoConverter applicationMenuRouterDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationDtoService applicationOrganizationDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationMenuDtoDataCollector applicationOrganizationMenuDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureBaseDtoService featureBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private GetGroupMenuQtoService getGroupMenuQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuRouterDtoConverter menuRouterDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBaseDtoService routerBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterDtoConverter routerDtoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "96b700d7-441d-39ba-b224-7ac4bdc16235")
    public void collectDataDefault(ApplicationOrganizationMenuDtoDataHolder dataHolder) {
        applicationOrganizationMenuDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "d795bcbb-a7a9-3f7c-9cac-3209f95e67d4")
    private void fillDataWhenNecessary(ApplicationOrganizationMenuDtoDataHolder dataHolder) {
        List<ApplicationOrganizationDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.application == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<String, ApplicationMenuFullDto> applicationMenuFullDtoMap =
                    applicationMenuFullDtoConverter
                            .convertFromApplicationBaseDtoToApplicationMenuFullDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationMenuFullDto::getId, Function.identity()));
            dataHolder.application =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    applicationMenuFullDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.application2ApplicationMenuRouterList == null) {
            Set<String> ids =
                    dataHolder.application.keySet().stream()
                            .map(ApplicationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationMenuBaseDto> baseDtoList =
                    applicationMenuBaseDtoService.getByApplicationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ApplicationMenuBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<ApplicationMenuBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ApplicationMenuBaseDto::getApplicationId));
            Map<String, ApplicationMenuRouterDto> applicationMenuRouterDtoMap =
                    applicationMenuRouterDtoConverter
                            .convertFromApplicationMenuBaseDtoToApplicationMenuRouterDto(
                                    baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationMenuRouterDto::getId, Function.identity()));
            dataHolder.application2ApplicationMenuRouterList =
                    dataHolder.application.keySet().stream()
                            .map(ApplicationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    applicationMenuRouterDtoMap.get(
                                                            baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.application2ApplicationMenuRouterList2Menu == null) {
            Set<String> ids =
                    dataHolder.application2ApplicationMenuRouterList.keySet().stream()
                            .map(ApplicationMenuBaseDto::getMenuId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<MenuBaseDto> baseDtoList =
                    menuBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(MenuBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, MenuBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(MenuBaseDto::getId, Function.identity()));
            Map<String, MenuRouterDto> menuRouterDtoMap =
                    menuRouterDtoConverter
                            .convertFromMenuBaseDtoToMenuRouterDto(baseDtoList)
                            .stream()
                            .collect(Collectors.toMap(MenuRouterDto::getId, Function.identity()));
            dataHolder.application2ApplicationMenuRouterList2Menu =
                    dataHolder.application2ApplicationMenuRouterList.keySet().stream()
                            .map(ApplicationMenuBaseDto::getMenuId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> menuRouterDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.application2ApplicationMenuRouterList2Menu2Router == null) {
            Set<String> ids =
                    dataHolder.application2ApplicationMenuRouterList2Menu.keySet().stream()
                            .map(MenuBaseDto::getRouterId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<RouterBaseDto> baseDtoList =
                    routerBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(RouterBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, RouterBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(RouterBaseDto::getId, Function.identity()));
            Map<String, RouterDto> routerDtoMap =
                    routerDtoConverter.convertFromRouterBaseDtoToRouterDto(baseDtoList).stream()
                            .collect(Collectors.toMap(RouterDto::getId, Function.identity()));
            dataHolder.application2ApplicationMenuRouterList2Menu2Router =
                    dataHolder.application2ApplicationMenuRouterList2Menu.keySet().stream()
                            .map(MenuBaseDto::getRouterId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> routerDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.application2ApplicationMenuRouterList2Menu2Router2Feature == null) {
            Set<String> ids =
                    dataHolder.application2ApplicationMenuRouterList2Menu2Router.keySet().stream()
                            .map(RouterBaseDto::getFeatureId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<FeatureBaseDto> baseDtoList =
                    featureBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(FeatureBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, FeatureBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(FeatureBaseDto::getId, Function.identity()));
            dataHolder.application2ApplicationMenuRouterList2Menu2Router2Feature =
                    dataHolder.application2ApplicationMenuRouterList2Menu2Router.keySet().stream()
                            .map(RouterBaseDto::getFeatureId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** 根据GetGroupMenuQto采集Dto数据 */
    @AutoGenerated(locked = true, uuid = "e0c44ce6-0d3f-39ab-9bd6-0803511e0cdc")
    public void collectDataByGetGroupMenuQto(
            ApplicationOrganizationMenuDtoDataHolder dataHolder, GetGroupMenuQto qto) {
        List<String> ids =
                dataHolder.getRootBaseDtoList().stream()
                        .map(ApplicationOrganizationDto::getId)
                        .collect(Collectors.toList());
        List<Map> dtoData = getGroupMenuQtoService.filter(ids, qto);
        Set<String> application2ApplicationMenuRouterListIdSet = new HashSet<>();
        Set<String> applicationIdSet = new HashSet<>();
        for (Map map : dtoData) {
            String application2ApplicationMenuRouterListId =
                    (String) map.get("application_applicationMenuRouterList");
            if (application2ApplicationMenuRouterListId != null) {
                application2ApplicationMenuRouterListIdSet.add(
                        application2ApplicationMenuRouterListId);
            }
            String applicationId = (String) map.get("application");
            if (applicationId != null) {
                applicationIdSet.add(applicationId);
            }
        }

        // access application2ApplicationMenuRouterList
        List<ApplicationMenuBaseDto> application2ApplicationMenuRouterListList =
                applicationMenuBaseDtoService
                        .getByIds(new ArrayList<>(application2ApplicationMenuRouterListIdSet))
                        .stream()
                        .sorted(Comparator.comparing(ApplicationMenuBaseDto::getId))
                        .collect(Collectors.toList());
        Map<String, ApplicationMenuRouterDto> application2ApplicationMenuRouterListDtoMap =
                applicationMenuRouterDtoConverter
                        .convertFromApplicationMenuBaseDtoToApplicationMenuRouterDto(
                                application2ApplicationMenuRouterListList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ApplicationMenuRouterDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        dataHolder.application2ApplicationMenuRouterList =
                application2ApplicationMenuRouterListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                application2ApplicationMenuRouterListDtoMap.get(
                                                        baseDto.getId()),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access application
        List<ApplicationBaseDto> applicationList =
                applicationBaseDtoService.getByIds(new ArrayList<>(applicationIdSet)).stream()
                        .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                        .collect(Collectors.toList());
        Map<String, ApplicationMenuFullDto> applicationDtoMap =
                applicationMenuFullDtoConverter
                        .convertFromApplicationBaseDtoToApplicationMenuFullDto(applicationList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ApplicationMenuFullDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        dataHolder.application =
                applicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> applicationDtoMap.get(baseDto.getId()),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
