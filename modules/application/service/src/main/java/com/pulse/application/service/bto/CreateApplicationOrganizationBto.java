package com.pulse.application.service.bto;

import com.pulse.application.common.enums.ApplicationStatusEnum;
import com.pulse.application.common.enums.ApplicationTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.ProducerDrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Application
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "e033da38-95ac-4cf6-a84d-102fa633643a|BTO|DEFINITION")
public class CreateApplicationOrganizationBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "6df597e3-4260-41d8-ba0a-edc881cad4e1")
    private List<CreateApplicationOrganizationBto.ApplicationOrganizationBto>
            applicationOrganizationBtoList;

    /** 应用编码 */
    @AutoGenerated(locked = true, uuid = "eb62a870-d835-4753-83a0-75649ac6773f")
    private String code;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5669c5ec-00cb-4e8b-b6d3-b54552345309")
    private String id;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "f46df7a6-8103-45c2-9eee-9deb4e78be3f")
    private ApplicationStatusEnum status;

    @AutoGenerated(locked = true)
    public void setApplicationOrganizationBtoList(
            List<CreateApplicationOrganizationBto.ApplicationOrganizationBto>
                    applicationOrganizationBtoList) {
        this.__$validPropertySet.add("applicationOrganizationBtoList");
        this.applicationOrganizationBtoList = applicationOrganizationBtoList;
    }

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setStatus(ApplicationStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    /**
     * <b>[源自]</b> ApplicationOrganization
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ApplicationOrganizationBto {
        /** 组织ID */
        @AutoGenerated(locked = true, uuid = "ab7906e3-ddf8-459a-a8fd-d1037599b4db")
        private String organizationId;

        /** 描述 */
        @AutoGenerated(locked = true, uuid = "d23077cc-f966-43a6-aee9-5d491d4f4d38")
        private String description;

        /** 排序编号 */
        @AutoGenerated(locked = true, uuid = "0265c0da-8b6a-4455-aee3-9924ccfe0070")
        private Integer sortNumber;

        /** 应用ID */
        @AutoGenerated(locked = true, uuid = "b6ae0660-fc96-4a2d-9c79-c840465f84a9")
        private String applicationId;

        /** 应用简称 */
        @AutoGenerated(locked = true, uuid = "82fc0d6b-b6cc-47ba-bc64-64f2b56c8cfc")
        private String shortName;

        /** 输入码 */
        @Valid
        @AutoGenerated(locked = true, uuid = "e52b4940-023a-4ef6-940a-e696313d2ca9")
        private InputCodeEo inputCode;

        /** 库存 */
        @AutoGenerated(locked = true, uuid = "c3a3720f-e166-44cc-b622-7c52c63e4097")
        private String inventory;

        /** 急诊 */
        @AutoGenerated(locked = true, uuid = "0a0bb9bf-7436-4d0f-b4c0-9c43d2384def")
        private Boolean emergencyFlag;

        /** 门诊费用 */
        @AutoGenerated(locked = true, uuid = "0a3bdecd-dc49-4c3e-ab52-3446cbc50592")
        private Boolean outpatientFeeFlag;

        /** 药房标志 */
        @AutoGenerated(locked = true, uuid = "47750b0f-1b43-41af-b9af-039dcff25eb0")
        private Boolean pharmacyFlag;

        @Valid
        @AutoGenerated(locked = true, uuid = "c1991430-15f0-46af-8a3e-a59edf9331df")
        private List<CreateApplicationOrganizationBto.ApplicationDetailBto>
                applicationDetailBtoList;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setDescription(String description) {
            this.__$validPropertySet.add("description");
            this.description = description;
        }

        @AutoGenerated(locked = true)
        public void setSortNumber(Integer sortNumber) {
            this.__$validPropertySet.add("sortNumber");
            this.sortNumber = sortNumber;
        }

        @AutoGenerated(locked = true)
        public void setApplicationId(String applicationId) {
            this.__$validPropertySet.add("applicationId");
            this.applicationId = applicationId;
        }

        @AutoGenerated(locked = true)
        public void setShortName(String shortName) {
            this.__$validPropertySet.add("shortName");
            this.shortName = shortName;
        }

        @AutoGenerated(locked = true)
        public void setInputCode(InputCodeEo inputCode) {
            this.__$validPropertySet.add("inputCode");
            this.inputCode = inputCode;
        }

        @AutoGenerated(locked = true)
        public void setInventory(String inventory) {
            this.__$validPropertySet.add("inventory");
            this.inventory = inventory;
        }

        @AutoGenerated(locked = true)
        public void setEmergencyFlag(Boolean emergencyFlag) {
            this.__$validPropertySet.add("emergencyFlag");
            this.emergencyFlag = emergencyFlag;
        }

        @AutoGenerated(locked = true)
        public void setOutpatientFeeFlag(Boolean outpatientFeeFlag) {
            this.__$validPropertySet.add("outpatientFeeFlag");
            this.outpatientFeeFlag = outpatientFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setPharmacyFlag(Boolean pharmacyFlag) {
            this.__$validPropertySet.add("pharmacyFlag");
            this.pharmacyFlag = pharmacyFlag;
        }

        @AutoGenerated(locked = true)
        public void setApplicationDetailBtoList(
                List<CreateApplicationOrganizationBto.ApplicationDetailBto>
                        applicationDetailBtoList) {
            this.__$validPropertySet.add("applicationDetailBtoList");
            this.applicationDetailBtoList = applicationDetailBtoList;
        }
    }

    /**
     * <b>[源自]</b> ApplicationDetail
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ApplicationDetailBto {
        /** 目标应用ID */
        @AutoGenerated(locked = true, uuid = "a05444db-f6bb-4a18-8c45-88891ec584a5")
        private String targetApplicationId;

        /**
         * 目标类型 对应类型：9手麻-门诊药房, 8手麻-住院药房,7手麻-药库,6住院收费-药房,5药
         * 房-窗口,4住院医生站-药房,3病区护士站-药房,2门诊诊间-药房,1门诊收费-药房(手工处方),18对应普通手术应用,17对应介入手术应用,16药房-药房(代发医嘱用药),15住院医生站-三升袋,14病区护士站-三升袋,13住院医生站-化疗,12病区护士站-化疗,11住院医生站-静脉配,10病区护士站-静脉配,23门办-挂号收费
         */
        @AutoGenerated(locked = true, uuid = "0936508e-342e-4ac1-b881-2fdd63bd19c8")
        private String targetType;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "e56e552b-f77e-46ac-97bd-2a5d18664160")
        private Boolean activeFlag;

        /** 类型 项目类型:1西药，2成药，3草药 */
        @AutoGenerated(locked = true, uuid = "e811de90-cf07-4e3c-8124-6e39163b4efb")
        private ProducerDrugTypeEnum type;

        /** 应用类型 门诊住院标志,0：门诊 1：住院 3：急诊 , 对于病区护士站与药房对应时 0 表示夜间医嘱对应的药房 */
        @AutoGenerated(locked = true, uuid = "6beffa5d-48af-4503-94e6-d7f5adce2379")
        private ApplicationTypeEnum applicationType;

        /** 默认标志 */
        @AutoGenerated(locked = true, uuid = "c2fbc3ad-1496-4ba3-9b4b-c5e63660e754")
        private Boolean defaultFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setTargetApplicationId(String targetApplicationId) {
            this.__$validPropertySet.add("targetApplicationId");
            this.targetApplicationId = targetApplicationId;
        }

        @AutoGenerated(locked = true)
        public void setTargetType(String targetType) {
            this.__$validPropertySet.add("targetType");
            this.targetType = targetType;
        }

        @AutoGenerated(locked = true)
        public void setActiveFlag(Boolean activeFlag) {
            this.__$validPropertySet.add("activeFlag");
            this.activeFlag = activeFlag;
        }

        @AutoGenerated(locked = true)
        public void setType(ProducerDrugTypeEnum type) {
            this.__$validPropertySet.add("type");
            this.type = type;
        }

        @AutoGenerated(locked = true)
        public void setApplicationType(ApplicationTypeEnum applicationType) {
            this.__$validPropertySet.add("applicationType");
            this.applicationType = applicationType;
        }

        @AutoGenerated(locked = true)
        public void setDefaultFlag(Boolean defaultFlag) {
            this.__$validPropertySet.add("defaultFlag");
            this.defaultFlag = defaultFlag;
        }
    }
}
