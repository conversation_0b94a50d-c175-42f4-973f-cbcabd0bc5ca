package com.pulse.application.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuBaseDtoManager;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.service.converter.MenuBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "89f13610-7112-4692-8ded-95a77ac06804|DTO|SERVICE")
public class MenuBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoManager menuBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoServiceConverter menuBaseDtoServiceConverter;

    @PublicInterface(
            id = "c6823302-e1c1-493e-9203-9178cd0925c2",
            module = "application",
            moduleId = "2490b347-7947-4b60-afc4-0054306a277c",
            pubRpc = true,
            version = "1743562543144")
    @AutoGenerated(locked = false, uuid = "180f13e6-6464-3ea4-a884-ae2fff4782d7")
    public List<MenuBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<MenuBaseDto> menuBaseDtoList = menuBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuBaseDtoServiceConverter.MenuBaseDtoConverter(menuBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "654d0a3f-c318-4a03-b239-04fffe9c410b",
            module = "application",
            moduleId = "2490b347-7947-4b60-afc4-0054306a277c",
            pubRpc = true,
            version = "1743562543150")
    @AutoGenerated(locked = false, uuid = "36764f4e-c50a-3a00-94b9-16084b80ecab")
    public List<MenuBaseDto> getByRouterIds(
            @Valid @NotNull(message = "关联路由ID不能为空") List<String> routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        routerId = new ArrayList<>(new HashSet<>(routerId));
        List<MenuBaseDto> menuBaseDtoList = menuBaseDtoManager.getByRouterIds(routerId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuBaseDtoServiceConverter.MenuBaseDtoConverter(menuBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "4d5d7f48-dd32-403a-b998-7041c879a309",
            module = "application",
            moduleId = "2490b347-7947-4b60-afc4-0054306a277c",
            pubRpc = true,
            version = "1743562543147")
    @AutoGenerated(locked = false, uuid = "94df3df0-7a6c-3bde-a930-0c1ace587188")
    public List<MenuBaseDto> getByRouterId(@NotNull(message = "关联路由ID不能为空") String routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRouterIds(Arrays.asList(routerId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "9cb4e726-e5dc-4084-a632-46efcc31d62a",
            module = "application",
            moduleId = "2490b347-7947-4b60-afc4-0054306a277c",
            pubRpc = true,
            version = "1743562543142")
    @AutoGenerated(locked = false, uuid = "dcff04d0-dfc4-3164-b214-6915e8d33741")
    public MenuBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "6186e132-74c7-4c58-9918-6b26b665505b",
            module = "application",
            moduleId = "2490b347-7947-4b60-afc4-0054306a277c",
            pubRpc = true,
            version = "1749008408385")
    @AutoGenerated(locked = false, uuid = "e27199a1-5faa-3c1d-a53a-2b867135d487")
    public List<MenuBaseDto> getByParentIds(
            @Valid @NotNull(message = "父菜单ID不能为空") List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        parentId = new ArrayList<>(new HashSet<>(parentId));
        List<MenuBaseDto> menuBaseDtoList = menuBaseDtoManager.getByParentIds(parentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuBaseDtoServiceConverter.MenuBaseDtoConverter(menuBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "89f70255-c928-4b7d-81e5-56943605ba0e",
            module = "application",
            moduleId = "2490b347-7947-4b60-afc4-0054306a277c",
            pubRpc = true,
            version = "1749008408382")
    @AutoGenerated(locked = false, uuid = "f53ff31e-1a96-3b93-848b-68544a9d7128")
    public List<MenuBaseDto> getByParentId(@NotNull(message = "父菜单ID不能为空") String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByParentIds(Arrays.asList(parentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
