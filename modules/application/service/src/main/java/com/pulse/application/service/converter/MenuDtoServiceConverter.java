package com.pulse.application.service.converter;

import com.pulse.application.manager.dto.MenuDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "b5517171-e013-37cc-bd47-c09436ec42bb")
public class MenuDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<MenuDto> MenuDtoConverter(List<MenuDto> menuDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return menuDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
