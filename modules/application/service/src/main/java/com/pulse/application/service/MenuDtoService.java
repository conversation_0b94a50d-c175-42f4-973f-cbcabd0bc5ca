package com.pulse.application.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuDtoManager;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.service.converter.MenuDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "958b7379-c836-4f60-8c3d-fd3ba952f476|DTO|SERVICE")
public class MenuDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private MenuDtoManager menuDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuDtoServiceConverter menuDtoServiceConverter;

    @PublicInterface(id = "cf57a176-2119-4443-aaef-32a51b755579", module = "application")
    @AutoGenerated(locked = false, uuid = "180f13e6-6464-3ea4-a884-ae2fff4782d7")
    public List<MenuDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<MenuDto> menuDtoList = menuDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuDtoServiceConverter.MenuDtoConverter(menuDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "e9b13fad-11df-4fbe-9f92-1d03705e80c9", module = "application")
    @AutoGenerated(locked = false, uuid = "36764f4e-c50a-3a00-94b9-16084b80ecab")
    public List<MenuDto> getByRouterIds(
            @Valid @NotNull(message = "关联路由ID不能为空") List<String> routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        routerId = new ArrayList<>(new HashSet<>(routerId));
        List<MenuDto> menuDtoList = menuDtoManager.getByRouterIds(routerId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuDtoServiceConverter.MenuDtoConverter(menuDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f44bcb48-0666-49d9-8b6f-64e053162a4c", module = "application")
    @AutoGenerated(locked = false, uuid = "94df3df0-7a6c-3bde-a930-0c1ace587188")
    public List<MenuDto> getByRouterId(@NotNull(message = "关联路由ID不能为空") String routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRouterIds(Arrays.asList(routerId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1e6ac945-965d-43a2-a0a6-57208a47b555", module = "application")
    @AutoGenerated(locked = false, uuid = "dcff04d0-dfc4-3164-b214-6915e8d33741")
    public MenuDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "4397e44a-8228-4da1-b249-d8c500463141", module = "application")
    @AutoGenerated(locked = false, uuid = "e27199a1-5faa-3c1d-a53a-2b867135d487")
    public List<MenuDto> getByParentIds(
            @Valid @NotNull(message = "父菜单ID不能为空") List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        parentId = new ArrayList<>(new HashSet<>(parentId));
        List<MenuDto> menuDtoList = menuDtoManager.getByParentIds(parentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return menuDtoServiceConverter.MenuDtoConverter(menuDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "434000bc-cfc3-4395-ae59-85ab836b13a8", module = "application")
    @AutoGenerated(locked = false, uuid = "f53ff31e-1a96-3b93-848b-68544a9d7128")
    public List<MenuDto> getByParentId(@NotNull(message = "父菜单ID不能为空") String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByParentIds(Arrays.asList(parentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
