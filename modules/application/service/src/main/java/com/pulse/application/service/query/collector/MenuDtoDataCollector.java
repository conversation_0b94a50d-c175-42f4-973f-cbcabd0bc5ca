package com.pulse.application.service.query.collector;

import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.service.MenuBaseDtoService;
import com.pulse.application.service.query.assembler.MenuDtoDataAssembler.MenuDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装MenuDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "dc0fdb2b-b0a1-3e3d-abf9-d7b1dfbb306d")
public class MenuDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuDtoDataCollector menuDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "90af4799-00d0-30ea-ba97-bbd4c65ec927")
    private void fillDataWhenNecessary(MenuDtoDataHolder dataHolder) {
        List<MenuBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.parent == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(MenuBaseDto::getParentId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<MenuBaseDto> baseDtoList =
                    menuBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(MenuBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<MenuBaseDto>> baseDtoMap =
                    baseDtoList.stream().collect(Collectors.groupingBy(MenuBaseDto::getId));
            dataHolder.parent =
                    rootDtoList.stream()
                            .map(MenuBaseDto::getParentId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "a99a3dfb-1895-3539-8b51-c35e9e8bc254")
    public void collectDataDefault(MenuDtoDataHolder dataHolder) {
        menuDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
