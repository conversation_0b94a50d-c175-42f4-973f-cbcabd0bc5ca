package com.pulse.application.service.query;

import com.pulse.application.manager.converter.MenuDtoConverter;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.persist.qto.ListMenuWithParentQto;
import com.pulse.application.service.MenuBaseDtoService;
import com.pulse.application.service.index.entity.ListMenuWithParentQtoService;
import com.pulse.application.service.query.assembler.MenuDtoDataAssembler;
import com.pulse.application.service.query.assembler.MenuDtoDataAssembler.MenuDtoDataHolder;
import com.pulse.application.service.query.collector.MenuDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** MenuDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "992499cf-1002-39ef-b974-c3d71c392bea")
public class MenuDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListMenuWithParentQtoService listMenuWithParentQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuDtoConverter menuDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuDtoDataAssembler menuDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private MenuDtoDataCollector menuDtoDataCollector;

    /** 根据ListMenuWithParentQto查询MenuDto列表,分页 */
    @PublicInterface(id = "7711453e-f4a3-497f-8ee5-cd2e2c8c1502", module = "application")
    @AutoGenerated(locked = false, uuid = "34ed9dcd-9b18-392a-92a9-4c348a719c5b")
    public VSQueryResult<MenuDto> listMenuWithParentPaged(
            @Valid @NotNull ListMenuWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listMenuWithParentQtoService.queryPaged(qto);
        MenuDtoDataHolder dataHolder = new MenuDtoDataHolder();
        List<MenuDto> dtoList = toDtoList(ids, dataHolder);
        menuDtoDataCollector.collectDataDefault(dataHolder);
        menuDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listMenuWithParentQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "baa229db-4f63-311e-97dc-3e4a309c1adf")
    private List<MenuDto> toDtoList(List<String> ids, MenuDtoDataHolder dataHolder) {
        List<MenuBaseDto> baseDtoList = menuBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, MenuDto> dtoMap =
                menuDtoConverter.convertFromMenuBaseDtoToMenuDto(baseDtoList).stream()
                        .collect(Collectors.toMap(MenuDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
