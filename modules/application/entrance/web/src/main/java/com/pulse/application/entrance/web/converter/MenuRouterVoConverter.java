package com.pulse.application.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.entrance.web.query.assembler.MenuRouterVoDataAssembler;
import com.pulse.application.entrance.web.query.assembler.MenuRouterVoDataAssembler.MenuRouterVoDataHolder;
import com.pulse.application.entrance.web.query.collector.MenuRouterVoDataCollector;
import com.pulse.application.entrance.web.vo.MenuRouterVo;
import com.pulse.application.entrance.web.vo.RouterFeatureVo;
import com.pulse.application.manager.dto.MenuRouterDto;
import com.pulse.application.manager.dto.RouterDto;
import com.pulse.application.service.MenuBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到MenuRouterVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9c6f08d3-fa9f-4a95-ac8b-399989b7b643|VO|CONVERTER")
public class MenuRouterVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuRouterVoDataAssembler menuRouterVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private MenuRouterVoDataCollector menuRouterVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private RouterFeatureVoConverter routerFeatureVoConverter;

    /** 使用默认方式组装MenuRouterVo列表数据 */
    @AutoGenerated(locked = true, uuid = "6b84051b-c09b-38a7-988d-4ca394ce74bf")
    public List<MenuRouterVo> convertAndAssembleDataList(List<MenuRouterDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        MenuRouterVoDataHolder dataHolder = new MenuRouterVoDataHolder();
        dataHolder.setRootBaseDtoList(
                menuBaseDtoService.getByIds(
                        dtoList.stream().map(MenuRouterDto::getId).collect(Collectors.toList())));
        Map<String, MenuRouterVo> voMap =
                convertToMenuRouterVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        menuRouterVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        menuRouterVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把MenuRouterDto转换成MenuRouterVo */
    @AutoGenerated(locked = false, uuid = "9c6f08d3-fa9f-4a95-ac8b-399989b7b643-converter-Map")
    public Map<MenuRouterDto, MenuRouterVo> convertToMenuRouterVoMap(List<MenuRouterDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<RouterDto, RouterFeatureVo> routerMap =
                routerFeatureVoConverter.convertToRouterFeatureVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(MenuRouterDto::getRouter)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<MenuRouterDto, MenuRouterVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            MenuRouterVo vo = new MenuRouterVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setMenuLogo(dto.getMenuLogo());
                                            vo.setRouter(
                                                    dto.getRouter() == null
                                                            ? null
                                                            : routerMap.get(dto.getRouter()));
                                            vo.setParentId(dto.getParentId());
                                            vo.setSameLevelGroupNumber(
                                                    dto.getSameLevelGroupNumber());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setVisibleFlag(dto.getVisibleFlag());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setShortcutKey(dto.getShortcutKey());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setMenuType(dto.getMenuType());
                                            vo.setRemark(dto.getRemark());
                                            vo.setCode(dto.getCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把MenuRouterDto转换成MenuRouterVo */
    @AutoGenerated(locked = true, uuid = "9c6f08d3-fa9f-4a95-ac8b-399989b7b643-converter-list")
    public List<MenuRouterVo> convertToMenuRouterVoList(List<MenuRouterDto> dtoList) {
        return new ArrayList<>(convertToMenuRouterVoMap(dtoList).values());
    }

    /** 把MenuRouterDto转换成MenuRouterVo */
    @AutoGenerated(locked = true, uuid = "bc28a650-7149-3aa6-850c-56faf88e70d9")
    public MenuRouterVo convertToMenuRouterVo(MenuRouterDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToMenuRouterVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装MenuRouterVo数据 */
    @AutoGenerated(locked = true, uuid = "c212b97d-9203-3fb0-b97d-1142eb7725a0")
    public MenuRouterVo convertAndAssembleData(MenuRouterDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
