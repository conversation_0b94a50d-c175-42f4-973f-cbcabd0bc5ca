package com.pulse.application.entrance.web.vo;

import com.pulse.application.common.enums.MenuEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9c6f08d3-fa9f-4a95-ac8b-399989b7b643|VO|DEFINITION")
public class MenuRouterVo {
    /** 菜单编码 */
    @AutoGenerated(locked = true, uuid = "e39a3413-ebc1-4468-a5a0-fa8e576d3a1c")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "5cc6b084-e37e-47eb-9610-6976e14f9472")
    private Date createdAt;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "3ded087a-f76c-433e-84ff-1b3eb40da798")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3a655e88-9e8d-4482-b27b-5ff47b28fbb9")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "16cbcbbd-cc85-4b94-886d-55e4096d725b")
    private InputCodeEo inputCode;

    /** 菜单图标 */
    @AutoGenerated(locked = true, uuid = "58426ec7-9d4d-4ed9-b8dd-5df46866b45a")
    private String menuLogo;

    /** 菜单类型 */
    @AutoGenerated(locked = true, uuid = "d00093b8-2837-4b6c-9142-ed21488bc5df")
    private MenuEnum menuType;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "0102bf39-2145-4ced-957a-9dd6b258aacc")
    private String name;

    /** 父菜单ID */
    @AutoGenerated(locked = true, uuid = "2e410db7-87f5-402f-a0cd-97c1330ecdad")
    private String parentId;

    /** 菜单说明 */
    @AutoGenerated(locked = true, uuid = "98e40ccf-6935-42b1-923c-265e43169bc7")
    private String remark;

    /** 关联路由 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b81360ec-b4bf-4c92-a71e-b5e56e008068")
    private RouterFeatureVo router;

    /** 同级分组号 */
    @AutoGenerated(locked = true, uuid = "3e7758a1-6bf8-4dab-8647-eff701a68bee")
    private String sameLevelGroupNumber;

    /** 快捷键 */
    @AutoGenerated(locked = true, uuid = "*************-4e1d-b81e-278977c9e746")
    private String shortcutKey;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "38638537-638e-498d-b7cb-ed5a8588f0ad")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "eb00d32d-0828-4493-9831-9834c2e0ad9e")
    private Date updatedAt;

    /** 可见标识 */
    @AutoGenerated(locked = true, uuid = "e78d1fc0-1892-43b9-a8e7-c71754317cbb")
    private Boolean visibleFlag;
}
