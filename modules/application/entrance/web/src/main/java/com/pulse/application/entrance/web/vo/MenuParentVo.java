package com.pulse.application.entrance.web.vo;

import com.pulse.application.common.enums.MenuEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "62baba74-a641-48dd-9cf8-d9a50a16a79e|VO|DEFINITION")
public class MenuParentVo {
    /** 菜单编码 */
    @AutoGenerated(locked = true, uuid = "a9fc6c89-7309-4979-b5e8-9d6d661422a3")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "1cb0b3b3-aad3-45f5-9e47-6cc0c0f23e9e")
    private Date createdAt;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "0988c31e-0c54-473e-8e65-67adebfe2ee9")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "323c10b6-34c3-4bec-857d-ddfd0dac8175")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "32a1e294-ec0f-4511-8026-dcdde5c62ab3")
    private InputCodeEo inputCode;

    /** 菜单图标 */
    @AutoGenerated(locked = true, uuid = "fe634192-2b15-4658-bc11-99ce29e1f362")
    private String menuLogo;

    /** 菜单类型 */
    @AutoGenerated(locked = true, uuid = "4c8aaf7a-1e4d-49c0-b432-434290c99330")
    private MenuEnum menuType;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "a1d32deb-278d-4541-93f2-31ceb3d03a2c")
    private String name;

    /** 父菜单ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "59e00069-fe26-499c-a156-54ce606b47d2")
    private MenuBaseVo parent;

    /** 菜单说明 */
    @AutoGenerated(locked = true, uuid = "7ae680bf-33c9-4178-9601-8ff944210947")
    private String remark;

    /** 关联路由ID */
    @AutoGenerated(locked = true, uuid = "692fe3e0-8d5c-43f1-b5f7-f1c8aa7af80b")
    private String routerId;

    /** 同级分组号 */
    @AutoGenerated(locked = true, uuid = "eaa3c42a-995f-4efb-859b-2c0865c799fb")
    private String sameLevelGroupNumber;

    /** 快捷键 */
    @AutoGenerated(locked = true, uuid = "de3da481-5471-4955-ba0b-536819a48edf")
    private String shortcutKey;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "55db6544-3bf4-4592-a0b2-73eb9e10a370")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "828dc88d-ff1a-4987-8cff-7009f3fc348d")
    private Date updatedAt;

    /** 可见标识 */
    @AutoGenerated(locked = true, uuid = "a1a60cf0-592a-41c3-8954-6c495bc48ec1")
    private Boolean visibleFlag;
}
