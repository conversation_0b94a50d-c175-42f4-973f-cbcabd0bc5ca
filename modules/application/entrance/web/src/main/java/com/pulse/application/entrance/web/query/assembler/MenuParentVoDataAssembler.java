package com.pulse.application.entrance.web.query.assembler;

import com.pulse.application.entrance.web.vo.MenuBaseVo;
import com.pulse.application.entrance.web.vo.MenuParentVo;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.service.MenuBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** MenuParentVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "21a06e43-d191-3e3e-9c97-895a6e5494fe")
public class MenuParentVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    /** 批量自定义组装MenuParentVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "d791f80d-bde2-39e3-b364-445b4d827e5d")
    public void assembleDataCustomized(List<MenuParentVo> dataList) {
        // 自定义数据组装

    }

    /** 组装MenuParentVo数据 */
    @AutoGenerated(locked = true, uuid = "e0f90dc0-740d-3152-9d27-7a02e4f2ab61")
    public void assembleData(
            Map<String, MenuParentVo> voMap,
            MenuParentVoDataAssembler.MenuParentVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<MenuBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<MenuBaseDto, MenuBaseVo>> parent =
                dataHolder.parent.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.parent.get(dto)),
                                        (o1, o2) -> o1));

        for (MenuBaseDto baseDto : baseDtoList) {
            MenuParentVo vo = voMap.get(baseDto.getId());
            vo.setParent(
                    Optional.ofNullable(parent.get(baseDto.getParentId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class MenuParentVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<MenuBaseDto> rootBaseDtoList;

        /** 持有字段parent的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<MenuBaseDto, MenuBaseVo> parent;
    }
}
