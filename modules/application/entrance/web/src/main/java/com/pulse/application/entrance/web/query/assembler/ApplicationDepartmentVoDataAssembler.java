package com.pulse.application.entrance.web.query.assembler;

import com.pulse.application.entrance.web.vo.ApplicationDepartmentVo;
import com.pulse.application.manager.dto.ApplicationDepartmentBaseDto;
import com.pulse.application.service.ApplicationDepartmentBaseDtoService;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ApplicationDepartmentVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "371f5e00-dcda-387a-b59f-7d50361d8d07")
public class ApplicationDepartmentVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentBaseDtoService applicationDepartmentBaseDtoService;

    /** 组装ApplicationDepartmentVo数据 */
    @AutoGenerated(locked = true, uuid = "9db12f9b-7b15-3ecf-80fa-7ab5a2a3452f")
    public void assembleData(
            Map<String, ApplicationDepartmentVo> voMap,
            ApplicationDepartmentVoDataAssembler.ApplicationDepartmentVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ApplicationDepartmentBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<OrganizationBaseDto, ApplicationDepartmentVo.OrganizationBaseVo>>
                department =
                        dataHolder.department.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto -> Pair.of(dto, dataHolder.department.get(dto)),
                                                (o1, o2) -> o1));

        for (ApplicationDepartmentBaseDto baseDto : baseDtoList) {
            ApplicationDepartmentVo vo = voMap.get(baseDto.getId());
            vo.setDepartment(
                    Optional.ofNullable(department.get(baseDto.getDepartmentId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ApplicationDepartmentVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b50829a2-34e1-30e6-8148-ea8b362083c6")
    public void assembleDataCustomized(List<ApplicationDepartmentVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ApplicationDepartmentVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ApplicationDepartmentBaseDto> rootBaseDtoList;

        /** 持有字段department的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<OrganizationBaseDto, ApplicationDepartmentVo.OrganizationBaseVo> department;
    }
}
