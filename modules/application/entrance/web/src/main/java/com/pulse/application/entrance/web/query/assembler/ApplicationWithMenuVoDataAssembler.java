package com.pulse.application.entrance.web.query.assembler;

import com.pulse.application.entrance.web.vo.ApplicationMenuCodeVo;
import com.pulse.application.entrance.web.vo.ApplicationWithMenuVo;
import com.pulse.application.entrance.web.vo.MenuBaseVo;
import com.pulse.application.manager.dto.ApplicationMenuBaseDto;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.service.ApplicationMenuBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ApplicationWithMenuVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "a3329b00-d846-321b-93ad-30eef6b34f94")
public class ApplicationWithMenuVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationMenuBaseDtoService applicationMenuBaseDtoService;

    /** 组装ApplicationWithMenuVo数据 */
    @AutoGenerated(locked = true, uuid = "0b14b003-3a74-3340-a7cd-084dae8b8d3c")
    public void assembleData(
            Map<String, ApplicationWithMenuVo> voMap,
            ApplicationWithMenuVoDataAssembler.ApplicationWithMenuVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ApplicationMenuBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<MenuBaseDto, MenuBaseVo>> menuCode =
                dataHolder.menuCode.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.menuCode.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<ApplicationMenuBaseDto, ApplicationMenuCodeVo>> parentMenu =
                dataHolder.parentMenu.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.parentMenu.get(dto)),
                                        (o1, o2) -> o1));

        for (ApplicationMenuBaseDto baseDto : baseDtoList) {
            ApplicationWithMenuVo vo = voMap.get(baseDto.getId());
            vo.setMenuCode(
                    Optional.ofNullable(menuCode.get(baseDto.getMenuId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setParentMenu(
                    Optional.ofNullable(parentMenu.get(baseDto.getParentMenuId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleParentMenuData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 组装parentMenu数据 */
    @AutoGenerated(locked = true, uuid = "2673c037-1997-359c-973f-95688bc7e925")
    private void assembleParentMenuData(
            ApplicationWithMenuVoDataAssembler.ApplicationWithMenuVoDataHolder dataHolder) {
        Map<String, Pair<MenuBaseDto, MenuBaseVo>> parentMenu2MenuCode =
                dataHolder.parentMenu2MenuCode.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto ->
                                                Pair.of(
                                                        dto,
                                                        dataHolder.parentMenu2MenuCode.get(dto)),
                                        (o1, o2) -> o1));
        for (Map.Entry<ApplicationMenuBaseDto, ApplicationMenuCodeVo> parentMenu :
                dataHolder.parentMenu.entrySet()) {
            ApplicationMenuBaseDto baseDto = parentMenu.getKey();
            ApplicationMenuCodeVo vo = parentMenu.getValue();
            vo.setMenuCode(
                    Optional.ofNullable(parentMenu2MenuCode.get(baseDto.getMenuId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 批量自定义组装ApplicationWithMenuVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "eb3a3f3d-1aae-3069-b55b-f27aa0456689")
    public void assembleDataCustomized(List<ApplicationWithMenuVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ApplicationWithMenuVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ApplicationMenuBaseDto> rootBaseDtoList;

        /** 持有字段menuCode的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<MenuBaseDto, MenuBaseVo> menuCode;

        /** 持有字段parentMenu的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ApplicationMenuBaseDto, ApplicationMenuCodeVo> parentMenu;

        /** 持有字段parentMenu.menuCode的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<MenuBaseDto, MenuBaseVo> parentMenu2MenuCode;
    }
}
