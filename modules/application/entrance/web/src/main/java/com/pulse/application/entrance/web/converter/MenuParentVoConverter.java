package com.pulse.application.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.entrance.web.query.assembler.MenuParentVoDataAssembler;
import com.pulse.application.entrance.web.query.assembler.MenuParentVoDataAssembler.MenuParentVoDataHolder;
import com.pulse.application.entrance.web.query.collector.MenuParentVoDataCollector;
import com.pulse.application.entrance.web.vo.MenuBaseVo;
import com.pulse.application.entrance.web.vo.MenuParentVo;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.service.MenuBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到MenuParentVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "62baba74-a641-48dd-9cf8-d9a50a16a79e|VO|CONVERTER")
public class MenuParentVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseVoConverter menuBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuParentVoDataAssembler menuParentVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private MenuParentVoDataCollector menuParentVoDataCollector;

    /** 把MenuDto转换成MenuParentVo */
    @AutoGenerated(locked = false, uuid = "62baba74-a641-48dd-9cf8-d9a50a16a79e-converter-Map")
    public Map<MenuDto, MenuParentVo> convertToMenuParentVoMap(List<MenuDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<MenuBaseDto, MenuBaseVo> parentMap =
                menuBaseVoConverter.convertToMenuBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(MenuDto::getParent)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<MenuDto, MenuParentVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            MenuParentVo vo = new MenuParentVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setMenuLogo(dto.getMenuLogo());
                                            vo.setRouterId(dto.getRouterId());
                                            vo.setParent(
                                                    dto.getParent() == null
                                                            ? null
                                                            : parentMap.get(dto.getParent()));
                                            vo.setSameLevelGroupNumber(
                                                    dto.getSameLevelGroupNumber());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setVisibleFlag(dto.getVisibleFlag());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setShortcutKey(dto.getShortcutKey());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setMenuType(dto.getMenuType());
                                            vo.setRemark(dto.getRemark());
                                            vo.setCode(dto.getCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把MenuDto转换成MenuParentVo */
    @AutoGenerated(locked = true, uuid = "62baba74-a641-48dd-9cf8-d9a50a16a79e-converter-list")
    public List<MenuParentVo> convertToMenuParentVoList(List<MenuDto> dtoList) {
        return new ArrayList<>(convertToMenuParentVoMap(dtoList).values());
    }

    /** 把MenuDto转换成MenuParentVo */
    @AutoGenerated(locked = true, uuid = "85068ef8-0069-3537-b750-d69c894f00b3")
    public MenuParentVo convertToMenuParentVo(MenuDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToMenuParentVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装MenuParentVo数据 */
    @AutoGenerated(locked = true, uuid = "e67477f2-358e-34e6-98e8-791d927df4b0")
    public MenuParentVo convertAndAssembleData(MenuDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装MenuParentVo列表数据 */
    @AutoGenerated(locked = true, uuid = "e7b80f72-5de6-3659-90cd-51570d5fb5f0")
    public List<MenuParentVo> convertAndAssembleDataList(List<MenuDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        MenuParentVoDataHolder dataHolder = new MenuParentVoDataHolder();
        dataHolder.setRootBaseDtoList(
                menuBaseDtoService.getByIds(
                        dtoList.stream().map(MenuDto::getId).collect(Collectors.toList())));
        Map<String, MenuParentVo> voMap =
                convertToMenuParentVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        menuParentVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        menuParentVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
