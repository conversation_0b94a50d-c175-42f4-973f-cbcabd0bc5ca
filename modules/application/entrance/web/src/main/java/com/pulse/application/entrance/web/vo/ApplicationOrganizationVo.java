package com.pulse.application.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "e72c0f8c-0040-4520-8555-9396adee7016|VO|DEFINITION")
public class ApplicationOrganizationVo {
    /** 应用ID */
    @AutoGenerated(locked = true, uuid = "5718bb69-c2c9-4c17-bb37-1a2dcd3d3b37")
    private String applicationId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "8f6e1237-bee2-4437-9b18-c865b2d31473")
    private Date createdAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "a0825aa0-336e-443d-a29e-b2bfb7aaf6f4")
    private String description;

    /** 急诊 */
    @AutoGenerated(locked = true, uuid = "6575bd76-e18c-4d75-abc7-ce04581dfb12")
    private Boolean emergencyFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "db53138f-fdb2-4a6f-ba65-b9d354bc00d5")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0830e13c-3e12-4c27-b9f8-52287d6e805d")
    private InputCodeEo inputCode;

    /** 库存 */
    @AutoGenerated(locked = true, uuid = "448c1020-68a7-41dd-8b6e-ec288eef1abc")
    private String inventory;

    /** 分组名称 */
    @AutoGenerated(locked = true, uuid = "e7d6da0a-e288-4a27-a18e-41d9fc7f0f3b")
    private String organizationId;

    /** 门诊费用 */
    @AutoGenerated(locked = true, uuid = "ba1b5847-adc3-426f-8f8f-7ccc1f27bdc0")
    private Boolean outpatientFeeFlag;

    /** 药房标志 */
    @AutoGenerated(locked = true, uuid = "d8f06638-6492-4ddb-a51d-7ac6d9e4d72a")
    private Boolean pharmacyFlag;

    /** 应用简称 */
    @AutoGenerated(locked = true, uuid = "18f8204e-63e5-4b79-931a-ac15d870d374")
    private String shortName;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "d4046e7a-4e08-462f-af50-fc6bd5fffb3c")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "a04980f7-fe4c-463e-9fc8-0a9ff91f11c4")
    private Date updatedAt;
}
