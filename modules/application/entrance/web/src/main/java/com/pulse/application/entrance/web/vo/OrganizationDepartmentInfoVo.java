package com.pulse.application.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "1ca7b943-bb2a-4986-ab61-c3fa0b940845|VO|DEFINITION")
public class OrganizationDepartmentInfoVo {
    /** 应用含科室 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1b47d92c-554f-4867-b2be-1c8b88975332")
    private ApplicationWithDepartmentVo application;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9688d675-4e84-49e9-880c-e46f29f00320")
    private Date createdAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "0a0d3b48-75e7-4622-9348-8d3a29f6c4e3")
    private String description;

    /** 急诊 */
    @AutoGenerated(locked = true, uuid = "2ce33a6c-4c93-4728-ae0f-b71688d04e3b")
    private Boolean emergencyFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "01f72c51-8073-4b16-9145-d1cf1cdf0a05")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "70fe9518-1498-4320-8d57-9ce99961cab4")
    private InputCodeEo inputCode;

    /** 库存 */
    @AutoGenerated(locked = true, uuid = "4f8696a5-6bcb-44da-92ef-63051b18d6d6")
    private String inventory;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "d305f9a4-c42c-4d34-9a07-5f295124f3df")
    private String organizationId;

    /** 门诊费用 */
    @AutoGenerated(locked = true, uuid = "4584f2a0-851d-4093-8c01-5655481968f4")
    private Boolean outpatientFeeFlag;

    /** 药房标志 */
    @AutoGenerated(locked = true, uuid = "a3313e65-9751-4409-a902-5cbe9919cfb3")
    private Boolean pharmacyFlag;

    /** 应用简称 */
    @AutoGenerated(locked = true, uuid = "f0673253-e03e-4094-ae25-6cb4f4724a00")
    private String shortName;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "a088d4de-d546-48b8-9002-7b097856131d")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "c4e67bc7-b919-489d-876a-cb1f535c7100")
    private Date updatedAt;
}
