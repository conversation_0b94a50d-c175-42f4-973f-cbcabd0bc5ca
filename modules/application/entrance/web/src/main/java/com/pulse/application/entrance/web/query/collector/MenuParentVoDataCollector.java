package com.pulse.application.entrance.web.query.collector;

import com.pulse.application.entrance.web.converter.MenuBaseVoConverter;
import com.pulse.application.entrance.web.converter.MenuParentVoConverter;
import com.pulse.application.entrance.web.query.assembler.MenuParentVoDataAssembler.MenuParentVoDataHolder;
import com.pulse.application.entrance.web.vo.MenuBaseVo;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.service.MenuBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装MenuParentVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "44d6dd3e-3db0-3a7c-b346-d8d8b60fa8b5")
public class MenuParentVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoService menuBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseVoConverter menuBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuParentVoConverter menuParentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuParentVoDataCollector menuParentVoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "1a4b1bf2-641f-3fb3-a3f8-de0dfb560afc")
    private void fillDataWhenNecessary(MenuParentVoDataHolder dataHolder) {
        List<MenuBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.parent == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(MenuBaseDto::getParentId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<MenuBaseDto> baseDtoList =
                    menuBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(MenuBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<MenuBaseDto>> baseDtoMap =
                    baseDtoList.stream().collect(Collectors.groupingBy(MenuBaseDto::getId));
            Map<MenuBaseDto, MenuBaseVo> dtoVoMap =
                    menuBaseVoConverter.convertToMenuBaseVoMap(baseDtoList);
            Map<MenuBaseDto, MenuBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.parent =
                    rootDtoList.stream()
                            .map(MenuBaseDto::getParentId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取MenuDto数据填充MenuParentVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "26fb9206-09e3-34ee-8b24-90c692530ba9")
    public void collectDataWithDtoData(List<MenuDto> dtoList, MenuParentVoDataHolder dataHolder) {
        List<MenuBaseDto> parentList = new ArrayList<>();

        for (MenuDto rootDto : dtoList) {
            MenuBaseDto parentDto = rootDto.getParent();
            if (parentDto != null) {
                parentList.add(parentDto);
            }
        }

        // access parent
        Map<MenuBaseDto, MenuBaseVo> parentVoMap =
                menuBaseVoConverter.convertToMenuBaseVoMap(parentList);
        dataHolder.parent =
                parentList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> parentVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "b8c06def-5377-3d48-9a3e-1071e584f5f5")
    public void collectDataDefault(MenuParentVoDataHolder dataHolder) {
        menuParentVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
