package com.pulse.application.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuBaseDtoManager;
import com.pulse.application.manager.MenuRouterDtoManager;
import com.pulse.application.manager.RouterDtoManager;
import com.pulse.application.manager.converter.MenuBaseDtoConverter;
import com.pulse.application.manager.converter.MenuRouterDtoConverter;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuRouterDto;
import com.pulse.application.manager.dto.RouterDto;
import com.pulse.application.persist.dos.Menu;
import com.pulse.application.persist.mapper.MenuDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "f36e4344-2753-4659-9f5a-bd74f864015e|DTO|BASE_MANAGER_IMPL")
public abstract class MenuRouterDtoManagerBaseImpl implements MenuRouterDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoConverter menuBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoManager menuBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuDao menuDao;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuRouterDtoConverter menuRouterDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RouterDtoManager routerDtoManager;

    @AutoGenerated(locked = true, uuid = "0005a183-b2e0-377d-8ba0-85dd286bec3d")
    @Override
    public List<MenuRouterDto> getByParentIds(List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(parentId)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByParentIds(parentId);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return doConvertFromMenuToMenuRouterDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "19a50a44-2f5e-312c-895a-63b39001cbe3")
    @Override
    public MenuRouterDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuRouterDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        MenuRouterDto menuRouterDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return menuRouterDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "358011d4-5b53-3f05-9ac5-277619af34df")
    public List<MenuRouterDto> doConvertFromMenuToMenuRouterDto(List<Menu> menuList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        Map<String, String> routerIdMap =
                menuList.stream()
                        .filter(i -> i.getRouterId() != null)
                        .collect(Collectors.toMap(Menu::getId, Menu::getRouterId));
        List<RouterDto> routerIdRouterDtoList =
                routerDtoManager.getByIds(new ArrayList<>(new HashSet<>(routerIdMap.values())));
        Map<String, RouterDto> routerIdRouterDtoMapRaw =
                routerIdRouterDtoList.stream().collect(Collectors.toMap(RouterDto::getId, i -> i));
        Map<String, RouterDto> routerIdRouterDtoMap =
                routerIdMap.entrySet().stream()
                        .filter(i -> routerIdRouterDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> routerIdRouterDtoMapRaw.get(i.getValue())));

        List<MenuBaseDto> baseDtoList = menuBaseDtoConverter.convertFromMenuToMenuBaseDto(menuList);
        Map<String, MenuRouterDto> dtoMap =
                menuRouterDtoConverter.convertFromMenuBaseDtoToMenuRouterDto(baseDtoList).stream()
                        .collect(
                                Collectors.toMap(
                                        MenuRouterDto::getId, Function.identity(), (o1, o2) -> o1));

        List<MenuRouterDto> menuRouterDtoList = new ArrayList<>();
        for (Menu i : menuList) {
            MenuRouterDto menuRouterDto = dtoMap.get(i.getId());
            if (menuRouterDto == null) {
                continue;
            }

            if (null != i.getRouterId()) {
                menuRouterDto.setRouter(routerIdRouterDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            menuRouterDtoList.add(menuRouterDto);
        }
        return menuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "3f6c4db6-f7bc-3570-9818-aeb5b3cdca07")
    @Override
    public List<MenuRouterDto> getByRouterId(String routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuRouterDto> menuRouterDtoList = getByRouterIds(Arrays.asList(routerId));
        return menuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5c89c861-a12d-314e-8cee-25f04e01e5c9")
    @Override
    public List<MenuRouterDto> getByParentId(String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuRouterDto> menuRouterDtoList = getByParentIds(Arrays.asList(parentId));
        return menuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5ec4294f-bd73-3883-a191-f16ab9eeb5cd")
    @Override
    public List<MenuRouterDto> getByRouterIds(List<String> routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(routerId)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByRouterIds(routerId);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return doConvertFromMenuToMenuRouterDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c024bbb9-03dd-3b98-a79c-a3e9b7533376")
    @Override
    public List<MenuRouterDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByIds(id);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Menu> menuMap =
                menuList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        menuList =
                id.stream()
                        .map(i -> menuMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromMenuToMenuRouterDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
