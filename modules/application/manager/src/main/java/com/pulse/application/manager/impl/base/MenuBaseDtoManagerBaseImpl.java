package com.pulse.application.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuBaseDtoManager;
import com.pulse.application.manager.converter.MenuBaseDtoConverter;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.persist.dos.Menu;
import com.pulse.application.persist.mapper.MenuDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "89f13610-7112-4692-8ded-95a77ac06804|DTO|BASE_MANAGER_IMPL")
public abstract class MenuBaseDtoManagerBaseImpl implements MenuBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoConverter menuBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuDao menuDao;

    @AutoGenerated(locked = true, uuid = "0005a183-b2e0-377d-8ba0-85dd286bec3d")
    @Override
    public List<MenuBaseDto> getByParentIds(List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(parentId)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByParentIds(parentId);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return doConvertFromMenuToMenuBaseDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "19a50a44-2f5e-312c-895a-63b39001cbe3")
    @Override
    public MenuBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        MenuBaseDto menuBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return menuBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "20b1b75b-a767-3cf1-9096-e93b13a33eac")
    public List<MenuBaseDto> doConvertFromMenuToMenuBaseDto(List<Menu> menuList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        Map<String, MenuBaseDto> dtoMap =
                menuBaseDtoConverter.convertFromMenuToMenuBaseDto(menuList).stream()
                        .collect(
                                Collectors.toMap(
                                        MenuBaseDto::getId, Function.identity(), (o1, o2) -> o1));

        List<MenuBaseDto> menuBaseDtoList = new ArrayList<>();
        for (Menu i : menuList) {
            MenuBaseDto menuBaseDto = dtoMap.get(i.getId());
            if (menuBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            menuBaseDtoList.add(menuBaseDto);
        }
        return menuBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "3f6c4db6-f7bc-3570-9818-aeb5b3cdca07")
    @Override
    public List<MenuBaseDto> getByRouterId(String routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuBaseDto> menuBaseDtoList = getByRouterIds(Arrays.asList(routerId));
        return menuBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5c89c861-a12d-314e-8cee-25f04e01e5c9")
    @Override
    public List<MenuBaseDto> getByParentId(String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuBaseDto> menuBaseDtoList = getByParentIds(Arrays.asList(parentId));
        return menuBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5ec4294f-bd73-3883-a191-f16ab9eeb5cd")
    @Override
    public List<MenuBaseDto> getByRouterIds(List<String> routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(routerId)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByRouterIds(routerId);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return doConvertFromMenuToMenuBaseDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c024bbb9-03dd-3b98-a79c-a3e9b7533376")
    @Override
    public List<MenuBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByIds(id);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Menu> menuMap =
                menuList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        menuList =
                id.stream()
                        .map(i -> menuMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromMenuToMenuBaseDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
