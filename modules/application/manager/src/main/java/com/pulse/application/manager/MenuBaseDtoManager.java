package com.pulse.application.manager;

import com.pulse.application.manager.dto.MenuBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "89f13610-7112-4692-8ded-95a77ac06804|DTO|MANAGER")
public interface MenuBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "0c08a0fe-59b8-3811-8b65-451106e81e65")
    List<MenuBaseDto> getByParentIds(List<String> parentId);

    @AutoGenerated(locked = true, uuid = "103dede7-38bb-3cd4-9dca-b62dc870d1f9")
    List<MenuBaseDto> getByRouterId(String routerId);

    @AutoGenerated(locked = true, uuid = "3c6160a0-348f-32f3-8c3c-0284e1a1c4cf")
    List<MenuBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "495ccff1-751c-3099-96e9-81719280c640")
    List<MenuBaseDto> getByRouterIds(List<String> routerId);

    @AutoGenerated(locked = true, uuid = "e38a3cc5-0933-355d-a875-80233ed7bbf6")
    MenuBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "e489e367-1eb2-32fb-b187-1028d1d72d14")
    List<MenuBaseDto> getByParentId(String parentId);
}
