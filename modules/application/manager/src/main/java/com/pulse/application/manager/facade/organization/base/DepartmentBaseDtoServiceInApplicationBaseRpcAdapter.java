package com.pulse.application.manager.facade.organization.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "8cb9929e-3e80-3132-8df4-711e0fd14b1e")
public class DepartmentBaseDtoServiceInApplicationBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "dcc9406c-cce0-4a6b-83a2-1eb27db1be39|RPC|BASE_ADAPTER")
    public List<DepartmentBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/organization/dcc9406c-cce0-4a6b-83a2-1eb27db1be39/DepartmentBaseDtoService-getByIds",
                        "com.pulse.organization.service.DepartmentBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "2490b347-7947-4b60-afc4-0054306a277c",
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c"),
                new TypeReference<>() {});
    }
}
