package com.pulse.application.manager.dto;

import com.pulse.application.common.enums.MenuEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "958b7379-c836-4f60-8c3d-fd3ba952f476|DTO|DEFINITION")
public class MenuDto {
    /** 菜单编码 */
    @AutoGenerated(locked = true, uuid = "5cc49c16-e3d6-46c2-b418-28f36bb1d295")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "36aab37c-a1a1-4bb0-9714-548f9de90990")
    private Date createdAt;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "dc589dc0-169a-4017-9b4c-8b0551ab9c91")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f9861185-1734-47ac-ad96-459d898453ed")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ba4155b2-74e4-448b-beb4-7b594e387b49")
    private InputCodeEo inputCode;

    /** 菜单图标 */
    @AutoGenerated(locked = true, uuid = "9930910b-a15d-496f-8685-4015a1c81add")
    private String menuLogo;

    /** 菜单类型 */
    @AutoGenerated(locked = true, uuid = "37c45c84-2ce7-4cee-b063-13a11efde695")
    private MenuEnum menuType;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "4b4f2a96-0f92-4ef6-80d0-db978a90a5a8")
    private String name;

    /** 父菜单ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "05677fcb-f5b1-4e31-8369-5ffcba995925")
    private MenuBaseDto parent;

    /** 菜单说明 */
    @AutoGenerated(locked = true, uuid = "4fe19529-b0b9-4ce4-9681-b7f122261dea")
    private String remark;

    /** 关联路由ID */
    @AutoGenerated(locked = true, uuid = "589516cd-ae93-4e35-9fed-d46d41ab06a1")
    private String routerId;

    /** 同级分组号 */
    @AutoGenerated(locked = true, uuid = "9bb05ea2-9587-407a-8f7c-928a63a76b61")
    private String sameLevelGroupNumber;

    /** 快捷键 */
    @AutoGenerated(locked = true, uuid = "c5477a8a-4ad1-4a31-93a3-4ff4dc536eaf")
    private String shortcutKey;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "3e1d77e0-8247-4588-af5d-e79f8830f83b")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "0f6f610c-81b0-447f-b683-cfcf45afd2d8")
    private Date updatedAt;

    /** 可见标识 */
    @AutoGenerated(locked = true, uuid = "c6edd621-d608-485a-96f2-4a03a008cf24")
    private Boolean visibleFlag;
}
