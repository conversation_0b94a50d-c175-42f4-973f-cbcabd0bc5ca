package com.pulse.application.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuBaseDtoManager;
import com.pulse.application.manager.MenuDtoManager;
import com.pulse.application.manager.converter.MenuBaseDtoConverter;
import com.pulse.application.manager.converter.MenuDtoConverter;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.persist.dos.Menu;
import com.pulse.application.persist.mapper.MenuDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "958b7379-c836-4f60-8c3d-fd3ba952f476|DTO|BASE_MANAGER_IMPL")
public abstract class MenuDtoManagerBaseImpl implements MenuDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoConverter menuBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuBaseDtoManager menuBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuDao menuDao;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuDtoConverter menuDtoConverter;

    @AutoGenerated(locked = true, uuid = "0005a183-b2e0-377d-8ba0-85dd286bec3d")
    @Override
    public List<MenuDto> getByParentIds(List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(parentId)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByParentIds(parentId);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return doConvertFromMenuToMenuDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "01a58f17-0de8-364c-b0f2-6659282fb057")
    public List<MenuDto> doConvertFromMenuToMenuDto(List<Menu> menuList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        Map<String, String> parentIdMap =
                menuList.stream()
                        .filter(i -> i.getParentId() != null)
                        .collect(Collectors.toMap(Menu::getId, Menu::getParentId));
        List<MenuBaseDto> parentIdMenuBaseDtoList =
                menuBaseDtoManager.getByIds(new ArrayList<>(new HashSet<>(parentIdMap.values())));
        Map<String, MenuBaseDto> parentIdMenuBaseDtoMapRaw =
                parentIdMenuBaseDtoList.stream()
                        .collect(Collectors.toMap(MenuBaseDto::getId, i -> i));
        Map<String, MenuBaseDto> parentIdMenuBaseDtoMap =
                parentIdMap.entrySet().stream()
                        .filter(i -> parentIdMenuBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> parentIdMenuBaseDtoMapRaw.get(i.getValue())));

        List<MenuBaseDto> baseDtoList = menuBaseDtoConverter.convertFromMenuToMenuBaseDto(menuList);
        Map<String, MenuDto> dtoMap =
                menuDtoConverter.convertFromMenuBaseDtoToMenuDto(baseDtoList).stream()
                        .collect(
                                Collectors.toMap(
                                        MenuDto::getId, Function.identity(), (o1, o2) -> o1));

        List<MenuDto> menuDtoList = new ArrayList<>();
        for (Menu i : menuList) {
            MenuDto menuDto = dtoMap.get(i.getId());
            if (menuDto == null) {
                continue;
            }

            if (null != i.getParentId()) {
                menuDto.setParent(parentIdMenuBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            menuDtoList.add(menuDto);
        }
        return menuDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "19a50a44-2f5e-312c-895a-63b39001cbe3")
    @Override
    public MenuDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        MenuDto menuDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return menuDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3f6c4db6-f7bc-3570-9818-aeb5b3cdca07")
    @Override
    public List<MenuDto> getByRouterId(String routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuDto> menuDtoList = getByRouterIds(Arrays.asList(routerId));
        return menuDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5c89c861-a12d-314e-8cee-25f04e01e5c9")
    @Override
    public List<MenuDto> getByParentId(String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuDto> menuDtoList = getByParentIds(Arrays.asList(parentId));
        return menuDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5ec4294f-bd73-3883-a191-f16ab9eeb5cd")
    @Override
    public List<MenuDto> getByRouterIds(List<String> routerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(routerId)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByRouterIds(routerId);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return doConvertFromMenuToMenuDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c024bbb9-03dd-3b98-a79c-a3e9b7533376")
    @Override
    public List<MenuDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Menu> menuList = menuDao.getByIds(id);
        if (CollectionUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Menu> menuMap =
                menuList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        menuList =
                id.stream()
                        .map(i -> menuMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromMenuToMenuDto(menuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
