package com.pulse.application.manager.facade.organization;

import com.pulse.application.manager.facade.organization.base.CampusBaseDtoServiceInApplicationBaseRpcAdapter;
import com.pulse.organization.manager.dto.CampusBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "2490b347-7947-4b60-afc4-0054306a277c")
@AutoGenerated(locked = false, uuid = "6a53acf3-1a36-3431-a894-63bc317efbd8")
public class CampusBaseDtoServiceInApplicationRpcAdapter
        extends CampusBaseDtoServiceInApplicationBaseRpcAdapter {

    @RpcRefer(id = "d04a0a56-c091-4d56-aff4-8b69d966c245", version = "1744709296319")
    @AutoGenerated(locked = false, uuid = "d04a0a56-c091-4d56-aff4-8b69d966c245|RPC|ADAPTER")
    public List<CampusBaseDto> getByOrganizationIds(List<String> organizationId) {
        return super.getByOrganizationIds(organizationId);
    }

    @RpcRefer(id = "e8a52964-695e-41f8-a9bb-a53933d7baa2", version = "1744709296324")
    @AutoGenerated(locked = false, uuid = "e8a52964-695e-41f8-a9bb-a53933d7baa2|RPC|ADAPTER")
    public List<CampusBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
