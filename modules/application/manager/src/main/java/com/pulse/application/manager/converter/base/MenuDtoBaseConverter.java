package com.pulse.application.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.MenuBaseDtoManager;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "958b7379-c836-4f60-8c3d-fd3ba952f476|DTO|BASE_CONVERTER")
public class MenuDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoManager menuBaseDtoManager;

    @AutoGenerated(locked = true)
    public MenuDto convertFromMenuBaseDtoToMenuDto(MenuBaseDto menuBaseDto) {
        return convertFromMenuBaseDtoToMenuDto(List.of(menuBaseDto)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<MenuDto> convertFromMenuBaseDtoToMenuDto(List<MenuBaseDto> menuBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(menuBaseDtoList)) {
            return new ArrayList<>();
        }
        List<MenuDto> menuDtoList = new ArrayList<>();
        for (MenuBaseDto menuBaseDto : menuBaseDtoList) {
            if (menuBaseDto == null) {
                continue;
            }
            MenuDto menuDto = new MenuDto();
            menuDto.setId(menuBaseDto.getId());
            menuDto.setName(menuBaseDto.getName());
            menuDto.setMenuLogo(menuBaseDto.getMenuLogo());
            menuDto.setRouterId(menuBaseDto.getRouterId());
            menuDto.setSameLevelGroupNumber(menuBaseDto.getSameLevelGroupNumber());
            menuDto.setSortNumber(menuBaseDto.getSortNumber());
            menuDto.setVisibleFlag(menuBaseDto.getVisibleFlag());
            menuDto.setEnableFlag(menuBaseDto.getEnableFlag());
            menuDto.setShortcutKey(menuBaseDto.getShortcutKey());
            menuDto.setCreatedAt(menuBaseDto.getCreatedAt());
            menuDto.setUpdatedAt(menuBaseDto.getUpdatedAt());
            menuDto.setInputCode(menuBaseDto.getInputCode());
            menuDto.setMenuType(menuBaseDto.getMenuType());
            menuDto.setRemark(menuBaseDto.getRemark());
            menuDto.setCode(menuBaseDto.getCode());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            menuDtoList.add(menuDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return menuDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public MenuBaseDto convertFromMenuDtoToMenuBaseDto(MenuDto menuDto) {
        return convertFromMenuDtoToMenuBaseDto(List.of(menuDto)).stream().findAny().orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<MenuBaseDto> convertFromMenuDtoToMenuBaseDto(List<MenuDto> menuDtoList) {
        if (CollectionUtil.isEmpty(menuDtoList)) {
            return new ArrayList<>();
        }
        return menuBaseDtoManager.getByIds(
                menuDtoList.stream().map(MenuDto::getId).collect(Collectors.toList()));
    }
}
