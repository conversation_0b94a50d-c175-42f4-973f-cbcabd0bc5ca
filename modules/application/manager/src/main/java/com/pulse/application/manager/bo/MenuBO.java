package com.pulse.application.manager.bo;

import com.pulse.application.manager.bo.base.BaseMenuBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "menu")
@Entity
@AutoGenerated(locked = false, uuid = "de3171c0-b006-4bf6-a7d3-ebd590af70bf|BO|DEFINITION")
public class MenuBO extends BaseMenuBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "de3171c0-b006-4bf6-a7d3-ebd590af70bf|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
