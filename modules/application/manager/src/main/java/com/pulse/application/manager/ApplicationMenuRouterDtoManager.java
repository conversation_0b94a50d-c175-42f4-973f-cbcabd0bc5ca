package com.pulse.application.manager;

import com.pulse.application.manager.dto.ApplicationMenuRouterDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "82f4e2c1-d699-477a-912e-e818a5c6c56d|DTO|MANAGER")
public interface ApplicationMenuRouterDtoManager {

    @AutoGenerated(locked = true, uuid = "0d17f4b7-a1e5-39db-a046-121dcf461853")
    List<ApplicationMenuRouterDto> getByParentMenuId(String parentMenuId);

    @AutoGenerated(locked = true, uuid = "343e5236-9398-3871-b969-323d859edbae")
    List<ApplicationMenuRouterDto> getByMenuId(String menuId);

    @AutoGenerated(locked = true, uuid = "40686406-3ef9-33d2-be05-0ca68a05277d")
    ApplicationMenuRouterDto getById(String id);

    @AutoGenerated(locked = true, uuid = "46bd2fdd-32ef-3e92-93b8-63f7e34899a3")
    List<ApplicationMenuRouterDto> getByApplicationId(String applicationId);

    @AutoGenerated(locked = true, uuid = "50fc1e6e-0d7e-3c63-9d0b-f62469cf4f8c")
    List<ApplicationMenuRouterDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "64a0a708-48b8-39e4-8e2e-b79df99465b2")
    List<ApplicationMenuRouterDto> getByApplicationIds(List<String> applicationId);

    @AutoGenerated(locked = true, uuid = "6856abd8-591b-3ea5-a2a8-b4ba602f7d97")
    List<ApplicationMenuRouterDto> getByMenuIds(List<String> menuId);

    @AutoGenerated(locked = true, uuid = "ebc55611-d6ab-337a-8df6-64196e53a29d")
    List<ApplicationMenuRouterDto> getByParentMenuIds(List<String> parentMenuId);
}
