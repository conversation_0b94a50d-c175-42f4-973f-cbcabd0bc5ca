package com.pulse.pharmacy_warehouse_setting.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "aa517dde-5224-420f-a201-1925eaa07e06|ENUM|DEFINITION")
public enum ExportStrategyEnum {

    /** 先进先出 */
    BATCH_INVENTORY_ID_MIN(),

    /** 后进先出 */
    BATCH_INVENTORY_ID_MAX(),

    /** 先失效先出 */
    EXPIRATION_DATE_MIN(),

    /** 库存大先出 */
    BATCH_INVENTORY_AMOUNT_MAX();

    @AutoGenerated(locked = true)
    ExportStrategyEnum() {}
}
