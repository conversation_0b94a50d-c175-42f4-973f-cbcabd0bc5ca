package com.pulse.drug_dictionary.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "823ce510-6e40-4ad7-bcd5-3a1c6562a301|ENUM|DEFINITION")
public enum ProducerDrugTypeEnum {

    /** 西药 */
    WESTERN_MEDICINE(),

    /** 成药 */
    PATENT_MEDICINE(),

    /** 草药 */
    HERBAL_MEDICINE(),

    /** 卫材 */
    MATERIAL(),

    /** 制剂 */
    PREPARATION(),

    /** 试剂 */
    REAGENT(),

    /** 协定 */
    AGREEMENT(),

    /** 西成 */
    WESTERN_PATENT_MEDICINE();

    @AutoGenerated(locked = true)
    ProducerDrugTypeEnum() {}
}
