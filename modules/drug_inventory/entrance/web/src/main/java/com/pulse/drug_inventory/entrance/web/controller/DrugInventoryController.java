package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.converter.DrugLossReportBaseVoConverter;
import com.pulse.drug_inventory.entrance.web.query.executor.DrugStorageProfileVoQueryExecutor;
import com.pulse.drug_inventory.entrance.web.vo.DrugLossReportBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugStorageProfileVo;
import com.pulse.drug_inventory.manager.dto.DrugLossReportBaseDto;
import com.pulse.drug_inventory.persist.qto.ListLossReportLikeNumberQto;
import com.pulse.drug_inventory.persist.qto.ListStorageProfileByStorageQto;
import com.pulse.drug_inventory.service.query.DrugLossReportBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "59035fc3-2774-395f-bdc6-b8db0cefb253")
public class DrugInventoryController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBaseDtoQueryService drugLossReportBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBaseVoConverter drugLossReportBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStorageProfileVoQueryExecutor drugStorageProfileVoQueryExecutor;

    /** 功能： - 查询库存定义列表 - 产地药品摆放位置 */
    @PublicInterface(id = "384e5410-d22d-454f-b904-1bd8a8a5bcd1", version = "1740735804484")
    @AutoGenerated(locked = false, uuid = "384e5410-d22d-454f-b904-1bd8a8a5bcd1")
    @RequestMapping(
            value = {"/api/drug-inventory/query-list-storage-profile-by-storage-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugStorageProfileVo> queryListStorageProfileByStoragePaged(
            @Valid @NotNull(message = "查询参数不能为空") ListStorageProfileByStorageQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugStorageProfileVo> result =
                drugStorageProfileVoQueryExecutor.queryListStorageProfileByStoragePaged(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 报损单号模糊查询 */
    @PublicInterface(id = "d2d0fc4b-c380-4934-9b0d-ef244b99f9fc", version = "1747018721189")
    @AutoGenerated(locked = false, uuid = "d2d0fc4b-c380-4934-9b0d-ef244b99f9fc")
    @RequestMapping(
            value = {"/api/drug-inventory/list-loss-report-like-number-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugLossReportBaseVo> listLossReportLikeNumberPaged(
            @Valid ListLossReportLikeNumberQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugLossReportBaseDto> dtoResult =
                drugLossReportBaseDtoQueryService.listLossReportLikeNumberPaged(qto);
        VSQueryResult<DrugLossReportBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                drugLossReportBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
