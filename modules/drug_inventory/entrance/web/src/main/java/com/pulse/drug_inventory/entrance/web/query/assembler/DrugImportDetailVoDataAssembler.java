package com.pulse.drug_inventory.entrance.web.query.assembler;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_inventory.entrance.web.vo.DrugImportDetailVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugCategoryVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugDictionaryWithCatalogVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugNameDictionaryBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginExtensionBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginForDetailVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginSpecificationSimpleVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugProducerDictionarySimpleVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugSpecificationDictionaryWithDrugVo;
import com.pulse.drug_inventory.manager.dto.DrugImportDetailBaseDto;
import com.pulse.drug_inventory.service.DrugImportDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** DrugImportDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "be8d2988-bea2-3898-a77d-77cb321c77c3")
public class DrugImportDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private DrugImportDetailBaseDtoService drugImportDetailBaseDtoService;

    /** 组装drugOrigin数据 */
    @AutoGenerated(locked = true, uuid = "16f7ee11-4278-336a-a385-2ea22eb2f5d3")
    private void assembleDrugOriginData(
            DrugImportDetailVoDataAssembler.DrugImportDetailVoDataHolder dataHolder) {
        Map<String, Pair<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryWithDrugVo>>
                drugOrigin2DrugSpecification =
                        dataHolder.drugOrigin2DrugSpecification.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<String, Pair<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo>>
                drugOrigin2DrugOriginExtension =
                        dataHolder.drugOrigin2DrugOriginExtension.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getDrugOriginCode(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugOriginExtension
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<String, List<Pair<DrugNameDictionaryBaseDto, DrugInventoryRefDrugNameDictionaryBaseVo>>>
                drugOrigin2DrugNameDictionaryList =
                        dataHolder.drugOrigin2DrugNameDictionaryList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugOriginCode(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugNameDictionaryList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        for (Map.Entry<DrugOriginBaseDto, DrugInventoryRefDrugOriginForDetailVo> drugOrigin :
                dataHolder.drugOrigin.entrySet()) {
            DrugOriginBaseDto baseDto = drugOrigin.getKey();
            DrugInventoryRefDrugOriginForDetailVo vo = drugOrigin.getValue();
            vo.setDrugSpecification(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification.get(
                                            baseDto.getDrugSpecificationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugOriginExtension(
                    Optional.ofNullable(
                                    drugOrigin2DrugOriginExtension.get(baseDto.getDrugOriginCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugNameDictionaryList(
                    Optional.ofNullable(
                                    drugOrigin2DrugNameDictionaryList.get(
                                            baseDto.getDrugOriginCode()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }
        assembleDrugOrigin2DrugSpecificationData(dataHolder);
    }

    /** 组装drugOrigin2DrugSpecification数据 */
    @AutoGenerated(locked = true, uuid = "45471910-da74-3069-bc91-1e83ec1e9178")
    private void assembleDrugOrigin2DrugSpecificationData(
            DrugImportDetailVoDataAssembler.DrugImportDetailVoDataHolder dataHolder) {
        Map<String, Pair<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo>>
                drugOrigin2DrugSpecification2Drug =
                        dataHolder.drugOrigin2DrugSpecification2Drug.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getDrugCode(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification2Drug
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryWithDrugVo>
                drugOrigin2DrugSpecification : dataHolder.drugOrigin2DrugSpecification.entrySet()) {
            DrugSpecificationDictionaryBaseDto baseDto = drugOrigin2DrugSpecification.getKey();
            DrugSpecificationDictionaryWithDrugVo vo = drugOrigin2DrugSpecification.getValue();
            vo.setDrug(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2Drug.get(baseDto.getDrugCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
        assembleDrugOrigin2DrugSpecification2DrugData(dataHolder);
    }

    /** 批量自定义组装DrugImportDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "8b994aef-1871-3cff-8cc4-6982090aa12e")
    public void assembleDataCustomized(List<DrugImportDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 组装DrugImportDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "cd636798-7b72-3b67-8075-33d703383837")
    public void assembleData(
            Map<String, DrugImportDetailVo> voMap,
            DrugImportDetailVoDataAssembler.DrugImportDetailVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<DrugImportDetailBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<
                        String,
                        Pair<
                                DrugProducerDictionaryBaseDto,
                                DrugInventoryRefDrugProducerDictionarySimpleVo>>
                firm =
                        dataHolder.firm.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto -> Pair.of(dto, dataHolder.firm.get(dto)),
                                                (o1, o2) -> o1));
        Map<
                        String,
                        Pair<
                                DrugProducerDictionaryBaseDto,
                                DrugInventoryRefDrugProducerDictionarySimpleVo>>
                supplier =
                        dataHolder.supplier.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto -> Pair.of(dto, dataHolder.supplier.get(dto)),
                                                (o1, o2) -> o1));
        Map<String, Pair<DrugOriginBaseDto, DrugInventoryRefDrugOriginForDetailVo>> drugOrigin =
                dataHolder.drugOrigin.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getDrugOriginCode(),
                                        dto -> Pair.of(dto, dataHolder.drugOrigin.get(dto)),
                                        (o1, o2) -> o1));
        Map<
                        String,
                        Pair<
                                DrugOriginSpecificationBaseDto,
                                DrugInventoryRefDrugOriginSpecificationSimpleVo>>
                drugOriginSpecification =
                        dataHolder.drugOriginSpecification.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.drugOriginSpecification
                                                                        .get(dto)),
                                                (o1, o2) -> o1));

        for (DrugImportDetailBaseDto baseDto : baseDtoList) {
            DrugImportDetailVo vo = voMap.get(baseDto.getId());
            vo.setFirm(
                    Optional.ofNullable(firm.get(baseDto.getFirmId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setSupplier(
                    Optional.ofNullable(supplier.get(baseDto.getSupplierId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugOrigin(
                    Optional.ofNullable(drugOrigin.get(baseDto.getDrugOriginCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugOriginSpecification(
                    Optional.ofNullable(
                                    drugOriginSpecification.get(
                                            baseDto.getDrugOriginSpecificationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDrugOriginData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 组装drugOrigin2DrugSpecification2Drug数据 */
    @AutoGenerated(locked = true, uuid = "cf765ff4-13a9-3ea7-908e-fab98806dd68")
    private void assembleDrugOrigin2DrugSpecification2DrugData(
            DrugImportDetailVoDataAssembler.DrugImportDetailVoDataHolder dataHolder) {
        Map<String, Pair<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo>>
                drugOrigin2DrugSpecification2Drug2DrugCatalog =
                        dataHolder.drugOrigin2DrugSpecification2Drug2DrugCatalog.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification2Drug2DrugCatalog
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo>
                drugOrigin2DrugSpecification2Drug :
                        dataHolder.drugOrigin2DrugSpecification2Drug.entrySet()) {
            DrugDictionaryBaseDto baseDto = drugOrigin2DrugSpecification2Drug.getKey();
            DrugInventoryRefDrugDictionaryWithCatalogVo vo =
                    drugOrigin2DrugSpecification2Drug.getValue();
            vo.setDrugCatalog(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2Drug2DrugCatalog.get(
                                            baseDto.getDrugCatalogId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class DrugImportDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugImportDetailBaseDto> rootBaseDtoList;

        /** 持有字段firm的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
                firm;

        /** 持有字段supplier的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
                supplier;

        /** 持有字段drugOrigin的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginBaseDto, DrugInventoryRefDrugOriginForDetailVo> drugOrigin;

        /** 持有字段drugOriginSpecification的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginSpecificationBaseDto, DrugInventoryRefDrugOriginSpecificationSimpleVo>
                drugOriginSpecification;

        /** 持有字段drugOrigin.drugSpecification的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryWithDrugVo>
                drugOrigin2DrugSpecification;

        /** 持有字段drugOrigin.drugOriginExtension的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo>
                drugOrigin2DrugOriginExtension;

        /** 持有字段drugOrigin.drugNameDictionaryList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugNameDictionaryBaseDto, DrugInventoryRefDrugNameDictionaryBaseVo>
                drugOrigin2DrugNameDictionaryList;

        /** 持有字段drugOrigin.drugSpecification.drug的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo>
                drugOrigin2DrugSpecification2Drug;

        /** 持有字段drugOrigin.drugSpecification.drug.drugCatalog的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo>
                drugOrigin2DrugSpecification2Drug2DrugCatalog;
    }
}
