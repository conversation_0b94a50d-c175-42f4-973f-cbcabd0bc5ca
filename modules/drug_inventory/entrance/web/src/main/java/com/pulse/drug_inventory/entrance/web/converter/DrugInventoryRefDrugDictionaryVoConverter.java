package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugDictionaryVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugDictionaryVoDataAssembler.DrugInventoryRefDrugDictionaryVoDataHolder;
import com.pulse.drug_inventory.entrance.web.query.collector.DrugInventoryRefDrugDictionaryVoDataCollector;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugDictionaryVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugDictionaryVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "fa2bc375-15fa-44ba-9d67-d7a322b1e6ee|VO|CONVERTER")
public class DrugInventoryRefDrugDictionaryVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugDictionaryVoDataAssembler
            drugInventoryRefDrugDictionaryVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugDictionaryVoDataCollector
            drugInventoryRefDrugDictionaryVoDataCollector;

    /** 把DrugDictionaryBaseDto转换成DrugInventoryRefDrugDictionaryVo */
    @AutoGenerated(locked = true, uuid = "1389c0fe-9b23-38e2-bf00-c7184c3e022f")
    public DrugInventoryRefDrugDictionaryVo convertToDrugInventoryRefDrugDictionaryVo(
            DrugDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugDictionaryVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugInventoryRefDrugDictionaryVo列表数据 */
    @AutoGenerated(locked = true, uuid = "ca081ab1-21a9-3461-aac9-30523085e31e")
    public List<DrugInventoryRefDrugDictionaryVo> convertAndAssembleDataList(
            List<DrugDictionaryBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugInventoryRefDrugDictionaryVoDataHolder dataHolder =
                new DrugInventoryRefDrugDictionaryVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, DrugInventoryRefDrugDictionaryVo> voMap =
                convertToDrugInventoryRefDrugDictionaryVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getDrugCode(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugDictionaryVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        drugInventoryRefDrugDictionaryVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getDrugCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugInventoryRefDrugDictionaryVo数据 */
    @AutoGenerated(locked = true, uuid = "de58f65b-18fd-336c-97d2-106911488fc0")
    public DrugInventoryRefDrugDictionaryVo convertAndAssembleData(DrugDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugDictionaryBaseDto转换成DrugInventoryRefDrugDictionaryVo */
    @AutoGenerated(locked = false, uuid = "fa2bc375-15fa-44ba-9d67-d7a322b1e6ee-converter-Map")
    public Map<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryVo>
            convertToDrugInventoryRefDrugDictionaryVoMap(List<DrugDictionaryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugDictionaryVo vo =
                                                    new DrugInventoryRefDrugDictionaryVo();
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugName(dto.getDrugName());
                                            vo.setCommonNameCode(dto.getCommonNameCode());
                                            vo.setPharmacologicalType(dto.getPharmacologicalType());
                                            vo.setDrugType(dto.getDrugType());
                                            vo.setToxicType(dto.getToxicType());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugDictionaryBaseDto转换成DrugInventoryRefDrugDictionaryVo */
    @AutoGenerated(locked = true, uuid = "fa2bc375-15fa-44ba-9d67-d7a322b1e6ee-converter-list")
    public List<DrugInventoryRefDrugDictionaryVo> convertToDrugInventoryRefDrugDictionaryVoList(
            List<DrugDictionaryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugInventoryRefDrugDictionaryVoMap(dtoList).values());
    }
}
