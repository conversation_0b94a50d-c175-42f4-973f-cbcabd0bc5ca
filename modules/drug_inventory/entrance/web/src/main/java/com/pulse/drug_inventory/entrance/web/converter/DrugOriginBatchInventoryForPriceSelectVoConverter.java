package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugOriginBatchInventoryForPriceSelectVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugOriginBatchInventoryForPriceSelectVoDataAssembler.DrugOriginBatchInventoryForPriceSelectVoDataHolder;
import com.pulse.drug_inventory.entrance.web.query.collector.DrugOriginBatchInventoryForPriceSelectVoDataCollector;
import com.pulse.drug_inventory.entrance.web.vo.DrugOriginBatchInventoryForPriceSelectVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugOriginSpecificationBaseVo;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryWithSpecificationDto;
import com.pulse.drug_inventory.service.DrugOriginBatchInventoryBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugOriginBatchInventoryForPriceSelectVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "90536554-eaf2-4165-b26a-c795b37e8c19|VO|CONVERTER")
public class DrugOriginBatchInventoryForPriceSelectVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryBaseDtoService drugOriginBatchInventoryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryForPriceSelectVoDataAssembler
            drugOriginBatchInventoryForPriceSelectVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryForPriceSelectVoDataCollector
            drugOriginBatchInventoryForPriceSelectVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseVoConverter drugOriginSpecificationBaseVoConverter;

    /** 使用默认方式组装DrugOriginBatchInventoryForPriceSelectVo数据 */
    @AutoGenerated(locked = true, uuid = "15aac962-23cd-3703-9258-7040c08ca36d")
    public DrugOriginBatchInventoryForPriceSelectVo convertAndAssembleData(
            DrugOriginBatchInventoryWithSpecificationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginBatchInventoryWithSpecificationDto转换成DrugOriginBatchInventoryForPriceSelectVo */
    @AutoGenerated(locked = true, uuid = "59372c63-e078-3dbc-9a64-b1db923662d4")
    public DrugOriginBatchInventoryForPriceSelectVo
            convertToDrugOriginBatchInventoryForPriceSelectVo(
                    DrugOriginBatchInventoryWithSpecificationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginBatchInventoryForPriceSelectVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugOriginBatchInventoryWithSpecificationDto转换成DrugOriginBatchInventoryForPriceSelectVo */
    @AutoGenerated(locked = false, uuid = "90536554-eaf2-4165-b26a-c795b37e8c19-converter-Map")
    public Map<
                    DrugOriginBatchInventoryWithSpecificationDto,
                    DrugOriginBatchInventoryForPriceSelectVo>
            convertToDrugOriginBatchInventoryForPriceSelectVoMap(
                    List<DrugOriginBatchInventoryWithSpecificationDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationBaseVo>
                drugOriginSpecificationMap =
                        drugOriginSpecificationBaseVoConverter
                                .convertToDrugOriginSpecificationBaseVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(
                                                        DrugOriginBatchInventoryWithSpecificationDto
                                                                ::getDrugOriginSpecification)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginBatchInventoryWithSpecificationDto, DrugOriginBatchInventoryForPriceSelectVo>
                voMap =
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                dto -> {
                                                    DrugOriginBatchInventoryForPriceSelectVo vo =
                                                            new DrugOriginBatchInventoryForPriceSelectVo();
                                                    vo.setAmount(dto.getAmount());
                                                    vo.setBatchNumber(dto.getBatchNumber());
                                                    vo.setExpirationDate(dto.getExpirationDate());
                                                    vo.setPurchasePrice(dto.getPurchasePrice());
                                                    vo.setRetailPrice(dto.getRetailPrice());
                                                    vo.setImportDateTime(dto.getImportDateTime());
                                                    vo.setDrugOriginSpecification(
                                                            dto.getDrugOriginSpecification() == null
                                                                    ? null
                                                                    : drugOriginSpecificationMap
                                                                            .get(
                                                                                    dto
                                                                                            .getDrugOriginSpecification()));
                                                    vo.setVirtualAmount(dto.getVirtualAmount());
                                                    return vo;
                                                },
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginBatchInventoryWithSpecificationDto转换成DrugOriginBatchInventoryForPriceSelectVo */
    @AutoGenerated(locked = true, uuid = "90536554-eaf2-4165-b26a-c795b37e8c19-converter-list")
    public List<DrugOriginBatchInventoryForPriceSelectVo>
            convertToDrugOriginBatchInventoryForPriceSelectVoList(
                    List<DrugOriginBatchInventoryWithSpecificationDto> dtoList) {
        return new ArrayList<>(
                convertToDrugOriginBatchInventoryForPriceSelectVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugOriginBatchInventoryForPriceSelectVo列表数据 */
    @AutoGenerated(locked = true, uuid = "b7d6810e-6f9d-34aa-ad00-ade766c558ca")
    public List<DrugOriginBatchInventoryForPriceSelectVo> convertAndAssembleDataList(
            List<DrugOriginBatchInventoryWithSpecificationDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugOriginBatchInventoryForPriceSelectVoDataHolder dataHolder =
                new DrugOriginBatchInventoryForPriceSelectVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugOriginBatchInventoryBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(DrugOriginBatchInventoryWithSpecificationDto::getId)
                                .collect(Collectors.toList())));
        Map<String, DrugOriginBatchInventoryForPriceSelectVo> voMap =
                convertToDrugOriginBatchInventoryForPriceSelectVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugOriginBatchInventoryForPriceSelectVoDataCollector.collectDataWithDtoData(
                dtoList, dataHolder);
        drugOriginBatchInventoryForPriceSelectVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
