package com.pulse.drug_inventory.entrance.web.query.assembler;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugCategoryVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugDictionaryWithCatalogVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugSpecificationDictionaryWithDrugVo;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** DrugSpecificationDictionaryWithDrugVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "130bf151-0486-3b52-ba4f-d669fd68dae1")
public class DrugSpecificationDictionaryWithDrugVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    /** 组装drug数据 */
    @AutoGenerated(locked = true, uuid = "40355e3e-6465-3719-b2bc-5b4291718a2a")
    private void assembleDrugData(
            DrugSpecificationDictionaryWithDrugVoDataAssembler
                            .DrugSpecificationDictionaryWithDrugVoDataHolder
                    dataHolder) {
        Map<String, Pair<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo>> drug2DrugCatalog =
                dataHolder.drug2DrugCatalog.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.drug2DrugCatalog.get(dto)),
                                        (o1, o2) -> o1));
        for (Map.Entry<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo> drug :
                dataHolder.drug.entrySet()) {
            DrugDictionaryBaseDto baseDto = drug.getKey();
            DrugInventoryRefDrugDictionaryWithCatalogVo vo = drug.getValue();
            vo.setDrugCatalog(
                    Optional.ofNullable(drug2DrugCatalog.get(baseDto.getDrugCatalogId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 批量自定义组装DrugSpecificationDictionaryWithDrugVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "74507a97-75b0-3363-a5b0-5e16672535d5")
    public void assembleDataCustomized(List<DrugSpecificationDictionaryWithDrugVo> dataList) {
        // 自定义数据组装

    }

    /** 组装DrugSpecificationDictionaryWithDrugVo数据 */
    @AutoGenerated(locked = true, uuid = "80a2fe67-0da9-323a-a728-21ac16f9fecc")
    public void assembleData(
            Map<String, DrugSpecificationDictionaryWithDrugVo> voMap,
            DrugSpecificationDictionaryWithDrugVoDataAssembler
                            .DrugSpecificationDictionaryWithDrugVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<DrugSpecificationDictionaryBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo>> drug =
                dataHolder.drug.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getDrugCode(),
                                        dto -> Pair.of(dto, dataHolder.drug.get(dto)),
                                        (o1, o2) -> o1));

        for (DrugSpecificationDictionaryBaseDto baseDto : baseDtoList) {
            DrugSpecificationDictionaryWithDrugVo vo = voMap.get(baseDto.getId());
            vo.setDrug(
                    Optional.ofNullable(drug.get(baseDto.getDrugCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDrugData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class DrugSpecificationDictionaryWithDrugVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugSpecificationDictionaryBaseDto> rootBaseDtoList;

        /** 持有字段drug的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo> drug;

        /** 持有字段drug.drugCatalog的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> drug2DrugCatalog;
    }
}
