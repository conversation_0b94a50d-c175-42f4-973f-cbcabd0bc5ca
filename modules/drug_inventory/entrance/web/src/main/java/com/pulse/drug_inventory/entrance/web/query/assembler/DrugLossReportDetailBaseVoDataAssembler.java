package com.pulse.drug_inventory.entrance.web.query.assembler;

import com.pulse.drug_inventory.entrance.web.vo.DrugLossReportDetailBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** DrugLossReportDetailBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "74c7bfcd-fc77-3f76-a5db-730c31b47be9")
public class DrugLossReportDetailBaseVoDataAssembler {

    /** 批量自定义组装DrugLossReportDetailBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "2c18d55e-65e5-33d6-92fc-0d2d2bdb9a40")
    public void assembleDataCustomized(List<DrugLossReportDetailBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装DrugLossReportDetailBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "d4a10dad-c62a-30b3-b236-6dac5bca61c4")
    public void assembleData(Map<String, DrugLossReportDetailBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
