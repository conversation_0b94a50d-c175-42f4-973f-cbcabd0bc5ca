package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.converter.DrugExportDetailFirmExtVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugExportWithDrugExportDetailFirmExtVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugExportWithDrugExportDetailVoConverter;
import com.pulse.drug_inventory.entrance.web.vo.DrugExportDetailFirmExtVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugExportWithDrugExportDetailFirmExtVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugExportWithDrugExportDetailVo;
import com.pulse.drug_inventory.manager.dto.DrugExportDetailFirmExtDto;
import com.pulse.drug_inventory.manager.dto.DrugExportWithDrugExportDetailDto;
import com.pulse.drug_inventory.manager.dto.DrugExportWithDrugExportDetailFirmExtDto;
import com.pulse.drug_inventory.persist.qto.SearchDrugExportQto;
import com.pulse.drug_inventory.service.DrugExportDetailFirmExtDtoService;
import com.pulse.drug_inventory.service.DrugExportWithDrugExportDetailDtoService;
import com.pulse.drug_inventory.service.DrugExportWithDrugExportDetailFirmExtDtoService;
import com.pulse.drug_inventory.service.query.DrugExportWithDrugExportDetailFirmExtDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "6220bf2d-0f8c-3c05-a8ae-ee68b986bee1")
public class DrugExportController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugExportDetailFirmExtDtoService drugExportDetailFirmExtDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportDetailFirmExtVoConverter drugExportDetailFirmExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportWithDrugExportDetailDtoService drugExportWithDrugExportDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportWithDrugExportDetailFirmExtDtoQueryService
            drugExportWithDrugExportDetailFirmExtDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportWithDrugExportDetailFirmExtDtoService
            drugExportWithDrugExportDetailFirmExtDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportWithDrugExportDetailFirmExtVoConverter
            drugExportWithDrugExportDetailFirmExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportWithDrugExportDetailVoConverter drugExportWithDrugExportDetailVoConverter;

    /** 根据主键获取出库单 */
    @PublicInterface(id = "2cc56b26-0bf2-46f4-b66e-5dd4f2fc07f6", version = "1747123064916")
    @AutoGenerated(locked = false, uuid = "2cc56b26-0bf2-46f4-b66e-5dd4f2fc07f6")
    @RequestMapping(
            value = {"/api/drug-inventory/get-drug-export-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public DrugExportWithDrugExportDetailVo getDrugExportById(@NotNull String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugExportWithDrugExportDetailDto rpcResult =
                drugExportWithDrugExportDetailDtoService.getById(id);
        DrugExportWithDrugExportDetailVo result =
                drugExportWithDrugExportDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据出库单号获取药品出库单带出库单明细 */
    @PublicInterface(id = "9af49b7d-b93a-426c-b463-4b8db691c9f3", version = "1747905451803")
    @AutoGenerated(locked = false, uuid = "9af49b7d-b93a-426c-b463-4b8db691c9f3")
    @RequestMapping(
            value = {"/api/drug-inventory/get-drug-export-by-document-number"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public DrugExportWithDrugExportDetailFirmExtVo getDrugExportByDocumentNumber(
            @NotNull String documentNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugExportWithDrugExportDetailFirmExtDto rpcResult =
                drugExportWithDrugExportDetailFirmExtDtoService.getByDocumentNumber(documentNumber);
        DrugExportWithDrugExportDetailFirmExtVo result =
                drugExportWithDrugExportDetailFirmExtVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据药品出库主记录ID获取药品出库明细厂家扩展列表 */
    @PublicInterface(id = "a7078710-45ee-43ee-9903-0918f7ceddd5", version = "1747115563554")
    @AutoGenerated(locked = false, uuid = "a7078710-45ee-43ee-9903-0918f7ceddd5")
    @RequestMapping(
            value = {"/api/drug-inventory/get-drug-export-by-drug-export-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<DrugExportDetailFirmExtVo> getDrugExportDetailByDrugExportId(String drugExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailFirmExtDto> rpcResult =
                drugExportDetailFirmExtDtoService.getByDrugExportId(drugExportId);
        List<DrugExportDetailFirmExtVo> result =
                drugExportDetailFirmExtVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 搜索药品出库单 */
    @PublicInterface(id = "d938b561-7307-43be-ab28-8cc2fcebc5a7", version = "1747119340080")
    @AutoGenerated(locked = false, uuid = "d938b561-7307-43be-ab28-8cc2fcebc5a7")
    @RequestMapping(
            value = {"/api/drug-inventory/search-drug-export-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugExportWithDrugExportDetailFirmExtVo> searchDrugExportWaterfall(
            @Valid SearchDrugExportQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugExportWithDrugExportDetailFirmExtDto> dtoResult =
                drugExportWithDrugExportDetailFirmExtDtoQueryService.searchDrugExportWaterfall(qto);
        VSQueryResult<DrugExportWithDrugExportDetailFirmExtVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                drugExportWithDrugExportDetailFirmExtVoConverter.convertAndAssembleDataList(
                        dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
