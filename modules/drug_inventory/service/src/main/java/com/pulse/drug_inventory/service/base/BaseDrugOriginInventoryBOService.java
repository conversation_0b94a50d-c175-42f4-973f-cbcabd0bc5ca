package com.pulse.drug_inventory.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.bo.DrugOriginInventoryBO;
import com.pulse.drug_inventory.persist.dos.DrugOriginBatchInventory;
import com.pulse.drug_inventory.persist.dos.DrugOriginInventory;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.CreateDrugOriginBatchInventoryBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.CreateDrugOriginInventoryBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.MergeDrugOriginInventoryBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.SaveDrugOriginBatchInventoryListBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.UpdateDrugOriginInventoryAmountBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.UpdateDrugOriginInventoryPriceBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService.UpdateVitalAmountBoResult;
import com.pulse.drug_inventory.service.bto.CreateDrugOriginBatchInventoryBto;
import com.pulse.drug_inventory.service.bto.CreateDrugOriginInventoryBto;
import com.pulse.drug_inventory.service.bto.MergeDrugOriginInventoryBto;
import com.pulse.drug_inventory.service.bto.SaveDrugOriginBatchInventoryListBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugOriginInventoryAmountBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugOriginInventoryPriceBto;
import com.pulse.drug_inventory.service.bto.UpdateVitalAmountBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "00c7a338-5867-338a-8136-a98484516d9b")
public class BaseDrugOriginInventoryBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO createCreateDrugOriginInventoryOnDuplicateThrowEx(
            CreateDrugOriginInventoryBoResult boResult,
            CreateDrugOriginInventoryBto createDrugOriginInventoryBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createDrugOriginInventoryBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getById(createDrugOriginInventoryBto.getId());
            if (drugOriginInventoryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull =
                (createDrugOriginInventoryBto.getStorageCode() == null)
                        && (createDrugOriginInventoryBto.getDrugOriginCode() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getByStorageCodeAndDrugOriginCode(
                            createDrugOriginInventoryBto.getStorageCode(),
                            createDrugOriginInventoryBto.getDrugOriginCode());
            if (drugOriginInventoryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'storage_code'";
                matchedUkName += ",";
                matchedUkName += "'drug_origin_code'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (drugOriginInventoryBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        drugOriginInventoryBO.getId(),
                        "drug_origin_inventory");
                throw new IgnoredException(400, "药品总库存已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "drug_origin_inventory",
                        drugOriginInventoryBO.getId(),
                        "drug_origin_inventory");
                throw new IgnoredException(400, "药品总库存已存在");
            }
        } else {
            drugOriginInventoryBO = new DrugOriginInventoryBO();
            if (pkExist) {
                drugOriginInventoryBO.setId(createDrugOriginInventoryBto.getId());
            } else {
                drugOriginInventoryBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_origin_inventory")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "storageCode")) {
                drugOriginInventoryBO.setStorageCode(createDrugOriginInventoryBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "drugOriginCode")) {
                drugOriginInventoryBO.setDrugOriginCode(
                        createDrugOriginInventoryBto.getDrugOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "amount")) {
                drugOriginInventoryBO.setAmount(createDrugOriginInventoryBto.getAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "preOccupiedAmount")) {
                drugOriginInventoryBO.setPreOccupiedAmount(
                        createDrugOriginInventoryBto.getPreOccupiedAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "inTransitAmount")) {
                drugOriginInventoryBO.setInTransitAmount(
                        createDrugOriginInventoryBto.getInTransitAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "virtualAmount")) {
                drugOriginInventoryBO.setVirtualAmount(
                        createDrugOriginInventoryBto.getVirtualAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "useFrequency")) {
                drugOriginInventoryBO.setUseFrequency(
                        createDrugOriginInventoryBto.getUseFrequency());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "drugOriginSpecificationId")) {
                drugOriginInventoryBO.setDrugOriginSpecificationId(
                        createDrugOriginInventoryBto.getDrugOriginSpecificationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createDrugOriginInventoryBto);
            addedBto.setBo(drugOriginInventoryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugOriginInventoryBO;
    }

    /** 创建药品批次库存 */
    @AutoGenerated(locked = true)
    protected CreateDrugOriginBatchInventoryBoResult createDrugOriginBatchInventoryBase(
            CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto) {
        if (createDrugOriginBatchInventoryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDrugOriginBatchInventoryBoResult boResult =
                new CreateDrugOriginBatchInventoryBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                updateCreateDrugOriginBatchInventoryOnMissThrowEx(
                        boResult, createDrugOriginBatchInventoryBto);
        if (drugOriginInventoryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginBatchInventoryBto, "__$validPropertySet"),
                    "drugOriginBatchInventoryBtoList")) {
                createDrugOriginBatchInventoryBtoOnDuplicateThrowEx(
                        boResult, createDrugOriginBatchInventoryBto, drugOriginInventoryBO);
            }
        }
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    /** 创建对象:DrugOriginBatchInventoryBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createDrugOriginBatchInventoryBtoOnDuplicateThrowEx(
            BaseDrugOriginInventoryBOService.CreateDrugOriginInventoryBoResult boResult,
            CreateDrugOriginInventoryBto createDrugOriginInventoryBto,
            DrugOriginInventoryBO drugOriginInventoryBO) {
        if (CollectionUtil.isNotEmpty(
                createDrugOriginInventoryBto.getDrugOriginBatchInventoryBtoList())) {
            for (CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto item :
                    createDrugOriginInventoryBto.getDrugOriginBatchInventoryBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugOriginBatchInventoryBO> any =
                        drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().stream()
                                .filter(
                                        drugOriginBatchInventoryBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugOriginBatchInventoryBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "drug_origin_batch_inventory");
                        throw new IgnoredException(400, "药品批次库存已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "drug_origin_batch_inventory",
                                any.get().getId());
                        throw new IgnoredException(400, "药品批次库存已存在");
                    }
                } else {
                    DrugOriginBatchInventoryBO subBo = new DrugOriginBatchInventoryBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "storageCode")) {
                        subBo.setStorageCode(item.getStorageCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplyId")) {
                        subBo.setSupplyId(item.getSupplyId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchId")) {
                        subBo.setBatchId(item.getBatchId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "virtualAmount")) {
                        subBo.setVirtualAmount(item.getVirtualAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "gcpCode")) {
                        subBo.setGcpCode(item.getGcpCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importDateTime")) {
                        subBo.setImportDateTime(item.getImportDateTime());
                    }
                    subBo.setDrugOriginInventoryBO(drugOriginInventoryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "drug_origin_batch_inventory")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:DrugOriginBatchInventoryBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createDrugOriginBatchInventoryBtoOnDuplicateThrowEx(
            CreateDrugOriginBatchInventoryBoResult boResult,
            CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto,
            DrugOriginInventoryBO drugOriginInventoryBO) {
        if (CollectionUtil.isNotEmpty(
                createDrugOriginBatchInventoryBto.getDrugOriginBatchInventoryBtoList())) {
            for (CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto item :
                    createDrugOriginBatchInventoryBto.getDrugOriginBatchInventoryBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugOriginBatchInventoryBO> any =
                        drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().stream()
                                .filter(
                                        drugOriginBatchInventoryBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugOriginBatchInventoryBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "drug_origin_batch_inventory");
                        throw new IgnoredException(400, "药品批次库存已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "drug_origin_batch_inventory",
                                any.get().getId());
                        throw new IgnoredException(400, "药品批次库存已存在");
                    }
                } else {
                    DrugOriginBatchInventoryBO subBo = new DrugOriginBatchInventoryBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "storageCode")) {
                        subBo.setStorageCode(item.getStorageCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplyId")) {
                        subBo.setSupplyId(item.getSupplyId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchId")) {
                        subBo.setBatchId(item.getBatchId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "virtualAmount")) {
                        subBo.setVirtualAmount(item.getVirtualAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "gcpCode")) {
                        subBo.setGcpCode(item.getGcpCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importDateTime")) {
                        subBo.setImportDateTime(item.getImportDateTime());
                    }
                    subBo.setDrugOriginInventoryBO(drugOriginInventoryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "drug_origin_batch_inventory")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:DrugOriginBatchInventoryBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugOriginBatchInventoryBtoOnDuplicateUpdate(
            SaveDrugOriginBatchInventoryListBoResult boResult,
            SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto,
            DrugOriginInventoryBO drugOriginInventoryBO) {
        if (CollectionUtil.isNotEmpty(
                saveDrugOriginBatchInventoryListBto.getDrugOriginBatchInventoryBtoList())) {
            for (SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto item :
                    saveDrugOriginBatchInventoryListBto.getDrugOriginBatchInventoryBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugOriginBatchInventoryBO> any =
                        drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().stream()
                                .filter(
                                        drugOriginBatchInventoryBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugOriginBatchInventoryBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugOriginBatchInventoryBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugOriginBatchInventory());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "storageCode")) {
                            bo.setStorageCode(item.getStorageCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplyId")) {
                            bo.setSupplyId(item.getSupplyId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchId")) {
                            bo.setBatchId(item.getBatchId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "virtualAmount")) {
                            bo.setVirtualAmount(item.getVirtualAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpCode")) {
                            bo.setGcpCode(item.getGcpCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importDateTime")) {
                            bo.setImportDateTime(item.getImportDateTime());
                        }
                    } else {
                        DrugOriginBatchInventoryBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugOriginBatchInventory());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "storageCode")) {
                            bo.setStorageCode(item.getStorageCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplyId")) {
                            bo.setSupplyId(item.getSupplyId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchId")) {
                            bo.setBatchId(item.getBatchId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "virtualAmount")) {
                            bo.setVirtualAmount(item.getVirtualAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpCode")) {
                            bo.setGcpCode(item.getGcpCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importDateTime")) {
                            bo.setImportDateTime(item.getImportDateTime());
                        }
                    }
                } else {
                    DrugOriginBatchInventoryBO subBo = new DrugOriginBatchInventoryBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "storageCode")) {
                        subBo.setStorageCode(item.getStorageCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplyId")) {
                        subBo.setSupplyId(item.getSupplyId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchId")) {
                        subBo.setBatchId(item.getBatchId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "virtualAmount")) {
                        subBo.setVirtualAmount(item.getVirtualAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "gcpCode")) {
                        subBo.setGcpCode(item.getGcpCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importDateTime")) {
                        subBo.setImportDateTime(item.getImportDateTime());
                    }
                    subBo.setDrugOriginInventoryBO(drugOriginInventoryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "drug_origin_batch_inventory")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:DrugOriginBatchInventoryBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugOriginBatchInventoryBtoOnDuplicateUpdate(
            BaseDrugOriginInventoryBOService.MergeDrugOriginInventoryBoResult boResult,
            MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto,
            DrugOriginInventoryBO drugOriginInventoryBO) {
        if (CollectionUtil.isNotEmpty(
                mergeDrugOriginInventoryBto.getDrugOriginBatchInventoryBtoList())) {
            for (MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto item :
                    mergeDrugOriginInventoryBto.getDrugOriginBatchInventoryBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugOriginBatchInventoryBO> any =
                        drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().stream()
                                .filter(
                                        drugOriginBatchInventoryBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugOriginBatchInventoryBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugOriginBatchInventoryBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugOriginBatchInventory());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "storageCode")) {
                            bo.setStorageCode(item.getStorageCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amountIncr")) {
                            bo.setAmount(NumberUtil.add(item.getAmountIncr(), bo.getAmount()));
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplyId")) {
                            bo.setSupplyId(item.getSupplyId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchId")) {
                            bo.setBatchId(item.getBatchId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "virtualAmountIncr")) {
                            bo.setVirtualAmount(
                                    NumberUtil.add(
                                            item.getVirtualAmountIncr(), bo.getVirtualAmount()));
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpCode")) {
                            bo.setGcpCode(item.getGcpCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importDateTime")) {
                            bo.setImportDateTime(item.getImportDateTime());
                        }
                    } else {
                        DrugOriginBatchInventoryBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugOriginBatchInventory());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "storageCode")) {
                            bo.setStorageCode(item.getStorageCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amountIncr")) {
                            bo.setAmount(NumberUtil.add(item.getAmountIncr(), bo.getAmount()));
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplyId")) {
                            bo.setSupplyId(item.getSupplyId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchId")) {
                            bo.setBatchId(item.getBatchId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "virtualAmountIncr")) {
                            bo.setVirtualAmount(
                                    NumberUtil.add(
                                            item.getVirtualAmountIncr(), bo.getVirtualAmount()));
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpCode")) {
                            bo.setGcpCode(item.getGcpCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importDateTime")) {
                            bo.setImportDateTime(item.getImportDateTime());
                        }
                    }
                } else {
                    DrugOriginBatchInventoryBO subBo = new DrugOriginBatchInventoryBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "storageCode")) {
                        subBo.setStorageCode(item.getStorageCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amountIncr")) {
                        subBo.setAmount(NumberUtil.add(item.getAmountIncr(), subBo.getAmount()));
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplyId")) {
                        subBo.setSupplyId(item.getSupplyId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchId")) {
                        subBo.setBatchId(item.getBatchId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "virtualAmountIncr")) {
                        subBo.setVirtualAmount(
                                NumberUtil.add(
                                        item.getVirtualAmountIncr(), subBo.getVirtualAmount()));
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "gcpCode")) {
                        subBo.setGcpCode(item.getGcpCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importDateTime")) {
                        subBo.setImportDateTime(item.getImportDateTime());
                    }
                    subBo.setDrugOriginInventoryBO(drugOriginInventoryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "drug_origin_batch_inventory")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建药品总库存 */
    @AutoGenerated(locked = true)
    protected CreateDrugOriginInventoryBoResult createDrugOriginInventoryBase(
            CreateDrugOriginInventoryBto createDrugOriginInventoryBto) {
        if (createDrugOriginInventoryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDrugOriginInventoryBoResult boResult = new CreateDrugOriginInventoryBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                createCreateDrugOriginInventoryOnDuplicateThrowEx(
                        boResult, createDrugOriginInventoryBto);
        if (drugOriginInventoryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugOriginInventoryBto, "__$validPropertySet"),
                    "drugOriginBatchInventoryBtoList")) {
                createDrugOriginBatchInventoryBtoOnDuplicateThrowEx(
                        boResult, createDrugOriginInventoryBto, drugOriginInventoryBO);
            }
        }
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO createMergeDrugOriginInventoryOnDuplicateUpdate(
            MergeDrugOriginInventoryBoResult boResult,
            MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeDrugOriginInventoryBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getById(mergeDrugOriginInventoryBto.getId());
            if (drugOriginInventoryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull =
                (mergeDrugOriginInventoryBto.getStorageCode() == null)
                        && (mergeDrugOriginInventoryBto.getDrugOriginCode() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getByStorageCodeAndDrugOriginCode(
                            mergeDrugOriginInventoryBto.getStorageCode(),
                            mergeDrugOriginInventoryBto.getDrugOriginCode());
            if (drugOriginInventoryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'storage_code'";
                matchedUkName += ",";
                matchedUkName += "'drug_origin_code'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (drugOriginInventoryBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
                updatedBto.setBto(mergeDrugOriginInventoryBto);
                updatedBto.setBo(drugOriginInventoryBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugOriginInventoryBO.setStorageCode(
                            mergeDrugOriginInventoryBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "drugOriginCode")) {
                    drugOriginInventoryBO.setDrugOriginCode(
                            mergeDrugOriginInventoryBto.getDrugOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "amountIncr")) {
                    drugOriginInventoryBO.setAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getAmountIncr(),
                                    drugOriginInventoryBO.getAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "preOccupiedAmountIncr")) {
                    drugOriginInventoryBO.setPreOccupiedAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getPreOccupiedAmountIncr(),
                                    drugOriginInventoryBO.getPreOccupiedAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "inTransitAmountIncr")) {
                    drugOriginInventoryBO.setInTransitAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getInTransitAmountIncr(),
                                    drugOriginInventoryBO.getInTransitAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "virtualAmountIncr")) {
                    drugOriginInventoryBO.setVirtualAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getVirtualAmountIncr(),
                                    drugOriginInventoryBO.getVirtualAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "useFrequency")) {
                    drugOriginInventoryBO.setUseFrequency(
                            mergeDrugOriginInventoryBto.getUseFrequency());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "drugOriginSpecificationId")) {
                    drugOriginInventoryBO.setDrugOriginSpecificationId(
                            mergeDrugOriginInventoryBto.getDrugOriginSpecificationId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
                updatedBto.setBto(mergeDrugOriginInventoryBto);
                updatedBto.setBo(drugOriginInventoryBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugOriginInventoryBO.setStorageCode(
                            mergeDrugOriginInventoryBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "drugOriginCode")) {
                    drugOriginInventoryBO.setDrugOriginCode(
                            mergeDrugOriginInventoryBto.getDrugOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "amountIncr")) {
                    drugOriginInventoryBO.setAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getAmountIncr(),
                                    drugOriginInventoryBO.getAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "preOccupiedAmountIncr")) {
                    drugOriginInventoryBO.setPreOccupiedAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getPreOccupiedAmountIncr(),
                                    drugOriginInventoryBO.getPreOccupiedAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "inTransitAmountIncr")) {
                    drugOriginInventoryBO.setInTransitAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getInTransitAmountIncr(),
                                    drugOriginInventoryBO.getInTransitAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "virtualAmountIncr")) {
                    drugOriginInventoryBO.setVirtualAmount(
                            NumberUtil.add(
                                    mergeDrugOriginInventoryBto.getVirtualAmountIncr(),
                                    drugOriginInventoryBO.getVirtualAmount()));
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "useFrequency")) {
                    drugOriginInventoryBO.setUseFrequency(
                            mergeDrugOriginInventoryBto.getUseFrequency());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugOriginInventoryBto, "__$validPropertySet"),
                        "drugOriginSpecificationId")) {
                    drugOriginInventoryBO.setDrugOriginSpecificationId(
                            mergeDrugOriginInventoryBto.getDrugOriginSpecificationId());
                }
            }
        } else {
            drugOriginInventoryBO = new DrugOriginInventoryBO();
            if (pkExist) {
                drugOriginInventoryBO.setId(mergeDrugOriginInventoryBto.getId());
            } else {
                drugOriginInventoryBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_origin_inventory")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "storageCode")) {
                drugOriginInventoryBO.setStorageCode(mergeDrugOriginInventoryBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "drugOriginCode")) {
                drugOriginInventoryBO.setDrugOriginCode(
                        mergeDrugOriginInventoryBto.getDrugOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "amountIncr")) {
                drugOriginInventoryBO.setAmount(
                        NumberUtil.add(
                                mergeDrugOriginInventoryBto.getAmountIncr(),
                                drugOriginInventoryBO.getAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "preOccupiedAmountIncr")) {
                drugOriginInventoryBO.setPreOccupiedAmount(
                        NumberUtil.add(
                                mergeDrugOriginInventoryBto.getPreOccupiedAmountIncr(),
                                drugOriginInventoryBO.getPreOccupiedAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "inTransitAmountIncr")) {
                drugOriginInventoryBO.setInTransitAmount(
                        NumberUtil.add(
                                mergeDrugOriginInventoryBto.getInTransitAmountIncr(),
                                drugOriginInventoryBO.getInTransitAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "virtualAmountIncr")) {
                drugOriginInventoryBO.setVirtualAmount(
                        NumberUtil.add(
                                mergeDrugOriginInventoryBto.getVirtualAmountIncr(),
                                drugOriginInventoryBO.getVirtualAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "useFrequency")) {
                drugOriginInventoryBO.setUseFrequency(
                        mergeDrugOriginInventoryBto.getUseFrequency());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "drugOriginSpecificationId")) {
                drugOriginInventoryBO.setDrugOriginSpecificationId(
                        mergeDrugOriginInventoryBto.getDrugOriginSpecificationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeDrugOriginInventoryBto);
            addedBto.setBo(drugOriginInventoryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugOriginInventoryBO;
    }

    /** 保存总库存及批次库存，增量更新数量 */
    @AutoGenerated(locked = true)
    protected MergeDrugOriginInventoryBoResult mergeDrugOriginInventoryBase(
            MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto) {
        if (mergeDrugOriginInventoryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDrugOriginInventoryBoResult boResult = new MergeDrugOriginInventoryBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                createMergeDrugOriginInventoryOnDuplicateUpdate(
                        boResult, mergeDrugOriginInventoryBto);
        if (drugOriginInventoryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugOriginInventoryBto, "__$validPropertySet"),
                    "drugOriginBatchInventoryBtoList")) {
                createDrugOriginBatchInventoryBtoOnDuplicateUpdate(
                        boResult, mergeDrugOriginInventoryBto, drugOriginInventoryBO);
            }
        }
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    /** 增量保存药品批次库存并且更新药品总库存数量 */
    @AutoGenerated(locked = true)
    protected SaveDrugOriginBatchInventoryListBoResult saveDrugOriginBatchInventoryListBase(
            SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto) {
        if (saveDrugOriginBatchInventoryListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveDrugOriginBatchInventoryListBoResult boResult =
                new SaveDrugOriginBatchInventoryListBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                updateSaveDrugOriginBatchInventoryListOnMissThrowEx(
                        boResult, saveDrugOriginBatchInventoryListBto);
        if (drugOriginInventoryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugOriginBatchInventoryListBto, "__$validPropertySet"),
                    "drugOriginBatchInventoryBtoList")) {
                createDrugOriginBatchInventoryBtoOnDuplicateUpdate(
                        boResult, saveDrugOriginBatchInventoryListBto, drugOriginInventoryBO);
            }
        }
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    /** 更新对象:createDrugOriginBatchInventory,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO updateCreateDrugOriginBatchInventoryOnMissThrowEx(
            BaseDrugOriginInventoryBOService.CreateDrugOriginBatchInventoryBoResult boResult,
            CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createDrugOriginBatchInventoryBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getById(createDrugOriginBatchInventoryBto.getId());
            found = true;
        }
        if (drugOriginInventoryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
            updatedBto.setBto(createDrugOriginBatchInventoryBto);
            updatedBto.setBo(drugOriginInventoryBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugOriginInventoryBO;
        }
    }

    /** 更新对象:drugOriginBatchInventoryBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugOriginBatchInventoryBtoOnMissThrowEx(
            BaseDrugOriginInventoryBOService.UpdateDrugOriginInventoryAmountBoResult boResult,
            UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto,
            DrugOriginInventoryBO drugOriginInventoryBO) {
        if (CollectionUtil.isNotEmpty(
                updateDrugOriginInventoryAmountBto.getDrugOriginBatchInventoryBtoList())) {
            for (UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto bto :
                    updateDrugOriginInventoryAmountBto.getDrugOriginBatchInventoryBtoList()) {
                Optional<DrugOriginBatchInventoryBO> any =
                        drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().stream()
                                .filter(
                                        drugOriginBatchInventoryBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugOriginBatchInventoryBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugOriginBatchInventoryBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugOriginBatchInventory());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "amountIncr")) {
                        bo.setAmount(NumberUtil.add(bto.getAmountIncr(), bo.getAmount()));
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:drugOriginBatchInventoryBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugOriginBatchInventoryBtoOnMissThrowEx(
            BaseDrugOriginInventoryBOService.UpdateDrugOriginInventoryPriceBoResult boResult,
            UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto,
            DrugOriginInventoryBO drugOriginInventoryBO) {
        if (CollectionUtil.isNotEmpty(
                updateDrugOriginInventoryPriceBto.getDrugOriginBatchInventoryBtoList())) {
            for (UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto bto :
                    updateDrugOriginInventoryPriceBto.getDrugOriginBatchInventoryBtoList()) {
                Optional<DrugOriginBatchInventoryBO> any =
                        drugOriginInventoryBO.getDrugOriginBatchInventoryBOSet().stream()
                                .filter(
                                        drugOriginBatchInventoryBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugOriginBatchInventoryBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugOriginBatchInventoryBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugOriginBatchInventory());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "purchasePrice")) {
                        bo.setPurchasePrice(bto.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "retailPrice")) {
                        bo.setRetailPrice(bto.getRetailPrice());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新药品库存总数量以及批次库存数量 */
    @AutoGenerated(locked = true)
    protected UpdateDrugOriginInventoryAmountBoResult updateDrugOriginInventoryAmountBase(
            UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto) {
        if (updateDrugOriginInventoryAmountBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugOriginInventoryAmountBoResult boResult =
                new UpdateDrugOriginInventoryAmountBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                updateUpdateDrugOriginInventoryAmountOnMissThrowEx(
                        boResult, updateDrugOriginInventoryAmountBto);
        if (drugOriginInventoryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugOriginInventoryAmountBto, "__$validPropertySet"),
                    "drugOriginBatchInventoryBtoList")) {
                updateDrugOriginBatchInventoryBtoOnMissThrowEx(
                        boResult, updateDrugOriginInventoryAmountBto, drugOriginInventoryBO);
            }
        }
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    /** 更新批次库存价格 */
    @AutoGenerated(locked = true)
    protected UpdateDrugOriginInventoryPriceBoResult updateDrugOriginInventoryPriceBase(
            UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto) {
        if (updateDrugOriginInventoryPriceBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugOriginInventoryPriceBoResult boResult =
                new UpdateDrugOriginInventoryPriceBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                updateUpdateDrugOriginInventoryPriceOnMissThrowEx(
                        boResult, updateDrugOriginInventoryPriceBto);
        if (drugOriginInventoryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugOriginInventoryPriceBto, "__$validPropertySet"),
                    "drugOriginBatchInventoryBtoList")) {
                updateDrugOriginBatchInventoryBtoOnMissThrowEx(
                        boResult, updateDrugOriginInventoryPriceBto, drugOriginInventoryBO);
            }
        }
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    /** 更新对象:saveDrugOriginBatchInventoryList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO updateSaveDrugOriginBatchInventoryListOnMissThrowEx(
            BaseDrugOriginInventoryBOService.SaveDrugOriginBatchInventoryListBoResult boResult,
            SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveDrugOriginBatchInventoryListBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getById(saveDrugOriginBatchInventoryListBto.getId());
            found = true;
        }
        if (drugOriginInventoryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
            updatedBto.setBto(saveDrugOriginBatchInventoryListBto);
            updatedBto.setBo(drugOriginInventoryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugOriginBatchInventoryListBto, "__$validPropertySet"),
                    "amountIncr")) {
                drugOriginInventoryBO.setAmount(
                        NumberUtil.add(
                                saveDrugOriginBatchInventoryListBto.getAmountIncr(),
                                drugOriginInventoryBO.getAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugOriginBatchInventoryListBto, "__$validPropertySet"),
                    "preOccupiedAmountIncr")) {
                drugOriginInventoryBO.setPreOccupiedAmount(
                        NumberUtil.add(
                                saveDrugOriginBatchInventoryListBto.getPreOccupiedAmountIncr(),
                                drugOriginInventoryBO.getPreOccupiedAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugOriginBatchInventoryListBto, "__$validPropertySet"),
                    "inTransitAmountIncr")) {
                drugOriginInventoryBO.setInTransitAmount(
                        NumberUtil.add(
                                saveDrugOriginBatchInventoryListBto.getInTransitAmountIncr(),
                                drugOriginInventoryBO.getInTransitAmount()));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugOriginBatchInventoryListBto, "__$validPropertySet"),
                    "virtualAmountIncr")) {
                drugOriginInventoryBO.setVirtualAmount(
                        NumberUtil.add(
                                saveDrugOriginBatchInventoryListBto.getVirtualAmountIncr(),
                                drugOriginInventoryBO.getVirtualAmount()));
            }
            return drugOriginInventoryBO;
        }
    }

    /** 更新对象:updateDrugOriginInventoryAmount,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO updateUpdateDrugOriginInventoryAmountOnMissThrowEx(
            UpdateDrugOriginInventoryAmountBoResult boResult,
            UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugOriginInventoryAmountBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getById(updateDrugOriginInventoryAmountBto.getId());
            found = true;
        }
        if (drugOriginInventoryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
            updatedBto.setBto(updateDrugOriginInventoryAmountBto);
            updatedBto.setBo(drugOriginInventoryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugOriginInventoryAmountBto, "__$validPropertySet"),
                    "amountIncr")) {
                drugOriginInventoryBO.setAmount(
                        NumberUtil.add(
                                updateDrugOriginInventoryAmountBto.getAmountIncr(),
                                drugOriginInventoryBO.getAmount()));
            }
            return drugOriginInventoryBO;
        }
    }

    /** 更新对象:updateDrugOriginInventoryPrice,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO updateUpdateDrugOriginInventoryPriceOnMissThrowEx(
            UpdateDrugOriginInventoryPriceBoResult boResult,
            UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugOriginInventoryPriceBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO =
                    DrugOriginInventoryBO.getById(updateDrugOriginInventoryPriceBto.getId());
            found = true;
        }
        if (drugOriginInventoryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
            updatedBto.setBto(updateDrugOriginInventoryPriceBto);
            updatedBto.setBo(drugOriginInventoryBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugOriginInventoryBO;
        }
    }

    /** 更新对象:updateVitalAmount,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugOriginInventoryBO updateUpdateVitalAmountOnMissThrowEx(
            BaseDrugOriginInventoryBOService.UpdateVitalAmountBoResult boResult,
            UpdateVitalAmountBto updateVitalAmountBto) {
        DrugOriginInventoryBO drugOriginInventoryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateVitalAmountBto.getId() == null);
        if (!allNull && !found) {
            drugOriginInventoryBO = DrugOriginInventoryBO.getById(updateVitalAmountBto.getId());
            found = true;
        }
        if (drugOriginInventoryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugOriginInventoryBO.convertToDrugOriginInventory());
            updatedBto.setBto(updateVitalAmountBto);
            updatedBto.setBo(drugOriginInventoryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateVitalAmountBto, "__$validPropertySet"),
                    "virtualAmountIncr")) {
                drugOriginInventoryBO.setVirtualAmount(
                        NumberUtil.add(
                                updateVitalAmountBto.getVirtualAmountIncr(),
                                drugOriginInventoryBO.getVirtualAmount()));
            }
            return drugOriginInventoryBO;
        }
    }

    /** 功能：库存管理-修改商品虚库存 */
    @AutoGenerated(locked = true)
    protected UpdateVitalAmountBoResult updateVitalAmountBase(
            UpdateVitalAmountBto updateVitalAmountBto) {
        if (updateVitalAmountBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateVitalAmountBoResult boResult = new UpdateVitalAmountBoResult();
        DrugOriginInventoryBO drugOriginInventoryBO =
                updateUpdateVitalAmountOnMissThrowEx(boResult, updateVitalAmountBto);
        boResult.setRootBo(drugOriginInventoryBO);
        return boResult;
    }

    public static class UpdateVitalAmountBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateVitalAmountBto, DrugOriginInventoryBO> getCreatedBto(
                UpdateVitalAmountBto updateVitalAmountBto) {
            return this.getAddedResult(updateVitalAmountBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateVitalAmountBto, DrugOriginInventory, DrugOriginInventoryBO>
                getUpdatedBto(UpdateVitalAmountBto updateVitalAmountBto) {
            return super.getUpdatedResult(updateVitalAmountBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateVitalAmountBto, DrugOriginInventoryBO> getUnmodifiedBto(
                UpdateVitalAmountBto updateVitalAmountBto) {
            return super.getUnmodifiedResult(updateVitalAmountBto);
        }
    }

    public static class CreateDrugOriginInventoryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getCreatedBto(
                        CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return this.getAddedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDrugOriginInventoryBto, DrugOriginInventoryBO> getCreatedBto(
                CreateDrugOriginInventoryBto createDrugOriginInventoryBto) {
            return this.getAddedResult(createDrugOriginInventoryBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginBatchInventory getDeleted_DrugOriginBatchInventory() {
            return (DrugOriginBatchInventory)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugOriginBatchInventory.class));
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventory,
                        DrugOriginBatchInventoryBO>
                getUpdatedBto(
                        CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUpdatedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateDrugOriginInventoryBto, DrugOriginInventory, DrugOriginInventoryBO>
                getUpdatedBto(CreateDrugOriginInventoryBto createDrugOriginInventoryBto) {
            return super.getUpdatedResult(createDrugOriginInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getUnmodifiedBto(
                        CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDrugOriginInventoryBto, DrugOriginInventoryBO> getUnmodifiedBto(
                CreateDrugOriginInventoryBto createDrugOriginInventoryBto) {
            return super.getUnmodifiedResult(createDrugOriginInventoryBto);
        }
    }

    public static class CreateDrugOriginBatchInventoryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getCreatedBto(
                        CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return this.getAddedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDrugOriginBatchInventoryBto, DrugOriginInventoryBO> getCreatedBto(
                CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto) {
            return this.getAddedResult(createDrugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginBatchInventory getDeleted_DrugOriginBatchInventory() {
            return (DrugOriginBatchInventory)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugOriginBatchInventory.class));
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventory,
                        DrugOriginBatchInventoryBO>
                getUpdatedBto(
                        CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUpdatedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDrugOriginBatchInventoryBto,
                        DrugOriginInventory,
                        DrugOriginInventoryBO>
                getUpdatedBto(CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto) {
            return super.getUpdatedResult(createDrugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getUnmodifiedBto(
                        CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDrugOriginBatchInventoryBto, DrugOriginInventoryBO>
                getUnmodifiedBto(
                        CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(createDrugOriginBatchInventoryBto);
        }
    }

    public static class SaveDrugOriginBatchInventoryListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getCreatedBto(
                        SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return this.getAddedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveDrugOriginBatchInventoryListBto, DrugOriginInventoryBO> getCreatedBto(
                SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto) {
            return this.getAddedResult(saveDrugOriginBatchInventoryListBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginBatchInventory getDeleted_DrugOriginBatchInventory() {
            return (DrugOriginBatchInventory)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugOriginBatchInventory.class));
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventory,
                        DrugOriginBatchInventoryBO>
                getUpdatedBto(
                        SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUpdatedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveDrugOriginBatchInventoryListBto,
                        DrugOriginInventory,
                        DrugOriginInventoryBO>
                getUpdatedBto(
                        SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto) {
            return super.getUpdatedResult(saveDrugOriginBatchInventoryListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getUnmodifiedBto(
                        SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveDrugOriginBatchInventoryListBto, DrugOriginInventoryBO>
                getUnmodifiedBto(
                        SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto) {
            return super.getUnmodifiedResult(saveDrugOriginBatchInventoryListBto);
        }
    }

    public static class UpdateDrugOriginInventoryAmountBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getCreatedBto(
                        UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return this.getAddedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugOriginInventoryAmountBto, DrugOriginInventoryBO> getCreatedBto(
                UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto) {
            return this.getAddedResult(updateDrugOriginInventoryAmountBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginBatchInventory getDeleted_DrugOriginBatchInventory() {
            return (DrugOriginBatchInventory)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugOriginBatchInventory.class));
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventory,
                        DrugOriginBatchInventoryBO>
                getUpdatedBto(
                        UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUpdatedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugOriginInventoryAmountBto,
                        DrugOriginInventory,
                        DrugOriginInventoryBO>
                getUpdatedBto(
                        UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto) {
            return super.getUpdatedResult(updateDrugOriginInventoryAmountBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getUnmodifiedBto(
                        UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugOriginInventoryAmountBto, DrugOriginInventoryBO>
                getUnmodifiedBto(
                        UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto) {
            return super.getUnmodifiedResult(updateDrugOriginInventoryAmountBto);
        }
    }

    public static class UpdateDrugOriginInventoryPriceBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getCreatedBto(
                        UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return this.getAddedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugOriginInventoryPriceBto, DrugOriginInventoryBO> getCreatedBto(
                UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto) {
            return this.getAddedResult(updateDrugOriginInventoryPriceBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginBatchInventory getDeleted_DrugOriginBatchInventory() {
            return (DrugOriginBatchInventory)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugOriginBatchInventory.class));
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventory,
                        DrugOriginBatchInventoryBO>
                getUpdatedBto(
                        UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUpdatedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugOriginInventoryPriceBto,
                        DrugOriginInventory,
                        DrugOriginInventoryBO>
                getUpdatedBto(UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto) {
            return super.getUpdatedResult(updateDrugOriginInventoryPriceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getUnmodifiedBto(
                        UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugOriginInventoryPriceBto, DrugOriginInventoryBO>
                getUnmodifiedBto(
                        UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto) {
            return super.getUnmodifiedResult(updateDrugOriginInventoryPriceBto);
        }
    }

    public static class MergeDrugOriginInventoryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugOriginInventoryBO getRootBo() {
            return (DrugOriginInventoryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getCreatedBto(
                        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return this.getAddedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDrugOriginInventoryBto, DrugOriginInventoryBO> getCreatedBto(
                MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto) {
            return this.getAddedResult(mergeDrugOriginInventoryBto);
        }

        @AutoGenerated(locked = true)
        public DrugOriginBatchInventory getDeleted_DrugOriginBatchInventory() {
            return (DrugOriginBatchInventory)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugOriginBatchInventory.class));
        }

        @AutoGenerated(locked = true)
        public DrugOriginInventory getDeleted_DrugOriginInventory() {
            return (DrugOriginInventory)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugOriginInventory.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventory,
                        DrugOriginBatchInventoryBO>
                getUpdatedBto(
                        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUpdatedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeDrugOriginInventoryBto, DrugOriginInventory, DrugOriginInventoryBO>
                getUpdatedBto(MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto) {
            return super.getUpdatedResult(mergeDrugOriginInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                        DrugOriginBatchInventoryBO>
                getUnmodifiedBto(
                        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                                drugOriginBatchInventoryBto) {
            return super.getUnmodifiedResult(drugOriginBatchInventoryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDrugOriginInventoryBto, DrugOriginInventoryBO> getUnmodifiedBto(
                MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto) {
            return super.getUnmodifiedResult(mergeDrugOriginInventoryBto);
        }
    }
}
