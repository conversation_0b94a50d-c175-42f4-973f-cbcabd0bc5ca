package com.pulse.drug_inventory.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.bo.DrugStocktakingBO;
import com.pulse.drug_inventory.persist.dos.DrugStocktaking;
import com.pulse.drug_inventory.persist.dos.DrugStocktakingDetail;
import com.pulse.drug_inventory.service.base.BaseDrugStocktakingBOService.DeleteStocktakingBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugStocktakingBOService.MergeStocktakingBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugStocktakingBOService.UpdateDrugStocktakingForSummaryOrRevokeBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugStocktakingBOService.UpdateDrugStocktakingStatusBoResult;
import com.pulse.drug_inventory.service.bto.DeleteStocktakingBto;
import com.pulse.drug_inventory.service.bto.MergeStocktakingBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugStocktakingForSummaryOrRevokeBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugStocktakingStatusBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "b7eb4347-956f-3b44-a6bc-c2d2efa7fc11")
public class BaseDrugStocktakingBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 创建对象:DrugStocktakingDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugStocktakingDetailBtoOnDuplicateUpdate(
            BaseDrugStocktakingBOService.MergeStocktakingBoResult boResult,
            MergeStocktakingBto mergeStocktakingBto,
            DrugStocktakingBO drugStocktakingBO) {
        if (CollectionUtil.isEmpty(mergeStocktakingBto.getDrugStocktakingDetailBtoList())) {
            mergeStocktakingBto.setDrugStocktakingDetailBtoList(List.of());
        }
        drugStocktakingBO
                .getDrugStocktakingDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStocktakingBto.getDrugStocktakingDetailBtoList().stream()
                                            .filter(
                                                    drugStocktakingDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (drugStocktakingDetailBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            drugStocktakingDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToDrugStocktakingDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeStocktakingBto.getDrugStocktakingDetailBtoList())) {
            for (MergeStocktakingBto.DrugStocktakingDetailBto item :
                    mergeStocktakingBto.getDrugStocktakingDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugStocktakingDetailBO> any =
                        drugStocktakingBO.getDrugStocktakingDetailBOSet().stream()
                                .filter(
                                        drugStocktakingDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugStocktakingDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugStocktakingDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugStocktakingDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginName")) {
                            bo.setDrugOriginName(item.getDrugOriginName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "specification")) {
                            bo.setSpecification(item.getSpecification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amountPerPackage")) {
                            bo.setAmountPerPackage(item.getAmountPerPackage());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugProducerId")) {
                            bo.setDrugProducerId(item.getDrugProducerId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockPrice")) {
                            bo.setStockPrice(item.getStockPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "locationCode")) {
                            bo.setLocationCode(item.getLocationCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "profitLossType")) {
                            bo.setProfitLossType(item.getProfitLossType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "differenceAmount")) {
                            bo.setDifferenceAmount(item.getDifferenceAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageDifferenceAmount")) {
                            bo.setPackageDifferenceAmount(item.getPackageDifferenceAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "minDifferenceAmount")) {
                            bo.setMinDifferenceAmount(item.getMinDifferenceAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "differenceStockCost")) {
                            bo.setDifferenceStockCost(item.getDifferenceStockCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockAmount")) {
                            bo.setStockAmount(item.getStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageStockAmount")) {
                            bo.setPackageStockAmount(item.getPackageStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "minStockAmount")) {
                            bo.setMinStockAmount(item.getMinStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stocktakingAmount")) {
                            bo.setStocktakingAmount(item.getStocktakingAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageStocktakingAmount")) {
                            bo.setPackageStocktakingAmount(item.getPackageStocktakingAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "minStocktakingAmount")) {
                            bo.setMinStocktakingAmount(item.getMinStocktakingAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountTypeCode")) {
                            bo.setAccountTypeCode(item.getAccountTypeCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "summaryStocktakingDetailId")) {
                            bo.setSummaryStocktakingDetailId(item.getSummaryStocktakingDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "manageMode")) {
                            bo.setManageMode(item.getManageMode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "differenceRetailCost")) {
                            bo.setDifferenceRetailCost(item.getDifferenceRetailCost());
                        }
                    } else {
                        DrugStocktakingDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugStocktakingDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginName")) {
                            bo.setDrugOriginName(item.getDrugOriginName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "specification")) {
                            bo.setSpecification(item.getSpecification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amountPerPackage")) {
                            bo.setAmountPerPackage(item.getAmountPerPackage());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugProducerId")) {
                            bo.setDrugProducerId(item.getDrugProducerId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockPrice")) {
                            bo.setStockPrice(item.getStockPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "locationCode")) {
                            bo.setLocationCode(item.getLocationCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "profitLossType")) {
                            bo.setProfitLossType(item.getProfitLossType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "differenceAmount")) {
                            bo.setDifferenceAmount(item.getDifferenceAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageDifferenceAmount")) {
                            bo.setPackageDifferenceAmount(item.getPackageDifferenceAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "minDifferenceAmount")) {
                            bo.setMinDifferenceAmount(item.getMinDifferenceAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "differenceStockCost")) {
                            bo.setDifferenceStockCost(item.getDifferenceStockCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockAmount")) {
                            bo.setStockAmount(item.getStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageStockAmount")) {
                            bo.setPackageStockAmount(item.getPackageStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "minStockAmount")) {
                            bo.setMinStockAmount(item.getMinStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stocktakingAmount")) {
                            bo.setStocktakingAmount(item.getStocktakingAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageStocktakingAmount")) {
                            bo.setPackageStocktakingAmount(item.getPackageStocktakingAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "minStocktakingAmount")) {
                            bo.setMinStocktakingAmount(item.getMinStocktakingAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountTypeCode")) {
                            bo.setAccountTypeCode(item.getAccountTypeCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "summaryStocktakingDetailId")) {
                            bo.setSummaryStocktakingDetailId(item.getSummaryStocktakingDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "manageMode")) {
                            bo.setManageMode(item.getManageMode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "differenceRetailCost")) {
                            bo.setDifferenceRetailCost(item.getDifferenceRetailCost());
                        }
                    }
                } else {
                    DrugStocktakingDetailBO subBo = new DrugStocktakingDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchInventoryId")) {
                        subBo.setBatchInventoryId(item.getBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginName")) {
                        subBo.setDrugOriginName(item.getDrugOriginName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "specification")) {
                        subBo.setSpecification(item.getSpecification());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "unit")) {
                        subBo.setUnit(item.getUnit());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amountPerPackage")) {
                        subBo.setAmountPerPackage(item.getAmountPerPackage());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugProducerId")) {
                        subBo.setDrugProducerId(item.getDrugProducerId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockPrice")) {
                        subBo.setStockPrice(item.getStockPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "locationCode")) {
                        subBo.setLocationCode(item.getLocationCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "remark")) {
                        subBo.setRemark(item.getRemark());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "profitLossType")) {
                        subBo.setProfitLossType(item.getProfitLossType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "differenceAmount")) {
                        subBo.setDifferenceAmount(item.getDifferenceAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "packageDifferenceAmount")) {
                        subBo.setPackageDifferenceAmount(item.getPackageDifferenceAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "minDifferenceAmount")) {
                        subBo.setMinDifferenceAmount(item.getMinDifferenceAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "differenceStockCost")) {
                        subBo.setDifferenceStockCost(item.getDifferenceStockCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockAmount")) {
                        subBo.setStockAmount(item.getStockAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "packageStockAmount")) {
                        subBo.setPackageStockAmount(item.getPackageStockAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "minStockAmount")) {
                        subBo.setMinStockAmount(item.getMinStockAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stocktakingAmount")) {
                        subBo.setStocktakingAmount(item.getStocktakingAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "packageStocktakingAmount")) {
                        subBo.setPackageStocktakingAmount(item.getPackageStocktakingAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "minStocktakingAmount")) {
                        subBo.setMinStocktakingAmount(item.getMinStocktakingAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "accountTypeCode")) {
                        subBo.setAccountTypeCode(item.getAccountTypeCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "summaryStocktakingDetailId")) {
                        subBo.setSummaryStocktakingDetailId(item.getSummaryStocktakingDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "manageMode")) {
                        subBo.setManageMode(item.getManageMode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "differenceRetailCost")) {
                        subBo.setDifferenceRetailCost(item.getDifferenceRetailCost());
                    }
                    subBo.setDrugStocktakingBO(drugStocktakingBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("drug_stocktaking_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugStocktakingBO.getDrugStocktakingDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugStocktakingBO createMergeStocktakingOnDuplicateUpdate(
            MergeStocktakingBoResult boResult, MergeStocktakingBto mergeStocktakingBto) {
        DrugStocktakingBO drugStocktakingBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeStocktakingBto.getId() == null);
        if (!allNull && !found) {
            drugStocktakingBO = DrugStocktakingBO.getById(mergeStocktakingBto.getId());
            if (drugStocktakingBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugStocktakingBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugStocktakingBO.convertToDrugStocktaking());
                updatedBto.setBto(mergeStocktakingBto);
                updatedBto.setBo(drugStocktakingBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingNumber")) {
                    drugStocktakingBO.setStocktakingNumber(
                            mergeStocktakingBto.getStocktakingNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingCode")) {
                    drugStocktakingBO.setStocktakingCode(mergeStocktakingBto.getStocktakingCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugStocktakingBO.setStorageCode(mergeStocktakingBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "accountantDateTime")) {
                    drugStocktakingBO.setAccountantDateTime(
                            mergeStocktakingBto.getAccountantDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "createBy")) {
                    drugStocktakingBO.setCreateBy(mergeStocktakingBto.getCreateBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "accountantBy")) {
                    drugStocktakingBO.setAccountantBy(mergeStocktakingBto.getAccountantBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "remark")) {
                    drugStocktakingBO.setRemark(mergeStocktakingBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "herbType")) {
                    drugStocktakingBO.setHerbType(mergeStocktakingBto.getHerbType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingStatus")) {
                    drugStocktakingBO.setStocktakingStatus(
                            mergeStocktakingBto.getStocktakingStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "exportImportCode")) {
                    drugStocktakingBO.setExportImportCode(
                            mergeStocktakingBto.getExportImportCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "accountingPeriod")) {
                    drugStocktakingBO.setAccountingPeriod(
                            mergeStocktakingBto.getAccountingPeriod());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingType")) {
                    drugStocktakingBO.setStocktakingType(mergeStocktakingBto.getStocktakingType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "summaryStocktakingId")) {
                    drugStocktakingBO.setSummaryStocktakingId(
                            mergeStocktakingBto.getSummaryStocktakingId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugStocktakingBO.convertToDrugStocktaking());
                updatedBto.setBto(mergeStocktakingBto);
                updatedBto.setBo(drugStocktakingBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingNumber")) {
                    drugStocktakingBO.setStocktakingNumber(
                            mergeStocktakingBto.getStocktakingNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingCode")) {
                    drugStocktakingBO.setStocktakingCode(mergeStocktakingBto.getStocktakingCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugStocktakingBO.setStorageCode(mergeStocktakingBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "accountantDateTime")) {
                    drugStocktakingBO.setAccountantDateTime(
                            mergeStocktakingBto.getAccountantDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "createBy")) {
                    drugStocktakingBO.setCreateBy(mergeStocktakingBto.getCreateBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "accountantBy")) {
                    drugStocktakingBO.setAccountantBy(mergeStocktakingBto.getAccountantBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "remark")) {
                    drugStocktakingBO.setRemark(mergeStocktakingBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "herbType")) {
                    drugStocktakingBO.setHerbType(mergeStocktakingBto.getHerbType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingStatus")) {
                    drugStocktakingBO.setStocktakingStatus(
                            mergeStocktakingBto.getStocktakingStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "exportImportCode")) {
                    drugStocktakingBO.setExportImportCode(
                            mergeStocktakingBto.getExportImportCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "accountingPeriod")) {
                    drugStocktakingBO.setAccountingPeriod(
                            mergeStocktakingBto.getAccountingPeriod());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "stocktakingType")) {
                    drugStocktakingBO.setStocktakingType(mergeStocktakingBto.getStocktakingType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStocktakingBto, "__$validPropertySet"),
                        "summaryStocktakingId")) {
                    drugStocktakingBO.setSummaryStocktakingId(
                            mergeStocktakingBto.getSummaryStocktakingId());
                }
            }
        } else {
            drugStocktakingBO = new DrugStocktakingBO();
            if (pkExist) {
                drugStocktakingBO.setId(mergeStocktakingBto.getId());
            } else {
                drugStocktakingBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_stocktaking")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "stocktakingNumber")) {
                drugStocktakingBO.setStocktakingNumber(mergeStocktakingBto.getStocktakingNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "stocktakingCode")) {
                drugStocktakingBO.setStocktakingCode(mergeStocktakingBto.getStocktakingCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "storageCode")) {
                drugStocktakingBO.setStorageCode(mergeStocktakingBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "accountantDateTime")) {
                drugStocktakingBO.setAccountantDateTime(
                        mergeStocktakingBto.getAccountantDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "createBy")) {
                drugStocktakingBO.setCreateBy(mergeStocktakingBto.getCreateBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "accountantBy")) {
                drugStocktakingBO.setAccountantBy(mergeStocktakingBto.getAccountantBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "remark")) {
                drugStocktakingBO.setRemark(mergeStocktakingBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "herbType")) {
                drugStocktakingBO.setHerbType(mergeStocktakingBto.getHerbType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "stocktakingStatus")) {
                drugStocktakingBO.setStocktakingStatus(mergeStocktakingBto.getStocktakingStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "exportImportCode")) {
                drugStocktakingBO.setExportImportCode(mergeStocktakingBto.getExportImportCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "accountingPeriod")) {
                drugStocktakingBO.setAccountingPeriod(mergeStocktakingBto.getAccountingPeriod());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "stocktakingType")) {
                drugStocktakingBO.setStocktakingType(mergeStocktakingBto.getStocktakingType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "summaryStocktakingId")) {
                drugStocktakingBO.setSummaryStocktakingId(
                        mergeStocktakingBto.getSummaryStocktakingId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeStocktakingBto);
            addedBto.setBo(drugStocktakingBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugStocktakingBO;
    }

    /** 删除对象:deleteStocktaking,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugStocktakingBO deleteDeleteStocktakingOnMissThrowEx(
            BaseDrugStocktakingBOService.DeleteStocktakingBoResult boResult,
            DeleteStocktakingBto deleteStocktakingBto) {
        DrugStocktakingBO drugStocktakingBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteStocktakingBto.getId() == null);
        if (!allNull && !found) {
            drugStocktakingBO = DrugStocktakingBO.getById(deleteStocktakingBto.getId());
            found = true;
        }
        if (drugStocktakingBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugStocktakingBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteStocktakingBto);
            deletedBto.setEntity(drugStocktakingBO.convertToDrugStocktaking());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugStocktakingBO;
        }
    }

    /** 功能：删除盘存单 */
    @AutoGenerated(locked = true)
    protected DeleteStocktakingBoResult deleteStocktakingBase(
            DeleteStocktakingBto deleteStocktakingBto) {
        if (deleteStocktakingBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteStocktakingBoResult boResult = new DeleteStocktakingBoResult();
        DrugStocktakingBO drugStocktakingBO =
                deleteDeleteStocktakingOnMissThrowEx(boResult, deleteStocktakingBto);
        boResult.setRootBo(drugStocktakingBO);
        return boResult;
    }

    /** 功能：盘存-保存盘存单 */
    @AutoGenerated(locked = true)
    protected MergeStocktakingBoResult mergeStocktakingBase(
            MergeStocktakingBto mergeStocktakingBto) {
        if (mergeStocktakingBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeStocktakingBoResult boResult = new MergeStocktakingBoResult();
        DrugStocktakingBO drugStocktakingBO =
                createMergeStocktakingOnDuplicateUpdate(boResult, mergeStocktakingBto);
        if (drugStocktakingBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStocktakingBto, "__$validPropertySet"),
                    "drugStocktakingDetailBtoList")) {
                createDrugStocktakingDetailBtoOnDuplicateUpdate(
                        boResult, mergeStocktakingBto, drugStocktakingBO);
            }
        }
        boResult.setRootBo(drugStocktakingBO);
        return boResult;
    }

    /** 汇总盘存单或撤销汇总盘存单更新分站盘存单 */
    @AutoGenerated(locked = true)
    protected UpdateDrugStocktakingForSummaryOrRevokeBoResult
            updateDrugStocktakingForSummaryOrRevokeBase(
                    UpdateDrugStocktakingForSummaryOrRevokeBto
                            updateDrugStocktakingForSummaryOrRevokeBto) {
        if (updateDrugStocktakingForSummaryOrRevokeBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugStocktakingForSummaryOrRevokeBoResult boResult =
                new UpdateDrugStocktakingForSummaryOrRevokeBoResult();
        DrugStocktakingBO drugStocktakingBO =
                updateUpdateDrugStocktakingForSummaryOrRevokeOnMissThrowEx(
                        boResult, updateDrugStocktakingForSummaryOrRevokeBto);
        boResult.setRootBo(drugStocktakingBO);
        return boResult;
    }

    /** 更新盘存单状态 */
    @AutoGenerated(locked = true)
    protected UpdateDrugStocktakingStatusBoResult updateDrugStocktakingStatusBase(
            UpdateDrugStocktakingStatusBto updateDrugStocktakingStatusBto) {
        if (updateDrugStocktakingStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugStocktakingStatusBoResult boResult = new UpdateDrugStocktakingStatusBoResult();
        DrugStocktakingBO drugStocktakingBO =
                updateUpdateDrugStocktakingStatusOnMissThrowEx(
                        boResult, updateDrugStocktakingStatusBto);
        boResult.setRootBo(drugStocktakingBO);
        return boResult;
    }

    /** 更新对象:updateDrugStocktakingForSummaryOrRevoke,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugStocktakingBO updateUpdateDrugStocktakingForSummaryOrRevokeOnMissThrowEx(
            BaseDrugStocktakingBOService.UpdateDrugStocktakingForSummaryOrRevokeBoResult boResult,
            UpdateDrugStocktakingForSummaryOrRevokeBto updateDrugStocktakingForSummaryOrRevokeBto) {
        DrugStocktakingBO drugStocktakingBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugStocktakingForSummaryOrRevokeBto.getId() == null);
        if (!allNull && !found) {
            drugStocktakingBO =
                    DrugStocktakingBO.getById(updateDrugStocktakingForSummaryOrRevokeBto.getId());
            found = true;
        }
        if (drugStocktakingBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugStocktakingBO.convertToDrugStocktaking());
            updatedBto.setBto(updateDrugStocktakingForSummaryOrRevokeBto);
            updatedBto.setBo(drugStocktakingBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugStocktakingForSummaryOrRevokeBto,
                                    "__$validPropertySet"),
                    "stocktakingStatus")) {
                drugStocktakingBO.setStocktakingStatus(
                        updateDrugStocktakingForSummaryOrRevokeBto.getStocktakingStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugStocktakingForSummaryOrRevokeBto,
                                    "__$validPropertySet"),
                    "summaryStocktakingId")) {
                drugStocktakingBO.setSummaryStocktakingId(
                        updateDrugStocktakingForSummaryOrRevokeBto.getSummaryStocktakingId());
            }
            return drugStocktakingBO;
        }
    }

    /** 更新对象:updateDrugStocktakingStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugStocktakingBO updateUpdateDrugStocktakingStatusOnMissThrowEx(
            BaseDrugStocktakingBOService.UpdateDrugStocktakingStatusBoResult boResult,
            UpdateDrugStocktakingStatusBto updateDrugStocktakingStatusBto) {
        DrugStocktakingBO drugStocktakingBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugStocktakingStatusBto.getId() == null);
        if (!allNull && !found) {
            drugStocktakingBO = DrugStocktakingBO.getById(updateDrugStocktakingStatusBto.getId());
            found = true;
        }
        if (drugStocktakingBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugStocktakingBO.convertToDrugStocktaking());
            updatedBto.setBto(updateDrugStocktakingStatusBto);
            updatedBto.setBo(drugStocktakingBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugStocktakingStatusBto, "__$validPropertySet"),
                    "stocktakingStatus")) {
                drugStocktakingBO.setStocktakingStatus(
                        updateDrugStocktakingStatusBto.getStocktakingStatus());
            }
            return drugStocktakingBO;
        }
    }

    public static class MergeStocktakingBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStocktakingBO getRootBo() {
            return (DrugStocktakingBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStocktakingBto.DrugStocktakingDetailBto, DrugStocktakingDetailBO>
                getCreatedBto(
                        MergeStocktakingBto.DrugStocktakingDetailBto drugStocktakingDetailBto) {
            return this.getAddedResult(drugStocktakingDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStocktakingBto, DrugStocktakingBO> getCreatedBto(
                MergeStocktakingBto mergeStocktakingBto) {
            return this.getAddedResult(mergeStocktakingBto);
        }

        @AutoGenerated(locked = true)
        public DrugStocktakingDetail getDeleted_DrugStocktakingDetail() {
            return (DrugStocktakingDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStocktakingDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugStocktaking getDeleted_DrugStocktaking() {
            return (DrugStocktaking)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStocktaking.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeStocktakingBto.DrugStocktakingDetailBto,
                        DrugStocktakingDetail,
                        DrugStocktakingDetailBO>
                getUpdatedBto(
                        MergeStocktakingBto.DrugStocktakingDetailBto drugStocktakingDetailBto) {
            return super.getUpdatedResult(drugStocktakingDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStocktakingBto, DrugStocktaking, DrugStocktakingBO> getUpdatedBto(
                MergeStocktakingBto mergeStocktakingBto) {
            return super.getUpdatedResult(mergeStocktakingBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStocktakingBto.DrugStocktakingDetailBto, DrugStocktakingDetailBO>
                getUnmodifiedBto(
                        MergeStocktakingBto.DrugStocktakingDetailBto drugStocktakingDetailBto) {
            return super.getUnmodifiedResult(drugStocktakingDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStocktakingBto, DrugStocktakingBO> getUnmodifiedBto(
                MergeStocktakingBto mergeStocktakingBto) {
            return super.getUnmodifiedResult(mergeStocktakingBto);
        }
    }

    public static class DeleteStocktakingBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStocktakingBO getRootBo() {
            return (DrugStocktakingBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteStocktakingBto, DrugStocktakingBO> getCreatedBto(
                DeleteStocktakingBto deleteStocktakingBto) {
            return this.getAddedResult(deleteStocktakingBto);
        }

        @AutoGenerated(locked = true)
        public DrugStocktaking getDeleted_DrugStocktaking() {
            return (DrugStocktaking)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStocktaking.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteStocktakingBto, DrugStocktaking, DrugStocktakingBO> getUpdatedBto(
                DeleteStocktakingBto deleteStocktakingBto) {
            return super.getUpdatedResult(deleteStocktakingBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteStocktakingBto, DrugStocktakingBO> getUnmodifiedBto(
                DeleteStocktakingBto deleteStocktakingBto) {
            return super.getUnmodifiedResult(deleteStocktakingBto);
        }
    }

    public static class UpdateDrugStocktakingStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStocktakingBO getRootBo() {
            return (DrugStocktakingBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugStocktakingStatusBto, DrugStocktakingBO> getCreatedBto(
                UpdateDrugStocktakingStatusBto updateDrugStocktakingStatusBto) {
            return this.getAddedResult(updateDrugStocktakingStatusBto);
        }

        @AutoGenerated(locked = true)
        public DrugStocktaking getDeleted_DrugStocktaking() {
            return (DrugStocktaking)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStocktaking.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateDrugStocktakingStatusBto, DrugStocktaking, DrugStocktakingBO>
                getUpdatedBto(UpdateDrugStocktakingStatusBto updateDrugStocktakingStatusBto) {
            return super.getUpdatedResult(updateDrugStocktakingStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugStocktakingStatusBto, DrugStocktakingBO> getUnmodifiedBto(
                UpdateDrugStocktakingStatusBto updateDrugStocktakingStatusBto) {
            return super.getUnmodifiedResult(updateDrugStocktakingStatusBto);
        }
    }

    public static class UpdateDrugStocktakingForSummaryOrRevokeBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStocktakingBO getRootBo() {
            return (DrugStocktakingBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugStocktakingForSummaryOrRevokeBto, DrugStocktakingBO>
                getCreatedBto(
                        UpdateDrugStocktakingForSummaryOrRevokeBto
                                updateDrugStocktakingForSummaryOrRevokeBto) {
            return this.getAddedResult(updateDrugStocktakingForSummaryOrRevokeBto);
        }

        @AutoGenerated(locked = true)
        public DrugStocktaking getDeleted_DrugStocktaking() {
            return (DrugStocktaking)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStocktaking.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugStocktakingForSummaryOrRevokeBto,
                        DrugStocktaking,
                        DrugStocktakingBO>
                getUpdatedBto(
                        UpdateDrugStocktakingForSummaryOrRevokeBto
                                updateDrugStocktakingForSummaryOrRevokeBto) {
            return super.getUpdatedResult(updateDrugStocktakingForSummaryOrRevokeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugStocktakingForSummaryOrRevokeBto, DrugStocktakingBO>
                getUnmodifiedBto(
                        UpdateDrugStocktakingForSummaryOrRevokeBto
                                updateDrugStocktakingForSummaryOrRevokeBto) {
            return super.getUnmodifiedResult(updateDrugStocktakingForSummaryOrRevokeBto);
        }
    }
}
