package com.pulse.drug_inventory.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> DrugLocation
 *
 * <p><b>[操作]</b> CREATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "6d7683a0-9672-4452-af0e-0f2a249a24d9|BTO|DEFINITION")
public class CreateDrugLocationBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "88d03632-a965-447f-b643-ed17691d3d84")
    private String drugOriginCode;

    /** 药品规格ID */
    @AutoGenerated(locked = true, uuid = "e036a42c-30b9-4fe0-8a18-bf7d276bd4f9")
    private String drugSpecificationId;

    /** 标签类型 */
    @AutoGenerated(locked = true, uuid = "f9785386-0eba-43ac-992d-d8928526f6ae")
    private String labelType;

    /** 位置 */
    @AutoGenerated(locked = true, uuid = "2007f60c-3fcf-4462-b288-aec8760771b0")
    private String location;

    /** 静配标识 */
    @AutoGenerated(locked = true, uuid = "ac397725-5b27-4051-902e-655060d3bbc9")
    private Boolean pivasFlag;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "23ad0467-7fdc-42a3-a16e-e9b7c4302ae8")
    private String storageCode;

    @AutoGenerated(locked = true)
    public void setDrugOriginCode(String drugOriginCode) {
        this.__$validPropertySet.add("drugOriginCode");
        this.drugOriginCode = drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugSpecificationId(String drugSpecificationId) {
        this.__$validPropertySet.add("drugSpecificationId");
        this.drugSpecificationId = drugSpecificationId;
    }

    @AutoGenerated(locked = true)
    public void setLabelType(String labelType) {
        this.__$validPropertySet.add("labelType");
        this.labelType = labelType;
    }

    @AutoGenerated(locked = true)
    public void setLocation(String location) {
        this.__$validPropertySet.add("location");
        this.location = location;
    }

    @AutoGenerated(locked = true)
    public void setPivasFlag(Boolean pivasFlag) {
        this.__$validPropertySet.add("pivasFlag");
        this.pivasFlag = pivasFlag;
    }

    @AutoGenerated(locked = true)
    public void setStorageCode(String storageCode) {
        this.__$validPropertySet.add("storageCode");
        this.storageCode = storageCode;
    }
}
