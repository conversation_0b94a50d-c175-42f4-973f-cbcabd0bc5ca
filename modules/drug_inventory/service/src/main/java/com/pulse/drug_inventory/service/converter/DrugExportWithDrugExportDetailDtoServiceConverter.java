package com.pulse.drug_inventory.service.converter;

import com.pulse.drug_inventory.manager.dto.DrugExportWithDrugExportDetailDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "197b8ae4-b61b-38e6-8600-3d054d07ecb2")
public class DrugExportWithDrugExportDetailDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugExportWithDrugExportDetailDto> DrugExportWithDrugExportDetailDtoConverter(
            List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugExportWithDrugExportDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
