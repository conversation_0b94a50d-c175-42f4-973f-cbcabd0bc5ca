package com.pulse.drug_inventory.service.flow.node.drug_inventory_accounting_flow;

import com.pulse.drug_inventory.service.flow.context.DrugInventoryAccountingFlowContext;
import com.vs.code.AutoGenerated;
import com.vs.flow.node.NodeIfComponent;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Slf4j
@Component("drugInventory-drugInventoryAccountingFlow-validateData")
@AutoGenerated(locked = false, uuid = "********-2b2b-4de8-b551-aea39af8bc1c|FLOW_NODE|DEFINITION")
public class ValidateDataNode extends NodeIfComponent {

    /**
     * 实现流程判断逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "********-2b2b-4de8-b551-aea39af8bc1c")
    public boolean processIf() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        DrugInventoryAccountingFlowContext context = getFirstContextBean();
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑
        System.out.println("validate_data");
        var drugOriginWithDrugOriginSpecificationDto =
                context.getDrugOriginWithDrugOriginSpecificationDto();
        if (drugOriginWithDrugOriginSpecificationDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "药品信息为空！");
        }
        var exportImportWayBaseDto = context.getExportImportWayBaseDto();
        if (exportImportWayBaseDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "出入库方式不能未空！");
        }
        var eo = context.getInventoryAccountingConversionEo();
        if (eo == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "药品出入库信息不能为空！");
        }
        var organizationDepartmentDto = context.getOrganizationDepartmentDto();
        if (organizationDepartmentDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "组织信息不能为空！");
        } else if (organizationDepartmentDto.getDepartment() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "科室信息不能为空！");
        } else if (organizationDepartmentDto.getDepartment().getStorageType() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "库房类型不能为空！");
        }
        return true;
    }
}
