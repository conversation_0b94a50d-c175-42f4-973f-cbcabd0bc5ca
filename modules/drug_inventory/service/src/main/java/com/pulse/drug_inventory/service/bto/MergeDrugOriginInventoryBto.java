package com.pulse.drug_inventory.service.bto;

import com.pulse.drug_inventory.common.enums.UseFrequencyEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugOriginInventory
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "63c44028-31f9-423a-a5e5-fdba14d6c576|BTO|DEFINITION")
public class MergeDrugOriginInventoryBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 数量 库存数量，按最小规格单位存储 */
    @AutoGenerated(locked = true, uuid = "57f88e4d-aa0e-49a5-a3e2-8b6e09982e8b")
    private BigDecimal amountIncr;

    @Valid
    @AutoGenerated(locked = true, uuid = "660743cb-e8ae-437f-9212-e245b01ff4b0")
    private List<MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto>
            drugOriginBatchInventoryBtoList;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "0b495831-cf38-4019-9a74-a23d16d6e4c7")
    private String drugOriginCode;

    /** 药品产地规格id */
    @AutoGenerated(locked = true, uuid = "14f3c911-7661-4320-8b42-7a3e5ddc6add")
    private String drugOriginSpecificationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2e20a332-e148-4e71-aa06-fdc439adbd0e")
    private String id;

    /** 在途数量 药库已出库，当前库房未入库的数量 */
    @AutoGenerated(locked = true, uuid = "03a41dde-0220-4e91-860e-68c09d2f55c1")
    private BigDecimal inTransitAmountIncr;

    /** 预占数量 待发药数量 */
    @AutoGenerated(locked = true, uuid = "88de82cf-bc05-4409-9079-30a9e29cc439")
    private BigDecimal preOccupiedAmountIncr;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "79b76715-53f7-4e4e-9c17-00ab7cffbcdb")
    private String storageCode;

    /** 使用频率 */
    @AutoGenerated(locked = true, uuid = "600e5393-73d6-4732-8c15-ae89dca7dbc6")
    private UseFrequencyEnum useFrequency;

    /**
     * 虚库存数量 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
     * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
     * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
     */
    @AutoGenerated(locked = true, uuid = "ebefd863-c36d-4499-9efa-de24fd9ced84")
    private BigDecimal virtualAmountIncr;

    @AutoGenerated(locked = true)
    public void setAmountIncr(BigDecimal amountIncr) {
        this.__$validPropertySet.add("amountIncr");
        this.amountIncr = amountIncr;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginBatchInventoryBtoList(
            List<MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto>
                    drugOriginBatchInventoryBtoList) {
        this.__$validPropertySet.add("drugOriginBatchInventoryBtoList");
        this.drugOriginBatchInventoryBtoList = drugOriginBatchInventoryBtoList;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginCode(String drugOriginCode) {
        this.__$validPropertySet.add("drugOriginCode");
        this.drugOriginCode = drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginSpecificationId(String drugOriginSpecificationId) {
        this.__$validPropertySet.add("drugOriginSpecificationId");
        this.drugOriginSpecificationId = drugOriginSpecificationId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInTransitAmountIncr(BigDecimal inTransitAmountIncr) {
        this.__$validPropertySet.add("inTransitAmountIncr");
        this.inTransitAmountIncr = inTransitAmountIncr;
    }

    @AutoGenerated(locked = true)
    public void setPreOccupiedAmountIncr(BigDecimal preOccupiedAmountIncr) {
        this.__$validPropertySet.add("preOccupiedAmountIncr");
        this.preOccupiedAmountIncr = preOccupiedAmountIncr;
    }

    @AutoGenerated(locked = true)
    public void setStorageCode(String storageCode) {
        this.__$validPropertySet.add("storageCode");
        this.storageCode = storageCode;
    }

    @AutoGenerated(locked = true)
    public void setUseFrequency(UseFrequencyEnum useFrequency) {
        this.__$validPropertySet.add("useFrequency");
        this.useFrequency = useFrequency;
    }

    @AutoGenerated(locked = true)
    public void setVirtualAmountIncr(BigDecimal virtualAmountIncr) {
        this.__$validPropertySet.add("virtualAmountIncr");
        this.virtualAmountIncr = virtualAmountIncr;
    }

    /**
     * <b>[源自]</b> DrugOriginBatchInventory
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_IGNORE
     */
    @Getter
    @NoArgsConstructor
    public static class DrugOriginBatchInventoryBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "ceeffc83-00cd-497e-9d30-6bc084f34c98")
        private String id;

        /** 库房编码 */
        @AutoGenerated(locked = true, uuid = "78eea066-e4f7-4910-94b8-209dd5ba81cf")
        private String storageCode;

        /** 药品产地编码 冗余存 */
        @AutoGenerated(locked = true, uuid = "bbda282f-bc1d-4ad2-932a-18218c1dc626")
        private String drugOriginCode;

        /** 药品产地规格id */
        @AutoGenerated(locked = true, uuid = "b7066450-2560-4b46-9dd3-207620b23fce")
        private String drugOriginSpecificationId;

        /** 批号 */
        @AutoGenerated(locked = true, uuid = "fc06c7be-2817-42ec-9e1f-5fde99fde017")
        private String batchNumber;

        /** 有效期 批次效期 */
        @AutoGenerated(locked = true, uuid = "4c244b3f-0ee6-4b3f-8201-cc801147804c")
        private Date expirationDate;

        /** 数量 库存数量，按最小规格单位存储 */
        @AutoGenerated(locked = true, uuid = "1e652c27-cf1d-4920-b1e2-2368068f9492")
        private BigDecimal amountIncr;

        /** 可供标识id */
        @AutoGenerated(locked = true, uuid = "f9c62387-48da-49cc-9035-07008f141c4d")
        private String supplyId;

        /** 批次id */
        @AutoGenerated(locked = true, uuid = "277f329c-18b9-48ec-bc6a-217727ad0ef2")
        private String batchId;

        /**
         * 虚库存 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
         * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
         * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
         */
        @AutoGenerated(locked = true, uuid = "3b577729-4bca-4b57-b859-46084d09bd73")
        private BigDecimal virtualAmountIncr;

        /** gcp编码 gcp药品一物一码，开单发药时也按编码开单发药 */
        @AutoGenerated(locked = true, uuid = "038829d6-b450-4568-88f2-e0563012bc31")
        private String gcpCode;

        /** 进价 */
        @AutoGenerated(locked = true, uuid = "77387bf8-7f58-42c2-9be4-98afd0b6101e")
        private BigDecimal purchasePrice;

        /** 零售价 */
        @AutoGenerated(locked = true, uuid = "1c25baf1-5947-4bac-9d5b-9089d27f9d53")
        private BigDecimal retailPrice;

        /** 进货日期 */
        @AutoGenerated(locked = true, uuid = "74fd6e93-27d4-413d-a617-d2aa94427765")
        private Date importDateTime;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setStorageCode(String storageCode) {
            this.__$validPropertySet.add("storageCode");
            this.storageCode = storageCode;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginCode(String drugOriginCode) {
            this.__$validPropertySet.add("drugOriginCode");
            this.drugOriginCode = drugOriginCode;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginSpecificationId(String drugOriginSpecificationId) {
            this.__$validPropertySet.add("drugOriginSpecificationId");
            this.drugOriginSpecificationId = drugOriginSpecificationId;
        }

        @AutoGenerated(locked = true)
        public void setBatchNumber(String batchNumber) {
            this.__$validPropertySet.add("batchNumber");
            this.batchNumber = batchNumber;
        }

        @AutoGenerated(locked = true)
        public void setExpirationDate(Date expirationDate) {
            this.__$validPropertySet.add("expirationDate");
            this.expirationDate = expirationDate;
        }

        @AutoGenerated(locked = true)
        public void setAmountIncr(BigDecimal amountIncr) {
            this.__$validPropertySet.add("amountIncr");
            this.amountIncr = amountIncr;
        }

        @AutoGenerated(locked = true)
        public void setSupplyId(String supplyId) {
            this.__$validPropertySet.add("supplyId");
            this.supplyId = supplyId;
        }

        @AutoGenerated(locked = true)
        public void setBatchId(String batchId) {
            this.__$validPropertySet.add("batchId");
            this.batchId = batchId;
        }

        @AutoGenerated(locked = true)
        public void setVirtualAmountIncr(BigDecimal virtualAmountIncr) {
            this.__$validPropertySet.add("virtualAmountIncr");
            this.virtualAmountIncr = virtualAmountIncr;
        }

        @AutoGenerated(locked = true)
        public void setGcpCode(String gcpCode) {
            this.__$validPropertySet.add("gcpCode");
            this.gcpCode = gcpCode;
        }

        @AutoGenerated(locked = true)
        public void setPurchasePrice(BigDecimal purchasePrice) {
            this.__$validPropertySet.add("purchasePrice");
            this.purchasePrice = purchasePrice;
        }

        @AutoGenerated(locked = true)
        public void setRetailPrice(BigDecimal retailPrice) {
            this.__$validPropertySet.add("retailPrice");
            this.retailPrice = retailPrice;
        }

        @AutoGenerated(locked = true)
        public void setImportDateTime(Date importDateTime) {
            this.__$validPropertySet.add("importDateTime");
            this.importDateTime = importDateTime;
        }
    }
}
