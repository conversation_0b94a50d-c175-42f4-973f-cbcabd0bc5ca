package com.pulse.drug_inventory.service.converter.voConverter;

import com.pulse.drug_inventory.persist.dos.DrugOriginInventory;
import com.pulse.drug_inventory.persist.dos.DrugOriginInventory.DrugOriginCodeAndStorageCode;
import com.pulse.drug_inventory.persist.eo.IdxStorageCodeDrugOriginCodeEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "a1118b0a-04c1-3340-b852-0780b6afaae1")
public class DrugOriginInventoryIdxStorageCodeDrugOriginCodeConverter {

    @AutoGenerated(locked = true)
    public static DrugOriginInventory.DrugOriginCodeAndStorageCode
            convertFromIdxStorageCodeDrugOriginCodeToInner(
                    IdxStorageCodeDrugOriginCodeEo idxStorageCodeDrugOriginCode) {
        if (null == idxStorageCodeDrugOriginCode) {
            return null;
        }

        DrugOriginCodeAndStorageCode drugOriginCodeAndStorageCode =
                new DrugOriginCodeAndStorageCode();
        drugOriginCodeAndStorageCode.setStorageCode(idxStorageCodeDrugOriginCode.getStorageCode());
        drugOriginCodeAndStorageCode.setDrugOriginCode(
                idxStorageCodeDrugOriginCode.getDrugOriginCode());
        return drugOriginCodeAndStorageCode;
    }
}
