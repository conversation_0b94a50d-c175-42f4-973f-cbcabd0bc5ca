package com.pulse.drug_inventory.service.converter;

import com.pulse.drug_inventory.manager.dto.DrugLossReportBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "4efb2c92-cdca-3f25-a489-498080633f3f")
public class DrugLossReportBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugLossReportBaseDto> DrugLossReportBaseDtoConverter(
            List<DrugLossReportBaseDto> drugLossReportBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugLossReportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
