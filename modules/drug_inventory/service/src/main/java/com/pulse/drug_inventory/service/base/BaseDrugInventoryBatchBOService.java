package com.pulse.drug_inventory.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.bo.DrugInventoryBatchBO;
import com.pulse.drug_inventory.persist.dos.DrugInventoryBatch;
import com.pulse.drug_inventory.service.base.BaseDrugInventoryBatchBOService.CreateDrugInventoryBatchBoResult;
import com.pulse.drug_inventory.service.bto.CreateDrugInventoryBatchBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "43633ecd-379b-34a1-9d94-d7768fb80eb0")
public class BaseDrugInventoryBatchBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private DrugInventoryBatchBO createCreateDrugInventoryBatchOnDuplicateThrowEx(
            BaseDrugInventoryBatchBOService.CreateDrugInventoryBatchBoResult boResult,
            CreateDrugInventoryBatchBto createDrugInventoryBatchBto) {
        DrugInventoryBatchBO drugInventoryBatchBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createDrugInventoryBatchBto.getId() == null);
        if (!allNull && !found) {
            drugInventoryBatchBO =
                    DrugInventoryBatchBO.getById(createDrugInventoryBatchBto.getId());
            if (drugInventoryBatchBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugInventoryBatchBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        drugInventoryBatchBO.getId(),
                        "drug_inventory_batch");
                throw new IgnoredException(400, "药品库存批次已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "drug_inventory_batch",
                        drugInventoryBatchBO.getId(),
                        "drug_inventory_batch");
                throw new IgnoredException(400, "药品库存批次已存在");
            }
        } else {
            drugInventoryBatchBO = new DrugInventoryBatchBO();
            if (pkExist) {
                drugInventoryBatchBO.setId(createDrugInventoryBatchBto.getId());
            } else {
                drugInventoryBatchBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_inventory_batch")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "drugOriginCode")) {
                drugInventoryBatchBO.setDrugOriginCode(
                        createDrugInventoryBatchBto.getDrugOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "drugOriginSpecificationId")) {
                drugInventoryBatchBO.setDrugOriginSpecificationId(
                        createDrugInventoryBatchBto.getDrugOriginSpecificationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "batchNumber")) {
                drugInventoryBatchBO.setBatchNumber(createDrugInventoryBatchBto.getBatchNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "batchDate")) {
                drugInventoryBatchBO.setBatchDate(createDrugInventoryBatchBto.getBatchDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "expirationDate")) {
                drugInventoryBatchBO.setExpirationDate(
                        createDrugInventoryBatchBto.getExpirationDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "supplierId")) {
                drugInventoryBatchBO.setSupplierId(createDrugInventoryBatchBto.getSupplierId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "discountRate")) {
                drugInventoryBatchBO.setDiscountRate(createDrugInventoryBatchBto.getDiscountRate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "importDateTime")) {
                drugInventoryBatchBO.setImportDateTime(
                        createDrugInventoryBatchBto.getImportDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "storageCode")) {
                drugInventoryBatchBO.setStorageCode(createDrugInventoryBatchBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "supplyFlag")) {
                drugInventoryBatchBO.setSupplyFlag(createDrugInventoryBatchBto.getSupplyFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createDrugInventoryBatchBto, "__$validPropertySet"),
                    "importId")) {
                drugInventoryBatchBO.setImportId(createDrugInventoryBatchBto.getImportId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createDrugInventoryBatchBto);
            addedBto.setBo(drugInventoryBatchBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugInventoryBatchBO;
    }

    /** 创建药品库存批次 */
    @AutoGenerated(locked = true)
    protected CreateDrugInventoryBatchBoResult createDrugInventoryBatchBase(
            CreateDrugInventoryBatchBto createDrugInventoryBatchBto) {
        if (createDrugInventoryBatchBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDrugInventoryBatchBoResult boResult = new CreateDrugInventoryBatchBoResult();
        DrugInventoryBatchBO drugInventoryBatchBO =
                createCreateDrugInventoryBatchOnDuplicateThrowEx(
                        boResult, createDrugInventoryBatchBto);
        boResult.setRootBo(drugInventoryBatchBO);
        return boResult;
    }

    public static class CreateDrugInventoryBatchBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugInventoryBatchBO getRootBo() {
            return (DrugInventoryBatchBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDrugInventoryBatchBto, DrugInventoryBatchBO> getCreatedBto(
                CreateDrugInventoryBatchBto createDrugInventoryBatchBto) {
            return this.getAddedResult(createDrugInventoryBatchBto);
        }

        @AutoGenerated(locked = true)
        public DrugInventoryBatch getDeleted_DrugInventoryBatch() {
            return (DrugInventoryBatch)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugInventoryBatch.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateDrugInventoryBatchBto, DrugInventoryBatch, DrugInventoryBatchBO>
                getUpdatedBto(CreateDrugInventoryBatchBto createDrugInventoryBatchBto) {
            return super.getUpdatedResult(createDrugInventoryBatchBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDrugInventoryBatchBto, DrugInventoryBatchBO> getUnmodifiedBto(
                CreateDrugInventoryBatchBto createDrugInventoryBatchBto) {
            return super.getUnmodifiedResult(createDrugInventoryBatchBto);
        }
    }
}
