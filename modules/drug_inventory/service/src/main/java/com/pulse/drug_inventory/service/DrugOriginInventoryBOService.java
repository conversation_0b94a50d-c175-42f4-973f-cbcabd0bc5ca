package com.pulse.drug_inventory.service;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.pulse.drug_inventory.persist.dos.DrugOriginBatchInventory;
import com.pulse.drug_inventory.persist.dos.DrugOriginInventory;
import com.pulse.drug_inventory.service.base.BaseDrugOriginInventoryBOService;
import com.pulse.drug_inventory.service.bto.CreateDrugOriginBatchInventoryBto;
import com.pulse.drug_inventory.service.bto.CreateDrugOriginInventoryBto;
import com.pulse.drug_inventory.service.bto.MergeDrugOriginInventoryBto;
import com.pulse.drug_inventory.service.bto.SaveDrugOriginBatchInventoryListBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugOriginInventoryAmountBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugOriginInventoryPriceBto;
import com.pulse.drug_inventory.service.bto.UpdateVitalAmountBto;
import com.vs.bo.AddedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "90a4f1e8-c31d-48c9-8bb0-694e3205cad1|BO|SERVICE")
public class DrugOriginInventoryBOService extends BaseDrugOriginInventoryBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryBaseDtoService drugOriginInventoryBaseDtoService;

    /** 创建药品总库存 */
    @PublicInterface(id = "7a66e804-39fd-4e18-b905-15dd4a1881a3", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "2dd144d1-2b1a-4315-b82e-0b379dd08501")
    public String createDrugOriginInventory(
            @Valid @NotNull CreateDrugOriginInventoryBto createDrugOriginInventoryBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateDrugOriginInventoryBoResult boResult =
                super.createDrugOriginInventoryBase(createDrugOriginInventoryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto */
        {
            for (CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto bto :
                    boResult.<CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto>getBtoOfType(
                            CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto.class)) {
                AddedBto<
                                CreateDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventoryBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 CreateDrugOriginInventoryBto */
        {
            CreateDrugOriginInventoryBto bto =
                    boResult
                            .<CreateDrugOriginInventoryBto>getBtoOfType(
                                    CreateDrugOriginInventoryBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateDrugOriginInventoryBto, DrugOriginInventoryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新批次库存价格 */
    @PublicInterface(
            id = "16dbcfe2-5ac2-41e9-b6c8-5614423eb2a2",
            module = "drug_inventory",
            moduleId = "7ead5bf0-4f48-414d-9026-d83055a95c8b",
            pubRpc = true,
            version = "1747130484728")
    @Transactional
    @AutoGenerated(locked = false, uuid = "58827c0a-2dcb-4f6e-a691-81edce2e76c5")
    public String updateDrugOriginInventoryPrice(
            @Valid @NotNull UpdateDrugOriginInventoryPriceBto updateDrugOriginInventoryPriceBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginInventoryBaseDto drugOriginInventoryBaseDto =
                drugOriginInventoryBaseDtoService.getById(
                        updateDrugOriginInventoryPriceBto.getId());
        UpdateDrugOriginInventoryPriceBoResult boResult =
                super.updateDrugOriginInventoryPriceBase(updateDrugOriginInventoryPriceBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto */
        {
            for (UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto bto :
                    boResult
                            .<UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto>
                                    getBtoOfType(
                                            UpdateDrugOriginInventoryPriceBto
                                                    .DrugOriginBatchInventoryBto.class)) {
                UpdatedBto<
                                UpdateDrugOriginInventoryPriceBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventory,
                                DrugOriginBatchInventoryBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugOriginBatchInventory entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 UpdateDrugOriginInventoryPriceBto */
        {
            UpdateDrugOriginInventoryPriceBto bto =
                    boResult
                            .<UpdateDrugOriginInventoryPriceBto>getBtoOfType(
                                    UpdateDrugOriginInventoryPriceBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            UpdateDrugOriginInventoryPriceBto,
                            DrugOriginInventory,
                            DrugOriginInventoryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugOriginInventory entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存总库存及批次库存，增量更新数量 */
    @PublicInterface(id = "099d74f2-07b8-463a-b754-43a9883b4b55", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "63c44028-31f9-423a-a5e5-fdba14d6c576")
    public String mergeDrugOriginInventory(
            @Valid @NotNull MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginInventoryBaseDto drugOriginInventoryBaseDto = null;
        if (mergeDrugOriginInventoryBto.getId() != null) {
            drugOriginInventoryBaseDto =
                    drugOriginInventoryBaseDtoService.getById(mergeDrugOriginInventoryBto.getId());
        }
        MergeDrugOriginInventoryBoResult boResult =
                super.mergeDrugOriginInventoryBase(mergeDrugOriginInventoryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto */
        {
            for (MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto bto :
                    boResult.<MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto>getBtoOfType(
                            MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto.class)) {
                UpdatedBto<
                                MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventory,
                                DrugOriginBatchInventoryBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventoryBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugOriginBatchInventory entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 MergeDrugOriginInventoryBto */
        {
            MergeDrugOriginInventoryBto bto =
                    boResult
                            .<MergeDrugOriginInventoryBto>getBtoOfType(
                                    MergeDrugOriginInventoryBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeDrugOriginInventoryBto, DrugOriginInventory, DrugOriginInventoryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeDrugOriginInventoryBto, DrugOriginInventoryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugOriginInventory entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 增量保存药品批次库存并且更新药品总库存数量 */
    @PublicInterface(id = "6f9ccabe-f5b2-431e-b0bf-4747fc9508a7", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "9bf93b74-d4a7-4bd8-ab3d-77031d0992cb")
    public String saveDrugOriginBatchInventoryList(
            @Valid @NotNull
                    SaveDrugOriginBatchInventoryListBto saveDrugOriginBatchInventoryListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginInventoryBaseDto drugOriginInventoryBaseDto =
                drugOriginInventoryBaseDtoService.getById(
                        saveDrugOriginBatchInventoryListBto.getId());
        SaveDrugOriginBatchInventoryListBoResult boResult =
                super.saveDrugOriginBatchInventoryListBase(saveDrugOriginBatchInventoryListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto */
        {
            for (SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto bto :
                    boResult
                            .<SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto>
                                    getBtoOfType(
                                            SaveDrugOriginBatchInventoryListBto
                                                    .DrugOriginBatchInventoryBto.class)) {
                UpdatedBto<
                                SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventory,
                                DrugOriginBatchInventoryBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventoryBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugOriginBatchInventory entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 SaveDrugOriginBatchInventoryListBto */
        {
            SaveDrugOriginBatchInventoryListBto bto =
                    boResult
                            .<SaveDrugOriginBatchInventoryListBto>getBtoOfType(
                                    SaveDrugOriginBatchInventoryListBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            SaveDrugOriginBatchInventoryListBto,
                            DrugOriginInventory,
                            DrugOriginInventoryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugOriginInventory entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建药品批次库存 */
    @PublicInterface(id = "7c7bc12a-255f-4424-ab10-5ca9710783c0", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "c030936e-3d5e-4fbd-86f6-6eada0bec1e1")
    public String createDrugOriginBatchInventory(
            @Valid @NotNull
                    CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto
                            drugOriginBatchInventoryBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateDrugOriginBatchInventoryBto createDrugOriginBatchInventoryBto =
                new CreateDrugOriginBatchInventoryBto();
        createDrugOriginBatchInventoryBto.setDrugOriginBatchInventoryBtoList(
                List.of(drugOriginBatchInventoryBto));
        DrugOriginInventoryBaseDto drugOriginInventoryBaseDto =
                drugOriginInventoryBaseDtoService.getById(
                        drugOriginBatchInventoryBto.getInventoryId());
        createDrugOriginBatchInventoryBto.setId(drugOriginInventoryBaseDto.getId());
        CreateDrugOriginBatchInventoryBoResult boResult =
                super.createDrugOriginBatchInventoryBase(createDrugOriginBatchInventoryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto */
        {
            for (CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto bto :
                    boResult
                            .<CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto>
                                    getBtoOfType(
                                            CreateDrugOriginBatchInventoryBto
                                                    .DrugOriginBatchInventoryBto.class)) {
                AddedBto<
                                CreateDrugOriginBatchInventoryBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventoryBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 功能：库存管理-修改商品虚库存 */
    @PublicInterface(id = "806f8fae-cd48-4b02-842e-51eab9c09b73", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "c03228fe-b73f-4a06-b229-171274146fd2")
    public String updateVitalAmount(@Valid @NotNull UpdateVitalAmountBto updateVitalAmountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginInventoryBaseDto drugOriginInventoryBaseDto =
                drugOriginInventoryBaseDtoService.getById(updateVitalAmountBto.getId());
        UpdateVitalAmountBoResult boResult = super.updateVitalAmountBase(updateVitalAmountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateVitalAmountBto */
        {
            UpdateVitalAmountBto bto =
                    boResult.<UpdateVitalAmountBto>getBtoOfType(UpdateVitalAmountBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateVitalAmountBto, DrugOriginInventory, DrugOriginInventoryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugOriginInventory entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新药品库存总数量以及批次库存数量 */
    @PublicInterface(id = "e99a10b8-96ab-4279-9aee-445c5fb54089", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "cd79ae8b-895c-417f-ac1a-4baf64e04b19")
    public String updateDrugOriginInventoryAmount(
            @Valid @NotNull UpdateDrugOriginInventoryAmountBto updateDrugOriginInventoryAmountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginInventoryBaseDto drugOriginInventoryBaseDto =
                drugOriginInventoryBaseDtoService.getById(
                        updateDrugOriginInventoryAmountBto.getId());
        UpdateDrugOriginInventoryAmountBoResult boResult =
                super.updateDrugOriginInventoryAmountBase(updateDrugOriginInventoryAmountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto */
        {
            for (UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto bto :
                    boResult
                            .<UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto>
                                    getBtoOfType(
                                            UpdateDrugOriginInventoryAmountBto
                                                    .DrugOriginBatchInventoryBto.class)) {
                UpdatedBto<
                                UpdateDrugOriginInventoryAmountBto.DrugOriginBatchInventoryBto,
                                DrugOriginBatchInventory,
                                DrugOriginBatchInventoryBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugOriginBatchInventoryBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugOriginBatchInventory entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 UpdateDrugOriginInventoryAmountBto */
        {
            UpdateDrugOriginInventoryAmountBto bto =
                    boResult
                            .<UpdateDrugOriginInventoryAmountBto>getBtoOfType(
                                    UpdateDrugOriginInventoryAmountBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            UpdateDrugOriginInventoryAmountBto,
                            DrugOriginInventory,
                            DrugOriginInventoryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugOriginInventoryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugOriginInventory entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
