package com.pulse.drug_inventory.service;

import com.pulse.drug_inventory.service.base.BaseSubDrugStocktakingBOService;
import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "b785cae3-e9eb-488a-b097-731348e2a2ea|BO|SERVICE")
public class SubDrugStocktakingBOService extends BaseSubDrugStocktakingBOService {}
