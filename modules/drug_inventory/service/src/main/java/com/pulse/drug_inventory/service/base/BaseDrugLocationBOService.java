package com.pulse.drug_inventory.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.bo.DrugLocationBO;
import com.pulse.drug_inventory.persist.dos.DrugLocation;
import com.pulse.drug_inventory.service.base.BaseDrugLocationBOService.CreateDrugLocationBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugLocationBOService.DeleteDrugLocationBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugLocationBOService.MergeDrugLocationBoResult;
import com.pulse.drug_inventory.service.bto.CreateDrugLocationBto;
import com.pulse.drug_inventory.service.bto.DeleteDrugLocationBto;
import com.pulse.drug_inventory.service.bto.MergeDrugLocationBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "1f66cbfa-0575-3e5a-8a19-a1e7e6af4c0b")
public class BaseDrugLocationBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugLocationBO createCreateDrugLocation(
            BaseDrugLocationBOService.CreateDrugLocationBoResult boResult,
            CreateDrugLocationBto createDrugLocationBto) {
        DrugLocationBO drugLocationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        if (drugLocationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", drugLocationBO.getId(), "drug_location");
                throw new IgnoredException(400, "药品摆放位置已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "drug_location",
                        drugLocationBO.getId());
                throw new IgnoredException(400, "药品摆放位置已存在");
            }
        } else {
            drugLocationBO = new DrugLocationBO();
            if (pkExist) {
                drugLocationBO.setId(String.valueOf(this.idGenerator.allocateId("drug_location")));
            } else {
                drugLocationBO.setId(String.valueOf(this.idGenerator.allocateId("drug_location")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDrugLocationBto, "__$validPropertySet"),
                    "storageCode")) {
                drugLocationBO.setStorageCode(createDrugLocationBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDrugLocationBto, "__$validPropertySet"),
                    "drugOriginCode")) {
                drugLocationBO.setDrugOriginCode(createDrugLocationBto.getDrugOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDrugLocationBto, "__$validPropertySet"),
                    "location")) {
                drugLocationBO.setLocation(createDrugLocationBto.getLocation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDrugLocationBto, "__$validPropertySet"),
                    "pivasFlag")) {
                drugLocationBO.setPivasFlag(createDrugLocationBto.getPivasFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDrugLocationBto, "__$validPropertySet"),
                    "labelType")) {
                drugLocationBO.setLabelType(createDrugLocationBto.getLabelType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDrugLocationBto, "__$validPropertySet"),
                    "drugSpecificationId")) {
                drugLocationBO.setDrugSpecificationId(
                        createDrugLocationBto.getDrugSpecificationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createDrugLocationBto);
            addedBto.setBo(drugLocationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugLocationBO;
    }

    /** 新建药品摆放位置 */
    @AutoGenerated(locked = true)
    protected CreateDrugLocationBoResult createDrugLocationBase(
            CreateDrugLocationBto createDrugLocationBto) {
        if (createDrugLocationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDrugLocationBoResult boResult = new CreateDrugLocationBoResult();
        DrugLocationBO drugLocationBO = createCreateDrugLocation(boResult, createDrugLocationBto);
        boResult.setRootBo(drugLocationBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugLocationBO createMergeDrugLocationOnDuplicateUpdate(
            BaseDrugLocationBOService.MergeDrugLocationBoResult boResult,
            MergeDrugLocationBto mergeDrugLocationBto) {
        DrugLocationBO drugLocationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeDrugLocationBto.getId() == null);
        if (!allNull && !found) {
            drugLocationBO = DrugLocationBO.getById(mergeDrugLocationBto.getId());
            if (drugLocationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugLocationBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugLocationBO.convertToDrugLocation());
                updatedBto.setBto(mergeDrugLocationBto);
                updatedBto.setBo(drugLocationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugLocationBO.setStorageCode(mergeDrugLocationBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "drugOriginCode")) {
                    drugLocationBO.setDrugOriginCode(mergeDrugLocationBto.getDrugOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "location")) {
                    drugLocationBO.setLocation(mergeDrugLocationBto.getLocation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "pivasFlag")) {
                    drugLocationBO.setPivasFlag(mergeDrugLocationBto.getPivasFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "labelType")) {
                    drugLocationBO.setLabelType(mergeDrugLocationBto.getLabelType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "drugSpecificationId")) {
                    drugLocationBO.setDrugSpecificationId(
                            mergeDrugLocationBto.getDrugSpecificationId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugLocationBO.convertToDrugLocation());
                updatedBto.setBto(mergeDrugLocationBto);
                updatedBto.setBo(drugLocationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugLocationBO.setStorageCode(mergeDrugLocationBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "drugOriginCode")) {
                    drugLocationBO.setDrugOriginCode(mergeDrugLocationBto.getDrugOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "location")) {
                    drugLocationBO.setLocation(mergeDrugLocationBto.getLocation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "pivasFlag")) {
                    drugLocationBO.setPivasFlag(mergeDrugLocationBto.getPivasFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "labelType")) {
                    drugLocationBO.setLabelType(mergeDrugLocationBto.getLabelType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugLocationBto, "__$validPropertySet"),
                        "drugSpecificationId")) {
                    drugLocationBO.setDrugSpecificationId(
                            mergeDrugLocationBto.getDrugSpecificationId());
                }
            }
        } else {
            drugLocationBO = new DrugLocationBO();
            if (pkExist) {
                drugLocationBO.setId(mergeDrugLocationBto.getId());
            } else {
                drugLocationBO.setId(String.valueOf(this.idGenerator.allocateId("drug_location")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugLocationBto, "__$validPropertySet"),
                    "storageCode")) {
                drugLocationBO.setStorageCode(mergeDrugLocationBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugLocationBto, "__$validPropertySet"),
                    "drugOriginCode")) {
                drugLocationBO.setDrugOriginCode(mergeDrugLocationBto.getDrugOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugLocationBto, "__$validPropertySet"),
                    "location")) {
                drugLocationBO.setLocation(mergeDrugLocationBto.getLocation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugLocationBto, "__$validPropertySet"),
                    "pivasFlag")) {
                drugLocationBO.setPivasFlag(mergeDrugLocationBto.getPivasFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugLocationBto, "__$validPropertySet"),
                    "labelType")) {
                drugLocationBO.setLabelType(mergeDrugLocationBto.getLabelType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugLocationBto, "__$validPropertySet"),
                    "drugSpecificationId")) {
                drugLocationBO.setDrugSpecificationId(
                        mergeDrugLocationBto.getDrugSpecificationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeDrugLocationBto);
            addedBto.setBo(drugLocationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugLocationBO;
    }

    /** 删除对象:deleteDrugLocation,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugLocationBO deleteDeleteDrugLocationOnMissThrowEx(
            BaseDrugLocationBOService.DeleteDrugLocationBoResult boResult,
            DeleteDrugLocationBto deleteDrugLocationBto) {
        DrugLocationBO drugLocationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteDrugLocationBto.getId() == null);
        if (!allNull && !found) {
            drugLocationBO = DrugLocationBO.getById(deleteDrugLocationBto.getId());
            found = true;
        }
        if (drugLocationBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugLocationBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteDrugLocationBto);
            deletedBto.setEntity(drugLocationBO.convertToDrugLocation());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugLocationBO;
        }
    }

    /** 删除药品摆放位置 */
    @AutoGenerated(locked = true)
    protected DeleteDrugLocationBoResult deleteDrugLocationBase(
            DeleteDrugLocationBto deleteDrugLocationBto) {
        if (deleteDrugLocationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteDrugLocationBoResult boResult = new DeleteDrugLocationBoResult();
        DrugLocationBO drugLocationBO =
                deleteDeleteDrugLocationOnMissThrowEx(boResult, deleteDrugLocationBto);
        boResult.setRootBo(drugLocationBO);
        return boResult;
    }

    /** merge药品摆放方式 */
    @AutoGenerated(locked = true)
    protected MergeDrugLocationBoResult mergeDrugLocationBase(
            MergeDrugLocationBto mergeDrugLocationBto) {
        if (mergeDrugLocationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDrugLocationBoResult boResult = new MergeDrugLocationBoResult();
        DrugLocationBO drugLocationBO =
                createMergeDrugLocationOnDuplicateUpdate(boResult, mergeDrugLocationBto);
        boResult.setRootBo(drugLocationBO);
        return boResult;
    }

    public static class CreateDrugLocationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugLocationBO getRootBo() {
            return (DrugLocationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDrugLocationBto, DrugLocationBO> getCreatedBto(
                CreateDrugLocationBto createDrugLocationBto) {
            return this.getAddedResult(createDrugLocationBto);
        }

        @AutoGenerated(locked = true)
        public DrugLocation getDeleted_DrugLocation() {
            return (DrugLocation)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugLocation.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateDrugLocationBto, DrugLocation, DrugLocationBO> getUpdatedBto(
                CreateDrugLocationBto createDrugLocationBto) {
            return super.getUpdatedResult(createDrugLocationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDrugLocationBto, DrugLocationBO> getUnmodifiedBto(
                CreateDrugLocationBto createDrugLocationBto) {
            return super.getUnmodifiedResult(createDrugLocationBto);
        }
    }

    public static class MergeDrugLocationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugLocationBO getRootBo() {
            return (DrugLocationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDrugLocationBto, DrugLocationBO> getCreatedBto(
                MergeDrugLocationBto mergeDrugLocationBto) {
            return this.getAddedResult(mergeDrugLocationBto);
        }

        @AutoGenerated(locked = true)
        public DrugLocation getDeleted_DrugLocation() {
            return (DrugLocation)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugLocation.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeDrugLocationBto, DrugLocation, DrugLocationBO> getUpdatedBto(
                MergeDrugLocationBto mergeDrugLocationBto) {
            return super.getUpdatedResult(mergeDrugLocationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDrugLocationBto, DrugLocationBO> getUnmodifiedBto(
                MergeDrugLocationBto mergeDrugLocationBto) {
            return super.getUnmodifiedResult(mergeDrugLocationBto);
        }
    }

    public static class DeleteDrugLocationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugLocationBO getRootBo() {
            return (DrugLocationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDrugLocationBto, DrugLocationBO> getCreatedBto(
                DeleteDrugLocationBto deleteDrugLocationBto) {
            return this.getAddedResult(deleteDrugLocationBto);
        }

        @AutoGenerated(locked = true)
        public DrugLocation getDeleted_DrugLocation() {
            return (DrugLocation)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugLocation.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteDrugLocationBto, DrugLocation, DrugLocationBO> getUpdatedBto(
                DeleteDrugLocationBto deleteDrugLocationBto) {
            return super.getUpdatedResult(deleteDrugLocationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDrugLocationBto, DrugLocationBO> getUnmodifiedBto(
                DeleteDrugLocationBto deleteDrugLocationBto) {
            return super.getUnmodifiedResult(deleteDrugLocationBto);
        }
    }
}
