package com.pulse.drug_inventory.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugLocationDtoManager;
import com.pulse.drug_inventory.manager.dto.DrugLocationDto;
import com.pulse.drug_inventory.service.converter.DrugLocationDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "360f2ab6-c4e2-48ae-9f91-569f91538120|DTO|SERVICE")
public class DrugLocationDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugLocationDtoManager drugLocationDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugLocationDtoServiceConverter drugLocationDtoServiceConverter;

    @PublicInterface(id = "12bcbbec-aee9-4e3c-b001-b1fe6a6fc2ca", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "142f42da-b096-3563-95a1-2aa7af7a71f4")
    public List<DrugLocationDto> getByDrugSpecificationIds(
            @Valid @NotNull(message = "药品规格ID不能为空") List<String> drugSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugSpecificationId = new ArrayList<>(new HashSet<>(drugSpecificationId));
        List<DrugLocationDto> drugLocationDtoList =
                drugLocationDtoManager.getByDrugSpecificationIds(drugSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugLocationDtoServiceConverter.DrugLocationDtoConverter(drugLocationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f7924ae2-e14e-4846-a0b7-dd857308cb58", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "b15338e2-58f6-325d-b914-ba3b7629ae21")
    public List<DrugLocationDto> getByStorageCodes(
            @Valid @NotNull(message = "库房编码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<DrugLocationDto> drugLocationDtoList =
                drugLocationDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugLocationDtoServiceConverter.DrugLocationDtoConverter(drugLocationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "dd2ee898-b849-4d38-9a62-a73bd65c8f0d", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "be5704e5-c196-33a1-aa8c-2b7d39184eae")
    public List<DrugLocationDto> getByDrugOriginCodes(
            @Valid @NotNull(message = "药品产地编码不能为空") List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginCode = new ArrayList<>(new HashSet<>(drugOriginCode));
        List<DrugLocationDto> drugLocationDtoList =
                drugLocationDtoManager.getByDrugOriginCodes(drugOriginCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugLocationDtoServiceConverter.DrugLocationDtoConverter(drugLocationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "6902d0f6-d563-481e-a12a-818c6a18fcbc", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "c7e34d54-4bb1-3559-ac97-3bf772648371")
    public DrugLocationDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLocationDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "f2d78f43-bc32-44a0-a720-565e0ab6a514", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "d1234809-e287-3c2b-8993-9f7c8aae9ba4")
    public List<DrugLocationDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugLocationDto> drugLocationDtoList = drugLocationDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugLocationDtoServiceConverter.DrugLocationDtoConverter(drugLocationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "40865a25-3664-476f-b96d-0482057c2f1d", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "deef46b5-df97-38e7-aa10-b32602113b37")
    public List<DrugLocationDto> getByStorageCode(
            @NotNull(message = "库房编码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "376ab78a-a624-4006-bcbd-3585876f8651", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "f998beff-b5ee-36ca-8229-858b62070ab7")
    public List<DrugLocationDto> getByDrugOriginCode(
            @NotNull(message = "药品产地编码不能为空") String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c0d2c3f5-9dff-465c-9fa7-0f5114e7e6a5", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "fdbe8c86-92cf-3566-b2a3-0b13810e4416")
    public List<DrugLocationDto> getByDrugSpecificationId(
            @NotNull(message = "药品规格ID不能为空") String drugSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugSpecificationIds(Arrays.asList(drugSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
