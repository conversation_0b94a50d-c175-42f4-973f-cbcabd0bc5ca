package com.pulse.drug_inventory.service.flow.context;

import com.pulse.drug_dictionary.manager.dto.DrugOriginWithDrugOriginSpecificationDto;
import com.pulse.drug_financial.manager.dto.DrugPriceContrastBaseDto;
import com.pulse.drug_inventory.persist.eo.InventoryAccountingConversionEo;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/** 流程：drugInventoryAccountingFlow的上下文，用于传递入参和出参 */
@Getter
@Setter
@AutoGenerated(locked = false, uuid = "fe0bdd98-b3f0-4b81-a3ea-1948a63da71c|FLOW|CONTEXT")
public class DrugInventoryAccountingFlowContext extends DrugInventoryContext {
    // 库存扣减转换eo
    public InventoryAccountingConversionEo inventoryAccountingConversionEo;
    // 药品产地及药品产地规格信息
    public DrugOriginWithDrugOriginSpecificationDto drugOriginWithDrugOriginSpecificationDto;
    // 出入库方式
    public ExportImportWayBaseDto exportImportWayBaseDto;
    // 库房信息
    public OrganizationDepartmentDto organizationDepartmentDto;
    // 价格对照信息
    public List<DrugPriceContrastBaseDto> drugPriceContrastBaseDtos;
}
