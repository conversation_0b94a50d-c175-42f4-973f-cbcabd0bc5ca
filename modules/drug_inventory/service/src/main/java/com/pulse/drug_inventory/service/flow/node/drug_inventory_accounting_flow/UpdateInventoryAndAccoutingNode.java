package com.pulse.drug_inventory.service.flow.node.drug_inventory_accounting_flow;

import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginWithDrugOriginSpecificationDto;
import com.pulse.drug_financial.common.enums.PriceTypeEnum;
import com.pulse.drug_financial.manager.dto.DrugPriceContrastBaseDto;
import com.pulse.drug_inventory.common.enums.UseFrequencyEnum;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.pulse.drug_inventory.persist.eo.IdxStorageCodeDrugOriginCodeEo;
import com.pulse.drug_inventory.persist.eo.InventoryAccountingConversionEo;
import com.pulse.drug_inventory.service.DrugOriginInventoryBOService;
import com.pulse.drug_inventory.service.DrugOriginInventoryForDetailDtoService;
import com.pulse.drug_inventory.service.bto.MergeDrugOriginInventoryBto;
import com.pulse.drug_inventory.service.flow.context.DrugInventoryAccountingFlowContext;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.pharmacy_warehouse_setting.common.enums.InventoryIncreaseReduceEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.StorageTypeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.SystemExportImportWayEnum;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayBaseDto;
import com.pulse.pulse.common.constants.DrugConstants;
import com.vs.code.AutoGenerated;
import com.vs.flow.node.NodeComponent;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Component("drugInventory-drugInventoryAccountingFlow-updateInventoryAndAccouting")
@AutoGenerated(locked = false, uuid = "dc79026b-16c3-4a86-a6c9-1823e685c52c|FLOW_NODE|DEFINITION")
public class UpdateInventoryAndAccoutingNode extends NodeComponent {

    @Resource private DrugOriginInventoryForDetailDtoService drugOriginInventoryForDetailDtoService;

    @Resource private DrugOriginInventoryBOService drugOriginInventoryBOService;

    /**
     * 实现节点处理逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "dc79026b-16c3-4a86-a6c9-1823e685c52c")
    public void process() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        DrugInventoryAccountingFlowContext context = getFirstContextBean();
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑

        var inventoryAccountingConversionEo = context.getInventoryAccountingConversionEo();
        var drugOriginWithDrugOriginSpecificationDto =
                context.getDrugOriginWithDrugOriginSpecificationDto();
        var exportImportWayBaseDto = context.getExportImportWayBaseDto();
        var organizationDepartmentDto = context.getOrganizationDepartmentDto();
        var documentSpecId = inventoryAccountingConversionEo.getDocumentSpecId();
        var drugPriceContrastBaseDtos = context.getDrugPriceContrastBaseDtos();

        // 单据数量绝对值
        var documentAmount = inventoryAccountingConversionEo.getDocumentAmount().abs();

        var inventorySpec =
                getInventoryOriginSpec(
                        drugOriginWithDrugOriginSpecificationDto,
                        documentSpecId,
                        organizationDepartmentDto);
        var targetSpec =
                getTargetOriginSpec(drugOriginWithDrugOriginSpecificationDto, documentSpecId);
        BigDecimal amountSum =
                documentAmount
                        .multiply(BigDecimal.valueOf(inventorySpec.getAmountPerPackage()))
                        .divide(
                                BigDecimal.valueOf(targetSpec.getAmountPerPackage()),
                                DrugConstants.drugAmountAccuracy,
                                BigDecimal.ROUND_HALF_UP);

        // TODO 取管理模式
        var manageModel = ManageModelEnum.TOTAL_MANAGE;

        String storageCode = organizationDepartmentDto.getId();

        // 单据进价
        var documentPurchasePrice = inventoryAccountingConversionEo.getPurchasePrice();
        // 单据零售价
        var documentRetailPrice = inventoryAccountingConversionEo.getRetailPrice();
        // 库存进价
        BigDecimal inventoryPurchasePrice =
                tarnsInventoryPrice(
                        targetSpec,
                        inventorySpec,
                        documentPurchasePrice,
                        PriceTypeEnum.PURCHASE_PRICE,
                        null);
        // 库存零售价
        BigDecimal inventoryRetailPrice =
                tarnsInventoryPrice(
                        targetSpec,
                        inventorySpec,
                        documentRetailPrice,
                        PriceTypeEnum.RETAIL_PRICE,
                        null);
        // 库存进价金额
        BigDecimal inventoryPurchaseCost = inventoryPurchasePrice.multiply(amountSum);
        // 库存零售金额
        BigDecimal inventoryRetailCost = inventoryRetailPrice.multiply(amountSum);

        // 定义变量，是否需要判断调价补登
        var isNeedPriceContrast = false;
        if (Boolean.TRUE.equals(inventoryAccountingConversionEo.getRedFlag())
                || SystemExportImportWayEnum.RETURNED_PURCHASE.equals(
                        exportImportWayBaseDto.getWayCode())) {
            isNeedPriceContrast = true;
        }

        // 取库存增减标志
        var inventoryIncreaseReduce = inventoryAccountingConversionEo.getInventoryIncreaseReduce();

        if (InventoryIncreaseReduceEnum.INCREASE.equals(inventoryIncreaseReduce)) {
            // 库存增加
            // 取库存记录
            var idxStorageCodeDrugOriginCodeEo =
                    new IdxStorageCodeDrugOriginCodeEo(
                            inventoryAccountingConversionEo.getStorageCode(),
                            inventoryAccountingConversionEo.getDrugOriginCode());
            var inventory =
                    drugOriginInventoryForDetailDtoService.getByStorageCodeAndDrugOriginCode(
                            idxStorageCodeDrugOriginCodeEo);
            List<DrugOriginBatchInventoryBaseDto> inventoryBatchInventory = null;

            // 总库存初始化
            MergeDrugOriginInventoryBto mergeDrugOriginInventoryBto = null;
            // 批次库存初始化
            MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto batchBto = null;
            var batchBtoList =
                    new ArrayList<MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto>();

            if (inventory == null) {
                // 总库存为空
                // TODO 组装库存表新增数据 MergeDrugOriginInventoryBto
                mergeDrugOriginInventoryBto =
                        assembleNewInventory(inventorySpec, amountSum, storageCode);
                // TODO 批次库存表新增数据 MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                batchBto =
                        assembleNewBatchInventory(
                                inventoryAccountingConversionEo,
                                inventorySpec,
                                amountSum,
                                storageCode,
                                inventoryPurchasePrice,
                                inventoryRetailPrice);

                // 判断是否需要插入价格对照数据
                if (drugPriceContrastBaseDtos == null || drugPriceContrastBaseDtos.isEmpty()) {
                    // TODO 组装价格对照信息 CreateDrugPriceContrastBto

                }
            } else {
                // 组装总库存更新，MergeDrugOriginInventoryBto
                mergeDrugOriginInventoryBto =
                        assembleUpdateInventory(
                                amountSum, InventoryIncreaseReduceEnum.INCREASE, inventory.getId());
                inventoryBatchInventory = inventory.getDrugOriginBatchInventoryList();
            }

            if (inventoryBatchInventory != null) {

                // 获取对应批次库存id的库存批次数据
                var batchInventory =
                        inventoryBatchInventory.stream()
                                .filter(
                                        item ->
                                                item.getId()
                                                        .equals(
                                                                inventoryAccountingConversionEo
                                                                        .getBatchInventoryId()))
                                .findFirst()
                                .orElse(null);
                if (batchInventory != null) {
                    // 批次库存不为空
                    // 组装批次库存更新数据 MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                    batchBto =
                            assembleUpdateBatchInventory(
                                    amountSum,
                                    InventoryIncreaseReduceEnum.INCREASE,
                                    batchInventory.getId());

                } else {
                    // 批次库存为空
                    // 组装批次库存新增数据 MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                    batchBto =
                            assembleNewBatchInventory(
                                    inventoryAccountingConversionEo,
                                    inventorySpec,
                                    amountSum,
                                    storageCode,
                                    inventoryPurchasePrice,
                                    inventoryRetailPrice);
                }
            } else {
                // 批次库存为空
                // 组装批次库存新增数据  MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                batchBto =
                        assembleNewBatchInventory(
                                inventoryAccountingConversionEo,
                                inventorySpec,
                                amountSum,
                                storageCode,
                                inventoryPurchasePrice,
                                inventoryRetailPrice);
            }

            // 新增或更新库存
            batchBtoList.add(batchBto);
            mergeDrugOriginInventoryBto.setDrugOriginBatchInventoryBtoList(batchBtoList);
            var mergeResult =
                    drugOriginInventoryBOService.mergeDrugOriginInventory(
                            mergeDrugOriginInventoryBto);

            // TODO 组装库存表更、库存批次价格、明细账、

            // TODO 判断是否需要调价补登，并组装明细账调价补登数据

        } else if (InventoryIncreaseReduceEnum.REDUCE.equals(inventoryIncreaseReduce)) {
            // 库存减少
            // 取库存记录
            var idxStorageCodeDrugOriginCodeEo =
                    new IdxStorageCodeDrugOriginCodeEo(
                            inventoryAccountingConversionEo.getStorageCode(),
                            inventoryAccountingConversionEo.getDrugOriginCode());
            var inventory =
                    drugOriginInventoryForDetailDtoService.getByStorageCodeAndDrugOriginCode(
                            idxStorageCodeDrugOriginCodeEo);
            List<DrugOriginBatchInventoryBaseDto> batchInventoryList = null;
            if (inventory == null) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "无库存！");
            } else if (amountSum.compareTo(inventory.getAmount()) > 0) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "库存不满足支出！");
            }

            // TODO 组装库存主记录扣减数据 MergeDrugOriginInventoryBto
            var mergeDrugOriginInventoryBto =
                    assembleUpdateInventory(
                            amountSum, InventoryIncreaseReduceEnum.REDUCE, inventory.getId());
            // 批次库存初始化
            MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto batchBto = null;
            var batchBtoList =
                    new ArrayList<MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto>();

            batchInventoryList = inventory.getDrugOriginBatchInventoryList();
            // 批号模式
            if (ManageModelEnum.BATCH_NUMBER_MANAGE.equals(manageModel)) {
                batchInventoryList =
                        filterBatchInventoryByBatchNumberMode(
                                batchInventoryList, inventoryAccountingConversionEo);
            }
            // 过滤价格
            batchInventoryList =
                    filterBatchInventoryByPrice(
                            batchInventoryList,
                            inventoryPurchasePrice,
                            inventoryRetailPrice,
                            exportImportWayBaseDto);

            // 根据出库策略排序
            batchInventoryList =
                    sortBatchInventoryByExportStrategy(batchInventoryList, exportImportWayBaseDto);

            // 如果批次id不为空，将batchInventoryList.id = 批次id的数据排序到最前面
            if (inventoryAccountingConversionEo.getBatchInventoryId() != null) {
                // 将指定批次ID的数据排序到最前面，其他数据保持原有顺序
                batchInventoryList =
                        sortBatchInventoryByBatchId(
                                batchInventoryList,
                                inventoryAccountingConversionEo.getBatchInventoryId());
            }
            BigDecimal reduceAmountSum = amountSum;
            for (var batchInventory : batchInventoryList) {
                BigDecimal batchAmount = batchInventory.getAmount();
                String batchId = batchInventory.getBatchId();

                if (batchAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                } else if (batchAmount.compareTo(reduceAmountSum) >= 0) {
                    // TODO 当前批次库存够扣
                    // TODO 组装批次库存扣减数据  MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                    batchBto =
                            assembleUpdateBatchInventory(
                                    batchAmount, InventoryIncreaseReduceEnum.REDUCE, batchId);

                    // TODO 计算进价金额、零售金额

                    // TODO 组装明细账、库存变更
                    break;
                } else {
                    // TODO 单个批次库存不够扣

                    // TODO 组装批次库存扣减数据 MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto
                    batchBto =
                            assembleUpdateBatchInventory(
                                    batchAmount, InventoryIncreaseReduceEnum.REDUCE, batchId);

                    // TODO 计算进价金额、零售金额

                    // TODO 组装明细账 CreateDrugDetailLedger、库存变更CreateDrugInventoryCirculationDetial

                    reduceAmountSum = reduceAmountSum.subtract(batchAmount);
                }
                batchBtoList.add(batchBto);
            }
            // TODO 是否需要调价补登

            if (reduceAmountSum.compareTo(BigDecimal.ZERO) > 0) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "库存不满足支出！");
            }

            mergeDrugOriginInventoryBto.setDrugOriginBatchInventoryBtoList(batchBtoList);
            var mergeResult =
                    drugOriginInventoryBOService.mergeDrugOriginInventory(
                            mergeDrugOriginInventoryBto);
        }
    }

    /**
     * 根据批号模式过滤库存
     *
     * @param batchInventoryList
     * @param inventoryAccountingConversionEo
     * @return
     */
    private List<DrugOriginBatchInventoryBaseDto> filterBatchInventoryByBatchNumberMode(
            List<DrugOriginBatchInventoryBaseDto> batchInventoryList,
            InventoryAccountingConversionEo inventoryAccountingConversionEo) {
        if (inventoryAccountingConversionEo.getBatchNumber() != null) {
            batchInventoryList =
                    batchInventoryList.stream()
                            .filter(
                                    item ->
                                            item.getBatchNumber()
                                                    .equals(
                                                            inventoryAccountingConversionEo
                                                                    .getBatchNumber()))
                            .collect(Collectors.toList());
        }
        if (inventoryAccountingConversionEo.getExpirationDate() != null) {
            batchInventoryList =
                    batchInventoryList.stream()
                            .filter(
                                    item ->
                                            item.getExpirationDate()
                                                    .equals(
                                                            inventoryAccountingConversionEo
                                                                    .getExpirationDate()))
                            .collect(Collectors.toList());
        }
        return batchInventoryList;
    }

    /**
     * 根据批次id过滤批次库存
     *
     * @param batchInventoryList
     * @param batchId
     * @return
     */
    private List<DrugOriginBatchInventoryBaseDto> sortBatchInventoryByBatchId(
            List<DrugOriginBatchInventoryBaseDto> batchInventoryList, String batchId) {
        batchInventoryList.sort(
                (o1, o2) -> {
                    // 判断当前元素是否匹配指定批次ID
                    boolean isO1Match = o1.getId().equals(batchId);
                    boolean isO2Match = o2.getId().equals(batchId);
                    if (isO1Match && !isO2Match) {
                        return -1; // o1 匹配，排在前面
                    } else if (!isO1Match && isO2Match) {
                        return 1; // o2 匹配，排在前面
                    } else {
                        return 0; // 两者都匹配或都不匹配，保持原有顺序
                    }
                });
        return batchInventoryList;
    }

    /**
     * 根据价格过滤批次库存
     *
     * @param batchInventoryList
     * @param inventoryPurchasePrice
     * @param inventoryRetailPrice
     * @param exportImportWayBaseDto
     * @return
     */
    private List<DrugOriginBatchInventoryBaseDto> filterBatchInventoryByPrice(
            List<DrugOriginBatchInventoryBaseDto> batchInventoryList,
            BigDecimal inventoryPurchasePrice,
            BigDecimal inventoryRetailPrice,
            ExportImportWayBaseDto exportImportWayBaseDto) {
        // 过滤进价
        if (SystemExportImportWayEnum.RETURNED_PURCHASE
                        .getWayCode()
                        .equals(exportImportWayBaseDto.getWayCode())
                && inventoryPurchasePrice.compareTo(BigDecimal.ZERO) != 0) {
            batchInventoryList =
                    batchInventoryList.stream()
                            .filter(
                                    item ->
                                            item.getPurchasePrice()
                                                            .compareTo(inventoryPurchasePrice)
                                                    == 0)
                            .collect(Collectors.toList());
        }
        // 过滤零售价
        if (inventoryRetailPrice.compareTo(BigDecimal.ZERO) != 0) {
            batchInventoryList =
                    batchInventoryList.stream()
                            .filter(
                                    item ->
                                            item.getRetailPrice().compareTo(inventoryRetailPrice)
                                                    == 0)
                            .collect(Collectors.toList());
        }
        return batchInventoryList;
    }

    /**
     * 根据出库策略排序
     *
     * @param batchInventoryList
     * @param exportImportWayBaseDto
     * @return
     */
    private List<DrugOriginBatchInventoryBaseDto> sortBatchInventoryByExportStrategy(
            List<DrugOriginBatchInventoryBaseDto> batchInventoryList,
            ExportImportWayBaseDto exportImportWayBaseDto) {
        // 过滤进价
        switch (exportImportWayBaseDto.getExportStrategy()) {
                /** 先进先出 */
            case BATCH_INVENTORY_ID_MIN:
                // 根据批次库存id 排序
                batchInventoryList =
                        batchInventoryList.stream()
                                .sorted(
                                        Comparator.comparing(
                                                DrugOriginBatchInventoryBaseDto::getId))
                                .collect(Collectors.toList());
                break;

                /** 先失效先出 */
            case EXPIRATION_DATE_MIN:
                batchInventoryList =
                        batchInventoryList.stream()
                                .sorted(
                                        Comparator.comparing(
                                                DrugOriginBatchInventoryBaseDto::getExpirationDate))
                                .collect(Collectors.toList());
                break;
                /** 后进先出 */
            case BATCH_INVENTORY_ID_MAX:
                batchInventoryList =
                        batchInventoryList.stream()
                                .sorted(
                                        Comparator.comparing(DrugOriginBatchInventoryBaseDto::getId)
                                                .reversed())
                                .collect(Collectors.toList());
                break;
                /** 库存大先出 */
            case BATCH_INVENTORY_AMOUNT_MAX:
                batchInventoryList =
                        batchInventoryList.stream()
                                .sorted(
                                        Comparator.comparing(
                                                        DrugOriginBatchInventoryBaseDto::getAmount)
                                                .reversed())
                                .collect(Collectors.toList());
                break;
        }
        return batchInventoryList;
    }

    /**
     * 取对应库房库存规格
     *
     * @param drugOriginWithDrugOriginSpecificationDto
     * @param targetDrugOriginSpecificationId
     * @param organizationDepartmentDto
     * @return
     */
    private DrugOriginSpecificationBaseDto getInventoryOriginSpec(
            DrugOriginWithDrugOriginSpecificationDto drugOriginWithDrugOriginSpecificationDto,
            String targetDrugOriginSpecificationId,
            OrganizationDepartmentDto organizationDepartmentDto) {

        getTargetOriginSpec(
                drugOriginWithDrugOriginSpecificationDto, targetDrugOriginSpecificationId);

        var drugOriginSpecificationList =
                drugOriginWithDrugOriginSpecificationDto.getDrugOriginSpecificationList();
        if (organizationDepartmentDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "组织信息不能为空！");
        } else if (organizationDepartmentDto.getDepartment() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "组织下科室库房信息不能为空！");
        } else if (organizationDepartmentDto.getDepartment().getStorageType() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "科室库房下库房类型不能为空！");
        }

        var storage = organizationDepartmentDto.getDepartment();
        var packageSpec =
                drugOriginSpecificationList.stream()
                        .filter(
                                item ->
                                        SpecificationTypeEnum.PACKAGE_SPECIFICATION.equals(
                                                item.getSpecificationType()))
                        .findFirst()
                        .orElse(null);
        // 药库库存为大规格
        if (packageSpec == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "药品产地规格信息列表中不存在大规格！");
        }
        if (StorageTypeEnum.DRUG_STORAGE.equals(storage.getStorageType())) {
            // 药库库存为大规格
            return packageSpec;
        } else if (StorageTypeEnum.PHARMACY.equals(storage.getStorageType())) {
            var minSpec =
                    drugOriginSpecificationList.stream()
                            .filter(
                                    item ->
                                            SpecificationTypeEnum.MIN_SPECIFICATION.equals(
                                                    item.getSpecificationType()))
                            .findFirst()
                            .orElse(null);
            // 药房库存为小规格
            if (minSpec == null) {
                return packageSpec;

            } else {
                return minSpec;
            }
        }
        return null;
    }

    /**
     * 取对应药品产地规格
     *
     * @param drugOriginWithDrugOriginSpecificationDto
     * @param targetDrugOriginSpecificationId
     * @return
     */
    private DrugOriginSpecificationBaseDto getTargetOriginSpec(
            DrugOriginWithDrugOriginSpecificationDto drugOriginWithDrugOriginSpecificationDto,
            String targetDrugOriginSpecificationId) {
        if (drugOriginWithDrugOriginSpecificationDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "药品信息不能为空！");
        } else if (drugOriginWithDrugOriginSpecificationDto.getDrugOriginSpecificationList() == null
                || drugOriginWithDrugOriginSpecificationDto
                        .getDrugOriginSpecificationList()
                        .isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "药品产地规格信息列表不能为空！");
        }
        var drugOriginSpecificationList =
                drugOriginWithDrugOriginSpecificationDto.getDrugOriginSpecificationList();

        var targetDrugOriginSpecification =
                drugOriginSpecificationList.stream()
                        .filter(item -> item.getId().equals(targetDrugOriginSpecificationId))
                        .findFirst()
                        .orElse(null);
        if (targetDrugOriginSpecification == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未找到目标药品产地规格信息！");
        }
        return targetDrugOriginSpecification;
    }

    /**
     * 转换价格
     *
     * @param originSpec
     * @param targetSpec
     * @param price
     * @param priceType
     * @param drugPriceContrastBaseDtos
     * @return
     */
    private BigDecimal tarnsInventoryPrice(
            DrugOriginSpecificationBaseDto originSpec,
            DrugOriginSpecificationBaseDto targetSpec,
            BigDecimal price,
            PriceTypeEnum priceType,
            List<DrugPriceContrastBaseDto> drugPriceContrastBaseDtos) {
        // 判断各个参数不能为空，如果为空，则抛出异常
        if (originSpec == null || targetSpec == null || price == null || priceType == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "转换库存价格，入参不能为空！");
        }

        // 如果originSpec 和 targetSpec规格类型相同，直接返回price
        if (originSpec.getSpecificationType().equals(targetSpec.getSpecificationType())) {
            return price;
        }

        // 如果originSpec 和 targetSpec规格类型不同，且targetSpec的规格类型为大规格
        if (SpecificationTypeEnum.PACKAGE_SPECIFICATION.equals(targetSpec.getSpecificationType())) {

            if (PriceTypeEnum.RETAIL_PRICE.equals(priceType)
                    || PriceTypeEnum.PURCHASE_PRICE.equals(priceType)) {
                // 取出drugPriceAdjustBaseDtos中符合条件的记录
                BigDecimal currentPrice = BigDecimal.ZERO;
                if (drugPriceContrastBaseDtos != null) {
                    currentPrice =
                            drugPriceContrastBaseDtos.stream()
                                    .filter(
                                            contrast ->
                                                    targetSpec
                                                                    .getId()
                                                                    .equals(
                                                                            contrast
                                                                                    .getPackageOriginSpecificationId())
                                                            && priceType.equals(
                                                                    contrast.getPriceType())
                                                            && price.equals(
                                                                    contrast
                                                                            .getMinOriginSpecificationPrice()))
                                    .map(DrugPriceContrastBaseDto::getPackageSpecificationPrice)
                                    .findFirst()
                                    .orElse(BigDecimal.ZERO);
                }
                // 没有对照则返回字典价格
                if (currentPrice.compareTo(BigDecimal.ZERO) == 0
                        && PriceTypeEnum.RETAIL_PRICE.equals(priceType)) {
                    if (PriceTypeEnum.RETAIL_PRICE.equals(priceType)) {
                        // TODO 价格体系逻辑未添加（待确认）
                        return targetSpec.getReferenceRetailPriceOne(); // 返回参考零售价1
                    }
                }
            }
        }

        // 如果originSpec 和 targetSpec规格类型不同，且targetSpec的规格类型不为大规格
        return price.multiply(new BigDecimal(targetSpec.getAmountPerPackage()))
                .divide(
                        new BigDecimal(originSpec.getAmountPerPackage()),
                        DrugConstants.drugPriceAccuracy,
                        BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 组装新增总库存
     *
     * @param drugOriginSpecificationBaseDto
     * @param amountSum
     * @param storageCode
     * @return
     */
    private MergeDrugOriginInventoryBto assembleNewInventory(
            DrugOriginSpecificationBaseDto drugOriginSpecificationBaseDto,
            BigDecimal amountSum,
            String storageCode) {
        amountSum = amountSum.abs();
        MergeDrugOriginInventoryBto bto = new MergeDrugOriginInventoryBto();
        bto.setAmountIncr(amountSum);
        bto.setDrugOriginCode(drugOriginSpecificationBaseDto.getDrugOriginCode());
        bto.setDrugOriginSpecificationId(drugOriginSpecificationBaseDto.getId());
        bto.setStorageCode(storageCode);
        bto.setInTransitAmountIncr(BigDecimal.ZERO);
        bto.setUseFrequency(UseFrequencyEnum.COMMON);
        bto.setVirtualAmountIncr(BigDecimal.ZERO);
        bto.setPreOccupiedAmountIncr(BigDecimal.ZERO);
        return bto;
    }

    /**
     * 组装更新总库存
     *
     * @param amountSum
     * @param inventoryIncreaseReduce
     * @param id
     * @return
     */
    private MergeDrugOriginInventoryBto assembleUpdateInventory(
            BigDecimal amountSum, InventoryIncreaseReduceEnum inventoryIncreaseReduce, String id) {

        amountSum = amountSum.abs();

        MergeDrugOriginInventoryBto bto = new MergeDrugOriginInventoryBto();
        bto.setId(id);
        if (InventoryIncreaseReduceEnum.REDUCE.equals(inventoryIncreaseReduce)) {
            amountSum = amountSum.negate();
        }
        bto.setAmountIncr(amountSum);
        // 在途
        // bto.setInTransitAmountIncr(BigDecimal.ZERO);
        // 已收未发
        // bto.setPreOccupiedAmountIncr(BigDecimal.ZERO);
        return bto;
    }

    /**
     * 组装新增批次库存
     *
     * @param eo
     * @param drugOriginSpecDto
     * @param amountSum
     * @param storageCode
     * @param purchasePrice
     * @param retailPrice
     * @return
     */
    private MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto assembleNewBatchInventory(
            InventoryAccountingConversionEo eo,
            DrugOriginSpecificationBaseDto drugOriginSpecDto,
            BigDecimal amountSum,
            String storageCode,
            BigDecimal purchasePrice,
            BigDecimal retailPrice) {
        amountSum = amountSum.abs();
        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto bto =
                new MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto();
        bto.setAmountIncr(amountSum);
        bto.setBatchNumber(eo.getBatchNumber());
        bto.setExpirationDate(eo.getExpirationDate());
        bto.setGcpCode(eo.getGcpCode());
        bto.setDrugOriginCode(drugOriginSpecDto.getDrugOriginCode());
        bto.setDrugOriginSpecificationId(drugOriginSpecDto.getId());
        bto.setImportDateTime(new Date(System.currentTimeMillis()));
        bto.setPurchasePrice(purchasePrice);
        bto.setRetailPrice(retailPrice);
        bto.setVirtualAmountIncr(BigDecimal.ZERO);
        // bto.setSupplyId();
        return bto;
    }

    private MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto assembleUpdateBatchInventory(
            BigDecimal amountSum, InventoryIncreaseReduceEnum inventoryIncreaseReduce, String id) {
        amountSum = amountSum.abs();

        MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto bto =
                new MergeDrugOriginInventoryBto.DrugOriginBatchInventoryBto();
        bto.setId(id);
        if (InventoryIncreaseReduceEnum.REDUCE.equals(inventoryIncreaseReduce)) {
            amountSum = amountSum.negate();
        }
        bto.setAmountIncr(amountSum);
        return bto;
    }
}
