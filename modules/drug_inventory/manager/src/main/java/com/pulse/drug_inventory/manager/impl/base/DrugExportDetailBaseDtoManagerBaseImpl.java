package com.pulse.drug_inventory.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugExportDetailBaseDtoManager;
import com.pulse.drug_inventory.manager.converter.DrugExportDetailBaseDtoConverter;
import com.pulse.drug_inventory.manager.dto.DrugExportDetailBaseDto;
import com.pulse.drug_inventory.persist.dos.DrugExportDetail;
import com.pulse.drug_inventory.persist.mapper.DrugExportDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "3389d1f8-0ff1-49ef-9339-3cffc07cee1c|DTO|BASE_MANAGER_IMPL")
public abstract class DrugExportDetailBaseDtoManagerBaseImpl
        implements DrugExportDetailBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportDetailBaseDtoConverter drugExportDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportDetailDao drugExportDetailDao;

    @AutoGenerated(locked = true, uuid = "0fb9db1e-3387-3cd2-a973-a97f22625551")
    @Override
    public List<DrugExportDetailBaseDto> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginSpecificationId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByDrugOriginSpecificationIds(drugOriginSpecificationId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "391ea832-aa98-3f69-aabb-5c683c609930")
    @Override
    public List<DrugExportDetailBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList = drugExportDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugExportDetail> drugExportDetailMap =
                drugExportDetailList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugExportDetailList =
                id.stream()
                        .map(i -> drugExportDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3f13d783-fb89-3973-962d-b63f7182422b")
    public List<DrugExportDetailBaseDto> doConvertFromDrugExportDetailToDrugExportDetailBaseDto(
            List<DrugExportDetail> drugExportDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        Map<String, DrugExportDetailBaseDto> dtoMap =
                drugExportDetailBaseDtoConverter
                        .convertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugExportDetailBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList = new ArrayList<>();
        for (DrugExportDetail i : drugExportDetailList) {
            DrugExportDetailBaseDto drugExportDetailBaseDto = dtoMap.get(i.getId());
            if (drugExportDetailBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugExportDetailBaseDtoList.add(drugExportDetailBaseDto);
        }
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "43813f01-8e0c-35a2-b2ee-63b6f9b728bd")
    @Override
    public List<DrugExportDetailBaseDto> getByStocktakingDetailIds(
            List<String> stocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(stocktakingDetailId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByStocktakingDetailIds(stocktakingDetailId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "528bd1d8-d53b-359b-ace2-4a466fc8f7f3")
    @Override
    public List<DrugExportDetailBaseDto> getByStocktakingDetailId(String stocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByStocktakingDetailIds(Arrays.asList(stocktakingDetailId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "52d514ac-ff8e-31f5-bb5b-1a653f7084a5")
    @Override
    public List<DrugExportDetailBaseDto> getByFirmIds(List<String> firmId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(firmId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList = drugExportDetailDao.getByFirmIds(firmId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "59df34a8-3b1d-3682-a77a-d8a260bdde6d")
    @Override
    public List<DrugExportDetailBaseDto> getByRefundExportDetailIds(
            List<String> refundExportDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(refundExportDetailId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByRefundExportDetailIds(refundExportDetailId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "609fb3c6-ebcd-36c9-b9b5-71174be40e80")
    @Override
    public List<DrugExportDetailBaseDto> getByBatchInventoryIds(List<String> batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(batchInventoryId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByBatchInventoryIds(batchInventoryId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "7265b41f-23f5-35a0-9f8e-fb3275b99a49")
    @Override
    public List<DrugExportDetailBaseDto> getByApplyDetailId(String applyDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByApplyDetailIds(Arrays.asList(applyDetailId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "7a11f143-401c-32bb-a737-76f14b9044cd")
    @Override
    public List<DrugExportDetailBaseDto> getByBatchInventoryId(String batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByBatchInventoryIds(Arrays.asList(batchInventoryId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "7e268944-73be-3ae2-94c7-448ed4a814ce")
    @Override
    public List<DrugExportDetailBaseDto> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "*************-39c3-b0ed-d28eadcd6ca3")
    @Override
    public List<DrugExportDetailBaseDto> getByDrugOriginCodes(List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginCode)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByDrugOriginCodes(drugOriginCode);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "81adfa0c-0654-3b20-bdda-4c42dd2fc9d4")
    @Override
    public List<DrugExportDetailBaseDto> getByFirmId(String firmId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByFirmIds(Arrays.asList(firmId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8454f5ca-6bdb-33df-8bf5-39b59bf4a036")
    @Override
    public List<DrugExportDetailBaseDto> getByDrugExportIds(List<String> drugExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugExportId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByDrugExportIds(drugExportId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "892b1ad9-f35f-38a0-b677-3d91643bfa42")
    @Override
    public List<DrugExportDetailBaseDto> getByApplyDetailIds(List<String> applyDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applyDetailId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByApplyDetailIds(applyDetailId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "959e5163-8f20-3f03-b1b5-0294934b4778")
    @Override
    public List<DrugExportDetailBaseDto> getByDrugOriginCode(String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a33bce46-395c-3487-ad7d-56f184b5c455")
    @Override
    public List<DrugExportDetailBaseDto> getByRefundExportDetailId(String refundExportDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByRefundExportDetailIds(Arrays.asList(refundExportDetailId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b5dad9b6-1165-3f3d-b3d3-ed7b644b5bb0")
    @Override
    public List<DrugExportDetailBaseDto> getByPurchaseDetailId(String purchaseDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByPurchaseDetailIds(Arrays.asList(purchaseDetailId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b61aebdb-3fb2-36e8-a74f-b84d1fad08dd")
    @Override
    public List<DrugExportDetailBaseDto> getByDrugExportId(String drugExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> drugExportDetailBaseDtoList =
                getByDrugExportIds(Arrays.asList(drugExportId));
        return drugExportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bc2757c3-3756-30a9-ac34-e52b701cfefb")
    @Override
    public List<DrugExportDetailBaseDto> getByPurchaseDetailIds(List<String> purchaseDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(purchaseDetailId)) {
            return Collections.emptyList();
        }

        List<DrugExportDetail> drugExportDetailList =
                drugExportDetailDao.getByPurchaseDetailIds(purchaseDetailId);
        if (CollectionUtil.isEmpty(drugExportDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportDetailToDrugExportDetailBaseDto(drugExportDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "fa60e09d-febb-3542-b97e-1cfb7964fbc4")
    @Override
    public DrugExportDetailBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugExportDetailBaseDto drugExportDetailBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugExportDetailBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
