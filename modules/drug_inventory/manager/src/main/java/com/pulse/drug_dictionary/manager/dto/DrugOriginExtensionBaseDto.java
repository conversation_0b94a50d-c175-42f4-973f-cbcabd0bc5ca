package com.pulse.drug_dictionary.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "dfd8ff7a-f870-4f0b-a76e-d66760d82100|DTO|DEFINITION")
public class DrugOriginExtensionBaseDto {
    /** 进院日期 */
    @AutoGenerated(locked = true, uuid = "f4040892-95d1-4a48-8cd2-f5839b789572")
    private Date admissionDateTime;

    /** 招标药品标志 出/入库单字段维护 */
    @AutoGenerated(locked = true, uuid = "64655d09-d44d-41f1-a5e9-92e8fd59545c")
    private Boolean bidFlag;

    /** 招标类型 只做了维护 */
    @AutoGenerated(locked = true, uuid = "1a86c5be-3ed9-42c1-8376-2c6905feefd1")
    private String bidType;

    /** 分类标识编码 药房只做了维护，未发现其他业务流程 */
    @AutoGenerated(locked = true, uuid = "517e21e0-c33c-4507-842e-47c48ecf41e8")
    private String categoricalCode;

    /** 颗粒剂转换比-分母 */
    @AutoGenerated(locked = true, uuid = "0e6db73e-bdcf-4864-afb1-0feb3ee7328b")
    private BigDecimal conversionRatioDenominator;

    /** 颗粒剂转换比-分子 */
    @AutoGenerated(locked = true, uuid = "3559dd1f-092c-4eec-b163-3986f44e9361")
    private BigDecimal conversionRatioNumerator;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9ab488e5-**************-3273bc7796a0")
    private Date createdAt;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "981b7a4c-0303-4cfb-9165-6dd076e34c73")
    private Long deletedAt;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "0dc05230-e058-46fa-8d57-4ccc29b951de")
    private String drugOriginCode;

    /** gcp药品编码 */
    @AutoGenerated(locked = true, uuid = "28973b4d-cfdd-49be-9953-68ca9f1a3585")
    private String gcpCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "00022ca7-a1e4-4231-8004-aea7278a0bc2")
    private String id;

    /** 药品进口证号 */
    @AutoGenerated(locked = true, uuid = "9b88cb2e-f71d-42cc-8f23-22823f055120")
    private String importLicenceNumber;

    /** 加价率 */
    @AutoGenerated(locked = true, uuid = "c88721e4-52bf-4b1c-8a00-3d9d7d9c2005")
    private BigDecimal markupRate;

    /** 国家医保代码 */
    @AutoGenerated(locked = true, uuid = "675b526b-2c16-4880-b0d3-f0f9ad1756b2")
    private String nationalMedicalInsuranceCode;

    /** 国家医保名称 */
    @AutoGenerated(locked = true, uuid = "3d24c9a0-250f-403d-b464-4440212f48ec")
    private String nationalMedicalInsuranceName;

    /**
     * 药械平台药品id
     * 药械平台id，药械平台对应药品主键，做显示使用（药品检索方案也会显示），方便药师查找药械平台对应药品。这个字段浙二应该没有维护（对接完药械后会有一张医院药品和平台药品的字典对照表）
     */
    @AutoGenerated(locked = true, uuid = "7f1c63b1-860c-4ae8-b9de-9f4cd80b6ccc")
    private String purchasePlatformDrugId;

    /** 批准文号 入库业务中会保存该字段 */
    @AutoGenerated(locked = true, uuid = "d6f58382-ce6a-4f21-82b4-7d214203aa64")
    private String qualificationCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "57834b19-3b28-4c27-8828-dcea29603f06")
    private Date updatedAt;
}
