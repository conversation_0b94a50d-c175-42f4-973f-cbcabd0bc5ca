package com.pulse.drug_inventory.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugOriginBatchInventoryBaseDtoManager;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryWithSpecificationDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "ec66d800-dbf2-468e-b908-e50e0ce1f418|DTO|BASE_CONVERTER")
public class DrugOriginBatchInventoryWithSpecificationDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryBaseDtoManager drugOriginBatchInventoryBaseDtoManager;

    @AutoGenerated(locked = true)
    public DrugOriginBatchInventoryWithSpecificationDto
            convertFromDrugOriginBatchInventoryBaseDtoToDrugOriginBatchInventoryWithSpecificationDto(
                    DrugOriginBatchInventoryBaseDto drugOriginBatchInventoryBaseDto) {
        return convertFromDrugOriginBatchInventoryBaseDtoToDrugOriginBatchInventoryWithSpecificationDto(
                        List.of(drugOriginBatchInventoryBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<DrugOriginBatchInventoryWithSpecificationDto>
            convertFromDrugOriginBatchInventoryBaseDtoToDrugOriginBatchInventoryWithSpecificationDto(
                    List<DrugOriginBatchInventoryBaseDto> drugOriginBatchInventoryBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginBatchInventoryBaseDtoList)) {
            return new ArrayList<>();
        }
        List<DrugOriginBatchInventoryWithSpecificationDto>
                drugOriginBatchInventoryWithSpecificationDtoList = new ArrayList<>();
        for (DrugOriginBatchInventoryBaseDto drugOriginBatchInventoryBaseDto :
                drugOriginBatchInventoryBaseDtoList) {
            if (drugOriginBatchInventoryBaseDto == null) {
                continue;
            }
            DrugOriginBatchInventoryWithSpecificationDto
                    drugOriginBatchInventoryWithSpecificationDto =
                            new DrugOriginBatchInventoryWithSpecificationDto();
            drugOriginBatchInventoryWithSpecificationDto.setId(
                    drugOriginBatchInventoryBaseDto.getId());
            drugOriginBatchInventoryWithSpecificationDto.setBatchId(
                    drugOriginBatchInventoryBaseDto.getBatchId());
            drugOriginBatchInventoryWithSpecificationDto.setInventoryId(
                    drugOriginBatchInventoryBaseDto.getInventoryId());
            drugOriginBatchInventoryWithSpecificationDto.setDrugOriginCode(
                    drugOriginBatchInventoryBaseDto.getDrugOriginCode());
            drugOriginBatchInventoryWithSpecificationDto.setStorageCode(
                    drugOriginBatchInventoryBaseDto.getStorageCode());
            drugOriginBatchInventoryWithSpecificationDto.setSupplyId(
                    drugOriginBatchInventoryBaseDto.getSupplyId());
            drugOriginBatchInventoryWithSpecificationDto.setAmount(
                    drugOriginBatchInventoryBaseDto.getAmount());
            drugOriginBatchInventoryWithSpecificationDto.setBatchNumber(
                    drugOriginBatchInventoryBaseDto.getBatchNumber());
            drugOriginBatchInventoryWithSpecificationDto.setCreatedAt(
                    drugOriginBatchInventoryBaseDto.getCreatedAt());
            drugOriginBatchInventoryWithSpecificationDto.setDeletedAt(
                    drugOriginBatchInventoryBaseDto.getDeletedAt());
            drugOriginBatchInventoryWithSpecificationDto.setExpirationDate(
                    drugOriginBatchInventoryBaseDto.getExpirationDate());
            drugOriginBatchInventoryWithSpecificationDto.setGcpCode(
                    drugOriginBatchInventoryBaseDto.getGcpCode());
            drugOriginBatchInventoryWithSpecificationDto.setUpdatedAt(
                    drugOriginBatchInventoryBaseDto.getUpdatedAt());
            drugOriginBatchInventoryWithSpecificationDto.setVirtualAmount(
                    drugOriginBatchInventoryBaseDto.getVirtualAmount());
            drugOriginBatchInventoryWithSpecificationDto.setPurchasePrice(
                    drugOriginBatchInventoryBaseDto.getPurchasePrice());
            drugOriginBatchInventoryWithSpecificationDto.setRetailPrice(
                    drugOriginBatchInventoryBaseDto.getRetailPrice());
            drugOriginBatchInventoryWithSpecificationDto.setImportDateTime(
                    drugOriginBatchInventoryBaseDto.getImportDateTime());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugOriginBatchInventoryWithSpecificationDtoList.add(
                    drugOriginBatchInventoryWithSpecificationDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugOriginBatchInventoryWithSpecificationDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public DrugOriginBatchInventoryBaseDto
            convertFromDrugOriginBatchInventoryWithSpecificationDtoToDrugOriginBatchInventoryBaseDto(
                    DrugOriginBatchInventoryWithSpecificationDto
                            drugOriginBatchInventoryWithSpecificationDto) {
        return convertFromDrugOriginBatchInventoryWithSpecificationDtoToDrugOriginBatchInventoryBaseDto(
                        List.of(drugOriginBatchInventoryWithSpecificationDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugOriginBatchInventoryBaseDto>
            convertFromDrugOriginBatchInventoryWithSpecificationDtoToDrugOriginBatchInventoryBaseDto(
                    List<DrugOriginBatchInventoryWithSpecificationDto>
                            drugOriginBatchInventoryWithSpecificationDtoList) {
        if (CollectionUtil.isEmpty(drugOriginBatchInventoryWithSpecificationDtoList)) {
            return new ArrayList<>();
        }
        return drugOriginBatchInventoryBaseDtoManager.getByIds(
                drugOriginBatchInventoryWithSpecificationDtoList.stream()
                        .map(DrugOriginBatchInventoryWithSpecificationDto::getId)
                        .collect(Collectors.toList()));
    }
}
