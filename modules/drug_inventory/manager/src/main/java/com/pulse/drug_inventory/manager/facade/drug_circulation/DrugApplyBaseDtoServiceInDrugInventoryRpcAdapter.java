package com.pulse.drug_inventory.manager.facade.drug_circulation;

import com.pulse.drug_circulation.manager.dto.DrugApplyBaseDto;
import com.pulse.drug_inventory.manager.facade.drug_circulation.base.DrugApplyBaseDtoServiceInDrugInventoryBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "7ead5bf0-4f48-414d-9026-d83055a95c8b")
@AutoGenerated(locked = false, uuid = "2ea6a5dc-79ce-3145-a163-2d3eed6a5f20")
public class DrugApplyBaseDtoServiceInDrugInventoryRpcAdapter
        extends DrugApplyBaseDtoServiceInDrugInventoryBaseRpcAdapter {

    @RpcRefer(id = "a96611cf-9774-4326-be74-df7f2d1db933", version = "1747276981432")
    @AutoGenerated(locked = false, uuid = "a96611cf-9774-4326-be74-df7f2d1db933|RPC|ADAPTER")
    public DrugApplyBaseDto getById(String id) {
        return super.getById(id);
    }
}
