package com.pulse.drug_inventory.manager.dto;

import com.pulse.drug_inventory.common.enums.UseFrequencyEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "6f6a7c8f-77ff-4013-98b2-edfb24cf5936|DTO|DEFINITION")
public class DrugOriginInventoryBaseDto {
    /** 数量 库存数量，按最小规格单位存储 */
    @AutoGenerated(locked = true, uuid = "09885e01-5737-4422-baf3-391e722e2d47")
    private BigDecimal amount;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "415f7d32-7156-4ec0-8af6-f8b636b2ea5f")
    private Date createdAt;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "2e0f445d-89e3-4823-a5e4-782e9edc0fc7")
    private String drugOriginCode;

    /** 药品产地规格id */
    @AutoGenerated(locked = true, uuid = "f166f92d-297d-436e-a9f7-6ed0c8026c18")
    private String drugOriginSpecificationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4ec82edf-0731-480f-9fec-373ae9f98f73")
    private String id;

    /** 在途数量 药库已出库，当前库房未入库的数量 */
    @AutoGenerated(locked = true, uuid = "bab8e661-9e5d-42d3-906c-967cc6af9c47")
    private BigDecimal inTransitAmount;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "c0447a54-1a0b-4179-a4b5-9bec1f0520a9")
    private Long lockVersion;

    /** 预占数量 待发药数量 */
    @AutoGenerated(locked = true, uuid = "b62a0d9d-a77d-4a93-808c-f8bd5ea98191")
    private BigDecimal preOccupiedAmount;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "3b9b5306-a74a-4ad7-99d9-3c74c6a66154")
    private String storageCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "5f48d2f4-45a4-4662-838b-8530dd720338")
    private Date updatedAt;

    /** 使用频率 */
    @AutoGenerated(locked = true, uuid = "47729d0d-fe00-45a5-8820-ee8f40a7be67")
    private UseFrequencyEnum useFrequency;

    /**
     * 虚库存数量 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
     * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
     * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
     */
    @AutoGenerated(locked = true, uuid = "74b0b2c8-7e5e-4689-ac90-ab9f310169c2")
    private BigDecimal virtualAmount;
}
