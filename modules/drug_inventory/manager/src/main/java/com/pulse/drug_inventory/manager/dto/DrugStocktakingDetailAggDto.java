package com.pulse.drug_inventory.manager.dto;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_inventory.common.enums.ProfitLossTypeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "ce5c7ac4-1be1-4b46-b31b-e1fb73c58d6d|DTO|DEFINITION")
public class DrugStocktakingDetailAggDto {
    /** 帐簿类别 */
    @AutoGenerated(locked = true, uuid = "4b116a25-7688-41e0-9de9-f47b662e7c7e")
    private String accountTypeCode;

    /** 拆分系数 冗余存 */
    @AutoGenerated(locked = true, uuid = "43dc953a-2177-4f19-9c6a-f9d441d7d61c")
    private Long amountPerPackage;

    /** 库存记录id */
    @AutoGenerated(locked = true, uuid = "ac253d58-6886-4bb1-b55c-8d0372ee5bc4")
    private String batchInventoryId;

    /** 批号 冗余存 */
    @AutoGenerated(locked = true, uuid = "48c141dc-63ae-4037-959d-ea4a4eff1d61")
    private String batchNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "84eb476e-75ed-4f14-b4dc-41ceb54cb53a")
    private Date createdAt;

    /** 盈亏数量 盘存数量-库存数量 盈：线下库存比线上库存多 亏：线下库存比线上库存少 */
    @AutoGenerated(locked = true, uuid = "37512e6c-10e4-45ee-984a-e297f8db26ce")
    private BigDecimal differenceAmount;

    /** 零售盈亏金额 */
    @AutoGenerated(locked = true, uuid = "02d0d93b-9493-428a-b224-a682bd26819a")
    private BigDecimal differenceRetailCost;

    /** 盈亏金额 批次的盈亏金额，盈为正。亏为负。（将挪批的金额也计入内） */
    @AutoGenerated(locked = true, uuid = "224e744e-b2a5-4b8a-9394-b1547413911e")
    private BigDecimal differenceStockCost;

    /** 药品产地编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "bb5f9134-43f8-442e-a233-a843eab9c4cf")
    private DrugOriginBaseDto drugOriginCode;

    /** 药品产地名称 冗余存 */
    @AutoGenerated(locked = true, uuid = "226d3625-eb41-4e8b-b6b6-89a1b4224ab0")
    private String drugOriginName;

    /** 药品产地规格id 冗余存 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2dca7326-090a-47d0-a247-febcb1d05fea")
    private DrugOriginSpecificationBaseDto drugOriginSpecification;

    /** 生产商ID 冗余存 */
    @Valid
    @AutoGenerated(locked = true, uuid = "bb3585a9-6f42-4ec7-97b7-c8d22598ec3f")
    private DrugProducerDictionaryBaseDto drugProducer;

    /** 盘点单id */
    @AutoGenerated(locked = true, uuid = "7d176aa5-32ac-4ef6-9df0-2ec2bc54f215")
    private String drugStocktakingId;

    /** 药品效期 冗余存 */
    @AutoGenerated(locked = true, uuid = "f4cde789-fe64-436f-a5cb-2784ecf235ac")
    private Date expirationDate;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "bcd7dbf5-0ee5-47a5-bbc6-c2867bd2581f")
    private String id;

    /** 货位编码 drug_stocktaking_type.location_code */
    @AutoGenerated(locked = true, uuid = "d49f604f-5dad-4ba7-bd43-63951eef57ab")
    private String locationCode;

    /** 管理模式 */
    @AutoGenerated(locked = true, uuid = "9c665266-9da8-4430-9359-535df944a92d")
    private ManageModelEnum manageMode;

    /** 盈亏零数量 */
    @AutoGenerated(locked = true, uuid = "63e4a8a2-ca39-4afc-b982-235e97c22393")
    private BigDecimal minDifferenceAmount;

    /** 库存零数量 */
    @AutoGenerated(locked = true, uuid = "d90d6aa7-212d-4c8f-9207-1b2f82920e87")
    private BigDecimal minStockAmount;

    /** 盘存零数量 */
    @AutoGenerated(locked = true, uuid = "ba539b93-59db-4a6a-89f9-0e84a53d75ad")
    private BigDecimal minStocktakingAmount;

    /** 盈亏整数量 */
    @AutoGenerated(locked = true, uuid = "5686868e-bfdc-41a5-b94f-51831e88dc1e")
    private BigDecimal packageDifferenceAmount;

    /** 库存整数量 */
    @AutoGenerated(locked = true, uuid = "274c8e20-5b3c-4c24-84ff-088ec456cd58")
    private BigDecimal packageStockAmount;

    /** 盘存整数量 */
    @AutoGenerated(locked = true, uuid = "1f0246b0-7128-42eb-922e-3015c128ee8c")
    private BigDecimal packageStocktakingAmount;

    /** 盈亏类型 盘盈、盘亏、数量一致 */
    @AutoGenerated(locked = true, uuid = "b9ea2226-9aeb-4b6c-88aa-c9c29f776ab1")
    private ProfitLossTypeEnum profitLossType;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "2a46fff3-63c6-426d-abda-f42d4e37a01a")
    private String remark;

    /** 市场零售价 记录盘点时的零售价，取价表的实时价格 */
    @AutoGenerated(locked = true, uuid = "17535bdf-8d52-41c5-923b-4007552c1988")
    private BigDecimal retailPrice;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "ec46661b-9e9e-4561-9893-732a20ab37de")
    private Long sortNumber;

    /** 规格 冗余存 */
    @AutoGenerated(locked = true, uuid = "d4c53f33-8feb-4d7d-9100-161bc49c1879")
    private String specification;

    /** 库存数量 drug_stock.amount */
    @AutoGenerated(locked = true, uuid = "d20db435-44da-4326-a30d-d4388ecdafd7")
    private BigDecimal stockAmount;

    /** 库存价/进价 记录批次库存的的库存价，取drug_stock_batch.purchase_price */
    @AutoGenerated(locked = true, uuid = "9581d044-e8eb-4f09-b359-682bd6fd0a19")
    private BigDecimal stockPrice;

    /** 盘存数量 页面录入的盘存数量 */
    @AutoGenerated(locked = true, uuid = "5b68d9fd-816a-499b-9434-1132980d1c33")
    private BigDecimal stocktakingAmount;

    /** 汇总盘存单明细ID */
    @AutoGenerated(locked = true, uuid = "f2024a52-8e0f-4030-bd1d-1ad6856262d4")
    private String summaryStocktakingDetailId;

    /** 单位 冗余存 */
    @AutoGenerated(locked = true, uuid = "bfc06e64-1d43-4959-a329-11c99e613173")
    private String unit;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "4b89b502-9e75-4934-ba52-3182fc113fb3")
    private Date updatedAt;
}
