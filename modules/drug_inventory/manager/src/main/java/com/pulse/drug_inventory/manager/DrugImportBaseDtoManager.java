package com.pulse.drug_inventory.manager;

import com.pulse.drug_inventory.manager.dto.DrugImportBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "b1116cea-88f7-44af-83e9-b56a45722726|DTO|MANAGER")
public interface DrugImportBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "3997b4a4-4767-370a-8afc-a5ed01fe6380")
    List<DrugImportBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "39e6972f-d27f-3fee-87a2-244b0602158e")
    List<DrugImportBaseDto> getByAccountingPeriodId(String accountingPeriodId);

    @AutoGenerated(locked = true, uuid = "4af06d12-a1bc-3713-b610-4f73e00152ce")
    List<DrugImportBaseDto> getByStocktakingIds(List<String> stocktakingId);

    @AutoGenerated(locked = true, uuid = "5327d8ac-f82a-30b2-b100-826e6b8e5f4f")
    List<DrugImportBaseDto> getByDrugPurchaseId(String drugPurchaseId);

    @AutoGenerated(locked = true, uuid = "5bd0e831-8ddf-33c7-974d-76940b8a310a")
    List<DrugImportBaseDto> getByAccountingPeriodIds(List<String> accountingPeriodId);

    @AutoGenerated(locked = true, uuid = "5f809f07-e714-342a-ab02-401b6aeaff52")
    List<DrugImportBaseDto> getByApplyStaffIds(List<String> applyStaffId);

    @AutoGenerated(locked = true, uuid = "63fe9236-875e-30b9-94db-1d5c35c5bdbd")
    List<DrugImportBaseDto> getBySupplierIds(List<String> supplierId);

    @AutoGenerated(locked = true, uuid = "992c8777-c195-3f1a-8060-8a349246f728")
    DrugImportBaseDto getByDocumentNumber(String documentNumber);

    @AutoGenerated(locked = true, uuid = "a9d5725a-eac0-376b-bb01-c71dcf787734")
    List<DrugImportBaseDto> getByDocumentNumbers(List<String> documentNumber);

    @AutoGenerated(locked = true, uuid = "af717e93-b915-3e6a-b60b-************")
    List<DrugImportBaseDto> getByRefundImportIds(List<String> refundImportId);

    @AutoGenerated(locked = true, uuid = "b1cad500-18a3-3e0a-be97-f95984618219")
    List<DrugImportBaseDto> getByAccountantStaffId(String accountantStaffId);

    @AutoGenerated(locked = true, uuid = "c09f1d67-0ac7-3e83-bd10-9907f533458f")
    List<DrugImportBaseDto> getByAccountantStaffIds(List<String> accountantStaffId);

    @AutoGenerated(locked = true, uuid = "c24f9835-125a-337c-ba79-0da7dfcc2727")
    List<DrugImportBaseDto> getByDrugExportId(String drugExportId);

    @AutoGenerated(locked = true, uuid = "c5da0cfb-b40b-3f83-8403-66142fc4a081")
    DrugImportBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "c9a135f2-8303-322f-86d0-c11196839a42")
    List<DrugImportBaseDto> getByStorageCode(String storageCode);

    @AutoGenerated(locked = true, uuid = "dbbb932e-924d-36fc-a672-23a6e66d5304")
    List<DrugImportBaseDto> getByStocktakingId(String stocktakingId);

    @AutoGenerated(locked = true, uuid = "df1faf61-7507-3d05-9eed-d389f596fd7b")
    List<DrugImportBaseDto> getByRefundImportId(String refundImportId);

    @AutoGenerated(locked = true, uuid = "e1445954-bd97-39ad-a3e1-e24cd4884871")
    List<DrugImportBaseDto> getBySupplierId(String supplierId);

    @AutoGenerated(locked = true, uuid = "ec7c6005-a534-37a1-b007-3150646ea450")
    List<DrugImportBaseDto> getByDrugPurchaseIds(List<String> drugPurchaseId);

    @AutoGenerated(locked = true, uuid = "f660341c-16bd-33e4-91a5-e651566203e8")
    List<DrugImportBaseDto> getByApplyStaffId(String applyStaffId);

    @AutoGenerated(locked = true, uuid = "fb7695ea-be94-3505-b1fb-93f5d6204932")
    List<DrugImportBaseDto> getByDrugExportIds(List<String> drugExportId);

    @AutoGenerated(locked = true, uuid = "fbad84c5-0b26-3886-8b40-0a3a791cb8aa")
    List<DrugImportBaseDto> getByStorageCodes(List<String> storageCode);
}
