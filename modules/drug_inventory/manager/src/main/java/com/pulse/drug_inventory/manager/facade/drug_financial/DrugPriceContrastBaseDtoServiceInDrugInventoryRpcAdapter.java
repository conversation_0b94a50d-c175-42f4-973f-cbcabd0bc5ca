package com.pulse.drug_inventory.manager.facade.drug_financial;

import com.pulse.drug_financial.manager.dto.DrugPriceContrastBaseDto;
import com.pulse.drug_financial.persist.eo.UkPackageSpecificationIdPriceTypeEo;
import com.pulse.drug_inventory.manager.facade.drug_financial.base.DrugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "7ead5bf0-4f48-414d-9026-d83055a95c8b")
@AutoGenerated(locked = false, uuid = "9763554b-254c-3ae5-a970-e3718eb8aab4")
public class DrugPriceContrastBaseDtoServiceInDrugInventoryRpcAdapter
        extends DrugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter {

    @RpcRefer(id = "d333774e-0fc9-473c-b746-05e8075f3fda", version = "1747298687005")
    @AutoGenerated(locked = false, uuid = "d333774e-0fc9-473c-b746-05e8075f3fda|RPC|ADAPTER")
    public List<DrugPriceContrastBaseDto> getByPackageOriginSpecificationId(
            String packageOriginSpecificationId) {
        return super.getByPackageOriginSpecificationId(packageOriginSpecificationId);
    }

    @RpcRefer(id = "e959248a-f728-4fd4-a56f-d2148b1da80a", version = "1747298687013")
    @AutoGenerated(locked = false, uuid = "e959248a-f728-4fd4-a56f-d2148b1da80a|RPC|ADAPTER")
    public List<DrugPriceContrastBaseDto>
            getByPackageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType(
                    UkPackageSpecificationIdPriceTypeEo var) {
        return super.getByPackageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType(var);
    }

    @RpcRefer(id = "ef060622-f15c-4f22-8151-3a15725ccd32", version = "1747298687009")
    @AutoGenerated(locked = false, uuid = "ef060622-f15c-4f22-8151-3a15725ccd32|RPC|ADAPTER")
    public List<DrugPriceContrastBaseDto> getByMinSpecificationId(String minSpecificationId) {
        return super.getByMinSpecificationId(minSpecificationId);
    }
}
