package com.pulse.drug_inventory.manager.bo.base;

import com.pulse.drug_inventory.common.enums.AmountChargeWayEnum;
import com.pulse.drug_inventory.common.enums.DrugOperationTypeEnum;
import com.pulse.drug_inventory.manager.bo.DrugInventoryCirculationDetailBO;
import com.pulse.drug_inventory.persist.dos.DrugInventoryCirculationDetail;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_inventory_circulation_detail")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "38494d1b-9d89-308f-a0dc-7e477f91ed67")
public abstract class BaseDrugInventoryCirculationDetailBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 会计期间ID */
    @Column(name = "accounting_period_id")
    @AutoGenerated(locked = true, uuid = "d698a720-ec56-42ad-8da2-5e233951d773")
    private String accountingPeriodId;

    /** 库存变化类型 增加、扣减 */
    @Column(name = "amount_charge_way")
    @AutoGenerated(locked = true, uuid = "6e9a9845-aca1-34f3-8125-edb9e467dc6e")
    @Enumerated(EnumType.STRING)
    private AmountChargeWayEnum amountChargeWay;

    /** 库存id */
    @Column(name = "batch_inventory_id")
    @AutoGenerated(locked = true, uuid = "13e010fb-1cfc-3b20-ab20-cd93b1f03200")
    private String batchInventoryId;

    /** 变更数量 按最小规格存储（注意规格转换） */
    @Column(name = "change_amount")
    @AutoGenerated(locked = true, uuid = "6af78fc8-7b06-3506-93fa-b04e80c960b4")
    private BigDecimal changeAmount;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "769f8863-fda2-3fcb-ad32-cbb2eca38eb0")
    private Date createdAt;

    /** 变更后数量 */
    @Column(name = "current_amount")
    @AutoGenerated(locked = true, uuid = "296bbb09-30f4-3683-a65f-0c3694688987")
    private BigDecimal currentAmount;

    /** 借还药明细id */
    @Column(name = "drug_borrow_detail_id")
    @AutoGenerated(locked = true, uuid = "9a8544dd-7298-3857-a815-ae5f49782094")
    private String drugBorrowDetailId;

    /** 出库明细id */
    @Column(name = "drug_export_detail_id")
    @AutoGenerated(locked = true, uuid = "a10076f5-0475-331b-bded-c4ee69b10303")
    private String drugExportDetailId;

    /** 入库明细id */
    @Column(name = "drug_import_detail_id")
    @AutoGenerated(locked = true, uuid = "2700836b-cef8-328a-96b7-29d253cc3225")
    private String drugImportDetailId;

    /** 业务类型 出入库、发退药、借还药、报损、虚库存变更 */
    @Column(name = "drug_operation_type")
    @AutoGenerated(locked = true, uuid = "9860d64e-75cc-3491-b608-a2ed763edbd8")
    @Enumerated(EnumType.STRING)
    private DrugOperationTypeEnum drugOperationType;

    /** 摆药明细id */
    @Column(name = "drug_order_dispense_detail_id")
    @AutoGenerated(locked = true, uuid = "06bafbfa-ed70-3379-897c-41f79aa51798")
    private String drugOrderDispenseDetailId;

    /** 发药明细id */
    @Column(name = "drug_prescription_dispense_detail_id")
    @AutoGenerated(locked = true, uuid = "422700e0-23e3-3187-9bb2-b377cd88d551")
    private String drugPrescriptionDispenseDetailId;

    /** 商品规格ID */
    @Column(name = "drug_product_specification_id")
    @AutoGenerated(locked = true, uuid = "c2d46b2e-087e-4d71-9783-f7bc39caaa48")
    private String drugProductSpecificationId;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "192905ee-0f58-383b-93b1-241d9b19237c")
    @Id
    private String id;

    /** 出入库方式id */
    @Column(name = "import_export_way_id")
    @AutoGenerated(locked = true, uuid = "1f72ea1f-b5c4-4d85-9ae8-b89c7604197a")
    private String importExportWayId;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "c87edcc3-3e70-4823-8353-82bcf9c55061")
    @Version
    private Long lockVersion;

    /** 报损明细id */
    @Column(name = "loss_report_detail_id")
    @AutoGenerated(locked = true, uuid = "38471d92-45f6-40a7-89ad-f0cad763ff34")
    private String lossReportDetailId;

    /** 变更前数量 */
    @Column(name = "original_amount")
    @AutoGenerated(locked = true, uuid = "2515f6aa-85c8-3126-bef8-2c7d60215cba")
    private BigDecimal originalAmount;

    /** 库房代码 */
    @Column(name = "storage_code")
    @AutoGenerated(locked = true, uuid = "5b8b6fac-583e-4c21-8cfb-3a27846b0b94")
    private String storageCode;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "dcd6a307-35e1-36a1-a2a6-ee7e13ae0975")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetail convertToDrugInventoryCirculationDetail() {
        DrugInventoryCirculationDetail entity = new DrugInventoryCirculationDetail();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "batchInventoryId",
                "drugOperationType",
                "amountChargeWay",
                "originalAmount",
                "changeAmount",
                "currentAmount",
                "drugImportDetailId",
                "drugExportDetailId",
                "drugPrescriptionDispenseDetailId",
                "drugOrderDispenseDetailId",
                "drugBorrowDetailId",
                "drugProductSpecificationId",
                "lossReportDetailId",
                "accountingPeriodId",
                "importExportWayId",
                "storageCode",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public String getAccountingPeriodId() {
        return this.accountingPeriodId;
    }

    @AutoGenerated(locked = true)
    public AmountChargeWayEnum getAmountChargeWay() {
        return this.amountChargeWay;
    }

    @AutoGenerated(locked = true)
    public String getBatchInventoryId() {
        return this.batchInventoryId;
    }

    @AutoGenerated(locked = true)
    public static DrugInventoryCirculationDetailBO getByDrugBorrowDetailId(
            String drugBorrowDetailId) {
        Session session = TransactionalSessionFactory.getSession();
        DrugInventoryCirculationDetailBO drugInventoryCirculationDetail =
                (DrugInventoryCirculationDetailBO)
                        session.createQuery(
                                        "from DrugInventoryCirculationDetailBO where "
                                                + "drugBorrowDetailId =: drugBorrowDetailId ")
                                .setParameter("drugBorrowDetailId", drugBorrowDetailId)
                                .uniqueResult();
        return drugInventoryCirculationDetail;
    }

    @AutoGenerated(locked = true)
    public static DrugInventoryCirculationDetailBO getByDrugOrderDispenseDetailId(
            String drugOrderDispenseDetailId) {
        Session session = TransactionalSessionFactory.getSession();
        DrugInventoryCirculationDetailBO drugInventoryCirculationDetail =
                (DrugInventoryCirculationDetailBO)
                        session.createQuery(
                                        "from DrugInventoryCirculationDetailBO where"
                                            + " drugOrderDispenseDetailId =:"
                                            + " drugOrderDispenseDetailId ")
                                .setParameter(
                                        "drugOrderDispenseDetailId", drugOrderDispenseDetailId)
                                .uniqueResult();
        return drugInventoryCirculationDetail;
    }

    @AutoGenerated(locked = true)
    public static DrugInventoryCirculationDetailBO getByDrugPrescriptionDispenseDetailId(
            String drugPrescriptionDispenseDetailId) {
        Session session = TransactionalSessionFactory.getSession();
        DrugInventoryCirculationDetailBO drugInventoryCirculationDetail =
                (DrugInventoryCirculationDetailBO)
                        session.createQuery(
                                        "from DrugInventoryCirculationDetailBO where"
                                            + " drugPrescriptionDispenseDetailId =:"
                                            + " drugPrescriptionDispenseDetailId ")
                                .setParameter(
                                        "drugPrescriptionDispenseDetailId",
                                        drugPrescriptionDispenseDetailId)
                                .uniqueResult();
        return drugInventoryCirculationDetail;
    }

    @AutoGenerated(locked = true)
    public static DrugInventoryCirculationDetailBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugInventoryCirculationDetailBO drugInventoryCirculationDetail =
                (DrugInventoryCirculationDetailBO)
                        session.createQuery(
                                        "from DrugInventoryCirculationDetailBO where "
                                                + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugInventoryCirculationDetail;
    }

    @AutoGenerated(locked = true)
    public static DrugInventoryCirculationDetailBO getByLossReportDetailId(
            String lossReportDetailId) {
        Session session = TransactionalSessionFactory.getSession();
        DrugInventoryCirculationDetailBO drugInventoryCirculationDetail =
                (DrugInventoryCirculationDetailBO)
                        session.createQuery(
                                        "from DrugInventoryCirculationDetailBO where "
                                                + "lossReportDetailId =: lossReportDetailId ")
                                .setParameter("lossReportDetailId", lossReportDetailId)
                                .uniqueResult();
        return drugInventoryCirculationDetail;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getChangeAmount() {
        return this.changeAmount;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getCurrentAmount() {
        return this.currentAmount;
    }

    @AutoGenerated(locked = true)
    public String getDrugBorrowDetailId() {
        return this.drugBorrowDetailId;
    }

    @AutoGenerated(locked = true)
    public String getDrugExportDetailId() {
        return this.drugExportDetailId;
    }

    @AutoGenerated(locked = true)
    public String getDrugImportDetailId() {
        return this.drugImportDetailId;
    }

    @AutoGenerated(locked = true)
    public DrugOperationTypeEnum getDrugOperationType() {
        return this.drugOperationType;
    }

    @AutoGenerated(locked = true)
    public String getDrugOrderDispenseDetailId() {
        return this.drugOrderDispenseDetailId;
    }

    @AutoGenerated(locked = true)
    public String getDrugPrescriptionDispenseDetailId() {
        return this.drugPrescriptionDispenseDetailId;
    }

    @AutoGenerated(locked = true)
    public String getDrugProductSpecificationId() {
        return this.drugProductSpecificationId;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getImportExportWayId() {
        return this.importExportWayId;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getLossReportDetailId() {
        return this.lossReportDetailId;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getOriginalAmount() {
        return this.originalAmount;
    }

    @AutoGenerated(locked = true)
    public String getStorageCode() {
        return this.storageCode;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setAccountingPeriodId(String accountingPeriodId) {
        this.accountingPeriodId = accountingPeriodId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setAmountChargeWay(
            AmountChargeWayEnum amountChargeWay) {
        this.amountChargeWay = amountChargeWay;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setBatchInventoryId(String batchInventoryId) {
        this.batchInventoryId = batchInventoryId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugBorrowDetailId(String drugBorrowDetailId) {
        this.drugBorrowDetailId = drugBorrowDetailId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugExportDetailId(String drugExportDetailId) {
        this.drugExportDetailId = drugExportDetailId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugImportDetailId(String drugImportDetailId) {
        this.drugImportDetailId = drugImportDetailId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugOperationType(
            DrugOperationTypeEnum drugOperationType) {
        this.drugOperationType = drugOperationType;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugOrderDispenseDetailId(
            String drugOrderDispenseDetailId) {
        this.drugOrderDispenseDetailId = drugOrderDispenseDetailId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugPrescriptionDispenseDetailId(
            String drugPrescriptionDispenseDetailId) {
        this.drugPrescriptionDispenseDetailId = drugPrescriptionDispenseDetailId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setDrugProductSpecificationId(
            String drugProductSpecificationId) {
        this.drugProductSpecificationId = drugProductSpecificationId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setId(String id) {
        this.id = id;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setImportExportWayId(String importExportWayId) {
        this.importExportWayId = importExportWayId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setLossReportDetailId(String lossReportDetailId) {
        this.lossReportDetailId = lossReportDetailId;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setStorageCode(String storageCode) {
        this.storageCode = storageCode;
        return (DrugInventoryCirculationDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugInventoryCirculationDetailBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugInventoryCirculationDetailBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
