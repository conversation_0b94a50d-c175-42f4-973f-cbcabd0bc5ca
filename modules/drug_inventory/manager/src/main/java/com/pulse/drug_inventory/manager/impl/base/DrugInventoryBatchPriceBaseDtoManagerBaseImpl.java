package com.pulse.drug_inventory.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugInventoryBatchPriceBaseDtoManager;
import com.pulse.drug_inventory.manager.converter.DrugInventoryBatchPriceBaseDtoConverter;
import com.pulse.drug_inventory.manager.dto.DrugInventoryBatchPriceBaseDto;
import com.pulse.drug_inventory.persist.dos.DrugOriginBatchInventoryPrice;
import com.pulse.drug_inventory.persist.mapper.DrugOriginBatchInventoryPriceDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "aa265462-5751-480a-a1a7-1b9144189a5e|DTO|BASE_MANAGER_IMPL")
public abstract class DrugInventoryBatchPriceBaseDtoManagerBaseImpl
        implements DrugInventoryBatchPriceBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugInventoryBatchPriceBaseDtoConverter drugInventoryBatchPriceBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginBatchInventoryPriceDao drugOriginBatchInventoryPriceDao;

    @AutoGenerated(locked = true, uuid = "4df99624-52ad-3017-9119-5c7ebdf19025")
    @Override
    public List<DrugInventoryBatchPriceBaseDto> getByIds(List<Long> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugOriginBatchInventoryPrice> drugOriginBatchInventoryPriceList =
                drugOriginBatchInventoryPriceDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugOriginBatchInventoryPriceList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<Long, DrugOriginBatchInventoryPrice> drugOriginBatchInventoryPriceMap =
                drugOriginBatchInventoryPriceList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugOriginBatchInventoryPriceList =
                id.stream()
                        .map(i -> drugOriginBatchInventoryPriceMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugOriginBatchInventoryPriceToDrugInventoryBatchPriceBaseDto(
                drugOriginBatchInventoryPriceList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "aae1ecd4-d373-3968-9951-6d46451a87c1")
    public List<DrugInventoryBatchPriceBaseDto>
            doConvertFromDrugOriginBatchInventoryPriceToDrugInventoryBatchPriceBaseDto(
                    List<DrugOriginBatchInventoryPrice> drugOriginBatchInventoryPriceList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginBatchInventoryPriceList)) {
            return Collections.emptyList();
        }

        Map<Long, DrugInventoryBatchPriceBaseDto> dtoMap =
                drugInventoryBatchPriceBaseDtoConverter
                        .convertFromDrugOriginBatchInventoryPriceToDrugInventoryBatchPriceBaseDto(
                                drugOriginBatchInventoryPriceList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugInventoryBatchPriceBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugInventoryBatchPriceBaseDto> drugInventoryBatchPriceBaseDtoList = new ArrayList<>();
        for (DrugOriginBatchInventoryPrice i : drugOriginBatchInventoryPriceList) {
            DrugInventoryBatchPriceBaseDto drugInventoryBatchPriceBaseDto = dtoMap.get(i.getId());
            if (drugInventoryBatchPriceBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugInventoryBatchPriceBaseDtoList.add(drugInventoryBatchPriceBaseDto);
        }
        return drugInventoryBatchPriceBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "b3d03342-bf5b-376e-a797-4faada6615dc")
    @Override
    public List<DrugInventoryBatchPriceBaseDto> getByBatchInventoryIds(
            List<String> batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(batchInventoryId)) {
            return Collections.emptyList();
        }

        List<DrugOriginBatchInventoryPrice> drugOriginBatchInventoryPriceList =
                drugOriginBatchInventoryPriceDao.getByBatchInventoryIds(batchInventoryId);
        if (CollectionUtil.isEmpty(drugOriginBatchInventoryPriceList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginBatchInventoryPriceToDrugInventoryBatchPriceBaseDto(
                drugOriginBatchInventoryPriceList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d3a72323-177e-3c8b-b9c1-da8f11a2b999")
    @Override
    public DrugInventoryBatchPriceBaseDto getById(Long id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugInventoryBatchPriceBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugInventoryBatchPriceBaseDto drugInventoryBatchPriceBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugInventoryBatchPriceBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d9545605-099b-3bcb-960d-48811bd3eb11")
    @Override
    public List<DrugInventoryBatchPriceBaseDto> getByBatchInventoryId(String batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugInventoryBatchPriceBaseDto> drugInventoryBatchPriceBaseDtoList =
                getByBatchInventoryIds(Arrays.asList(batchInventoryId));
        return drugInventoryBatchPriceBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
