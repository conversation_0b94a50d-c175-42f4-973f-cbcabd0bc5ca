package com.pulse.drug_inventory.manager.facade.drug_dictionary;

import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryWithDrugDto;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.base.DrugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "7ead5bf0-4f48-414d-9026-d83055a95c8b")
@AutoGenerated(locked = false, uuid = "328ed74f-9f73-34a6-b731-4ae560f4a844")
public class DrugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryRpcAdapter
        extends DrugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryBaseRpcAdapter {

    @RpcRefer(id = "97e77590-3a7e-4ff4-bf8f-f164c7f645cc", version = "1747729992615")
    @AutoGenerated(locked = false, uuid = "97e77590-3a7e-4ff4-bf8f-f164c7f645cc|RPC|ADAPTER")
    public List<DrugSpecificationDictionaryWithDrugDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
