package com.pulse.drug_inventory.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "c1f72183-8f1d-316f-8ca0-ef7e6d92eb56|EO|DEFINITION")
public class IdxDrugOriginSpecificationIdStorageCodeBatchNumberEo {
    @AutoGenerated(locked = true, uuid = "03d04101-95b1-39d0-88a0-848cd4088e4f")
    private String drugOriginSpecificationId;

    @AutoGenerated(locked = true, uuid = "68ab5715-ec04-36c9-919d-72ed68605178")
    private String storageCode;

    @AutoGenerated(locked = true, uuid = "a80de9a9-d961-3528-afa7-cb982138540b")
    private String batchNumber;
}
