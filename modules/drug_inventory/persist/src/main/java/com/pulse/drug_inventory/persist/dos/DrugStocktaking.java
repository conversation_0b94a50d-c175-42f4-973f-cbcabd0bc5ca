package com.pulse.drug_inventory.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.drug_inventory.common.enums.StocktakingStatusEnum;
import com.pulse.drug_inventory.common.enums.StocktakingTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "drug_stocktaking", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "0ab39728-11e0-367c-b923-1fee2308bfac|ENTITY|DEFINITION")
public class DrugStocktaking {
    @AutoGenerated(locked = true, uuid = "9eb6e903-47ad-35b7-b3a0-3054f67cd217")
    @TableField(value = "accountant_by")
    private String accountantBy;

    @AutoGenerated(locked = true, uuid = "1a7c4d0c-556a-3cb4-971d-9909e7cb9a63")
    @TableField(value = "accountant_date_time")
    private Date accountantDateTime;

    @AutoGenerated(locked = true, uuid = "58ebc2bb-dae8-47f7-ba17-35dce6a6525e")
    @TableField(value = "accounting_period")
    private String accountingPeriod;

    @AutoGenerated(locked = true, uuid = "4faf5775-d50e-3d6a-9af3-46d603aa38c5")
    @TableField(value = "create_by")
    private String createBy;

    @AutoGenerated(locked = true, uuid = "da94b8bf-6a0c-3d11-95e7-4c426c0046f9")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "87c471a6-b1d8-54b9-9bf7-92a7cd6c9b15")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "c2a4ecf6-896f-35b9-9ecd-9a325b1fb8fe")
    @TableField(value = "export_import_code")
    private String exportImportCode;

    @AutoGenerated(locked = true, uuid = "78a95891-b9e3-3795-a17e-4a0ecea3c2d8")
    @TableField(value = "herb_type")
    private String herbType;

    @AutoGenerated(locked = true, uuid = "94e7bad6-9df8-39b3-866a-943ce1cd321f")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "a0aaae76-1e9b-437c-8184-9057d1707d06")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "8e8a528d-f686-3215-b7be-ee4226edf9a0")
    @TableField(value = "remark")
    private String remark;

    @AutoGenerated(locked = true, uuid = "5be087c4-f097-37f8-8bca-36750468cc83")
    @TableField(value = "stocktaking_code")
    private String stocktakingCode;

    /** 盘存单号 */
    @AutoGenerated(locked = true, uuid = "54e9ca65-40b0-35a4-b5eb-81bd38f78a75")
    @TableField(value = "stocktaking_number")
    private String stocktakingNumber;

    /** 已保存、已记账、已作废 */
    @AutoGenerated(locked = true, uuid = "*************-3751-8b11-a8fe3f592539")
    @TableField(value = "stocktaking_status")
    private StocktakingStatusEnum stocktakingStatus;

    @AutoGenerated(locked = true, uuid = "c3230b0e-ca7a-4429-b078-c428f943da06")
    @TableField(value = "stocktaking_type")
    private StocktakingTypeEnum stocktakingType;

    @AutoGenerated(locked = true, uuid = "815c76a0-dede-355b-bf65-22e7981713a7")
    @TableField(value = "storage_code")
    private String storageCode;

    @AutoGenerated(locked = true, uuid = "7f60a9e7-52de-4dff-bd3f-f8284de39819")
    @TableField(value = "summary_stocktaking_id")
    private String summaryStocktakingId;

    @AutoGenerated(locked = true, uuid = "1c79c021-4308-39bd-be04-a32e86fb6380")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
