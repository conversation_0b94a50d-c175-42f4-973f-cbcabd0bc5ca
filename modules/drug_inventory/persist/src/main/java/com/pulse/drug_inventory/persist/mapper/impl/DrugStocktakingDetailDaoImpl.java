package com.pulse.drug_inventory.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.drug_inventory.persist.dos.DrugStocktakingDetail;
import com.pulse.drug_inventory.persist.mapper.DrugStocktakingDetailDao;
import com.pulse.drug_inventory.persist.mapper.mybatis.DrugStocktakingDetailMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "5e0306f5-03c2-3ff9-8a91-722b8fb370d5|ENTITY|DAO")
public class DrugStocktakingDetailDaoImpl implements DrugStocktakingDetailDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingDetailMapper drugStocktakingDetailMapper;

    @AutoGenerated(locked = true, uuid = "09395e10-9209-350f-9040-89e3bcee1fe8")
    @Override
    public List<DrugStocktakingDetail> getByBatchInventoryIds(List<String> batchInventoryId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("batch_inventory_id", batchInventoryId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "257f2f1b-dcce-3d23-b304-c04f1a2ca2c1")
    @Override
    public DrugStocktakingDetail getById(String id) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugStocktakingDetailMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "2dca20c2-dc6d-351b-9b81-6b4eb38919ed")
    @Override
    public List<DrugStocktakingDetail> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "30019595-a399-3811-bad5-411b33f400ef")
    @Override
    public List<DrugStocktakingDetail> getByDrugOriginCode(String drugOriginCode) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_code", drugOriginCode).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "387e1b45-c53e-3e90-944e-11515c54735c")
    @Override
    public List<DrugStocktakingDetail> getBySummaryStocktakingDetailId(
            String summaryStocktakingDetailId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("summary_stocktaking_detail_id", summaryStocktakingDetailId)
                .orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "575acde9-bcab-385f-9519-6f9fd508a65b")
    @Override
    public List<DrugStocktakingDetail> getByDrugStocktakingIds(List<String> drugStocktakingId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_stocktaking_id", drugStocktakingId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "73075da4-cdfc-368b-b775-3d3a10c2e1f8")
    @Override
    public List<DrugStocktakingDetail> getByDrugOriginCodes(List<String> drugOriginCode) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_code", drugOriginCode).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9f502b9b-8a62-3c06-a3e3-ff9cfded9e9c")
    @Override
    public List<DrugStocktakingDetail> getByDrugStocktakingId(String drugStocktakingId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_stocktaking_id", drugStocktakingId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b0f6387d-453f-3d54-9afc-5860517c31ce")
    @Override
    public List<DrugStocktakingDetail> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "dcb72de5-e459-377c-8750-6becca3b4ee1")
    @Override
    public List<DrugStocktakingDetail> getByIds(List<String> id) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "df2daec3-14ac-3e2b-9ccd-14ea9da12282")
    @Override
    public List<DrugStocktakingDetail> getByDrugProducerId(String drugProducerId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_producer_id", drugProducerId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e3906ff5-8b38-3ad6-941b-431d0d460899")
    @Override
    public List<DrugStocktakingDetail> getByDrugProducerIds(List<String> drugProducerId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_producer_id", drugProducerId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f7dbcd76-b9ca-3811-b38a-17022a323692")
    @Override
    public List<DrugStocktakingDetail> getByBatchInventoryId(String batchInventoryId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("batch_inventory_id", batchInventoryId).orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "fb8b549d-fc50-3f1d-a522-ef81449f455f")
    @Override
    public List<DrugStocktakingDetail> getBySummaryStocktakingDetailIds(
            List<String> summaryStocktakingDetailId) {
        QueryWrapper<DrugStocktakingDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("summary_stocktaking_detail_id", summaryStocktakingDetailId)
                .orderByAsc("id");
        return drugStocktakingDetailMapper.selectList(queryWrapper);
    }
}
