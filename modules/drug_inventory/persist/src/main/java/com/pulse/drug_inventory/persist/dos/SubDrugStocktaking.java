package com.pulse.drug_inventory.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.drug_inventory.common.enums.SubStocktakingStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "sub_drug_stocktaking", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "e3ca33b5-e5d1-35c5-bed0-6b8229d63435|ENTITY|DEFINITION")
public class SubDrugStocktaking {
    @AutoGenerated(locked = true, uuid = "2a14c982-ab33-38e0-9950-9944142c5d4e")
    @TableField(value = "batch_flag")
    private Boolean batchFlag;

    @AutoGenerated(locked = true, uuid = "ed595672-9105-3110-a806-80084023a029")
    @TableField(value = "create_staff_id")
    private String createStaffId;

    @AutoGenerated(locked = true, uuid = "d979b48c-cd26-3589-8d8c-f1af959f3554")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "410c7062-7f73-3af5-b3e3-cd59b817099c")
    @TableField(value = "drug_stocktaking_id")
    private String drugStocktakingId;

    @AutoGenerated(locked = true, uuid = "047a50cf-2172-3761-a37f-2a9b6cb0c025")
    @TableField(value = "export_import_code")
    private String exportImportCode;

    @AutoGenerated(locked = true, uuid = "c4095a13-d318-3df6-9080-341bbadd1c4f")
    @TableField(value = "herb_type")
    private String herbType;

    @AutoGenerated(locked = true, uuid = "68db0040-b876-3480-b91c-585dcf850fe5")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "a4aa7683-c0ac-4dc6-8e3c-29a55e2745c7")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "30535385-f1f4-3bf5-893f-2126b0eb6ecf")
    @TableField(value = "remark")
    private String remark;

    @AutoGenerated(locked = true, uuid = "644e4e73-fb85-43b9-860d-90c50f06b19d")
    @TableField(value = "status")
    private SubStocktakingStatusEnum status;

    @AutoGenerated(locked = true, uuid = "88d7c159-e866-33ae-935c-e8d05aae879e")
    @TableField(value = "stocktaking_code")
    private String stocktakingCode;

    @AutoGenerated(locked = true, uuid = "8a901c62-29e3-3336-88be-f9d714dd1819")
    @TableField(value = "storage_code")
    private String storageCode;

    @AutoGenerated(locked = true, uuid = "dcdc15f4-28bb-350d-8d53-6f3c0ae84564")
    @TableField(value = "sub_stocktaking_number")
    private String subStocktakingNumber;

    @AutoGenerated(locked = true, uuid = "32d92d8b-71c6-35cb-8b2c-bc9c86baf749")
    @TableField(value = "submit_date_time")
    private Date submitDateTime;

    @AutoGenerated(locked = true, uuid = "f3e844b2-ed39-3ce7-9c3e-f56210eed671")
    @TableField(value = "submit_staff_id")
    private String submitStaffId;

    @AutoGenerated(locked = true, uuid = "804f5ab0-2e01-38ac-8705-************")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
