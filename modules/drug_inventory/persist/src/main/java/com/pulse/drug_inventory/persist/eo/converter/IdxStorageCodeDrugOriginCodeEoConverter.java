package com.pulse.drug_inventory.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_inventory.persist.eo.IdxStorageCodeDrugOriginCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for IdxStorageCodeDrugOriginCodeEo */
@Converter
@AutoGenerated(locked = true, uuid = "ccf81580-ed4d-32ee-99bc-c16603e00af5")
public class IdxStorageCodeDrugOriginCodeEoConverter
        implements AttributeConverter<IdxStorageCodeDrugOriginCodeEo, String> {

    /** convert DB column to IdxStorageCodeDrugOriginCodeEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            IdxStorageCodeDrugOriginCodeEo idxStorageCodeDrugOriginCodeEo) {
        if (idxStorageCodeDrugOriginCodeEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(idxStorageCodeDrugOriginCodeEo);
        }
    }

    /** convert DB column to IdxStorageCodeDrugOriginCodeEo */
    @AutoGenerated(locked = true)
    public IdxStorageCodeDrugOriginCodeEo convertToEntityAttribute(
            String idxStorageCodeDrugOriginCodeEoJson) {
        if (StrUtil.isEmpty(idxStorageCodeDrugOriginCodeEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    idxStorageCodeDrugOriginCodeEoJson,
                    new TypeReference<IdxStorageCodeDrugOriginCodeEo>() {});
        }
    }
}
