package com.pulse.drug_inventory.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_inventory.persist.eo.IdxDrugOriginCodeStorageCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for IdxDrugOriginCodeStorageCodeEo */
@Converter
@AutoGenerated(locked = true, uuid = "81818291-fa14-3667-a4a1-5d30d0a4141d")
public class IdxDrugOriginCodeStorageCodeEoConverter
        implements AttributeConverter<IdxDrugOriginCodeStorageCodeEo, String> {

    /** convert DB column to IdxDrugOriginCodeStorageCodeEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            IdxDrugOriginCodeStorageCodeEo idxDrugOriginCodeStorageCodeEo) {
        if (idxDrugOriginCodeStorageCodeEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(idxDrugOriginCodeStorageCodeEo);
        }
    }

    /** convert DB column to IdxDrugOriginCodeStorageCodeEo */
    @AutoGenerated(locked = true)
    public IdxDrugOriginCodeStorageCodeEo convertToEntityAttribute(
            String idxDrugOriginCodeStorageCodeEoJson) {
        if (StrUtil.isEmpty(idxDrugOriginCodeStorageCodeEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    idxDrugOriginCodeStorageCodeEoJson,
                    new TypeReference<IdxDrugOriginCodeStorageCodeEo>() {});
        }
    }
}
