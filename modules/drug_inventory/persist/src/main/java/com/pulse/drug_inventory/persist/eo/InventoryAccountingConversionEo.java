package com.pulse.drug_inventory.persist.eo;

import com.pulse.pharmacy_warehouse_setting.common.enums.InventoryIncreaseReduceEnum;
import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/** */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "d5f69811-4b4a-477d-b2f8-472e8e68b1d2|EO|DEFINITION")
public class InventoryAccountingConversionEo {
    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "0b37285e-7913-4fe8-9402-f43ac9b3e13f")
    private String storageCode;

    /** 单据数量 */
    @AutoGenerated(locked = true, uuid = "b4206762-9e94-492e-80ac-d230fc2324b3")
    private BigDecimal documentAmount;

    /** 单据药品产地规格id */
    @AutoGenerated(locked = true, uuid = "f3c5b64a-64f6-4625-9e70-4ffcdb70b429")
    private String documentSpecId;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "883247ca-0c08-40f5-aa12-ac094714dcc9")
    private BigDecimal purchasePrice;

    /** 零售价 */
    @AutoGenerated(locked = true, uuid = "2144eebf-c951-4abe-a1c1-3f39a7df26f7")
    private BigDecimal retailPrice;

    /** 进价金额 */
    @AutoGenerated(locked = true, uuid = "5a23c554-4057-43fa-8d46-7fbf957649bd")
    private BigDecimal purchaseCost;

    /** 零售金额 */
    @AutoGenerated(locked = true, uuid = "7ee0b118-faca-442f-8612-0ada23bcd199")
    private BigDecimal retailCost;

    /** 红单标志 */
    @AutoGenerated(locked = true, uuid = "5985611a-a8c9-4610-a453-d2af77f7bc20")
    private Boolean redFlag;

    /** 库存增减类型 */
    @AutoGenerated(locked = true, uuid = "1e485ed8-93a1-4d4d-86f1-b066803e3b0e")
    private InventoryIncreaseReduceEnum inventoryIncreaseReduce;

    /** 批次库存id */
    @AutoGenerated(locked = true, uuid = "199547be-7aca-46fa-99c4-7f0fcfbd2ff3")
    private String batchInventoryId;

    /** 扣率 */
    @AutoGenerated(locked = true, uuid = "42fc043b-07cb-45c3-9e44-051ff15407a8")
    private String discount;

    /** 药品效期 */
    @AutoGenerated(locked = true, uuid = "5d1e4b9d-1d89-40f1-87b1-de024aea1351")
    private Date expirationDate;

    /** 批号 */
    @AutoGenerated(locked = true, uuid = "2ac3bcdb-3c74-4d97-a68f-d07de4e499a6")
    private String batchNumber;

    /** 单据id */
    @AutoGenerated(locked = true, uuid = "0f7a6482-8ba9-44dc-889c-421c77b17b90")
    private String documentId;

    /** 单据明细id */
    @AutoGenerated(locked = true, uuid = "3df31ea6-b927-4cdf-96e1-5d4447aeb800")
    private String documentDetailId;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "ffdfcb7e-32c5-4fc6-b657-4f8f6df787ff")
    private String remark;

    /** 会计期间id */
    @AutoGenerated(locked = true, uuid = "305608bc-f12b-44a2-98dc-507c71903b84")
    private String accountingId;

    /** 会计日历 */
    @AutoGenerated(locked = true, uuid = "657a0799-332b-4b4a-ac4d-6bf91f691f41")
    private String accountingCalendar;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "b0c20227-f859-430b-b89a-7de13f6cf851")
    private String drugOriginCode;

    /** gcp编码 */
    @AutoGenerated(locked = true, uuid = "c59aac50-efd7-4777-a0f4-67093f2ace00")
    private String gcpCode;
}
