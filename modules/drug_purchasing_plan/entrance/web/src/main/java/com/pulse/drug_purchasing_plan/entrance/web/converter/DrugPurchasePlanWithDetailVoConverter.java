package com.pulse.drug_purchasing_plan.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_purchasing_plan.entrance.web.query.assembler.DrugPurchasePlanWithDetailVoDataAssembler;
import com.pulse.drug_purchasing_plan.entrance.web.query.assembler.DrugPurchasePlanWithDetailVoDataAssembler.DrugPurchasePlanWithDetailVoDataHolder;
import com.pulse.drug_purchasing_plan.entrance.web.query.collector.DrugPurchasePlanWithDetailVoDataCollector;
import com.pulse.drug_purchasing_plan.entrance.web.vo.DrugPurchasePlanDetailVo;
import com.pulse.drug_purchasing_plan.entrance.web.vo.DrugPurchasePlanWithDetailVo;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanDetailDto;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanWithDetailDto;
import com.pulse.drug_purchasing_plan.service.DrugPurchasePlanBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugPurchasePlanWithDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "d15c95d9-e89d-42e9-ab15-9f4f8d85ea66|VO|CONVERTER")
public class DrugPurchasePlanWithDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugPurchasePlanBaseDtoService drugPurchasePlanBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPurchasePlanDetailVoConverter drugPurchasePlanDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPurchasePlanWithDetailVoDataAssembler drugPurchasePlanWithDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugPurchasePlanWithDetailVoDataCollector drugPurchasePlanWithDetailVoDataCollector;

    /** 使用默认方式组装DrugPurchasePlanWithDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "31eb45b0-d598-37bc-b864-91534b71a747")
    public List<DrugPurchasePlanWithDetailVo> convertAndAssembleDataList(
            List<DrugPurchasePlanWithDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugPurchasePlanWithDetailVoDataHolder dataHolder =
                new DrugPurchasePlanWithDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugPurchasePlanBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(DrugPurchasePlanWithDetailDto::getId)
                                .collect(Collectors.toList())));
        Map<String, DrugPurchasePlanWithDetailVo> voMap =
                convertToDrugPurchasePlanWithDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugPurchasePlanWithDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        drugPurchasePlanWithDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugPurchasePlanWithDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "40929a5d-8a75-3265-a72c-3e036a49ce1d")
    public DrugPurchasePlanWithDetailVo convertAndAssembleData(DrugPurchasePlanWithDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugPurchasePlanWithDetailDto转换成DrugPurchasePlanWithDetailVo */
    @AutoGenerated(locked = true, uuid = "9856af34-1e40-3805-adfe-e9a4548be183")
    public DrugPurchasePlanWithDetailVo convertToDrugPurchasePlanWithDetailVo(
            DrugPurchasePlanWithDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugPurchasePlanWithDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugPurchasePlanWithDetailDto转换成DrugPurchasePlanWithDetailVo */
    @AutoGenerated(locked = false, uuid = "d15c95d9-e89d-42e9-ab15-9f4f8d85ea66-converter-Map")
    public Map<DrugPurchasePlanWithDetailDto, DrugPurchasePlanWithDetailVo>
            convertToDrugPurchasePlanWithDetailVoMap(List<DrugPurchasePlanWithDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugPurchasePlanDetailDto, DrugPurchasePlanDetailVo> drugPurchasePlanDetailListMap =
                drugPurchasePlanDetailVoConverter.convertToDrugPurchasePlanDetailVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getDrugPurchasePlanDetailList()))
                                .flatMap(dto -> dto.getDrugPurchasePlanDetailList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugPurchasePlanWithDetailDto, DrugPurchasePlanWithDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugPurchasePlanWithDetailVo vo =
                                                    new DrugPurchasePlanWithDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setAuditStaffId(dto.getAuditStaffId());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setAuditDateTime(dto.getAuditDateTime());
                                            vo.setAuditStatus(dto.getAuditStatus());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDeliveryAddress(dto.getDeliveryAddress());
                                            vo.setDeliveryDrugStorageId(
                                                    dto.getDeliveryDrugStorageId());
                                            vo.setHerbType(dto.getHerbType());
                                            vo.setPerformStatus(dto.getPerformStatus());
                                            vo.setPlanType(dto.getPlanType());
                                            vo.setPurchaseAccount(dto.getPurchaseAccount());
                                            vo.setPurchasePlanNumber(dto.getPurchasePlanNumber());
                                            vo.setPurchasePlatformType(
                                                    dto.getPurchasePlatformType());
                                            vo.setRemark(dto.getRemark());
                                            vo.setRetailCost(dto.getRetailCost());
                                            vo.setStartDateTime(dto.getStartDateTime());
                                            vo.setSubmitDateTime(dto.getSubmitDateTime());
                                            vo.setDrugPurchasePlanDetailList(
                                                    dto.getDrugPurchasePlanDetailList() == null
                                                            ? null
                                                            : dto
                                                                    .getDrugPurchasePlanDetailList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    drugPurchasePlanDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugPurchasePlanWithDetailDto转换成DrugPurchasePlanWithDetailVo */
    @AutoGenerated(locked = true, uuid = "d15c95d9-e89d-42e9-ab15-9f4f8d85ea66-converter-list")
    public List<DrugPurchasePlanWithDetailVo> convertToDrugPurchasePlanWithDetailVoList(
            List<DrugPurchasePlanWithDetailDto> dtoList) {
        return new ArrayList<>(convertToDrugPurchasePlanWithDetailVoMap(dtoList).values());
    }
}
