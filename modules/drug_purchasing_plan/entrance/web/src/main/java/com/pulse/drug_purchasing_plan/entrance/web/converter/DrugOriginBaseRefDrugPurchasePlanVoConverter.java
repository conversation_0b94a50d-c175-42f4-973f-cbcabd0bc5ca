package com.pulse.drug_purchasing_plan.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_purchasing_plan.entrance.web.query.assembler.DrugOriginBaseRefDrugPurchasePlanVoDataAssembler;
import com.pulse.drug_purchasing_plan.entrance.web.vo.DrugOriginBaseRefDrugPurchasePlanVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugOriginBaseRefDrugPurchasePlanVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "18a4a00a-de44-4786-b396-876c141a9caf|VO|CONVERTER")
public class DrugOriginBaseRefDrugPurchasePlanVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseRefDrugPurchasePlanVoDataAssembler
            drugOriginBaseRefDrugPurchasePlanVoDataAssembler;

    /** 把DrugOriginBaseDto转换成DrugOriginBaseRefDrugPurchasePlanVo */
    @AutoGenerated(locked = false, uuid = "18a4a00a-de44-4786-b396-876c141a9caf-converter-Map")
    public Map<DrugOriginBaseDto, DrugOriginBaseRefDrugPurchasePlanVo>
            convertToDrugOriginBaseRefDrugPurchasePlanVoMap(List<DrugOriginBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginBaseDto, DrugOriginBaseRefDrugPurchasePlanVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginBaseRefDrugPurchasePlanVo vo =
                                                    new DrugOriginBaseRefDrugPurchasePlanVo();
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugOriginName(dto.getDrugOriginName());
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugSpecificationId(dto.getDrugSpecificationId());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setPackageSpecification(
                                                    dto.getPackageSpecification());
                                            vo.setPackageUnit(dto.getPackageUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            vo.setCentralPurchaseFlag(dto.getCentralPurchaseFlag());
                                            vo.setGmpFlag(dto.getGmpFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginBaseDto转换成DrugOriginBaseRefDrugPurchasePlanVo */
    @AutoGenerated(locked = true, uuid = "18a4a00a-de44-4786-b396-876c141a9caf-converter-list")
    public List<DrugOriginBaseRefDrugPurchasePlanVo>
            convertToDrugOriginBaseRefDrugPurchasePlanVoList(List<DrugOriginBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginBaseRefDrugPurchasePlanVoMap(dtoList).values());
    }

    /** 把DrugOriginBaseDto转换成DrugOriginBaseRefDrugPurchasePlanVo */
    @AutoGenerated(locked = true, uuid = "*************-3b1c-9618-ae90bfd3347b")
    public DrugOriginBaseRefDrugPurchasePlanVo convertToDrugOriginBaseRefDrugPurchasePlanVo(
            DrugOriginBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginBaseRefDrugPurchasePlanVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugOriginBaseRefDrugPurchasePlanVo列表数据 */
    @AutoGenerated(locked = true, uuid = "972b2c6b-e9f9-3367-b940-4a63eac715c9")
    public List<DrugOriginBaseRefDrugPurchasePlanVo> convertAndAssembleDataList(
            List<DrugOriginBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugOriginBaseRefDrugPurchasePlanVo> voMap =
                convertToDrugOriginBaseRefDrugPurchasePlanVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getDrugOriginCode(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugOriginBaseRefDrugPurchasePlanVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getDrugOriginCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugOriginBaseRefDrugPurchasePlanVo数据 */
    @AutoGenerated(locked = true, uuid = "eb0314d6-7e28-37f2-beb2-47efb003a495")
    public DrugOriginBaseRefDrugPurchasePlanVo convertAndAssembleData(DrugOriginBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
