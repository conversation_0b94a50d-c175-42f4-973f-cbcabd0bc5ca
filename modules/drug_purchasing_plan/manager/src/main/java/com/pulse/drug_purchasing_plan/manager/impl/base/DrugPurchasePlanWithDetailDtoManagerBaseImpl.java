package com.pulse.drug_purchasing_plan.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.drug_purchasing_plan.manager.DrugPurchasePlanBaseDtoManager;
import com.pulse.drug_purchasing_plan.manager.DrugPurchasePlanDetailDtoManager;
import com.pulse.drug_purchasing_plan.manager.DrugPurchasePlanWithDetailDtoManager;
import com.pulse.drug_purchasing_plan.manager.converter.DrugPurchasePlanBaseDtoConverter;
import com.pulse.drug_purchasing_plan.manager.converter.DrugPurchasePlanWithDetailDtoConverter;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanBaseDto;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanDetailDto;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanWithDetailDto;
import com.pulse.drug_purchasing_plan.persist.dos.DrugPurchasePlan;
import com.pulse.drug_purchasing_plan.persist.mapper.DrugPurchasePlanDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "a56de036-3ac9-4626-a1e2-391f8a053573|DTO|BASE_MANAGER_IMPL")
public abstract class DrugPurchasePlanWithDetailDtoManagerBaseImpl
        implements DrugPurchasePlanWithDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanBaseDtoConverter drugPurchasePlanBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanBaseDtoManager drugPurchasePlanBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanDao drugPurchasePlanDao;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanDetailDtoManager drugPurchasePlanDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanWithDetailDtoConverter drugPurchasePlanWithDetailDtoConverter;

    @AutoGenerated(locked = true, uuid = "44411142-805c-3fab-80e7-0ac8741454ee")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByCreatedBy(String createdBy) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchasePlanWithDetailDto> drugPurchasePlanWithDetailDtoList =
                getByCreatedBys(Arrays.asList(createdBy));
        return drugPurchasePlanWithDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5a79ce50-ed72-3716-ad4d-9adcc42d35d9")
    public List<DrugPurchasePlanWithDetailDto>
            doConvertFromDrugPurchasePlanToDrugPurchasePlanWithDetailDto(
                    List<DrugPurchasePlan> drugPurchasePlanList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugPurchasePlanList)) {
            return Collections.emptyList();
        }

        List<DrugPurchasePlanDetailDto> drugPurchasePlanDetailDtoList =
                drugPurchasePlanDetailDtoManager.getByDrugPurchasePlanIds(
                        drugPurchasePlanList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<DrugPurchasePlanDetailDto>> idDrugPurchasePlanDetailDtoListMap =
                drugPurchasePlanDetailDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getDrugPurchasePlanId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<DrugPurchasePlanBaseDto> baseDtoList =
                drugPurchasePlanBaseDtoConverter
                        .convertFromDrugPurchasePlanToDrugPurchasePlanBaseDto(drugPurchasePlanList);
        Map<String, DrugPurchasePlanWithDetailDto> dtoMap =
                drugPurchasePlanWithDetailDtoConverter
                        .convertFromDrugPurchasePlanBaseDtoToDrugPurchasePlanWithDetailDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugPurchasePlanWithDetailDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugPurchasePlanWithDetailDto> drugPurchasePlanWithDetailDtoList = new ArrayList<>();
        for (DrugPurchasePlan i : drugPurchasePlanList) {
            DrugPurchasePlanWithDetailDto drugPurchasePlanWithDetailDto = dtoMap.get(i.getId());
            if (drugPurchasePlanWithDetailDto == null) {
                continue;
            }

            if (null != i.getId()) {
                drugPurchasePlanWithDetailDto.setDrugPurchasePlanDetailList(
                        idDrugPurchasePlanDetailDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugPurchasePlanWithDetailDtoList.add(drugPurchasePlanWithDetailDto);
        }
        return drugPurchasePlanWithDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "6432f46d-b065-3ff7-8981-350d5d1bdf85")
    @Override
    public DrugPurchasePlanWithDetailDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchasePlanWithDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugPurchasePlanWithDetailDto drugPurchasePlanWithDetailDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugPurchasePlanWithDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "79b9842f-1408-3cdb-a561-e0dfd2a7d0b4")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugPurchasePlan> drugPurchasePlanList = drugPurchasePlanDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugPurchasePlanList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugPurchasePlan> drugPurchasePlanMap =
                drugPurchasePlanList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugPurchasePlanList =
                id.stream()
                        .map(i -> drugPurchasePlanMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugPurchasePlanToDrugPurchasePlanWithDetailDto(drugPurchasePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "92b9999d-8f27-3398-996f-897341775b86")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByCreatedBys(List<String> createdBy) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(createdBy)) {
            return Collections.emptyList();
        }

        List<DrugPurchasePlan> drugPurchasePlanList =
                drugPurchasePlanDao.getByCreatedBys(createdBy);
        if (CollectionUtil.isEmpty(drugPurchasePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPurchasePlanToDrugPurchasePlanWithDetailDto(drugPurchasePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "977b253f-824a-3a7e-a2b2-fcd68cc6e7a8")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByDeliveryDrugStorageId(
            String deliveryDrugStorageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchasePlanWithDetailDto> drugPurchasePlanWithDetailDtoList =
                getByDeliveryDrugStorageIds(Arrays.asList(deliveryDrugStorageId));
        return drugPurchasePlanWithDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b17086a6-c08f-34ab-aa7f-af500da927cb")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByStorageCode(String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchasePlanWithDetailDto> drugPurchasePlanWithDetailDtoList =
                getByStorageCodes(Arrays.asList(storageCode));
        return drugPurchasePlanWithDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cf035344-7a3b-3c5b-8531-194f23eea722")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByAuditStaffId(String auditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchasePlanWithDetailDto> drugPurchasePlanWithDetailDtoList =
                getByAuditStaffIds(Arrays.asList(auditStaffId));
        return drugPurchasePlanWithDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f9a1457c-cc07-3c3b-804b-4ab0c3601c4d")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByStorageCodes(List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(storageCode)) {
            return Collections.emptyList();
        }

        List<DrugPurchasePlan> drugPurchasePlanList =
                drugPurchasePlanDao.getByStorageCodes(storageCode);
        if (CollectionUtil.isEmpty(drugPurchasePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPurchasePlanToDrugPurchasePlanWithDetailDto(drugPurchasePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "fe758d39-8084-3aa5-8f2a-4487832186e4")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByDeliveryDrugStorageIds(
            List<String> deliveryDrugStorageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(deliveryDrugStorageId)) {
            return Collections.emptyList();
        }

        List<DrugPurchasePlan> drugPurchasePlanList =
                drugPurchasePlanDao.getByDeliveryDrugStorageIds(deliveryDrugStorageId);
        if (CollectionUtil.isEmpty(drugPurchasePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPurchasePlanToDrugPurchasePlanWithDetailDto(drugPurchasePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ff051c45-c11d-37a3-b551-1a88a1c54e3e")
    @Override
    public List<DrugPurchasePlanWithDetailDto> getByAuditStaffIds(List<String> auditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(auditStaffId)) {
            return Collections.emptyList();
        }

        List<DrugPurchasePlan> drugPurchasePlanList =
                drugPurchasePlanDao.getByAuditStaffIds(auditStaffId);
        if (CollectionUtil.isEmpty(drugPurchasePlanList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPurchasePlanToDrugPurchasePlanWithDetailDto(drugPurchasePlanList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
