package com.pulse.drug_purchasing_plan.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.drug_purchasing_plan.common.enums.PurchaseAuditTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchasePlatformTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchaseTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "drug_purchase_plan", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "7fad69bc-255d-301b-895d-16a9c81f6555|ENTITY|DEFINITION")
public class DrugPurchasePlan {
    @AutoGenerated(locked = true, uuid = "9363ed22-aa0b-3225-8c7a-2baa034ee19f")
    @TableField(value = "audit_date_time")
    private Date auditDateTime;

    @AutoGenerated(locked = true, uuid = "fabb74cf-be51-3e2d-b88a-2e5ce4d5fdfa")
    @TableField(value = "audit_staff_id")
    private String auditStaffId;

    /** 已保存、已提交、已审核 、已作废 */
    @AutoGenerated(locked = true, uuid = "6cab9a4a-301f-3a66-ac1e-0def9b3bb6d7")
    @TableField(value = "audit_status")
    private PurchaseAuditTypeEnum auditStatus;

    @AutoGenerated(locked = true, uuid = "f57944ad-b0e1-378e-9f27-f81c7a21601b")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "eff3bbf5-94a9-36de-b4e8-e871426a47bb")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "b6c51cd4-c79c-38f2-8f79-88ff43130a97")
    @TableField(value = "delivery_address")
    private String deliveryAddress;

    @AutoGenerated(locked = true, uuid = "bfd04831-1bf7-38c1-b8aa-554dbd4acc7c")
    @TableField(value = "delivery_drug_storage_id")
    private String deliveryDrugStorageId;

    @AutoGenerated(locked = true, uuid = "bf85974c-5232-3c33-897a-d8ebeb69376b")
    @TableField(value = "end_date_time")
    private Date endDateTime;

    @AutoGenerated(locked = true, uuid = "5e93c7f5-173f-3989-9b86-10c4f4723f09")
    @TableField(value = "herb_type")
    private String herbType;

    @AutoGenerated(locked = true, uuid = "cd11f4cc-804d-3616-a3d7-02a25ed8fcbe")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "690f461b-db45-45f4-a121-912e594322c8")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "fbfeacc8-455a-4271-81be-7fbe1deb780f")
    @TableField(value = "perform_status")
    private Boolean performStatus;

    /** 药械平台采购、普通采购 、中草药采购 */
    @AutoGenerated(locked = true, uuid = "619e1c43-265d-3d84-85b4-3b08d11a8663")
    @TableField(value = "plan_type")
    private PurchaseTypeEnum planType;

    @AutoGenerated(locked = true, uuid = "6bc106a5-e069-3df0-91fd-bb662be6df29")
    @TableField(value = "purchase_account")
    private BigDecimal purchaseAccount;

    @AutoGenerated(locked = true, uuid = "7b84dcbb-cb4e-3d67-8391-0c6b4696f3de")
    @TableField(value = "purchase_plan_number")
    private String purchasePlanNumber;

    /** 正常订单、急救药品临时订单 */
    @AutoGenerated(locked = true, uuid = "bd90e9ec-4753-3776-aab8-11e32b963702")
    @TableField(value = "purchase_platform_type")
    private PurchasePlatformTypeEnum purchasePlatformType;

    @AutoGenerated(locked = true, uuid = "8202a77e-ede2-357e-b772-14041551945a")
    @TableField(value = "remark")
    private String remark;

    @AutoGenerated(locked = true, uuid = "ec9f779c-a23c-431e-8c89-a19d3ec13d27")
    @TableField(value = "retail_cost")
    private BigDecimal retailCost;

    /** 计算消耗量的时间范围 */
    @AutoGenerated(locked = true, uuid = "fdde26e9-5e68-3ec4-9ab1-40217d55919c")
    @TableField(value = "start_date_time")
    private Date startDateTime;

    @AutoGenerated(locked = true, uuid = "de5549d9-89d2-3f0b-ac67-1e4ea36cad96")
    @TableField(value = "storage_code")
    private String storageCode;

    @AutoGenerated(locked = true, uuid = "6c7587d7-f765-3bb1-81f3-2fd71813cf23")
    @TableField(value = "submit_date_time")
    private Date submitDateTime;

    @AutoGenerated(locked = true, uuid = "655c3807-ba18-325c-91b3-0c2154133dd4")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "9e47122b-2add-4d01-b272-d94ab0743430")
    @TableField(value = "updated_by")
    private String updatedBy;
}
