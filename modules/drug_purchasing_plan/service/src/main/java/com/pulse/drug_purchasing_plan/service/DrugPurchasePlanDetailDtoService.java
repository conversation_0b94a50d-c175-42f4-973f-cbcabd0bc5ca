package com.pulse.drug_purchasing_plan.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_purchasing_plan.manager.DrugPurchasePlanDetailDtoManager;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanDetailDto;
import com.pulse.drug_purchasing_plan.service.converter.DrugPurchasePlanDetailDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "074292a8-4de6-4bad-bfae-541f4f459fc3|DTO|SERVICE")
public class DrugPurchasePlanDetailDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanDetailDtoManager drugPurchasePlanDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchasePlanDetailDtoServiceConverter drugPurchasePlanDetailDtoServiceConverter;

    @PublicInterface(id = "b3f0dc7d-48c6-4a58-a677-b1baa3a3dc9a", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "031726aa-bcc0-33ff-b1c9-2e41e441544a")
    public List<DrugPurchasePlanDetailDto> getByDrugOriginCode(
            @NotNull(message = "药品产地编码不能为空") String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "161061a1-4d9b-4f75-b6b0-749d6213f766", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "104710c5-1b0d-3607-889c-3a73fefe4853")
    public DrugPurchasePlanDetailDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchasePlanDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "fef8db0b-8dc5-4efe-8803-956fd99abbf3", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "3a75484f-f470-338e-ac5f-ab7dd884fc17")
    public List<DrugPurchasePlanDetailDto> getBySupplierIds(
            @Valid @NotNull(message = "供货商id不能为空") List<String> supplierId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        supplierId = new ArrayList<>(new HashSet<>(supplierId));
        List<DrugPurchasePlanDetailDto> drugPurchasePlanDetailDtoList =
                drugPurchasePlanDetailDtoManager.getBySupplierIds(supplierId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchasePlanDetailDtoServiceConverter.DrugPurchasePlanDetailDtoConverter(
                drugPurchasePlanDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c279b4d5-84eb-4919-9252-600a6a192cde", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "48039cf6-664c-31b0-8a21-c2007c8fd0f7")
    public List<DrugPurchasePlanDetailDto> getByDrugPurchasePlanId(
            @NotNull(message = "采购计划主记录ID不能为空") String drugPurchasePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugPurchasePlanIds(Arrays.asList(drugPurchasePlanId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "cd91b7ad-c7e0-4823-8b31-81e96eac9247", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "4bf06299-66a6-3ac8-b04b-020baaf14092")
    public List<DrugPurchasePlanDetailDto> getByDrugOriginCodes(
            @Valid @NotNull(message = "药品产地编码不能为空") List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginCode = new ArrayList<>(new HashSet<>(drugOriginCode));
        List<DrugPurchasePlanDetailDto> drugPurchasePlanDetailDtoList =
                drugPurchasePlanDetailDtoManager.getByDrugOriginCodes(drugOriginCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchasePlanDetailDtoServiceConverter.DrugPurchasePlanDetailDtoConverter(
                drugPurchasePlanDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "5e829f43-c681-454e-950e-cc226db76e3c", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "76aa6ea5-d3cd-316b-98ec-0a88e274e90a")
    public List<DrugPurchasePlanDetailDto> getByDrugOriginSpecificationIds(
            @Valid @NotNull(message = "药品产地规格id不能为空") List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginSpecificationId = new ArrayList<>(new HashSet<>(drugOriginSpecificationId));
        List<DrugPurchasePlanDetailDto> drugPurchasePlanDetailDtoList =
                drugPurchasePlanDetailDtoManager.getByDrugOriginSpecificationIds(
                        drugOriginSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchasePlanDetailDtoServiceConverter.DrugPurchasePlanDetailDtoConverter(
                drugPurchasePlanDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c3b7f01e-628c-437f-9287-84671700aa0a", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "a34d97cb-e215-3226-8e12-988bdab94a0b")
    public List<DrugPurchasePlanDetailDto> getByDrugPurchasePlanIds(
            @Valid @NotNull(message = "采购计划主记录ID不能为空") List<String> drugPurchasePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugPurchasePlanId = new ArrayList<>(new HashSet<>(drugPurchasePlanId));
        List<DrugPurchasePlanDetailDto> drugPurchasePlanDetailDtoList =
                drugPurchasePlanDetailDtoManager.getByDrugPurchasePlanIds(drugPurchasePlanId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchasePlanDetailDtoServiceConverter.DrugPurchasePlanDetailDtoConverter(
                drugPurchasePlanDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "26d9953b-a7f9-4ffd-a3c5-726d7f569913", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "d657bc1e-16f7-3e50-ba6e-ee94c66ee57d")
    public List<DrugPurchasePlanDetailDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugPurchasePlanDetailDto> drugPurchasePlanDetailDtoList =
                drugPurchasePlanDetailDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchasePlanDetailDtoServiceConverter.DrugPurchasePlanDetailDtoConverter(
                drugPurchasePlanDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "66689694-d801-4959-8ffd-c1ef315cd619", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "deb465e4-2e04-34fe-b656-562c7f60cdbc")
    public List<DrugPurchasePlanDetailDto> getByDrugOriginSpecificationId(
            @NotNull(message = "药品产地规格id不能为空") String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "45a4b529-0bfa-45eb-b3a5-09860bde56fb", module = "drug_purchasing_plan")
    @AutoGenerated(locked = false, uuid = "e2d3f0d7-bff2-345a-be5b-6f660f82ea95")
    public List<DrugPurchasePlanDetailDto> getBySupplierId(
            @NotNull(message = "供货商id不能为空") String supplierId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySupplierIds(Arrays.asList(supplierId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
