package com.pulse.drug_purchasing_plan.service.converter;

import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchasePlanBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "df261542-5a6f-3bf4-81cf-995a80f2b617")
public class DrugPurchasePlanBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugPurchasePlanBaseDto> DrugPurchasePlanBaseDtoConverter(
            List<DrugPurchasePlanBaseDto> drugPurchasePlanBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugPurchasePlanBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
