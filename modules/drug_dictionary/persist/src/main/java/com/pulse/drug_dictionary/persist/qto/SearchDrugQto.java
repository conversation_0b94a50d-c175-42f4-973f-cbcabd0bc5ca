package com.pulse.drug_dictionary.persist.qto;

import com.pulse.drug_dictionary.common.enums.SourceTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "dd1ee449-bbc6-4633-b472-557b4ee9ec16|QTO|DEFINITION")
public class SearchDrugQto {
    /** 药品分类ID drug_dictionary.drug_catalog_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "60501803-7a11-427d-b732-ac66a87df04f")
    private List<String> drugCatalogIn;

    /** 药品编码 drug_dictionary.drug_code */
    @AutoGenerated(locked = true, uuid = "40d7e822-dd06-4d4d-9970-e25659c7d65c")
    private String drugCodeIs;

    @AutoGenerated(locked = true, uuid = "c863c85a-ee86-4b15-b80c-86f8b7b0abd1")
    private Integer from;

    /** 输入码 drug_dictionary.input_code */
    @AutoGenerated(locked = true, uuid = "51aaf4c4-e2d3-4a79-af6e-1fd9ad037ea7")
    private String inputCodeCustomLike;

    /** 药品名称 drug_dictionary.drug_name */
    @AutoGenerated(locked = true, uuid = "3aeb9f4c-4186-4f6c-b76c-b636dff6282c")
    private String inputCodeLike;

    /** 输入码 drug_dictionary.input_code */
    @AutoGenerated(locked = true, uuid = "1762c834-5ca4-4c43-a0bd-0fb9b3475145")
    private String inputCodePinyinLike;

    /** 输入码 drug_dictionary.input_code */
    @AutoGenerated(locked = true, uuid = "4580c53e-ef32-43fa-8f8a-c09955e4ca15")
    private String inputCodeWubiLike;

    /** 作废标识 drug_dictionary.invalid_flag */
    @AutoGenerated(locked = true, uuid = "91bdd154-615c-4494-a185-9b4a7c0a91c8")
    private Boolean invalidFlagIs;

    @AutoGenerated(locked = true, uuid = "a0f8c234-7a28-4105-b027-7021e614a8f6")
    private String scrollId;

    @AutoGenerated(locked = true, uuid = "9e4ec506-4b68-444f-a6fc-4a84ac6a44f0")
    private Integer size;

    /** 来源 drug_dictionary.source_type */
    @Valid
    @AutoGenerated(locked = true, uuid = "b797f7ea-d8b7-4274-8738-10de11f1734e")
    private List<SourceTypeEnum> sourceTypeIn;
}
