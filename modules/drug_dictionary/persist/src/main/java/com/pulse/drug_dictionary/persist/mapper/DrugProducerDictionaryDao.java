package com.pulse.drug_dictionary.persist.mapper;

import com.pulse.drug_dictionary.persist.dos.DrugProducerDictionary;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "47b4a082-6e1a-331e-bf67-77a5299e7084|ENTITY|IDAO")
public interface DrugProducerDictionaryDao {

    @AutoGenerated(locked = true, uuid = "1810fc1a-a742-3231-8db3-2ab961b5d5ee")
    DrugProducerDictionary getById(String id);

    @AutoGenerated(locked = true, uuid = "d10ec4ff-03e4-3bae-9d3e-bf32631af9ae")
    List<DrugProducerDictionary> getByIds(List<String> id);
}
