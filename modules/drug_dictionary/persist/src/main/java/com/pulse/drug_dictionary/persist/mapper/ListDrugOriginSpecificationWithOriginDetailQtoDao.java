package com.pulse.drug_dictionary.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.persist.qto.ListDrugOriginSpecificationWithOriginDetailQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "8437469c-c9c2-4e70-8168-d94d6760cd01|QTO|DAO")
public class ListDrugOriginSpecificationWithOriginDetailQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 药品产地规格详情信息 */
    @AutoGenerated(locked = false, uuid = "8437469c-c9c2-4e70-8168-d94d6760cd01-count")
    public Integer count(ListDrugOriginSpecificationWithOriginDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_origin_specification.id) FROM drug_origin_specification WHERE"
                    + " drug_origin_specification.id in #idIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getIdIn())) {
            conditionToRemove.add("#idIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification\"");
        softDeleteTableAlias.add("drug_origin_specification");
        softDeleteTableAlias.add("\"drugOriginCode_drugOriginExtension\"");
        softDeleteTableAlias.add("\"drugOriginCode\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugNameDictionaryList\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug_drugCatalog\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                        "#idIn",
                        CollectionUtil.isEmpty(qto.getIdIn())
                                ? "()"
                                : SqlUtil.buildInSqlPram(qto.getIdIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#idIn")) {
                sqlParams.addAll(qto.getIdIn());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 药品产地规格详情信息 */
    @AutoGenerated(locked = false, uuid = "8437469c-c9c2-4e70-8168-d94d6760cd01-query-all")
    public List<String> query(ListDrugOriginSpecificationWithOriginDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_origin_specification.id FROM drug_origin_specification WHERE"
                    + " drug_origin_specification.id in #idIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getIdIn())) {
            conditionToRemove.add("#idIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification\"");
        softDeleteTableAlias.add("drug_origin_specification");
        softDeleteTableAlias.add("\"drugOriginCode_drugOriginExtension\"");
        softDeleteTableAlias.add("\"drugOriginCode\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugNameDictionaryList\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug_drugCatalog\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                        "#idIn",
                        CollectionUtil.isEmpty(qto.getIdIn())
                                ? "()"
                                : SqlUtil.buildInSqlPram(qto.getIdIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#idIn")) {
                sqlParams.addAll(qto.getIdIn());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_origin_specification.id asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
