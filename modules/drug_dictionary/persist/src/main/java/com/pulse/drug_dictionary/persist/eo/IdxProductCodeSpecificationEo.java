package com.pulse.drug_dictionary.persist.eo;

import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "12618e11-6703-3433-bb54-235c65fbe36c|EO|DEFINITION")
public class IdxProductCodeSpecificationEo {
    @AutoGenerated(locked = true, uuid = "661c4bef-98e7-3cba-bcdc-01ecb944b2b0")
    private String drugOriginCode;

    @AutoGenerated(locked = true, uuid = "4cd3106f-d0af-35ba-9131-3b68bc78c99a")
    private SpecificationTypeEnum specificationType;

    @AutoGenerated(locked = true, uuid = "f306827d-28c7-373a-a310-61e34368b3f3")
    private String drugSpecification;

    @AutoGenerated(locked = true, uuid = "3014ccd7-15a0-3c9f-b9b0-de6ff94c2590")
    private String unit;

    @AutoGenerated(locked = true, uuid = "2d48148b-4790-3d46-b3d0-************")
    private Long amountPerPackage;
}
