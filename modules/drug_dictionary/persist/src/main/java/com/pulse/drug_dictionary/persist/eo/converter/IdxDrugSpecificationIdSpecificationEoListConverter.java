package com.pulse.drug_dictionary.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_dictionary.persist.eo.IdxDrugSpecificationIdSpecificationEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<IdxDrugSpecificationIdSpecificationEo> */
@Converter
@AutoGenerated(locked = true, uuid = "86d7f088-a917-3775-a2a8-09ac51b87ea5")
public class IdxDrugSpecificationIdSpecificationEoListConverter
        implements AttributeConverter<List<IdxDrugSpecificationIdSpecificationEo>, String> {

    /** convert List<IdxDrugSpecificationIdSpecificationEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            List<IdxDrugSpecificationIdSpecificationEo> idxDrugSpecificationIdSpecificationEoList) {
        if (idxDrugSpecificationIdSpecificationEoList == null
                || idxDrugSpecificationIdSpecificationEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(idxDrugSpecificationIdSpecificationEoList);
        }
    }

    /** convert DB column to List<IdxDrugSpecificationIdSpecificationEo> */
    @AutoGenerated(locked = true)
    public List<IdxDrugSpecificationIdSpecificationEo> convertToEntityAttribute(
            String idxDrugSpecificationIdSpecificationEoListJson) {
        if (StrUtil.isEmpty(idxDrugSpecificationIdSpecificationEoListJson)) {
            return new ArrayList<IdxDrugSpecificationIdSpecificationEo>();
        } else {
            return JsonUtils.readObject(
                    idxDrugSpecificationIdSpecificationEoListJson,
                    new TypeReference<List<IdxDrugSpecificationIdSpecificationEo>>() {});
        }
    }
}
