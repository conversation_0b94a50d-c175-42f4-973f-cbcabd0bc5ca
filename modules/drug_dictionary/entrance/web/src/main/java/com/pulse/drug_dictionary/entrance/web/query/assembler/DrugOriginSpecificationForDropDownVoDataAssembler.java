package com.pulse.drug_dictionary.entrance.web.query.assembler;

import com.pulse.drug_dictionary.entrance.web.vo.DrugCategorySimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryForDropDownVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryRefDrugLocationVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryRefDrugOriginInventoryVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugNameDictionarySimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginCampusDisableSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginExtensionSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginForDropDownVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginSpecificationForDropDownVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginSpecificationSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugProducerSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugSpecificationDictionaryForDropDownVo;
import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginCampusDisableBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_dictionary.service.DrugOriginSpecificationBaseDtoService;
import com.pulse.drug_inventory.manager.dto.DrugLocationBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.pulse.pulse.common.constants.DrugConstants;
import com.pulse.pulse.common.context.UserContext;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** DrugOriginSpecificationForDropDownVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "f7d52828-f551-3402-8b44-f4caa5789fb5")
public class DrugOriginSpecificationForDropDownVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoService drugOriginSpecificationBaseDtoService;

    /** 组装DrugOriginSpecificationForDropDownVo数据 */
    @AutoGenerated(locked = true, uuid = "2d744720-4c9c-39a6-ad90-cee1fb1e22a1")
    public void assembleData(
            Map<String, DrugOriginSpecificationForDropDownVo> voMap,
            DrugOriginSpecificationForDropDownVoDataAssembler
                            .DrugOriginSpecificationForDropDownVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<DrugOriginSpecificationBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<DrugOriginBaseDto, DrugOriginForDropDownVo>> drugOrigin =
                dataHolder.drugOrigin.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getDrugOriginCode(),
                                        dto -> Pair.of(dto, dataHolder.drugOrigin.get(dto)),
                                        (o1, o2) -> o1));

        for (DrugOriginSpecificationBaseDto baseDto : baseDtoList) {
            DrugOriginSpecificationForDropDownVo vo = voMap.get(baseDto.getId());
            vo.setDrugOrigin(
                    Optional.ofNullable(drugOrigin.get(baseDto.getDrugOriginCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDrugOriginData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 组装drugOrigin数据 */
    @AutoGenerated(locked = true, uuid = "489dd24c-2d3e-34d4-8962-f77684d5db09")
    private void assembleDrugOriginData(
            DrugOriginSpecificationForDropDownVoDataAssembler
                            .DrugOriginSpecificationForDropDownVoDataHolder
                    dataHolder) {
        Map<String, List<Pair<DrugOriginInventoryBaseDto, DrugDictionaryRefDrugOriginInventoryVo>>>
                drugOrigin2DrugOriginInventoryList =
                        dataHolder.drugOrigin2DrugOriginInventoryList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugOriginCode(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugOriginInventoryList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, Pair<DrugOriginExtensionBaseDto, DrugOriginExtensionSimpleVo>>
                drugOrigin2DrugOriginExtension =
                        dataHolder.drugOrigin2DrugOriginExtension.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getDrugOriginCode(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugOriginExtension
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<String, List<Pair<DrugOriginCampusDisableBaseDto, DrugOriginCampusDisableSimpleVo>>>
                drugOrigin2DrugOriginCampusDisableList =
                        dataHolder.drugOrigin2DrugOriginCampusDisableList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugOriginCode(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugOriginCampusDisableList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, List<Pair<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo>>>
                drugOrigin2DrugNameForDrugOriginList =
                        dataHolder.drugOrigin2DrugNameForDrugOriginList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugOriginCode(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugNameForDrugOriginList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, Pair<DrugProducerDictionaryBaseDto, DrugProducerSimpleVo>>
                drugOrigin2DrugProducer =
                        dataHolder.drugOrigin2DrugProducer.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.drugOrigin2DrugProducer
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<String, List<Pair<DrugLocationBaseDto, DrugDictionaryRefDrugLocationVo>>>
                drugOrigin2DrugLocationList =
                        dataHolder.drugOrigin2DrugLocationList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugOriginCode(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugLocationList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<
                        String,
                        Pair<
                                DrugSpecificationDictionaryBaseDto,
                                DrugSpecificationDictionaryForDropDownVo>>
                drugOrigin2DrugSpecification =
                        dataHolder.drugOrigin2DrugSpecification.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugOriginBaseDto, DrugOriginForDropDownVo> drugOrigin :
                dataHolder.drugOrigin.entrySet()) {
            DrugOriginBaseDto baseDto = drugOrigin.getKey();
            DrugOriginForDropDownVo vo = drugOrigin.getValue();
            vo.setDrugOriginInventoryList(
                    Optional.ofNullable(
                                    drugOrigin2DrugOriginInventoryList.get(
                                            baseDto.getDrugOriginCode()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugOriginExtension(
                    Optional.ofNullable(
                                    drugOrigin2DrugOriginExtension.get(baseDto.getDrugOriginCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugOriginCampusDisableList(
                    Optional.ofNullable(
                                    drugOrigin2DrugOriginCampusDisableList.get(
                                            baseDto.getDrugOriginCode()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugNameForDrugOriginList(
                    Optional.ofNullable(
                                    drugOrigin2DrugNameForDrugOriginList.get(
                                            baseDto.getDrugOriginCode()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugProducer(
                    Optional.ofNullable(drugOrigin2DrugProducer.get(baseDto.getDrugProducerId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugLocationList(
                    Optional.ofNullable(
                                    drugOrigin2DrugLocationList.get(baseDto.getDrugOriginCode()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugSpecification(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification.get(
                                            baseDto.getDrugSpecificationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
        assembleDrugOrigin2DrugOriginInventoryListData(dataHolder);
        assembleDrugOrigin2DrugSpecificationData(dataHolder);
    }

    /** 组装drugOrigin2DrugOriginInventoryList数据 */
    @AutoGenerated(locked = true, uuid = "c379ac69-a726-342c-b241-fd3808f58cab")
    private void assembleDrugOrigin2DrugOriginInventoryListData(
            DrugOriginSpecificationForDropDownVoDataAssembler
                            .DrugOriginSpecificationForDropDownVoDataHolder
                    dataHolder) {
        Map<String, Pair<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo>>
                drugOrigin2DrugOriginInventoryList2DrugOriginSpecification =
                        dataHolder
                                .drugOrigin2DrugOriginInventoryList2DrugOriginSpecification
                                .keySet()
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugOriginInventoryList2DrugOriginSpecification
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugOriginInventoryBaseDto, DrugDictionaryRefDrugOriginInventoryVo>
                drugOrigin2DrugOriginInventoryList :
                        dataHolder.drugOrigin2DrugOriginInventoryList.entrySet()) {
            DrugOriginInventoryBaseDto baseDto = drugOrigin2DrugOriginInventoryList.getKey();
            DrugDictionaryRefDrugOriginInventoryVo vo =
                    drugOrigin2DrugOriginInventoryList.getValue();
            vo.setDrugOriginSpecification(
                    Optional.ofNullable(
                                    drugOrigin2DrugOriginInventoryList2DrugOriginSpecification.get(
                                            baseDto.getDrugOriginSpecificationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 组装drugOrigin2DrugSpecification数据 */
    @AutoGenerated(locked = true, uuid = "d7132b5e-344b-30c8-8253-b6cf7650f192")
    private void assembleDrugOrigin2DrugSpecificationData(
            DrugOriginSpecificationForDropDownVoDataAssembler
                            .DrugOriginSpecificationForDropDownVoDataHolder
                    dataHolder) {
        Map<String, List<Pair<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo>>>
                drugOrigin2DrugSpecification2DrugNameForDrugSpecificationList =
                        dataHolder
                                .drugOrigin2DrugSpecification2DrugNameForDrugSpecificationList
                                .keySet()
                                .stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugSpecificationId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugSpecification2DrugNameForDrugSpecificationList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, Pair<DrugDictionaryBaseDto, DrugDictionaryForDropDownVo>>
                drugOrigin2DrugSpecification2DrugDictionary =
                        dataHolder.drugOrigin2DrugSpecification2DrugDictionary.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getDrugCode(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification2DrugDictionary
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryForDropDownVo>
                drugOrigin2DrugSpecification : dataHolder.drugOrigin2DrugSpecification.entrySet()) {
            DrugSpecificationDictionaryBaseDto baseDto = drugOrigin2DrugSpecification.getKey();
            DrugSpecificationDictionaryForDropDownVo vo = drugOrigin2DrugSpecification.getValue();
            vo.setDrugNameForDrugSpecificationList(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2DrugNameForDrugSpecificationList
                                            .get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugDictionary(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2DrugDictionary.get(
                                            baseDto.getDrugCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
        assembleDrugOrigin2DrugSpecification2DrugDictionaryData(dataHolder);
    }

    /** 组装drugOrigin2DrugSpecification2DrugDictionary数据 */
    @AutoGenerated(locked = true, uuid = "dc841fda-b5a1-322e-bb0a-756be8428b5f")
    private void assembleDrugOrigin2DrugSpecification2DrugDictionaryData(
            DrugOriginSpecificationForDropDownVoDataAssembler
                            .DrugOriginSpecificationForDropDownVoDataHolder
                    dataHolder) {
        Map<String, List<Pair<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo>>>
                drugOrigin2DrugSpecification2DrugDictionary2DrugNameForDrugList =
                        dataHolder
                                .drugOrigin2DrugSpecification2DrugDictionary2DrugNameForDrugList
                                .keySet()
                                .stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugCode(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugOrigin2DrugSpecification2DrugDictionary2DrugNameForDrugList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, Pair<DrugCategoryBaseDto, DrugCategorySimpleVo>>
                drugOrigin2DrugSpecification2DrugDictionary2DrugCatalog =
                        dataHolder
                                .drugOrigin2DrugSpecification2DrugDictionary2DrugCatalog
                                .keySet()
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification2DrugDictionary2DrugCatalog
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugDictionaryBaseDto, DrugDictionaryForDropDownVo>
                drugOrigin2DrugSpecification2DrugDictionary :
                        dataHolder.drugOrigin2DrugSpecification2DrugDictionary.entrySet()) {
            DrugDictionaryBaseDto baseDto = drugOrigin2DrugSpecification2DrugDictionary.getKey();
            DrugDictionaryForDropDownVo vo = drugOrigin2DrugSpecification2DrugDictionary.getValue();
            vo.setDrugNameForDrugList(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2DrugDictionary2DrugNameForDrugList
                                            .get(baseDto.getDrugCode()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugCatalog(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2DrugDictionary2DrugCatalog.get(
                                            baseDto.getDrugCatalogId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 批量自定义组装DrugOriginSpecificationForDropDownVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "fcb8f994-73f7-3d03-af93-2652cd2f1b67")
    public void assembleDataCustomized(List<DrugOriginSpecificationForDropDownVo> dataList) {
        // 自定义数据组装
        // 过滤出dataList中库存列表
        for (DrugOriginSpecificationForDropDownVo vo : dataList) {
            if (vo.getDrugOrigin() != null) {
                var inventoryList = vo.getDrugOrigin().getDrugOriginInventoryList();
                if (inventoryList != null) {
                    var currentInventory =
                            inventoryList.stream()
                                    .filter(
                                            inventory ->
                                                    inventory
                                                            .getStorageCode()
                                                            .equals(UserContext.getDepartment()))
                                    .findFirst()
                                    .orElse(null);
                    if (currentInventory != null) {
                        var amountPerPackage =
                                currentInventory.getDrugOriginSpecification().getAmountPerPackage();
                        // 取库存数量currentInventory.getAmount() 乘以 amountPerPackage
                        // 再除以vo.getAmountPerPackage()
                        vo.setCurrentInventory(
                                currentInventory
                                        .getAmount()
                                        .multiply(BigDecimal.valueOf(amountPerPackage))
                                        .divide(
                                                BigDecimal.valueOf(vo.getAmountPerPackage()),
                                                DrugConstants.drugAmountAccuracy,
                                                BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
        }
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class DrugOriginSpecificationForDropDownVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugOriginSpecificationBaseDto> rootBaseDtoList;

        /** 持有字段drugOrigin的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginBaseDto, DrugOriginForDropDownVo> drugOrigin;

        /** 持有字段drugOrigin.drugOriginInventoryList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginInventoryBaseDto, DrugDictionaryRefDrugOriginInventoryVo>
                drugOrigin2DrugOriginInventoryList;

        /** 持有字段drugOrigin.drugOriginExtension的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginExtensionBaseDto, DrugOriginExtensionSimpleVo>
                drugOrigin2DrugOriginExtension;

        /** 持有字段drugOrigin.drugOriginCampusDisableList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginCampusDisableBaseDto, DrugOriginCampusDisableSimpleVo>
                drugOrigin2DrugOriginCampusDisableList;

        /** 持有字段drugOrigin.drugNameForDrugOriginList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo>
                drugOrigin2DrugNameForDrugOriginList;

        /** 持有字段drugOrigin.drugProducer的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugProducerDictionaryBaseDto, DrugProducerSimpleVo> drugOrigin2DrugProducer;

        /** 持有字段drugOrigin.drugLocationList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugLocationBaseDto, DrugDictionaryRefDrugLocationVo>
                drugOrigin2DrugLocationList;

        /** 持有字段drugOrigin.drugSpecification的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryForDropDownVo>
                drugOrigin2DrugSpecification;

        /** 持有字段drugOrigin.drugOriginInventoryList.drugOriginSpecification的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo>
                drugOrigin2DrugOriginInventoryList2DrugOriginSpecification;

        /** 持有字段drugOrigin.drugSpecification.drugNameForDrugSpecificationList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo>
                drugOrigin2DrugSpecification2DrugNameForDrugSpecificationList;

        /** 持有字段drugOrigin.drugSpecification.drugDictionary的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugDictionaryBaseDto, DrugDictionaryForDropDownVo>
                drugOrigin2DrugSpecification2DrugDictionary;

        /** 持有字段drugOrigin.drugSpecification.drugDictionary.drugNameForDrugList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo>
                drugOrigin2DrugSpecification2DrugDictionary2DrugNameForDrugList;

        /** 持有字段drugOrigin.drugSpecification.drugDictionary.drugCatalog的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugCategoryBaseDto, DrugCategorySimpleVo>
                drugOrigin2DrugSpecification2DrugDictionary2DrugCatalog;
    }
}
