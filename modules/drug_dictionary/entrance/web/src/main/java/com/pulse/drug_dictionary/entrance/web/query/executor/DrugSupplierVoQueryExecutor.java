package com.pulse.drug_dictionary.entrance.web.query.executor;

import com.pulse.drug_dictionary.entrance.web.converter.DrugSupplierVoConverter;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugSupplierVoDataAssembler;
import com.pulse.drug_dictionary.entrance.web.vo.DrugSupplierVo;
import com.pulse.drug_dictionary.manager.dto.DrugSupplierBaseDto;
import com.pulse.drug_dictionary.persist.qto.SearchDrugSupplierQto;
import com.pulse.drug_dictionary.service.DrugSupplierBaseDtoService;
import com.pulse.drug_dictionary.service.index.entity.SearchDrugSupplierQtoService;
import com.vs.code.AutoGenerated;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** DrugSupplierVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "6c6a6372-0b6e-3cbf-9c9c-3d6d4de9cb8e")
public class DrugSupplierVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private DrugSupplierBaseDtoService drugSupplierBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSupplierVoConverter drugSupplierVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSupplierVoDataAssembler drugSupplierVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private SearchDrugSupplierQtoService searchDrugSupplierQtoService;

    /** 根据SearchDrugSupplierQto查询DrugSupplierVo列表,分页 */
    @AutoGenerated(locked = false, uuid = "3450a728-445f-3556-b981-f228c7566992")
    public VSQueryResult<DrugSupplierVo> searchDrugSupplierPaged(
            @NotNull SearchDrugSupplierQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchDrugSupplierQtoService.queryPaged(qto);
        Map<String, DrugSupplierVo> idVoMap = toIdVoMap(ids);
        drugSupplierVoDataAssembler.assembleData(idVoMap);
        List<DrugSupplierVo> voList = new ArrayList<>(idVoMap.values());
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchDrugSupplierQtoService.count(qto));
        result.setResult(voList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "5abbbac2-9318-3e5c-aae0-a690b8e62ccf")
    private Map<String, DrugSupplierVo> toIdVoMap(List<String> ids) {
        List<DrugSupplierBaseDto> rootBaseDtoList = drugSupplierBaseDtoService.getByIds(ids);
        Map<String, DrugSupplierBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugSupplierBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<DrugSupplierBaseDto, DrugSupplierVo> voMap =
                drugSupplierVoConverter.convertToDrugSupplierVoMap(
                        new ArrayList<>(baseDtoMap.values()));
        Map<String, DrugSupplierVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugSupplierBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }
}
