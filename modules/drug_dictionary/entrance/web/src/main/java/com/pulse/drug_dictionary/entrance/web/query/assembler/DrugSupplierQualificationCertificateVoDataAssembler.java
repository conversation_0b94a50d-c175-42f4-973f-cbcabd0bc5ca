package com.pulse.drug_dictionary.entrance.web.query.assembler;

import com.pulse.drug_dictionary.entrance.web.vo.DrugSupplierQualificationCertificateVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** DrugSupplierQualificationCertificateVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "a9848645-ceed-3c21-ac62-d6fcd78061b6")
public class DrugSupplierQualificationCertificateVoDataAssembler {

    /** 组装DrugSupplierQualificationCertificateVo数据 */
    @AutoGenerated(locked = true, uuid = "176e373d-2d8e-3eb0-9bb8-6c50c1c77693")
    public void assembleData(Map<String, DrugSupplierQualificationCertificateVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装DrugSupplierQualificationCertificateVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "671ef313-bd30-3b9c-8bcc-1b4537db5661")
    public void assembleDataCustomized(List<DrugSupplierQualificationCertificateVo> dataList) {
        // 自定义数据组装

    }
}
