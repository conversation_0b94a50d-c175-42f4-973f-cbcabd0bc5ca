package com.pulse.drug_dictionary.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugOriginSpecificationSimpleVoDataAssembler;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginSpecificationSimpleVo;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugOriginSpecificationSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "85a44acd-b77c-4ded-9911-383105ab82f8|VO|CONVERTER")
public class DrugOriginSpecificationSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationSimpleVoDataAssembler
            drugOriginSpecificationSimpleVoDataAssembler;

    /** 把DrugOriginSpecificationBaseDto转换成DrugOriginSpecificationSimpleVo */
    @AutoGenerated(locked = true, uuid = "49a47ba1-68a9-3237-ab44-4cbc795ddb70")
    public DrugOriginSpecificationSimpleVo convertToDrugOriginSpecificationSimpleVo(
            DrugOriginSpecificationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginSpecificationSimpleVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugOriginSpecificationSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "55ed6f4f-c676-3620-af67-10ea0744e329")
    public List<DrugOriginSpecificationSimpleVo> convertAndAssembleDataList(
            List<DrugOriginSpecificationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugOriginSpecificationSimpleVo> voMap =
                convertToDrugOriginSpecificationSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugOriginSpecificationSimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugOriginSpecificationSimpleVo */
    @AutoGenerated(locked = false, uuid = "85a44acd-b77c-4ded-9911-383105ab82f8-converter-Map")
    public Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo>
            convertToDrugOriginSpecificationSimpleVoMap(
                    List<DrugOriginSpecificationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginSpecificationSimpleVo vo =
                                                    new DrugOriginSpecificationSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setSpecificationType(dto.getSpecificationType());
                                            vo.setDrugSpecification(dto.getDrugSpecification());
                                            vo.setUnit(dto.getUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            vo.setBidPurchasePrice(dto.getBidPurchasePrice());
                                            vo.setReferenceRetailPriceOne(
                                                    dto.getReferenceRetailPriceOne());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugOriginSpecificationSimpleVo */
    @AutoGenerated(locked = true, uuid = "85a44acd-b77c-4ded-9911-383105ab82f8-converter-list")
    public List<DrugOriginSpecificationSimpleVo> convertToDrugOriginSpecificationSimpleVoList(
            List<DrugOriginSpecificationBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginSpecificationSimpleVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugOriginSpecificationSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "f0c36def-95f5-38ea-893f-bce4cdf2ea86")
    public DrugOriginSpecificationSimpleVo convertAndAssembleData(
            DrugOriginSpecificationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
