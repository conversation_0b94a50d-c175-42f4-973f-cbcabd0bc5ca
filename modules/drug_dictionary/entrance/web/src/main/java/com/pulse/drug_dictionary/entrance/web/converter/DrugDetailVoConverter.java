package com.pulse.drug_dictionary.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugDetailVoDataAssembler;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugDetailVoDataAssembler.DrugDetailVoDataHolder;
import com.pulse.drug_dictionary.entrance.web.query.collector.DrugDetailVoDataCollector;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugDictionaryExtensionBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugOriginBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugOriginExtensionBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugOriginSpecificationBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugProducerDictionaryBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugSpecificationDetailBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugSpecificationDictionaryBaseVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugSpecificationExtensionBaseVo;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDetailBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationExtensionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "db3566f7-be75-448b-9add-1b8948263404|VO|CONVERTER")
public class DrugDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugDetailVoDataAssembler drugDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDetailVoDataCollector drugDetailVoDataCollector;

    /** 把DrugOriginBaseDto转换成DrugOriginBaseVo */
    @AutoGenerated(locked = false, uuid = "0d6437fe-199b-4a14-b7fc-a2386c8c6d55-converter-Map")
    public Map<DrugOriginBaseDto, DrugDetailVo.DrugOriginBaseVo> convertToDrugOriginBaseVoMap(
            List<DrugOriginBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginBaseDto, DrugOriginBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginBaseVo vo = new DrugOriginBaseVo();
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setGcpFlag(dto.getGcpFlag());
                                            vo.setCentralPurchaseFlag(dto.getCentralPurchaseFlag());
                                            vo.setGmpFlag(dto.getGmpFlag());
                                            vo.setBidLostInpLimitFlag(dto.getBidLostInpLimitFlag());
                                            vo.setTraceabilityCode(dto.getTraceabilityCode());
                                            vo.setDonationFlag(dto.getDonationFlag());
                                            vo.setFreeFlag(dto.getFreeFlag());
                                            vo.setSelfProvideFlag(dto.getSelfProvideFlag());
                                            vo.setOutpUsageFlag(dto.getOutpUsageFlag());
                                            vo.setInpUsageFlag(dto.getInpUsageFlag());
                                            vo.setInternetFlage(dto.getInternetFlage());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginBaseDto转换成DrugOriginBaseVo */
    @AutoGenerated(locked = true, uuid = "0d6437fe-199b-4a14-b7fc-a2386c8c6d55-converter-list")
    public List<DrugDetailVo.DrugOriginBaseVo> convertToDrugOriginBaseVoList(
            List<DrugOriginBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "2721b33b-df37-33bd-a0df-1b2e14e04fcc")
    public DrugDetailVo convertAndAssembleData(DrugDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginBaseDto转换成DrugOriginBaseVo */
    @AutoGenerated(locked = true, uuid = "4e995dab-b049-3fcd-8c57-67fa1b601b63")
    public DrugDetailVo.DrugOriginBaseVo convertToDrugOriginBaseVo(DrugOriginBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugSpecificationDictionaryBaseDto转换成DrugSpecificationDictionaryBaseVo */
    @AutoGenerated(locked = false, uuid = "4f15354c-a1ce-41d8-a5b3-d6450b6a8134-converter-Map")
    public Map<DrugSpecificationDictionaryBaseDto, DrugDetailVo.DrugSpecificationDictionaryBaseVo>
            convertToDrugSpecificationDictionaryBaseVoMap(
                    List<DrugSpecificationDictionaryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugSpecificationDictionaryBaseVo vo =
                                                    new DrugSpecificationDictionaryBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setSpecificationDrugName(
                                                    dto.getSpecificationDrugName());
                                            vo.setPackageSpecification(
                                                    dto.getPackageSpecification());
                                            vo.setPackageUnit(dto.getPackageUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            vo.setMinSpecification(dto.getMinSpecification());
                                            vo.setDosage(dto.getDosage());
                                            vo.setDosageUnit(dto.getDosageUnit());
                                            vo.setConcentration(dto.getConcentration());
                                            vo.setVolume(dto.getVolume());
                                            vo.setVolumeUnit(dto.getVolumeUnit());
                                            vo.setBasicFlag(dto.getBasicFlag());
                                            vo.setDrugForm(dto.getDrugForm());
                                            vo.setAccountType(dto.getAccountType());
                                            vo.setBigInfusionFlag(dto.getBigInfusionFlag());
                                            vo.setSpecialDosage(dto.getSpecialDosage());
                                            vo.setAntibioticFlag(dto.getAntibioticFlag());
                                            vo.setAntibioticLevel(dto.getAntibioticLevel());
                                            vo.setAntineoplasticLevel(dto.getAntineoplasticLevel());
                                            vo.setSkinTestLevel(dto.getSkinTestLevel());
                                            vo.setSkinTestType(dto.getSkinTestType());
                                            vo.setSkinTestEfficientDays(
                                                    dto.getSkinTestEfficientDays());
                                            vo.setDischargeFlag(dto.getDischargeFlag());
                                            vo.setChemotherapyFlag(dto.getChemotherapyFlag());
                                            vo.setEnFlag(dto.getEnFlag());
                                            vo.setTpnFlag(dto.getTpnFlag());
                                            vo.setSurgeryFlag(dto.getSurgeryFlag());
                                            vo.setAnesthesiaFlag(dto.getAnesthesiaFlag());
                                            vo.setColdFlag(dto.getColdFlag());
                                            vo.setDangerFlag(dto.getDangerFlag());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setOphthalmologyDosage(dto.getOphthalmologyDosage());
                                            vo.setOphthalmicDosageUnit(
                                                    dto.getOphthalmicDosageUnit());
                                            vo.setMinUnit(dto.getMinUnit());
                                            vo.setStoreCondition(dto.getStoreCondition());
                                            vo.setAmountPerBox(dto.getAmountPerBox());
                                            vo.setGramsPerPackage(dto.getGramsPerPackage());
                                            vo.setManageMode(dto.getManageMode());
                                            vo.setAntibacterialFlag(dto.getAntibacterialFlag());
                                            vo.setAntibacterialLevel(dto.getAntibacterialLevel());
                                            vo.setDailyUsage(dto.getDailyUsage());
                                            vo.setLabelUnit(dto.getLabelUnit());
                                            vo.setSkinTestFlag(dto.getSkinTestFlag());
                                            vo.setSpecialDosageUnit(dto.getSpecialDosageUnit());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugSpecificationDictionaryBaseDto转换成DrugSpecificationDictionaryBaseVo */
    @AutoGenerated(locked = true, uuid = "4f15354c-a1ce-41d8-a5b3-d6450b6a8134-converter-list")
    public List<DrugDetailVo.DrugSpecificationDictionaryBaseVo>
            convertToDrugSpecificationDictionaryBaseVoList(
                    List<DrugSpecificationDictionaryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugSpecificationDictionaryBaseVoMap(dtoList).values());
    }

    /** 把DrugDictionaryBaseDto转换成DrugDetailVo */
    @AutoGenerated(locked = true, uuid = "52cd3da8-742b-3106-9d10-65d406fe56ab")
    public DrugDetailVo convertToDrugDetailVo(DrugDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginExtensionBaseDto转换成DrugOriginExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "53e0c9ce-b20f-319c-9020-27b8590783cb")
    public DrugDetailVo.DrugOriginExtensionBaseVo convertToDrugOriginExtensionBaseVo(
            DrugOriginExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginExtensionBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugSpecificationExtensionBaseDto转换成DrugSpecificationExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "58683cb9-4878-311d-95a7-3a344ac575ee")
    public DrugDetailVo.DrugSpecificationExtensionBaseVo convertToDrugSpecificationExtensionBaseVo(
            DrugSpecificationExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugSpecificationExtensionBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugProducerDictionaryBaseVo */
    @AutoGenerated(locked = false, uuid = "58f3fe40-8273-4095-aa9f-a94e0a9aae99-converter-Map")
    public Map<DrugProducerDictionaryBaseDto, DrugDetailVo.DrugProducerDictionaryBaseVo>
            convertToDrugProducerDictionaryBaseVoMap(List<DrugProducerDictionaryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugProducerDictionaryBaseDto, DrugProducerDictionaryBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugProducerDictionaryBaseVo vo =
                                                    new DrugProducerDictionaryBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setProducerName(dto.getProducerName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugProducerDictionaryBaseVo */
    @AutoGenerated(locked = true, uuid = "58f3fe40-8273-4095-aa9f-a94e0a9aae99-converter-list")
    public List<DrugDetailVo.DrugProducerDictionaryBaseVo>
            convertToDrugProducerDictionaryBaseVoList(List<DrugProducerDictionaryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugProducerDictionaryBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "6e82519a-729b-335f-9dc0-f7bcbe859914")
    public List<DrugDetailVo> convertAndAssembleDataList(List<DrugDictionaryBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugDetailVoDataHolder dataHolder = new DrugDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, DrugDetailVo> voMap =
                convertToDrugDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getDrugCode(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        drugDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getDrugCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把DrugDictionaryExtensionBaseDto转换成DrugDictionaryExtensionBaseVo */
    @AutoGenerated(locked = false, uuid = "6f572687-6125-4570-9ec8-e1aa2bdc0517-converter-Map")
    public Map<DrugDictionaryExtensionBaseDto, DrugDetailVo.DrugDictionaryExtensionBaseVo>
            convertToDrugDictionaryExtensionBaseVoMap(
                    List<DrugDictionaryExtensionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugDictionaryExtensionBaseDto, DrugDictionaryExtensionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugDictionaryExtensionBaseVo vo =
                                                    new DrugDictionaryExtensionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setDischargeDescription(
                                                    dto.getDischargeDescription());
                                            vo.setOrderDescription(dto.getOrderDescription());
                                            vo.setEnglishDescription(dto.getEnglishDescription());
                                            vo.setOutpDescription(dto.getOutpDescription());
                                            vo.setPivasDescription(dto.getPivasDescription());
                                            vo.setAuditDescription(dto.getAuditDescription());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugDictionaryExtensionBaseDto转换成DrugDictionaryExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "6f572687-6125-4570-9ec8-e1aa2bdc0517-converter-list")
    public List<DrugDetailVo.DrugDictionaryExtensionBaseVo>
            convertToDrugDictionaryExtensionBaseVoList(
                    List<DrugDictionaryExtensionBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugDictionaryExtensionBaseVoMap(dtoList).values());
    }

    /** 把DrugSpecificationDetailBaseDto转换成DrugSpecificationDetailBaseVo */
    @AutoGenerated(locked = false, uuid = "95739d87-aef2-460d-95e5-ee26891451e1-converter-Map")
    public Map<DrugSpecificationDetailBaseDto, DrugDetailVo.DrugSpecificationDetailBaseVo>
            convertToDrugSpecificationDetailBaseVoMap(
                    List<DrugSpecificationDetailBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugSpecificationDetailBaseDto, DrugSpecificationDetailBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugSpecificationDetailBaseVo vo =
                                                    new DrugSpecificationDetailBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setSpecificationType(dto.getSpecificationType());
                                            vo.setSpecification(dto.getSpecification());
                                            vo.setUnit(dto.getUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugSpecificationDetailBaseDto转换成DrugSpecificationDetailBaseVo */
    @AutoGenerated(locked = true, uuid = "95739d87-aef2-460d-95e5-ee26891451e1-converter-list")
    public List<DrugDetailVo.DrugSpecificationDetailBaseVo>
            convertToDrugSpecificationDetailBaseVoList(
                    List<DrugSpecificationDetailBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugSpecificationDetailBaseVoMap(dtoList).values());
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugOriginSpecificationBaseVo */
    @AutoGenerated(locked = false, uuid = "9ea7a0bc-e5ea-4a72-9439-84622fd52dfc-converter-Map")
    public Map<DrugOriginSpecificationBaseDto, DrugDetailVo.DrugOriginSpecificationBaseVo>
            convertToDrugOriginSpecificationBaseVoMap(
                    List<DrugOriginSpecificationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginSpecificationBaseVo vo =
                                                    new DrugOriginSpecificationBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setDrugSpecificationDetailId(
                                                    dto.getDrugSpecificationDetailId());
                                            vo.setSpecificationType(dto.getSpecificationType());
                                            vo.setBidPurchasePrice(dto.getBidPurchasePrice());
                                            vo.setReferenceRetailPriceOne(
                                                    dto.getReferenceRetailPriceOne());
                                            vo.setInsurancePayPrice(dto.getInsurancePayPrice());
                                            vo.setDrugSpecification(dto.getDrugSpecification());
                                            vo.setUnit(dto.getUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugOriginSpecificationBaseVo */
    @AutoGenerated(locked = true, uuid = "9ea7a0bc-e5ea-4a72-9439-84622fd52dfc-converter-list")
    public List<DrugDetailVo.DrugOriginSpecificationBaseVo>
            convertToDrugOriginSpecificationBaseVoList(
                    List<DrugOriginSpecificationBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginSpecificationBaseVoMap(dtoList).values());
    }

    /** 把DrugDictionaryExtensionBaseDto转换成DrugDictionaryExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "9fd7fdf7-51ae-3c0c-be69-6d42e40feb88")
    public DrugDetailVo.DrugDictionaryExtensionBaseVo convertToDrugDictionaryExtensionBaseVo(
            DrugDictionaryExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugDictionaryExtensionBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugProducerDictionaryBaseVo */
    @AutoGenerated(locked = true, uuid = "ad57ec59-3149-3bb2-b626-3aff91bd46c2")
    public DrugDetailVo.DrugProducerDictionaryBaseVo convertToDrugProducerDictionaryBaseVo(
            DrugProducerDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugProducerDictionaryBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugSpecificationDetailBaseDto转换成DrugSpecificationDetailBaseVo */
    @AutoGenerated(locked = true, uuid = "d9ce548e-8104-33b7-a4cc-5fe81875e9c7")
    public DrugDetailVo.DrugSpecificationDetailBaseVo convertToDrugSpecificationDetailBaseVo(
            DrugSpecificationDetailBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugSpecificationDetailBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugDictionaryBaseDto转换成DrugDetailVo */
    @AutoGenerated(locked = false, uuid = "db3566f7-be75-448b-9add-1b8948263404-converter-Map")
    public Map<DrugDictionaryBaseDto, DrugDetailVo> convertToDrugDetailVoMap(
            List<DrugDictionaryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugDictionaryBaseDto, DrugDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugDetailVo vo = new DrugDetailVo();
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugName(dto.getDrugName());
                                            vo.setCommonNameCode(dto.getCommonNameCode());
                                            vo.setParentPharmacologicalType(
                                                    dto.getParentPharmacologicalType());
                                            vo.setPharmacologicalType(dto.getPharmacologicalType());
                                            vo.setDrugType(dto.getDrugType());
                                            vo.setToxicType(dto.getToxicType());
                                            vo.setSourceType(dto.getSourceType());
                                            vo.setHerbType(dto.getHerbType());
                                            vo.setDrugCatalogId(dto.getDrugCatalogId());
                                            vo.setDrugRootCatalogId(dto.getDrugRootCatalogId());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setStandardCode(dto.getStandardCode());
                                            vo.setEnglishName(dto.getEnglishName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugDictionaryBaseDto转换成DrugDetailVo */
    @AutoGenerated(locked = true, uuid = "db3566f7-be75-448b-9add-1b8948263404-converter-list")
    public List<DrugDetailVo> convertToDrugDetailVoList(List<DrugDictionaryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugDetailVoMap(dtoList).values());
    }

    /** 把DrugSpecificationDictionaryBaseDto转换成DrugSpecificationDictionaryBaseVo */
    @AutoGenerated(locked = true, uuid = "ea32b931-f506-3ca5-b685-510cb298ae53")
    public DrugDetailVo.DrugSpecificationDictionaryBaseVo
            convertToDrugSpecificationDictionaryBaseVo(DrugSpecificationDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugSpecificationDictionaryBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugOriginExtensionBaseDto转换成DrugOriginExtensionBaseVo */
    @AutoGenerated(locked = false, uuid = "ec882471-05ed-4033-8c49-ea571daad065-converter-Map")
    public Map<DrugOriginExtensionBaseDto, DrugDetailVo.DrugOriginExtensionBaseVo>
            convertToDrugOriginExtensionBaseVoMap(List<DrugOriginExtensionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginExtensionBaseDto, DrugOriginExtensionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginExtensionBaseVo vo =
                                                    new DrugOriginExtensionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setPurchasePlatformDrugId(
                                                    dto.getPurchasePlatformDrugId());
                                            vo.setQualificationCode(dto.getQualificationCode());
                                            vo.setBidFlag(dto.getBidFlag());
                                            vo.setBidType(dto.getBidType());
                                            vo.setNationalMedicalInsuranceCode(
                                                    dto.getNationalMedicalInsuranceCode());
                                            vo.setNationalMedicalInsuranceName(
                                                    dto.getNationalMedicalInsuranceName());
                                            vo.setMarkupRate(dto.getMarkupRate());
                                            vo.setImportLicenceNumber(dto.getImportLicenceNumber());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginExtensionBaseDto转换成DrugOriginExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "ec882471-05ed-4033-8c49-ea571daad065-converter-list")
    public List<DrugDetailVo.DrugOriginExtensionBaseVo> convertToDrugOriginExtensionBaseVoList(
            List<DrugOriginExtensionBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginExtensionBaseVoMap(dtoList).values());
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugOriginSpecificationBaseVo */
    @AutoGenerated(locked = true, uuid = "ed3ac95d-120e-3ea8-9de5-f444b0f4ddcf")
    public DrugDetailVo.DrugOriginSpecificationBaseVo convertToDrugOriginSpecificationBaseVo(
            DrugOriginSpecificationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginSpecificationBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugSpecificationExtensionBaseDto转换成DrugSpecificationExtensionBaseVo */
    @AutoGenerated(locked = false, uuid = "fbbb282a-53f7-4c4e-a168-7c3693ae850f-converter-Map")
    public Map<DrugSpecificationExtensionBaseDto, DrugDetailVo.DrugSpecificationExtensionBaseVo>
            convertToDrugSpecificationExtensionBaseVoMap(
                    List<DrugSpecificationExtensionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugSpecificationExtensionBaseDto, DrugSpecificationExtensionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugSpecificationExtensionBaseVo vo =
                                                    new DrugSpecificationExtensionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setSolventFlag(dto.getSolventFlag());
                                            vo.setMaterialFlag(dto.getMaterialFlag());
                                            vo.setPreparationFlag(dto.getPreparationFlag());
                                            vo.setDoubleSignatureFlag(dto.getDoubleSignatureFlag());
                                            vo.setNotUploadInsuranceFlag(
                                                    dto.getNotUploadInsuranceFlag());
                                            vo.setEasyFallFlag(dto.getEasyFallFlag());
                                            vo.setDischargeDescription(
                                                    dto.getDischargeDescription());
                                            vo.setOrderDescription(dto.getOrderDescription());
                                            vo.setEnglishDescription(dto.getEnglishDescription());
                                            vo.setOutpDescription(dto.getOutpDescription());
                                            vo.setPivasDescription(dto.getPivasDescription());
                                            vo.setAuditDescription(dto.getAuditDescription());
                                            vo.setAllergyType(dto.getAllergyTypeList());
                                            vo.setOtherAttribute(dto.getOtherAttributeList());
                                            vo.setRareDiseaseContrast(
                                                    dto.getRareDiseaseContrastList());
                                            vo.setStabilizationHour(dto.getStabilizationHour());
                                            vo.setMachinePackagingWay(dto.getMachinePackagingWay());
                                            vo.setInternalCode(dto.getInternalCode());
                                            vo.setClassifyIdentificationCode(
                                                    dto.getClassifyIdentificationCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugSpecificationExtensionBaseDto转换成DrugSpecificationExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "fbbb282a-53f7-4c4e-a168-7c3693ae850f-converter-list")
    public List<DrugDetailVo.DrugSpecificationExtensionBaseVo>
            convertToDrugSpecificationExtensionBaseVoList(
                    List<DrugSpecificationExtensionBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugSpecificationExtensionBaseVoMap(dtoList).values());
    }
}
