package com.pulse.drug_dictionary.entrance.web.query.collector;

import com.pulse.drug_dictionary.entrance.web.converter.DrugCategorySimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugDictionaryForDropDownVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugDictionaryRefDrugLocationVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugDictionaryRefDrugOriginInventoryVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugNameDictionarySimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugOriginCampusDisableSimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugOriginExtensionSimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugOriginForDropDownVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugOriginSpecificationSimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugProducerSimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugSpecificationDictionaryForDropDownVoConverter;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugOriginForDropDownVoDataAssembler.DrugOriginForDropDownVoDataHolder;
import com.pulse.drug_dictionary.entrance.web.vo.DrugCategorySimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryForDropDownVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryRefDrugLocationVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryRefDrugOriginInventoryVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugNameDictionarySimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginCampusDisableSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginExtensionSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginSpecificationSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugProducerSimpleVo;
import com.pulse.drug_dictionary.entrance.web.vo.DrugSpecificationDictionaryForDropDownVo;
import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginCampusDisableBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.facade.drug_inventory.DrugLocationBaseDtoServiceInDrugDictionaryRpcAdapter;
import com.pulse.drug_dictionary.manager.facade.drug_inventory.DrugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter;
import com.pulse.drug_dictionary.manager.facade.drug_inventory.DrugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter;
import com.pulse.drug_dictionary.service.DrugCategoryBaseDtoService;
import com.pulse.drug_dictionary.service.DrugDictionaryBaseDtoService;
import com.pulse.drug_dictionary.service.DrugNameDictionaryBaseDtoService;
import com.pulse.drug_dictionary.service.DrugOriginBaseDtoService;
import com.pulse.drug_dictionary.service.DrugOriginCampusDisableBaseDtoService;
import com.pulse.drug_dictionary.service.DrugOriginExtensionBaseDtoService;
import com.pulse.drug_dictionary.service.DrugOriginSpecificationBaseDtoService;
import com.pulse.drug_dictionary.service.DrugProducerDictionaryBaseDtoService;
import com.pulse.drug_dictionary.service.DrugSpecificationDictionaryBaseDtoService;
import com.pulse.drug_inventory.manager.dto.DrugLocationBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryWithSpecificationDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装DrugOriginForDropDownVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "e03655a9-e44e-303d-82aa-49ca1b0ce6a7")
public class DrugOriginForDropDownVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCategoryBaseDtoService drugCategoryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCategorySimpleVoConverter drugCategorySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryBaseDtoService drugDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryForDropDownVoConverter drugDictionaryForDropDownVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryRefDrugLocationVoConverter drugDictionaryRefDrugLocationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryRefDrugOriginInventoryVoConverter
            drugDictionaryRefDrugOriginInventoryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLocationBaseDtoServiceInDrugDictionaryRpcAdapter
            drugLocationBaseDtoServiceInDrugDictionaryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugNameDictionaryBaseDtoService drugNameDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugNameDictionarySimpleVoConverter drugNameDictionarySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseDtoService drugOriginBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginCampusDisableBaseDtoService drugOriginCampusDisableBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginCampusDisableSimpleVoConverter drugOriginCampusDisableSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginExtensionBaseDtoService drugOriginExtensionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginExtensionSimpleVoConverter drugOriginExtensionSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginForDropDownVoConverter drugOriginForDropDownVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginForDropDownVoDataCollector drugOriginForDropDownVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter
            drugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter
            drugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoService drugOriginSpecificationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationSimpleVoConverter drugOriginSpecificationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugProducerDictionaryBaseDtoService drugProducerDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugProducerSimpleVoConverter drugProducerSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryBaseDtoService drugSpecificationDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryForDropDownVoConverter
            drugSpecificationDictionaryForDropDownVoConverter;

    /** 获取DrugOriginBaseDto数据填充DrugOriginForDropDownVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "3aba7f8c-4402-3eac-8eb7-75221999ad5d")
    public void collectDataWithDtoData(
            List<DrugOriginBaseDto> dtoList, DrugOriginForDropDownVoDataHolder dataHolder) {

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "5032c9fd-192f-3881-a901-3a681de51624")
    private void fillDataWhenNecessary(DrugOriginForDropDownVoDataHolder dataHolder) {
        List<DrugOriginBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.drugOriginInventoryList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginInventoryBaseDto> baseDtoList =
                    drugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginInventoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugOriginInventoryBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugOriginInventoryBaseDto::getDrugOriginCode));
            Map<String, DrugOriginInventoryWithSpecificationDto>
                    drugOriginInventoryWithSpecificationDtoMap =
                            drugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter
                                    .getByDrugOriginCodes(
                                            baseDtoList.stream()
                                                    .map(
                                                            DrugOriginInventoryBaseDto
                                                                    ::getDrugOriginCode)
                                                    .collect(Collectors.toList()))
                                    .stream()
                                    .collect(
                                            Collectors.toMap(
                                                    DrugOriginInventoryWithSpecificationDto::getId,
                                                    Function.identity()));
            Map<DrugOriginInventoryWithSpecificationDto, DrugDictionaryRefDrugOriginInventoryVo>
                    dtoVoMap =
                            drugDictionaryRefDrugOriginInventoryVoConverter
                                    .convertToDrugDictionaryRefDrugOriginInventoryVoMap(
                                            new ArrayList<>(
                                                    drugOriginInventoryWithSpecificationDtoMap
                                                            .values()));
            Map<DrugOriginInventoryBaseDto, DrugDictionaryRefDrugOriginInventoryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            drugOriginInventoryWithSpecificationDtoMap.containsKey(
                                                    baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            drugOriginInventoryWithSpecificationDtoMap
                                                                    .get(baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOriginInventoryList =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOriginExtension == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginExtensionBaseDto> baseDtoList =
                    drugOriginExtensionBaseDtoService
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugOriginExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugOriginExtensionBaseDto::getDrugOriginCode));
            Map<DrugOriginExtensionBaseDto, DrugOriginExtensionSimpleVo> dtoVoMap =
                    drugOriginExtensionSimpleVoConverter.convertToDrugOriginExtensionSimpleVoMap(
                            baseDtoList);
            Map<DrugOriginExtensionBaseDto, DrugOriginExtensionSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOriginExtension =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOriginCampusDisableList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginCampusDisableBaseDto> baseDtoList =
                    drugOriginCampusDisableBaseDtoService
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginCampusDisableBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugOriginCampusDisableBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugOriginCampusDisableBaseDto::getDrugOriginCode));
            Map<DrugOriginCampusDisableBaseDto, DrugOriginCampusDisableSimpleVo> dtoVoMap =
                    drugOriginCampusDisableSimpleVoConverter
                            .convertToDrugOriginCampusDisableSimpleVoMap(baseDtoList);
            Map<DrugOriginCampusDisableBaseDto, DrugOriginCampusDisableSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOriginCampusDisableList =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugNameForDrugOriginList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugNameDictionaryBaseDto> baseDtoList =
                    drugNameDictionaryBaseDtoService
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugNameDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugNameDictionaryBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugNameDictionaryBaseDto::getDrugOriginCode));
            Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo> dtoVoMap =
                    drugNameDictionarySimpleVoConverter.convertToDrugNameDictionarySimpleVoMap(
                            baseDtoList);
            Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugNameForDrugOriginList =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugProducer == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugProducerId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugProducerDictionaryBaseDto> baseDtoList =
                    drugProducerDictionaryBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugProducerDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugProducerDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugProducerDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<DrugProducerDictionaryBaseDto, DrugProducerSimpleVo> dtoVoMap =
                    drugProducerSimpleVoConverter.convertToDrugProducerSimpleVoMap(baseDtoList);
            Map<DrugProducerDictionaryBaseDto, DrugProducerSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugProducer =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugProducerId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugLocationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugLocationBaseDto> baseDtoList =
                    drugLocationBaseDtoServiceInDrugDictionaryRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugLocationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugLocationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DrugLocationBaseDto::getDrugOriginCode));
            Map<DrugLocationBaseDto, DrugDictionaryRefDrugLocationVo> dtoVoMap =
                    drugDictionaryRefDrugLocationVoConverter
                            .convertToDrugDictionaryRefDrugLocationVoMap(baseDtoList);
            Map<DrugLocationBaseDto, DrugDictionaryRefDrugLocationVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugLocationList =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugSpecification == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugSpecificationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugSpecificationDictionaryBaseDto> baseDtoList =
                    drugSpecificationDictionaryBaseDtoService
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugSpecificationDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugSpecificationDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugSpecificationDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryForDropDownVo>
                    dtoVoMap =
                            drugSpecificationDictionaryForDropDownVoConverter
                                    .convertToDrugSpecificationDictionaryForDropDownVoMap(
                                            baseDtoList);
            Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryForDropDownVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugSpecification =
                    rootDtoList.stream()
                            .map(DrugOriginBaseDto::getDrugSpecificationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOriginInventoryList2DrugOriginSpecification == null) {
            Set<String> ids =
                    dataHolder.drugOriginInventoryList.keySet().stream()
                            .map(DrugOriginInventoryBaseDto::getDrugOriginSpecificationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginSpecificationBaseDto> baseDtoList =
                    drugOriginSpecificationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugOriginSpecificationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugOriginSpecificationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginSpecificationBaseDto::getId,
                                            Function.identity()));
            Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo> dtoVoMap =
                    drugOriginSpecificationSimpleVoConverter
                            .convertToDrugOriginSpecificationSimpleVoMap(baseDtoList);
            Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOriginInventoryList2DrugOriginSpecification =
                    dataHolder.drugOriginInventoryList.keySet().stream()
                            .map(DrugOriginInventoryBaseDto::getDrugOriginSpecificationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugSpecification2DrugNameForDrugSpecificationList == null) {
            Set<String> ids =
                    dataHolder.drugSpecification.keySet().stream()
                            .map(DrugSpecificationDictionaryBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugNameDictionaryBaseDto> baseDtoList =
                    drugNameDictionaryBaseDtoService
                            .getByDrugSpecificationIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugNameDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugNameDictionaryBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugNameDictionaryBaseDto::getDrugSpecificationId));
            Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo> dtoVoMap =
                    drugNameDictionarySimpleVoConverter.convertToDrugNameDictionarySimpleVoMap(
                            baseDtoList);
            Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugSpecification2DrugNameForDrugSpecificationList =
                    dataHolder.drugSpecification.keySet().stream()
                            .map(DrugSpecificationDictionaryBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugSpecification2DrugDictionary == null) {
            Set<String> ids =
                    dataHolder.drugSpecification.keySet().stream()
                            .map(DrugSpecificationDictionaryBaseDto::getDrugCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugDictionaryBaseDto> baseDtoList =
                    drugDictionaryBaseDtoService.getByDrugCodes(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugDictionaryBaseDto::getDrugCode))
                            .collect(Collectors.toList());
            Map<String, DrugDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugDictionaryBaseDto::getDrugCode,
                                            Function.identity()));
            Map<DrugDictionaryBaseDto, DrugDictionaryForDropDownVo> dtoVoMap =
                    drugDictionaryForDropDownVoConverter.convertToDrugDictionaryForDropDownVoMap(
                            baseDtoList);
            Map<DrugDictionaryBaseDto, DrugDictionaryForDropDownVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugSpecification2DrugDictionary =
                    dataHolder.drugSpecification.keySet().stream()
                            .map(DrugSpecificationDictionaryBaseDto::getDrugCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugSpecification2DrugDictionary2DrugNameForDrugList == null) {
            Set<String> ids =
                    dataHolder.drugSpecification2DrugDictionary.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugNameDictionaryBaseDto> baseDtoList =
                    drugNameDictionaryBaseDtoService.getByDrugCodes(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugNameDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugNameDictionaryBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DrugNameDictionaryBaseDto::getDrugCode));
            Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo> dtoVoMap =
                    drugNameDictionarySimpleVoConverter.convertToDrugNameDictionarySimpleVoMap(
                            baseDtoList);
            Map<DrugNameDictionaryBaseDto, DrugNameDictionarySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugSpecification2DrugDictionary2DrugNameForDrugList =
                    dataHolder.drugSpecification2DrugDictionary.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugSpecification2DrugDictionary2DrugCatalog == null) {
            Set<String> ids =
                    dataHolder.drugSpecification2DrugDictionary.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCatalogId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugCategoryBaseDto> baseDtoList =
                    drugCategoryBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugCategoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugCategoryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugCategoryBaseDto::getId, Function.identity()));
            Map<DrugCategoryBaseDto, DrugCategorySimpleVo> dtoVoMap =
                    drugCategorySimpleVoConverter.convertToDrugCategorySimpleVoMap(baseDtoList);
            Map<DrugCategoryBaseDto, DrugCategorySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugSpecification2DrugDictionary2DrugCatalog =
                    dataHolder.drugSpecification2DrugDictionary.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCatalogId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "63fe6ed8-8f2f-36dc-87a5-93aa9f883cca")
    public void collectDataDefault(DrugOriginForDropDownVoDataHolder dataHolder) {
        drugOriginForDropDownVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
