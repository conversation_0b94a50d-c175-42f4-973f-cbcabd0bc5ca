package com.pulse.drug_dictionary.entrance.web.query.executor;

import com.pulse.drug_dictionary.entrance.web.converter.DrugAliasForEditVoConverter;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugAliasForEditVoDataAssembler;
import com.pulse.drug_dictionary.entrance.web.vo.DrugAliasForEditVo;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.persist.qto.ListDrugAliasForEditQto;
import com.pulse.drug_dictionary.service.DrugNameDictionaryBaseDtoService;
import com.pulse.drug_dictionary.service.index.entity.ListDrugAliasForEditQtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** DrugAliasForEditVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "d34922b4-fb4f-30d1-bde7-53106ae20d08")
public class DrugAliasForEditVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private DrugAliasForEditVoConverter drugAliasForEditVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugAliasForEditVoDataAssembler drugAliasForEditVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugNameDictionaryBaseDtoService drugNameDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ListDrugAliasForEditQtoService listDrugAliasForEditQtoService;

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "336b600a-b8a9-3209-b583-78efbea00298")
    private Map<String, DrugAliasForEditVo> toIdVoMap(List<String> ids) {
        List<DrugNameDictionaryBaseDto> rootBaseDtoList =
                drugNameDictionaryBaseDtoService.getByIds(ids);
        Map<String, DrugNameDictionaryBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugNameDictionaryBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<DrugNameDictionaryBaseDto, DrugAliasForEditVo> voMap =
                drugAliasForEditVoConverter.convertToDrugAliasForEditVoMap(
                        new ArrayList<>(baseDtoMap.values()));
        Map<String, DrugAliasForEditVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugNameDictionaryBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }

    /** 根据ListDrugAliasForEditQto查询DrugAliasForEditVo列表,上限500 */
    @AutoGenerated(locked = false, uuid = "3688e12e-61ee-326c-8aa7-cb4bb1c22584")
    public List<DrugAliasForEditVo> queryByListDrugAliasForEdit(
            @NotNull ListDrugAliasForEditQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listDrugAliasForEditQtoService.query(qto);
        Map<String, DrugAliasForEditVo> idVoMap = toIdVoMap(ids);
        drugAliasForEditVoDataAssembler.assembleData(idVoMap);
        List<DrugAliasForEditVo> result =
                ids.stream()
                        .map(id -> idVoMap.get(id))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
