package com.pulse.drug_dictionary.entrance.web.query.executor;

import com.pulse.drug_dictionary.entrance.web.converter.DrugCategoryVoConverter;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugCategoryVoDataAssembler;
import com.pulse.drug_dictionary.entrance.web.vo.DrugCategoryVo;
import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.persist.qto.SearchDrugCategoryQto;
import com.pulse.drug_dictionary.service.DrugCategoryBaseDtoService;
import com.pulse.drug_dictionary.service.index.entity.SearchDrugCategoryQtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** DrugCategoryVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "a397cee0-7d71-39a2-84b8-3e272be91539")
public class DrugCategoryVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCategoryBaseDtoService drugCategoryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCategoryVoConverter drugCategoryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCategoryVoDataAssembler drugCategoryVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private SearchDrugCategoryQtoService searchDrugCategoryQtoService;

    /** 根据SearchDrugCategoryQto查询DrugCategoryVo列表,上限500 */
    @AutoGenerated(locked = false, uuid = "a44714e5-f03b-3331-99e1-261905c418ad")
    public List<DrugCategoryVo> searchDrugCategory(@NotNull SearchDrugCategoryQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchDrugCategoryQtoService.query(qto);
        Map<String, DrugCategoryVo> idVoMap = toIdVoMap(ids);
        drugCategoryVoDataAssembler.assembleData(idVoMap);
        List<DrugCategoryVo> result =
                ids.stream()
                        .map(id -> idVoMap.get(id))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "cee77fd7-**************-079366140de2")
    private Map<String, DrugCategoryVo> toIdVoMap(List<String> ids) {
        List<DrugCategoryBaseDto> rootBaseDtoList = drugCategoryBaseDtoService.getByIds(ids);
        Map<String, DrugCategoryBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCategoryBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<DrugCategoryBaseDto, DrugCategoryVo> voMap =
                drugCategoryVoConverter.convertToDrugCategoryVoMap(
                        new ArrayList<>(baseDtoMap.values()));
        Map<String, DrugCategoryVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCategoryBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }
}
