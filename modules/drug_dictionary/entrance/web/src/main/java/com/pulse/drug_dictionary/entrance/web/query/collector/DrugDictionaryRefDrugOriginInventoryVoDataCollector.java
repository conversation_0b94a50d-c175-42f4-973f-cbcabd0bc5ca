package com.pulse.drug_dictionary.entrance.web.query.collector;

import com.pulse.drug_dictionary.entrance.web.converter.DrugDictionaryRefDrugOriginInventoryVoConverter;
import com.pulse.drug_dictionary.entrance.web.converter.DrugOriginSpecificationSimpleVoConverter;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugDictionaryRefDrugOriginInventoryVoDataAssembler.DrugDictionaryRefDrugOriginInventoryVoDataHolder;
import com.pulse.drug_dictionary.entrance.web.vo.DrugOriginSpecificationSimpleVo;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.facade.drug_inventory.DrugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter;
import com.pulse.drug_dictionary.service.DrugOriginSpecificationBaseDtoService;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryWithSpecificationDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装DrugDictionaryRefDrugOriginInventoryVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "2b3895f0-c920-3679-9410-eadaf492f565")
public class DrugDictionaryRefDrugOriginInventoryVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryRefDrugOriginInventoryVoConverter
            drugDictionaryRefDrugOriginInventoryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryRefDrugOriginInventoryVoDataCollector
            drugDictionaryRefDrugOriginInventoryVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter
            drugOriginInventoryBaseDtoServiceInDrugDictionaryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoService drugOriginSpecificationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationSimpleVoConverter drugOriginSpecificationSimpleVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "15983585-95d8-3396-8405-e9ebe3759fbd")
    private void fillDataWhenNecessary(
            DrugDictionaryRefDrugOriginInventoryVoDataHolder dataHolder) {
        List<DrugOriginInventoryBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.drugOriginSpecification == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugOriginInventoryBaseDto::getDrugOriginSpecificationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginSpecificationBaseDto> baseDtoList =
                    drugOriginSpecificationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugOriginSpecificationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugOriginSpecificationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginSpecificationBaseDto::getId,
                                            Function.identity()));
            Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo> dtoVoMap =
                    drugOriginSpecificationSimpleVoConverter
                            .convertToDrugOriginSpecificationSimpleVoMap(baseDtoList);
            Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOriginSpecification =
                    rootDtoList.stream()
                            .map(DrugOriginInventoryBaseDto::getDrugOriginSpecificationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "25c84686-e9af-3dfc-ad1b-bb4d635394e5")
    public void collectDataDefault(DrugDictionaryRefDrugOriginInventoryVoDataHolder dataHolder) {
        drugDictionaryRefDrugOriginInventoryVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /**
     * 获取DrugOriginInventoryWithSpecificationDto数据填充DrugDictionaryRefDrugOriginInventoryVo，并根据扩展关系填充剩余数据
     */
    @AutoGenerated(locked = true, uuid = "c815f957-5d89-32d3-8521-38467cab95ac")
    public void collectDataWithDtoData(
            List<DrugOriginInventoryWithSpecificationDto> dtoList,
            DrugDictionaryRefDrugOriginInventoryVoDataHolder dataHolder) {
        List<DrugOriginSpecificationBaseDto> drugOriginSpecificationList = new ArrayList<>();

        for (DrugOriginInventoryWithSpecificationDto rootDto : dtoList) {
            DrugOriginSpecificationBaseDto drugOriginSpecificationDto =
                    rootDto.getDrugOriginSpecification();
            if (drugOriginSpecificationDto != null) {
                drugOriginSpecificationList.add(drugOriginSpecificationDto);
            }
        }

        // access drugOriginSpecification
        Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationSimpleVo>
                drugOriginSpecificationVoMap =
                        drugOriginSpecificationSimpleVoConverter
                                .convertToDrugOriginSpecificationSimpleVoMap(
                                        drugOriginSpecificationList);
        dataHolder.drugOriginSpecification =
                drugOriginSpecificationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> drugOriginSpecificationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
