package com.pulse.drug_dictionary.service.converter;

import com.pulse.drug_dictionary.manager.dto.DrugOriginTenderBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "c1de265c-a816-3b20-89ef-0c3a1979e24e")
public class DrugOriginTenderBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugOriginTenderBaseDto> DrugOriginTenderBaseDtoConverter(
            List<DrugOriginTenderBaseDto> drugOriginTenderBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugOriginTenderBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
