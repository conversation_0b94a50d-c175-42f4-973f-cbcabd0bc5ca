package com.pulse.drug_dictionary.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "9ed99995-d0bf-4df8-9dcd-2348e052f896|BTO|DEFINITION")
public class InvalidDrugOriginBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 药品编码 */
    @AutoGenerated(locked = true, uuid = "60af52be-16f5-4c0d-a80c-3ab78de2c0a7")
    private String drugCode;

    @Valid
    @AutoGenerated(locked = true, uuid = "1cf28e32-39c8-49e9-9610-62bb90b2234d")
    private List<InvalidDrugOriginBto.DrugSpecificationDictionaryBto>
            drugSpecificationDictionaryBtoList;

    @AutoGenerated(locked = true)
    public void setDrugCode(String drugCode) {
        this.__$validPropertySet.add("drugCode");
        this.drugCode = drugCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugSpecificationDictionaryBtoList(
            List<InvalidDrugOriginBto.DrugSpecificationDictionaryBto>
                    drugSpecificationDictionaryBtoList) {
        this.__$validPropertySet.add("drugSpecificationDictionaryBtoList");
        this.drugSpecificationDictionaryBtoList = drugSpecificationDictionaryBtoList;
    }

    /**
     * <b>[源自]</b> DrugOrigin
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class DrugOriginBto {
        /** 药品产地 */
        @AutoGenerated(locked = true, uuid = "9512b4d7-fe88-4cfb-928f-5404bf9a5817")
        private String drugOriginCode;

        /** 作废标志 */
        @AutoGenerated(locked = true, uuid = "cabb7b3e-bb99-42d2-bc8f-20db6ca0150f")
        private Boolean invalidFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setDrugOriginCode(String drugOriginCode) {
            this.__$validPropertySet.add("drugOriginCode");
            this.drugOriginCode = drugOriginCode;
        }

        @AutoGenerated(locked = true)
        public void setInvalidFlag(Boolean invalidFlag) {
            this.__$validPropertySet.add("invalidFlag");
            this.invalidFlag = invalidFlag;
        }
    }

    /**
     * <b>[源自]</b> DrugSpecificationDictionary
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class DrugSpecificationDictionaryBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "1f550b1b-a0ad-44d0-bd79-152b862e1ebb")
        private String id;

        @Valid
        @AutoGenerated(locked = true, uuid = "d7b7429b-9580-40ac-849c-2efcd35a7685")
        private List<InvalidDrugOriginBto.DrugOriginBto> drugOriginBtoList;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginBtoList(
                List<InvalidDrugOriginBto.DrugOriginBto> drugOriginBtoList) {
            this.__$validPropertySet.add("drugOriginBtoList");
            this.drugOriginBtoList = drugOriginBtoList;
        }
    }
}
