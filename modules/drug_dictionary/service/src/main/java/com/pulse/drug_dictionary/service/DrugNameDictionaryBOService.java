package com.pulse.drug_dictionary.service;

import com.pulse.drug_dictionary.manager.bo.*;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.persist.dos.DrugNameDictionary;
import com.pulse.drug_dictionary.service.base.BaseDrugNameDictionaryBOService;
import com.pulse.drug_dictionary.service.bto.DeleteAliasBto;
import com.pulse.drug_dictionary.service.bto.MergeAliasBto;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "3bb61ac4-c503-422c-bfe9-b3fbe10fb7a5|BO|SERVICE")
public class DrugNameDictionaryBOService extends BaseDrugNameDictionaryBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugNameDictionaryBaseDtoService drugNameDictionaryBaseDtoService;

    /** 功能：药品基本信息维护-保存别名 */
    @PublicInterface(id = "831d1e9e-64c2-432b-ba6e-968588fcb5ad", module = "drug_dictionary")
    @Transactional
    @AutoGenerated(locked = false, uuid = "4a548380-8847-4171-8c05-a35505138d62")
    public String mergeAlias(@Valid @NotNull MergeAliasBto mergeAliasBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugNameDictionaryBaseDto drugNameDictionaryBaseDto = null;
        if (mergeAliasBto.getId() != null) {
            drugNameDictionaryBaseDto =
                    drugNameDictionaryBaseDtoService.getById(mergeAliasBto.getId());
        }
        MergeAliasBoResult boResult = super.mergeAliasBase(mergeAliasBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeAliasBto */
        {
            MergeAliasBto bto =
                    boResult.<MergeAliasBto>getBtoOfType(MergeAliasBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeAliasBto, DrugNameDictionary, DrugNameDictionaryBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeAliasBto, DrugNameDictionaryBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugNameDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugNameDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DrugNameDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 功能：药品基本信息维护-删除别名 */
    @PublicInterface(id = "2eea6700-6be6-4c76-9a58-df164f8a31a0", module = "drug_dictionary")
    @Transactional
    @AutoGenerated(locked = false, uuid = "8e3bb2c5-a65e-4789-a2a2-462636180ff5")
    public String deleteAlias(@Valid @NotNull DeleteAliasBto deleteAliasBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugNameDictionaryBaseDto drugNameDictionaryBaseDto =
                drugNameDictionaryBaseDtoService.getById(deleteAliasBto.getId());
        DeleteAliasBoResult boResult = super.deleteAliasBase(deleteAliasBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteAliasBto */
        {
            DeleteAliasBto bto =
                    boResult.<DeleteAliasBto>getBtoOfType(DeleteAliasBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteAliasBto, DrugNameDictionary> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
