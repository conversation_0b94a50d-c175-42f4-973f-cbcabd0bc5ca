package com.pulse.drug_dictionary.service;

import com.pulse.drug_dictionary.manager.bo.*;
import com.pulse.drug_dictionary.manager.dto.DrugOriginTenderBaseDto;
import com.pulse.drug_dictionary.persist.dos.DrugOriginTender;
import com.pulse.drug_dictionary.service.base.BaseDrugOriginTenderBOService;
import com.pulse.drug_dictionary.service.bto.DeleteTenderBto;
import com.pulse.drug_dictionary.service.bto.SaveDrugTenderBto;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "0217b4b2-0c25-4505-ab92-bc8eb4698953|BO|SERVICE")
public class DrugOriginTenderBOService extends BaseDrugOriginTenderBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginTenderBaseDtoService drugOriginTenderBaseDtoService;

    /** 功能：招标单位维护-保存 */
    @PublicInterface(id = "caba7f2a-db6a-419e-bfa3-b7f4c0b0fe41", module = "drug_dictionary")
    @Transactional
    @AutoGenerated(locked = false, uuid = "0cd732e6-a0a7-4b37-b4cd-2ccd64489341")
    public String saveDrugTender(@Valid @NotNull SaveDrugTenderBto saveDrugTenderBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginTenderBaseDto drugOriginTenderBaseDto = null;
        if (saveDrugTenderBto.getId() != null) {
            drugOriginTenderBaseDto =
                    drugOriginTenderBaseDtoService.getById(saveDrugTenderBto.getId());
        }
        SaveDrugTenderBoResult boResult = super.saveDrugTenderBase(saveDrugTenderBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveDrugTenderBto */
        {
            SaveDrugTenderBto bto =
                    boResult.<SaveDrugTenderBto>getBtoOfType(SaveDrugTenderBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<SaveDrugTenderBto, DrugOriginTender, DrugOriginTenderBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveDrugTenderBto, DrugOriginTenderBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugOriginTenderBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugOriginTender entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DrugOriginTenderBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 功能：招标单位维护-删除招标单位 */
    @PublicInterface(id = "02d62cbc-5448-4de0-9a87-6da1305a3234", module = "drug_dictionary")
    @Transactional
    @AutoGenerated(locked = false, uuid = "d4bce62a-9764-4897-9e1e-a94aa3c19c57")
    public String deleteTender(@Valid @NotNull DeleteTenderBto deleteTenderBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugOriginTenderBaseDto drugOriginTenderBaseDto =
                drugOriginTenderBaseDtoService.getById(deleteTenderBto.getId());
        DeleteTenderBoResult boResult = super.deleteTenderBase(deleteTenderBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteTenderBto */
        {
            DeleteTenderBto bto =
                    boResult.<DeleteTenderBto>getBtoOfType(DeleteTenderBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteTenderBto, DrugOriginTender> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
