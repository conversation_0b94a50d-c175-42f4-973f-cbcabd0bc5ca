package com.pulse.drug_dictionary.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "dbb8f118-77ec-4128-93e8-95713002d299|BTO|DEFINITION")
public class DeleteDrugSpecificationDetailBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 药品编码 */
    @AutoGenerated(locked = true, uuid = "0d63d2f2-b1d9-4fcd-ae67-e2e8232469c0")
    private String drugCode;

    @Valid
    @AutoGenerated(locked = true, uuid = "4e430cd1-ed61-410b-a7c1-1bcfc79504c8")
    private List<DeleteDrugSpecificationDetailBto.DrugSpecificationDictionaryBto>
            drugSpecificationDictionaryBtoList;

    @AutoGenerated(locked = true)
    public void setDrugCode(String drugCode) {
        this.__$validPropertySet.add("drugCode");
        this.drugCode = drugCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugSpecificationDictionaryBtoList(
            List<DeleteDrugSpecificationDetailBto.DrugSpecificationDictionaryBto>
                    drugSpecificationDictionaryBtoList) {
        this.__$validPropertySet.add("drugSpecificationDictionaryBtoList");
        this.drugSpecificationDictionaryBtoList = drugSpecificationDictionaryBtoList;
    }

    /**
     * <b>[源自]</b> DrugSpecificationDetail
     *
     * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class DrugSpecificationDetailBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "bb01aeae-d8e7-4eb0-a397-f65844dd2f25")
        private String id;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }
    }

    /**
     * <b>[源自]</b> DrugSpecificationDictionary
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class DrugSpecificationDictionaryBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "c91cd052-6ea1-416f-85ca-1db7d2a7e395")
        private String id;

        @Valid
        @AutoGenerated(locked = true, uuid = "dd2facb3-6ef5-4d35-84ee-4761b7fc5e3e")
        private List<DeleteDrugSpecificationDetailBto.DrugSpecificationDetailBto>
                drugSpecificationDetailBtoList;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setDrugSpecificationDetailBtoList(
                List<DeleteDrugSpecificationDetailBto.DrugSpecificationDetailBto>
                        drugSpecificationDetailBtoList) {
            this.__$validPropertySet.add("drugSpecificationDetailBtoList");
            this.drugSpecificationDetailBtoList = drugSpecificationDetailBtoList;
        }
    }
}
