package com.pulse.drug_dictionary.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryWithCatalogDto;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginForDetailDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithOriginDetailDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryWithDrugDto;
import com.pulse.drug_dictionary.service.DrugOriginSpecificationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** DrugOriginSpecificationWithOriginDetailDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "28abdb55-0302-394a-981f-20b1f620b215")
public class DrugOriginSpecificationWithOriginDetailDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoService drugOriginSpecificationBaseDtoService;

    /** 批量自定义组装DrugOriginSpecificationWithOriginDetailDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "38100668-b571-3926-b785-e1c5b7a86a4d")
    public void assembleDataCustomized(List<DrugOriginSpecificationWithOriginDetailDto> dataList) {
        // 自定义数据组装

    }

    /** 组装drugOrigin2DrugSpecification数据 */
    @AutoGenerated(locked = true, uuid = "3defd363-c492-3b9c-92c6-9e4e37dfd7fe")
    private void assembleDrugOrigin2DrugSpecificationData(
            DrugOriginSpecificationWithOriginDetailDtoDataAssembler
                            .DrugOriginSpecificationWithOriginDetailDtoDataHolder
                    dataHolder) {
        Map<String, Pair<DrugDictionaryBaseDto, DrugDictionaryWithCatalogDto>>
                drugOrigin2DrugSpecification2Drug =
                        dataHolder.drugOrigin2DrugSpecification2Drug.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getDrugCode(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification2Drug
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryWithDrugDto>
                drugOrigin2DrugSpecification : dataHolder.drugOrigin2DrugSpecification.entrySet()) {
            DrugSpecificationDictionaryWithDrugDto dto = drugOrigin2DrugSpecification.getValue();
            dto.setDrug(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2Drug.get(
                                            drugOrigin2DrugSpecification.getKey().getDrugCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
        assembleDrugOrigin2DrugSpecification2DrugData(dataHolder);
    }

    /** 组装drugOrigin2DrugSpecification2Drug数据 */
    @AutoGenerated(locked = true, uuid = "562c0acb-0ea5-3bb9-bd0f-5dba64ffbad3")
    private void assembleDrugOrigin2DrugSpecification2DrugData(
            DrugOriginSpecificationWithOriginDetailDtoDataAssembler
                            .DrugOriginSpecificationWithOriginDetailDtoDataHolder
                    dataHolder) {
        Map<String, DrugCategoryBaseDto> drugOrigin2DrugSpecification2Drug2DrugCatalog =
                dataHolder.drugOrigin2DrugSpecification2Drug2DrugCatalog.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCategoryBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        for (Map.Entry<DrugDictionaryBaseDto, DrugDictionaryWithCatalogDto>
                drugOrigin2DrugSpecification2Drug :
                        dataHolder.drugOrigin2DrugSpecification2Drug.entrySet()) {
            DrugDictionaryWithCatalogDto dto = drugOrigin2DrugSpecification2Drug.getValue();
            dto.setDrugCatalog(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification2Drug2DrugCatalog.get(
                                            drugOrigin2DrugSpecification2Drug
                                                    .getKey()
                                                    .getDrugCatalogId()))
                            .orElse(null));
        }
    }

    /** 组装DrugOriginSpecificationWithOriginDetailDto数据 */
    @AutoGenerated(locked = true, uuid = "8af3ffd2-cab2-34f6-96e2-651cd14448e3")
    public void assembleData(
            List<DrugOriginSpecificationWithOriginDetailDto> dtoList,
            DrugOriginSpecificationWithOriginDetailDtoDataAssembler
                            .DrugOriginSpecificationWithOriginDetailDtoDataHolder
                    dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, DrugOriginSpecificationBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugOriginSpecificationBaseDto::getId,
                                        Function.identity()));

        Map<String, Pair<DrugOriginBaseDto, DrugOriginForDetailDto>> drugOrigin =
                dataHolder.drugOrigin.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getDrugOriginCode(),
                                        dto -> Pair.of(dto, dataHolder.drugOrigin.get(dto))));

        for (DrugOriginSpecificationWithOriginDetailDto dto : dtoList) {
            dto.setDrugOrigin(
                    Optional.ofNullable(
                                    drugOrigin.get(baseDtoMap.get(dto.getId()).getDrugOriginCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDrugOriginData(dataHolder);

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 组装drugOrigin数据 */
    @AutoGenerated(locked = true, uuid = "f8599bd5-7380-3a80-881d-a9c0a3ce6105")
    private void assembleDrugOriginData(
            DrugOriginSpecificationWithOriginDetailDtoDataAssembler
                            .DrugOriginSpecificationWithOriginDetailDtoDataHolder
                    dataHolder) {
        Map<
                        String,
                        Pair<
                                DrugSpecificationDictionaryBaseDto,
                                DrugSpecificationDictionaryWithDrugDto>>
                drugOrigin2DrugSpecification =
                        dataHolder.drugOrigin2DrugSpecification.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .drugOrigin2DrugSpecification
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<String, DrugProducerDictionaryBaseDto> drugOrigin2DrugProducer =
                dataHolder.drugOrigin2DrugProducer.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugProducerDictionaryBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        Map<String, DrugOriginExtensionBaseDto> drugOrigin2DrugOriginExtension =
                dataHolder.drugOrigin2DrugOriginExtension.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugOriginExtensionBaseDto::getDrugOriginCode,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        Map<String, List<DrugNameDictionaryBaseDto>> drugOrigin2DrugNameDictionaryList =
                dataHolder.drugOrigin2DrugNameDictionaryList.stream()
                        .collect(
                                Collectors.groupingBy(
                                        DrugNameDictionaryBaseDto::getDrugOriginCode));
        for (Map.Entry<DrugOriginBaseDto, DrugOriginForDetailDto> drugOrigin :
                dataHolder.drugOrigin.entrySet()) {
            DrugOriginForDetailDto dto = drugOrigin.getValue();
            dto.setDrugSpecification(
                    Optional.ofNullable(
                                    drugOrigin2DrugSpecification.get(
                                            drugOrigin.getKey().getDrugSpecificationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            dto.setDrugProducer(
                    Optional.ofNullable(
                                    drugOrigin2DrugProducer.get(
                                            drugOrigin.getKey().getDrugProducerId()))
                            .orElse(null));
            dto.setDrugOriginExtension(
                    Optional.ofNullable(drugOrigin2DrugOriginExtension.get(dto.getDrugOriginCode()))
                            .orElse(null));
            dto.setDrugNameDictionaryList(
                    Optional.ofNullable(
                                    drugOrigin2DrugNameDictionaryList.get(dto.getDrugOriginCode()))
                            .orElse(new ArrayList<>()));
        }
        assembleDrugOrigin2DrugSpecificationData(dataHolder);
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class DrugOriginSpecificationWithOriginDetailDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugOriginSpecificationBaseDto> rootBaseDtoList;

        /** 持有dto字段drugCatalog的Dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugCategoryBaseDto> drugOrigin2DrugSpecification2Drug2DrugCatalog;

        /** 持有dto字段drug的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugDictionaryBaseDto, DrugDictionaryWithCatalogDto>
                drugOrigin2DrugSpecification2Drug;

        /** 持有dto字段drugSpecification的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryWithDrugDto>
                drugOrigin2DrugSpecification;

        /** 持有dto字段drugProducer的Dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugProducerDictionaryBaseDto> drugOrigin2DrugProducer;

        /** 持有dto字段drugOriginExtension的Dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugOriginExtensionBaseDto> drugOrigin2DrugOriginExtension;

        /** 持有dto字段drugNameDictionaryList的Dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugNameDictionaryBaseDto> drugOrigin2DrugNameDictionaryList;

        /** 持有dto字段drugOrigin的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginBaseDto, DrugOriginForDetailDto> drugOrigin;
    }
}
