package com.pulse.drug_dictionary.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> DrugSupplierQualityEvaluation
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "fac536cf-72bb-4c61-b2bd-c6e61d3cf51b|BTO|DEFINITION")
public class CancelDrugSupplierQualityEvaluationBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "20bb00f0-92d5-4d1d-99bf-120997565a79")
    private String id;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "3d6ce78d-5f47-4f0d-a6d3-916e364e43ff")
    private Boolean invalidFlag;

    /** 作废人 */
    @AutoGenerated(locked = true, uuid = "8bcb4de6-ed27-4f2f-b029-4cb8ec419545")
    private String invalidReason;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInvalidFlag(Boolean invalidFlag) {
        this.__$validPropertySet.add("invalidFlag");
        this.invalidFlag = invalidFlag;
    }

    @AutoGenerated(locked = true)
    public void setInvalidReason(String invalidReason) {
        this.__$validPropertySet.add("invalidReason");
        this.invalidReason = invalidReason;
    }
}
