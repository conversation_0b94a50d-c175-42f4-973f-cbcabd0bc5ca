package com.pulse.drug_dictionary.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.OriginTypeEnum;
import com.pulse.drug_dictionary.common.enums.ProducerDrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "d5e7d99e-dfb0-4ef5-8318-fcc10736d538|DTO|DEFINITION")
public class DrugProducerDictionaryBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9a912dd7-97fd-4fd9-b8d5-9d3530ae3502")
    private Date createdAt;

    /** 创建者id */
    @AutoGenerated(locked = true, uuid = "ee79936a-6958-4eef-924f-e98bb78028f8")
    private String createdBy;

    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "c088e9b2-be46-4776-9beb-ca541f09cb1d")
    private ProducerDrugTypeEnum drugType;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "3c40341a-17be-40e4-9239-ed86b2a7a4bd")
    private Boolean enableFlag;

    /** 外配标志 */
    @AutoGenerated(locked = true, uuid = "d51795f2-fea7-47fa-a767-cfa3ae6e0c65")
    private Boolean externalFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "bd8a031e-839e-4000-a313-d666a531d641")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d20e8b3c-794f-4c26-a2e9-548c969ddfc1")
    private InputCodeEo inputCode;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "fb5e9635-f28e-43b0-8f0d-d1d51b6c8d71")
    private Long lockVersion;

    /** 生产商名称 */
    @AutoGenerated(locked = true, uuid = "ef6ec17a-b46c-4a85-af53-ea723cff437b")
    private String producerName;

    /** 生产商简称 */
    @AutoGenerated(locked = true, uuid = "68d7cfd7-c2d7-46f7-87d4-a8968ed00a97")
    private String producerNameAlias;

    /** 生产商类别 */
    @AutoGenerated(locked = true, uuid = "1610ea74-f573-4797-9ab0-6d5d3530eaa7")
    private OriginTypeEnum producerType;

    /** 省份编码 */
    @AutoGenerated(locked = true, uuid = "d7f17068-0f80-4de1-b725-3355fdedadd3")
    private String provinceCode;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "6f9255c4-9068-48d2-ac40-defeecb046ec")
    private String remark;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "343ff760-caf7-4836-8c81-2d4fa1963337")
    private String standardCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "276bc5fd-58de-42df-ba12-70d347bb771d")
    private Date updatedAt;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "56452051-f1f7-4756-b44b-81b362b0787b")
    private String updatedBy;
}
