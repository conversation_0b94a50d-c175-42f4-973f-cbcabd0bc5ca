package com.pulse.drug_dictionary.manager.bo.base;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.converter.InputCodeEoConverter;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.drug_dictionary.common.enums.SourceTypeEnum;
import com.pulse.drug_dictionary.manager.bo.DrugDictionaryBO;
import com.pulse.drug_dictionary.manager.bo.DrugDictionaryExtensionBO;
import com.pulse.drug_dictionary.manager.bo.DrugSpecificationDictionaryBO;
import com.pulse.drug_dictionary.persist.dos.DrugDictionary;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.Valid;

@DoNotModify
@Table(name = "drug_dictionary")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "5ca5242d-bf75-3949-b746-8c2618fb48a2")
public abstract class BaseDrugDictionaryBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 通用名 */
    @Column(name = "common_name_code")
    @AutoGenerated(locked = true, uuid = "0200e3fc-c123-4a59-af59-4308a3c12c79")
    private String commonNameCode;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "04b5360a-9829-5196-ad81-1932aff4f0cb")
    private Date createdAt;

    /** 创建人id */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "740613f6-b4f8-48f7-859c-df4b4ece9b49")
    private String createdBy;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "bf14e8e3-3bd2-5a2e-b6a4-b1218fb865d7")
    private Long deletedAt = 0L;

    /** 药品分类ID */
    @Column(name = "drug_catalog_id")
    @AutoGenerated(locked = true, uuid = "12d24571-b74c-44f4-a528-0c450b180d85")
    private String drugCatalogId;

    /** 药品编码 */
    @Column(name = "drug_code")
    @AutoGenerated(locked = true, uuid = "a6819dec-70ac-47a1-8b25-ef6f30993dec")
    @Id
    private String drugCode;

    @JoinColumn(name = "drug_code", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugDictionaryExtensionBO> drugDictionaryExtensionBOSet = new HashSet<>();

    /** 药品名称 */
    @Column(name = "drug_name")
    @AutoGenerated(locked = true, uuid = "1ea03a9d-2301-4a40-9d20-e02eb8248552")
    private String drugName;

    /** 药物根目录ID */
    @Column(name = "drug_root_catalog_id")
    @AutoGenerated(locked = true, uuid = "e6a7ed45-13cd-4f6d-8a30-020184b11ba7")
    private String drugRootCatalogId;

    @JoinColumn(name = "drug_code", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugSpecificationDictionaryBO> drugSpecificationDictionaryBOSet = new HashSet<>();

    /** 药品类别 西药、中草药、中成药、其它 */
    @Column(name = "drug_type")
    @AutoGenerated(locked = true, uuid = "d980127b-bce4-4abf-9471-ea3da472cf12")
    @Enumerated(EnumType.STRING)
    private DrugTypeEnum drugType;

    /** 英文名 */
    @Column(name = "english_name")
    @AutoGenerated(locked = true, uuid = "48604864-f602-4699-9e10-4fd73479326d")
    private String englishName;

    /** 中药类型 */
    @Column(name = "herb_type")
    @AutoGenerated(locked = true, uuid = "07f6b57e-330b-4a74-850f-69d889872ad4")
    private String herbType;

    /** 输入码 */
    @Column(name = "input_code")
    @Valid
    @AutoGenerated(locked = true, uuid = "3c1dd65c-2185-40e6-b403-daf32d0a9b5d")
    @Convert(converter = InputCodeEoConverter.class)
    private InputCodeEo inputCode;

    /** 机构id */
    @Column(name = "institution_id")
    @AutoGenerated(locked = true, uuid = "5287c2d8-bfce-43fc-9c62-a00bbb40d626")
    private String institutionId;

    /** 作废标识 */
    @Column(name = "invalid_flag")
    @AutoGenerated(locked = true, uuid = "c4c3a35d-a6ca-4c0a-8b6c-178af51b1bf9")
    private Boolean invalidFlag;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "44cd9f66-c640-4803-a751-cff2a8d6bcdc")
    @Version
    private Long lockVersion;

    /** 药理根类 码表可支持自定义 */
    @Column(name = "parent_pharmacological_type")
    @AutoGenerated(locked = true, uuid = "27f0a930-dadc-4853-a3b5-7b821310b82e")
    private String parentPharmacologicalType;

    /** 药理类别 码表可支持自定义 */
    @Column(name = "pharmacological_type")
    @AutoGenerated(locked = true, uuid = "6b7ea63f-a8b3-4af3-bb14-e9bf75a5666f")
    private String pharmacologicalType;

    /** 来源 医院药品，药店药品，试验药品 */
    @Column(name = "source_type")
    @AutoGenerated(locked = true, uuid = "dd30c164-0f9a-4350-918c-093aa45379b8")
    @Enumerated(EnumType.STRING)
    private SourceTypeEnum sourceType;

    /** 标准编码 */
    @Column(name = "standard_code")
    @AutoGenerated(locked = true, uuid = "1a05dbeb-c9f6-4b5a-b28d-11035f31b81a")
    private String standardCode;

    /** 毒理类别 */
    @Column(name = "toxic_type")
    @AutoGenerated(locked = true, uuid = "f3142ebe-9906-47a2-a39e-d4ea86e33e5a")
    private String toxicType;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "09af0cf5-0553-5701-9900-5746cffd80e5")
    private Date updatedAt;

    /** 修改人id */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "c0d55880-d353-4c24-81ce-501dfbe5bd7c")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public DrugDictionary convertToDrugDictionary() {
        DrugDictionary entity = new DrugDictionary();
        BoUtil.copyProperties(
                this,
                entity,
                "drugCode",
                "drugName",
                "invalidFlag",
                "commonNameCode",
                "parentPharmacologicalType",
                "pharmacologicalType",
                "drugType",
                "toxicType",
                "sourceType",
                "herbType",
                "inputCode",
                "createdBy",
                "updatedBy",
                "institutionId",
                "drugCatalogId",
                "drugRootCatalogId",
                "standardCode",
                "englishName",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static DrugDictionaryBO getByDrugCode(String drugCode) {
        Session session = TransactionalSessionFactory.getSession();
        DrugDictionaryBO drugDictionary =
                (DrugDictionaryBO)
                        session.createQuery(
                                        "from DrugDictionaryBO where " + "drugCode =: drugCode ")
                                .setParameter("drugCode", drugCode)
                                .uniqueResult();
        return drugDictionary;
    }

    @AutoGenerated(locked = true)
    public String getCommonNameCode() {
        return this.commonNameCode;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDrugCatalogId() {
        return this.drugCatalogId;
    }

    @AutoGenerated(locked = true)
    public String getDrugCode() {
        return this.drugCode;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryExtensionBO getDrugDictionaryExtensionBO() {
        return this.drugDictionaryExtensionBOSet.isEmpty()
                ? null
                : this.drugDictionaryExtensionBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<DrugDictionaryExtensionBO> getDrugDictionaryExtensionBOSet() {
        return this.drugDictionaryExtensionBOSet;
    }

    @AutoGenerated(locked = true)
    public String getDrugName() {
        return this.drugName;
    }

    @AutoGenerated(locked = true)
    public String getDrugRootCatalogId() {
        return this.drugRootCatalogId;
    }

    @AutoGenerated(locked = true)
    public Set<DrugSpecificationDictionaryBO> getDrugSpecificationDictionaryBOSet() {
        return this.drugSpecificationDictionaryBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugTypeEnum getDrugType() {
        return this.drugType;
    }

    @AutoGenerated(locked = true)
    public String getEnglishName() {
        return this.englishName;
    }

    @AutoGenerated(locked = true)
    public String getHerbType() {
        return this.herbType;
    }

    @AutoGenerated(locked = true)
    public InputCodeEo getInputCode() {
        return this.inputCode;
    }

    @AutoGenerated(locked = true)
    public String getInstitutionId() {
        return this.institutionId;
    }

    @AutoGenerated(locked = true)
    public Boolean getInvalidFlag() {
        return this.invalidFlag;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryExtensionBO getOrCreateDrugDictionaryExtensionBO() {
        if (this.getDrugDictionaryExtensionBO() == null) {
            DrugDictionaryExtensionBO obj = new DrugDictionaryExtensionBO();
            obj.setDrugDictionaryBO((DrugDictionaryBO) this);
            setDrugDictionaryExtensionBO(obj);
            return obj;
        } else {
            return this.getDrugDictionaryExtensionBO();
        }
    }

    @AutoGenerated(locked = true)
    public String getParentPharmacologicalType() {
        return this.parentPharmacologicalType;
    }

    @AutoGenerated(locked = true)
    public String getPharmacologicalType() {
        return this.pharmacologicalType;
    }

    @AutoGenerated(locked = true)
    public SourceTypeEnum getSourceType() {
        return this.sourceType;
    }

    @AutoGenerated(locked = true)
    public String getStandardCode() {
        return this.standardCode;
    }

    @AutoGenerated(locked = true)
    public String getToxicType() {
        return this.toxicType;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setCommonNameCode(String commonNameCode) {
        this.commonNameCode = commonNameCode;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDrugCatalogId(String drugCatalogId) {
        this.drugCatalogId = drugCatalogId;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDrugCode(String drugCode) {
        this.drugCode = drugCode;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDrugDictionaryExtensionBO(
            DrugDictionaryExtensionBO drugDictionaryExtensionBO) {
        if (this.drugDictionaryExtensionBOSet.size() > 0) {
            this.drugDictionaryExtensionBOSet.clear();
        }
        if (drugDictionaryExtensionBO != null) {
            this.drugDictionaryExtensionBOSet.add(drugDictionaryExtensionBO);
        }
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugDictionaryExtensionBOSet(
            Set<DrugDictionaryExtensionBO> drugDictionaryExtensionBOSet) {
        this.drugDictionaryExtensionBOSet = drugDictionaryExtensionBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDrugName(String drugName) {
        this.drugName = drugName;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDrugRootCatalogId(String drugRootCatalogId) {
        this.drugRootCatalogId = drugRootCatalogId;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugSpecificationDictionaryBOSet(
            Set<DrugSpecificationDictionaryBO> drugSpecificationDictionaryBOSet) {
        this.drugSpecificationDictionaryBOSet = drugSpecificationDictionaryBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setDrugType(DrugTypeEnum drugType) {
        this.drugType = drugType;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setEnglishName(String englishName) {
        this.englishName = englishName;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setHerbType(String herbType) {
        this.herbType = herbType;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setInputCode(InputCodeEo inputCode) {
        this.inputCode = inputCode;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setInstitutionId(String institutionId) {
        this.institutionId = institutionId;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setInvalidFlag(Boolean invalidFlag) {
        this.invalidFlag = invalidFlag;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setParentPharmacologicalType(String parentPharmacologicalType) {
        this.parentPharmacologicalType = parentPharmacologicalType;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setPharmacologicalType(String pharmacologicalType) {
        this.pharmacologicalType = pharmacologicalType;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setSourceType(SourceTypeEnum sourceType) {
        this.sourceType = sourceType;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setStandardCode(String standardCode) {
        this.standardCode = standardCode;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setToxicType(String toxicType) {
        this.toxicType = toxicType;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (DrugDictionaryBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
