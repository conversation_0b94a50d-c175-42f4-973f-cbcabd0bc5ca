package com.pulse.drug_dictionary.manager;

import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationForInventorySpecificationDto;
import com.pulse.drug_dictionary.persist.dos.DrugOriginSpecification;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "088bc30d-0cf0-4dbc-8449-bc34c43f9f44|DTO|MANAGER")
public interface DrugOriginSpecificationForInventorySpecificationDtoManager {

    @AutoGenerated(locked = true, uuid = "3b4965c7-0fb5-3085-8ecc-1b0cc2695124")
    List<DrugOriginSpecificationForInventorySpecificationDto> getByDrugOriginCode(
            String drugOriginCode);

    @AutoGenerated(locked = true, uuid = "3bcb6a20-f26e-3d3d-a594-b80d9713c1e1")
    List<DrugOriginSpecificationForInventorySpecificationDto> getByDrugSpecificationDetailId(
            String drugSpecificationDetailId);

    @AutoGenerated(locked = true, uuid = "4774b9f6-10f5-3e12-93c2-2472843c133c")
    List<DrugOriginSpecificationForInventorySpecificationDto> getByDrugSpecificationDetailIds(
            List<String> drugSpecificationDetailId);

    @AutoGenerated(locked = true, uuid = "7c7e389b-410d-3bf7-a3fe-463dcefb8dbc")
    List<DrugOriginSpecificationForInventorySpecificationDto> getByDrugOriginCodes(
            List<String> drugOriginCode);

    @AutoGenerated(locked = true, uuid = "8e060f0c-ebb2-3b8b-9d3f-db37eb33faf7")
    DrugOriginSpecificationForInventorySpecificationDto getById(String id);

    @AutoGenerated(locked = true, uuid = "b397cd3a-5f11-3b7d-a690-afb547f69700")
    List<DrugOriginSpecificationForInventorySpecificationDto>
            getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                    List<
                                    DrugOriginSpecification
                                            .AmountPerPackageAndDrugOriginCodeAndDrugSpecificationAndSpecificationTypeAndUnit>
                            var);

    @AutoGenerated(locked = true, uuid = "c996ccf8-cdb9-3d37-9aea-9ee136da5140")
    DrugOriginSpecificationForInventorySpecificationDto
            getByDrugOriginCodeAndSpecificationTypeAndDrugSpecificationAndUnitAndAmountPerPackage(
                    DrugOriginSpecification
                                    .AmountPerPackageAndDrugOriginCodeAndDrugSpecificationAndSpecificationTypeAndUnit
                            var);

    @AutoGenerated(locked = true, uuid = "ffc972ef-2a4a-3f41-9896-************")
    List<DrugOriginSpecificationForInventorySpecificationDto> getByIds(List<String> id);
}
