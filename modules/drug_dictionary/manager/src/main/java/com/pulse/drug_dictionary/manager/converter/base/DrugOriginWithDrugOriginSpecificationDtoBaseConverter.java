package com.pulse.drug_dictionary.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.DrugOriginBaseDtoManager;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginWithDrugOriginSpecificationDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "6639e97c-e2b7-4de0-b658-3031019e3b32|DTO|BASE_CONVERTER")
public class DrugOriginWithDrugOriginSpecificationDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseDtoManager drugOriginBaseDtoManager;

    @AutoGenerated(locked = true)
    public DrugOriginWithDrugOriginSpecificationDto
            convertFromDrugOriginBaseDtoToDrugOriginWithDrugOriginSpecificationDto(
                    DrugOriginBaseDto drugOriginBaseDto) {
        return convertFromDrugOriginBaseDtoToDrugOriginWithDrugOriginSpecificationDto(
                        List.of(drugOriginBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<DrugOriginWithDrugOriginSpecificationDto>
            convertFromDrugOriginBaseDtoToDrugOriginWithDrugOriginSpecificationDto(
                    List<DrugOriginBaseDto> drugOriginBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginBaseDtoList)) {
            return new ArrayList<>();
        }
        List<DrugOriginWithDrugOriginSpecificationDto>
                drugOriginWithDrugOriginSpecificationDtoList = new ArrayList<>();
        for (DrugOriginBaseDto drugOriginBaseDto : drugOriginBaseDtoList) {
            if (drugOriginBaseDto == null) {
                continue;
            }
            DrugOriginWithDrugOriginSpecificationDto drugOriginWithDrugOriginSpecificationDto =
                    new DrugOriginWithDrugOriginSpecificationDto();
            drugOriginWithDrugOriginSpecificationDto.setDeletedAt(drugOriginBaseDto.getDeletedAt());
            drugOriginWithDrugOriginSpecificationDto.setDrugOriginCode(
                    drugOriginBaseDto.getDrugOriginCode());
            drugOriginWithDrugOriginSpecificationDto.setDrugOriginName(
                    drugOriginBaseDto.getDrugOriginName());
            drugOriginWithDrugOriginSpecificationDto.setDrugCode(drugOriginBaseDto.getDrugCode());
            drugOriginWithDrugOriginSpecificationDto.setDrugSpecificationId(
                    drugOriginBaseDto.getDrugSpecificationId());
            drugOriginWithDrugOriginSpecificationDto.setDrugProducerId(
                    drugOriginBaseDto.getDrugProducerId());
            drugOriginWithDrugOriginSpecificationDto.setEnableFlag(
                    drugOriginBaseDto.getEnableFlag());
            drugOriginWithDrugOriginSpecificationDto.setPackageSpecification(
                    drugOriginBaseDto.getPackageSpecification());
            drugOriginWithDrugOriginSpecificationDto.setPackageUnit(
                    drugOriginBaseDto.getPackageUnit());
            drugOriginWithDrugOriginSpecificationDto.setAmountPerPackage(
                    drugOriginBaseDto.getAmountPerPackage());
            drugOriginWithDrugOriginSpecificationDto.setGcpFlag(drugOriginBaseDto.getGcpFlag());
            drugOriginWithDrugOriginSpecificationDto.setCentralPurchaseFlag(
                    drugOriginBaseDto.getCentralPurchaseFlag());
            drugOriginWithDrugOriginSpecificationDto.setGmpFlag(drugOriginBaseDto.getGmpFlag());
            drugOriginWithDrugOriginSpecificationDto.setPivasFlag(drugOriginBaseDto.getPivasFlag());
            drugOriginWithDrugOriginSpecificationDto.setPivasBatchPriority(
                    drugOriginBaseDto.getPivasBatchPriority());
            drugOriginWithDrugOriginSpecificationDto.setBidLostInpLimitFlag(
                    drugOriginBaseDto.getBidLostInpLimitFlag());
            drugOriginWithDrugOriginSpecificationDto.setStoreCondition(
                    drugOriginBaseDto.getStoreCondition());
            drugOriginWithDrugOriginSpecificationDto.setTraceabilityCode(
                    drugOriginBaseDto.getTraceabilityCode());
            drugOriginWithDrugOriginSpecificationDto.setPrescriptionRatio(
                    drugOriginBaseDto.getPrescriptionRatio());
            drugOriginWithDrugOriginSpecificationDto.setDonationFlag(
                    drugOriginBaseDto.getDonationFlag());
            drugOriginWithDrugOriginSpecificationDto.setFreeFlag(drugOriginBaseDto.getFreeFlag());
            drugOriginWithDrugOriginSpecificationDto.setSelfProvideFlag(
                    drugOriginBaseDto.getSelfProvideFlag());
            drugOriginWithDrugOriginSpecificationDto.setMaxDay(drugOriginBaseDto.getMaxDay());
            drugOriginWithDrugOriginSpecificationDto.setDayMaxDosage(
                    drugOriginBaseDto.getDayMaxDosage());
            drugOriginWithDrugOriginSpecificationDto.setMedicalRecordType(
                    drugOriginBaseDto.getMedicalRecordType());
            drugOriginWithDrugOriginSpecificationDto.setRemark(drugOriginBaseDto.getRemark());
            drugOriginWithDrugOriginSpecificationDto.setCreatedBy(drugOriginBaseDto.getCreatedBy());
            drugOriginWithDrugOriginSpecificationDto.setUpdatedBy(drugOriginBaseDto.getUpdatedBy());
            drugOriginWithDrugOriginSpecificationDto.setInstitutionId(
                    drugOriginBaseDto.getInstitutionId());
            drugOriginWithDrugOriginSpecificationDto.setInputCode(drugOriginBaseDto.getInputCode());
            drugOriginWithDrugOriginSpecificationDto.setCreatedAt(drugOriginBaseDto.getCreatedAt());
            drugOriginWithDrugOriginSpecificationDto.setUpdatedAt(drugOriginBaseDto.getUpdatedAt());
            drugOriginWithDrugOriginSpecificationDto.setMaxStockPrice(
                    drugOriginBaseDto.getMaxStockPrice());
            drugOriginWithDrugOriginSpecificationDto.setMaxRetailPrice(
                    drugOriginBaseDto.getMaxRetailPrice());
            drugOriginWithDrugOriginSpecificationDto.setOtcFlag(drugOriginBaseDto.getOtcFlag());
            drugOriginWithDrugOriginSpecificationDto.setOutpUsageFlag(
                    drugOriginBaseDto.getOutpUsageFlag());
            drugOriginWithDrugOriginSpecificationDto.setInpUsageFlag(
                    drugOriginBaseDto.getInpUsageFlag());
            drugOriginWithDrugOriginSpecificationDto.setInvalidFlag(
                    drugOriginBaseDto.getInvalidFlag());
            drugOriginWithDrugOriginSpecificationDto.setDrugType(drugOriginBaseDto.getDrugType());
            drugOriginWithDrugOriginSpecificationDto.setToxicType(drugOriginBaseDto.getToxicType());
            drugOriginWithDrugOriginSpecificationDto.setWinningNumber(
                    drugOriginBaseDto.getWinningNumber());
            drugOriginWithDrugOriginSpecificationDto.setRegisterBrand(
                    drugOriginBaseDto.getRegisterBrand());
            drugOriginWithDrugOriginSpecificationDto.setInternetFlage(
                    drugOriginBaseDto.getInternetFlage());
            drugOriginWithDrugOriginSpecificationDto.setAccountType(
                    drugOriginBaseDto.getAccountType());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugOriginWithDrugOriginSpecificationDtoList.add(
                    drugOriginWithDrugOriginSpecificationDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugOriginWithDrugOriginSpecificationDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public DrugOriginBaseDto convertFromDrugOriginWithDrugOriginSpecificationDtoToDrugOriginBaseDto(
            DrugOriginWithDrugOriginSpecificationDto drugOriginWithDrugOriginSpecificationDto) {
        return convertFromDrugOriginWithDrugOriginSpecificationDtoToDrugOriginBaseDto(
                        List.of(drugOriginWithDrugOriginSpecificationDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugOriginBaseDto>
            convertFromDrugOriginWithDrugOriginSpecificationDtoToDrugOriginBaseDto(
                    List<DrugOriginWithDrugOriginSpecificationDto>
                            drugOriginWithDrugOriginSpecificationDtoList) {
        if (CollectionUtil.isEmpty(drugOriginWithDrugOriginSpecificationDtoList)) {
            return new ArrayList<>();
        }
        return drugOriginBaseDtoManager.getByDrugOriginCodes(
                drugOriginWithDrugOriginSpecificationDtoList.stream()
                        .map(DrugOriginWithDrugOriginSpecificationDto::getDrugOriginCode)
                        .collect(Collectors.toList()));
    }
}
