package com.pulse.drug_dictionary.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.AntibacterialLevelEnum;
import com.pulse.drug_dictionary.common.enums.AntineoplasticLevelEnum;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.drug_dictionary.common.enums.SkinTestLevelEnum;
import com.pulse.drug_dictionary.common.enums.SkinTestTypeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "ff3b7bbb-7a8b-4050-bfeb-066e0df4bb94|DTO|DEFINITION")
public class DrugSpecificationDictionaryWithDrugDto {
    /** 帐簿类别 西药、成药、草药类型下得一个细分类型，比如：草药下面含有饮片和颗粒剂，在草药房发药时会根据饮片和颗粒剂区分为两个菜单进行发药 */
    @AutoGenerated(locked = true, uuid = "9d7c3eac-dfad-4e16-b744-7f6230d617c7")
    private String accountType;

    /** 每箱数量 */
    @AutoGenerated(locked = true, uuid = "161e277e-9a41-484a-b37c-084043e57731")
    private Long amountPerBox;

    /** 包装量 */
    @AutoGenerated(locked = true, uuid = "f9cf065f-8bff-4d09-8e35-281496cdfc76")
    private Long amountPerPackage;

    /** 麻醉用药标识 */
    @AutoGenerated(locked = true, uuid = "b45f98f0-9a49-4bdf-9774-67aa46aa325e")
    private Boolean anesthesiaFlag;

    /** 抗菌药标志 */
    @AutoGenerated(locked = true, uuid = "c958a1b6-9fff-4c1b-bd9c-e6449d3f2911")
    private Boolean antibacterialFlag;

    /** 抗菌药限级 */
    @AutoGenerated(locked = true, uuid = "287d2a07-4089-44cd-a38b-ef61eccd6ed8")
    private AntibacterialLevelEnum antibacterialLevel;

    /** 抗生素标识 */
    @AutoGenerated(locked = true, uuid = "d71bebe2-49e8-4049-a696-bf47179a77fb")
    private Boolean antibioticFlag;

    /** 抗生素级别 非限制级、限制级、特殊使用级 */
    @AutoGenerated(locked = true, uuid = "a04e7c73-5c25-4db4-8b5d-b1a368c2677e")
    private String antibioticLevel;

    /** 抗肿瘤药物级别 普通级、限制级 */
    @AutoGenerated(locked = true, uuid = "e06143b2-1b62-402e-95f6-dff8bd424361")
    private AntineoplasticLevelEnum antineoplasticLevel;

    /**
     * 基药标识 非基药、基药 （1、基药是满足基本医疗需求的药物，是用于预防、诊断和治疗最重要的疾病的药物。
     * 2、非基药是相对于基药而言的，并不是满足基本医疗需求的药物，可能用于一些特定或专科的治疗。）
     */
    @AutoGenerated(locked = true, uuid = "2c3d2111-72f7-4bd5-9c3f-e868b0056f68")
    private Boolean basicFlag;

    /** 大输液标志 医生站开立医嘱时，会通过大输液标志判断流向哪个药房 */
    @AutoGenerated(locked = true, uuid = "47575a09-e3e8-4821-a0c7-c3dfbf3c9ca7")
    private Boolean bigInfusionFlag;

    /** 化疗药品标识 是否可开化疗处方 */
    @AutoGenerated(locked = true, uuid = "29dfd3ef-45ae-46dd-adc8-4d6ef239a9ee")
    private Boolean chemotherapyFlag;

    /** 冷藏药标识 */
    @AutoGenerated(locked = true, uuid = "20ccdafb-321b-4059-bfa1-9287f42a09c5")
    private Boolean coldFlag;

    /** 浓度 */
    @AutoGenerated(locked = true, uuid = "ed04377f-c386-4a9c-b706-6a2a23f1be24")
    private BigDecimal concentration;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "30ca3366-6d20-450b-a5f1-7ee0b0c357ac")
    private Date createdAt;

    /** 日常用量 */
    @AutoGenerated(locked = true, uuid = "66cd3a97-eacb-4446-9769-faf2741b53f4")
    private BigDecimal dailyUsage;

    /** 高危药标识 */
    @AutoGenerated(locked = true, uuid = "1eca0334-a490-4a7e-8b7a-44229a989121")
    private Boolean dangerFlag;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "21802fed-7784-4140-9ccf-d05bf783a778")
    private Long deletedAt;

    /** 出院带药标识 是否可开出院带药处方 */
    @AutoGenerated(locked = true, uuid = "b2dfd20e-98a1-4938-9062-adebeea7f555")
    private Boolean dischargeFlag;

    /** 剂量 剂量 */
    @AutoGenerated(locked = true, uuid = "4d4707f7-434b-47f2-b93c-f4b1c80cf370")
    private BigDecimal dosage;

    /** 剂量单位 */
    @AutoGenerated(locked = true, uuid = "cd1cb639-0d5b-46f0-9cbf-971fc2dd41e6")
    private String dosageUnit;

    /** 药品编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ecdce56a-91de-4ea7-8341-633ef10b0106")
    private DrugDictionaryWithCatalogDto drug;

    /** 剂型 */
    @AutoGenerated(locked = true, uuid = "41731e47-ad4a-4b7b-9583-9ae568b6dcd7")
    private String drugForm;

    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "284f2f1e-a920-4080-ba47-af38ea2085f2")
    private DrugTypeEnum drugType;

    /** EN药品标识 */
    @AutoGenerated(locked = true, uuid = "67c3997c-e6eb-45bc-a9fc-5555f2943907")
    private Boolean enFlag;

    /** 每包克数 */
    @AutoGenerated(locked = true, uuid = "dfe520d4-5988-4e1b-a121-7a9424b92048")
    private BigDecimal gramsPerPackage;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "6fe9605b-1730-4f53-9520-8dc4e4af2d22")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4904bebf-0ccd-4046-b01f-6a1aa182ec1b")
    private InputCodeEo inputCode;

    /** 医保等级 */
    @AutoGenerated(locked = true, uuid = "d610cf80-b78f-4495-92cc-d7444afe7247")
    private String insuranceLevel;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "a8676ea5-0dc9-497a-83a0-b0b599db9ade")
    private Boolean invalidFlag;

    /** 标签单位 */
    @AutoGenerated(locked = true, uuid = "48d64f63-5d57-446f-a454-5fae5dd8a551")
    private String labelUnit;

    /** 管理模式 */
    @AutoGenerated(locked = true, uuid = "9cbaca1e-0828-4625-bdef-cf72dde49934")
    private ManageModelEnum manageMode;

    /** 最小规格 最小规格 */
    @AutoGenerated(locked = true, uuid = "b51c6097-cab9-44dc-a29b-3cc527a9d493")
    private String minSpecification;

    /** 最小单位 */
    @AutoGenerated(locked = true, uuid = "767a9373-503b-4f47-9027-06f7efcb58e6")
    private String minUnit;

    /** 眼科剂量单位 */
    @AutoGenerated(locked = true, uuid = "fd03b3db-5e52-4188-a3b8-9371e567630f")
    private String ophthalmicDosageUnit;

    /** 眼科剂量 */
    @AutoGenerated(locked = true, uuid = "62ff27ae-b864-444d-ba1b-310f7431afc2")
    private Long ophthalmologyDosage;

    /** 包装规格 包装规格 */
    @AutoGenerated(locked = true, uuid = "05d3f4b7-ff21-48aa-b057-2e9745993b60")
    private String packageSpecification;

    /** 包装单位 包装单位 */
    @AutoGenerated(locked = true, uuid = "ddf23c51-57b6-4dad-a5bf-90d9f144355c")
    private String packageUnit;

    /** 皮试有效天数 皮试结果有效时间 */
    @AutoGenerated(locked = true, uuid = "b5e610a8-4ac1-42fe-82eb-dba41eab6676")
    private Long skinTestEfficientDays;

    /** 皮试标志 */
    @AutoGenerated(locked = true, uuid = "e667495c-28cc-44d2-96a9-5f3453a98ae0")
    private Boolean skinTestFlag;

    /** 皮试等级 强制皮试，建议皮试 */
    @AutoGenerated(locked = true, uuid = "f46015d4-2d76-4fa4-9f4a-0470e189c90c")
    private SkinTestLevelEnum skinTestLevel;

    /** 皮试类型 原液皮试，非原液皮试，二者皆可 */
    @AutoGenerated(locked = true, uuid = "83d56ed1-b131-469d-b55c-5599f6ac74a3")
    private SkinTestTypeEnum skinTestType;

    /** 特殊剂量 */
    @AutoGenerated(locked = true, uuid = "bfa3e088-30ef-412c-9e89-1219d0c883dc")
    private BigDecimal specialDosage;

    /** 特殊剂量单位 */
    @AutoGenerated(locked = true, uuid = "21a8d198-3c4a-450d-b6b8-1ea8d0c649d7")
    private String specialDosageUnit;

    /** 药品规格名称 */
    @AutoGenerated(locked = true, uuid = "85b1c7bd-0679-415a-b9d4-82f27e6f2592")
    private String specificationDrugName;

    /** 存储条件 */
    @AutoGenerated(locked = true, uuid = "b60bbe6c-2d58-4ed9-9096-bf363a7591bc")
    private String storeCondition;

    /** 手术用药标识 */
    @AutoGenerated(locked = true, uuid = "13f348d2-bcc2-45f6-bb3c-ecd6854b87af")
    private Boolean surgeryFlag;

    /** 毒性类型 */
    @AutoGenerated(locked = true, uuid = "c25a3fb0-285a-4b82-9ce1-c434d153e82a")
    private String toxicType;

    /** TPN药品标识 */
    @AutoGenerated(locked = true, uuid = "a96a8b28-36da-4e2c-8882-b08f58a52ca8")
    private Boolean tpnFlag;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "fd93cd0a-bfa3-4a44-80e0-a7dd34241d26")
    private Date updatedAt;

    /** 体积 */
    @AutoGenerated(locked = true, uuid = "cf65fc2f-0df9-4eaf-b3ee-36d6f5010dbf")
    private BigDecimal volume;

    /** 体积单位 */
    @AutoGenerated(locked = true, uuid = "caef5bdd-32e3-4564-8158-856c3114ab85")
    private String volumeUnit;
}
