package com.pulse.drug_dictionary.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

@Data
@AutoGenerated(locked = false, uuid = "6717e4d2-1127-42a5-bef6-17aec00ebf02|DTO|DEFINITION")
public class DrugProducerDictionaryKeyValueDto {
    /** 主键 */
    @AutoGenerated(locked = true, uuid = "30dcd5fa-5c88-4b58-b57c-95d149a1bd75")
    private String id;

    /** 生产商名称 */
    @AutoGenerated(locked = true, uuid = "4fd33b01-0d87-4541-b293-fbb64789637f")
    private String producerName;
}
