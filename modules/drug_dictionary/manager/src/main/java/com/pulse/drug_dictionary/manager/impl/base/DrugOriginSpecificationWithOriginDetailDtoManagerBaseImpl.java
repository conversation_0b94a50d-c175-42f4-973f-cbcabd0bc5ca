package com.pulse.drug_dictionary.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.DrugOriginForDetailDtoManager;
import com.pulse.drug_dictionary.manager.DrugOriginSpecificationBaseDtoManager;
import com.pulse.drug_dictionary.manager.DrugOriginSpecificationWithOriginDetailDtoManager;
import com.pulse.drug_dictionary.manager.converter.DrugOriginSpecificationBaseDtoConverter;
import com.pulse.drug_dictionary.manager.converter.DrugOriginSpecificationWithOriginDetailDtoConverter;
import com.pulse.drug_dictionary.manager.dto.DrugOriginForDetailDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithOriginDetailDto;
import com.pulse.drug_dictionary.persist.dos.DrugOriginSpecification;
import com.pulse.drug_dictionary.persist.mapper.DrugOriginSpecificationDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "334fa2b7-1734-4d83-89d8-f4c1657eedaa|DTO|BASE_MANAGER_IMPL")
public abstract class DrugOriginSpecificationWithOriginDetailDtoManagerBaseImpl
        implements DrugOriginSpecificationWithOriginDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginForDetailDtoManager drugOriginForDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationBaseDtoConverter drugOriginSpecificationBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationBaseDtoManager drugOriginSpecificationBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationDao drugOriginSpecificationDao;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationWithOriginDetailDtoConverter
            drugOriginSpecificationWithOriginDetailDtoConverter;

    @AutoGenerated(locked = true, uuid = "1287f5ce-d90f-378f-a7ad-71d9d7647e73")
    public List<DrugOriginSpecificationWithOriginDetailDto>
            doConvertFromDrugOriginSpecificationToDrugOriginSpecificationWithOriginDetailDto(
                    List<DrugOriginSpecification> drugOriginSpecificationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        Map<String, String> drugOriginCodeMap =
                drugOriginSpecificationList.stream()
                        .filter(i -> i.getDrugOriginCode() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugOriginSpecification::getId,
                                        DrugOriginSpecification::getDrugOriginCode));
        List<DrugOriginForDetailDto> drugOriginCodeDrugOriginForDetailDtoList =
                drugOriginForDetailDtoManager.getByDrugOriginCodes(
                        new ArrayList<>(new HashSet<>(drugOriginCodeMap.values())));
        Map<String, DrugOriginForDetailDto> drugOriginCodeDrugOriginForDetailDtoMapRaw =
                drugOriginCodeDrugOriginForDetailDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugOriginForDetailDto::getDrugOriginCode, i -> i));
        Map<String, DrugOriginForDetailDto> drugOriginCodeDrugOriginForDetailDtoMap =
                drugOriginCodeMap.entrySet().stream()
                        .filter(
                                i ->
                                        drugOriginCodeDrugOriginForDetailDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                drugOriginCodeDrugOriginForDetailDtoMapRaw.get(
                                                        i.getValue())));

        List<DrugOriginSpecificationBaseDto> baseDtoList =
                drugOriginSpecificationBaseDtoConverter
                        .convertFromDrugOriginSpecificationToDrugOriginSpecificationBaseDto(
                                drugOriginSpecificationList);
        Map<String, DrugOriginSpecificationWithOriginDetailDto> dtoMap =
                drugOriginSpecificationWithOriginDetailDtoConverter
                        .convertFromDrugOriginSpecificationBaseDtoToDrugOriginSpecificationWithOriginDetailDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugOriginSpecificationWithOriginDetailDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugOriginSpecificationWithOriginDetailDto>
                drugOriginSpecificationWithOriginDetailDtoList = new ArrayList<>();
        for (DrugOriginSpecification i : drugOriginSpecificationList) {
            DrugOriginSpecificationWithOriginDetailDto drugOriginSpecificationWithOriginDetailDto =
                    dtoMap.get(i.getId());
            if (drugOriginSpecificationWithOriginDetailDto == null) {
                continue;
            }

            if (null != i.getDrugOriginCode()) {
                drugOriginSpecificationWithOriginDetailDto.setDrugOrigin(
                        drugOriginCodeDrugOriginForDetailDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugOriginSpecificationWithOriginDetailDtoList.add(
                    drugOriginSpecificationWithOriginDetailDto);
        }
        return drugOriginSpecificationWithOriginDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "132d4dd5-8a78-3792-bb57-13df91993aaa")
    @Override
    public List<DrugOriginSpecificationWithOriginDetailDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugOriginSpecification> drugOriginSpecificationMap =
                drugOriginSpecificationList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugOriginSpecificationList =
                id.stream()
                        .map(i -> drugOriginSpecificationMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationWithOriginDetailDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "37d4a1d8-88f1-3c4e-83b1-70d79d725ad5")
    @Override
    public List<DrugOriginSpecificationWithOriginDetailDto> getByDrugSpecificationDetailIds(
            List<String> drugSpecificationDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugSpecificationDetailId)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao.getByDrugSpecificationDetailIds(
                        drugSpecificationDetailId);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationWithOriginDetailDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3b53dd1e-171c-30d3-90ed-e5c747b00cea")
    @Override
    public DrugOriginSpecificationWithOriginDetailDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationWithOriginDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugOriginSpecificationWithOriginDetailDto drugOriginSpecificationWithOriginDetailDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugOriginSpecificationWithOriginDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "79eccfb8-a715-3eb6-a7ac-4f56ad10c9e7")
    @Override
    public List<DrugOriginSpecificationWithOriginDetailDto>
            getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                    List<
                                    DrugOriginSpecification
                                            .AmountPerPackageAndDrugOriginCodeAndDrugSpecificationAndSpecificationTypeAndUnit>
                            var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao
                        .getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                                var);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationWithOriginDetailDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8dfab32f-eb9a-32b5-8a55-24876eba57ba")
    @Override
    public List<DrugOriginSpecificationWithOriginDetailDto> getByDrugSpecificationDetailId(
            String drugSpecificationDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationWithOriginDetailDto>
                drugOriginSpecificationWithOriginDetailDtoList =
                        getByDrugSpecificationDetailIds(Arrays.asList(drugSpecificationDetailId));
        return drugOriginSpecificationWithOriginDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "90f2296c-20a6-3b49-90fe-fa975dbaff79")
    @Override
    public List<DrugOriginSpecificationWithOriginDetailDto> getByDrugOriginCodes(
            List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginCode)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao.getByDrugOriginCodes(drugOriginCode);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationWithOriginDetailDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b0a8c8ba-ee2e-3cd6-9292-e3babf116f56")
    @Override
    public DrugOriginSpecificationWithOriginDetailDto
            getByDrugOriginCodeAndSpecificationTypeAndDrugSpecificationAndUnitAndAmountPerPackage(
                    DrugOriginSpecification
                                    .AmountPerPackageAndDrugOriginCodeAndDrugSpecificationAndSpecificationTypeAndUnit
                            var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationWithOriginDetailDto> ret =
                getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                        Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugOriginSpecificationWithOriginDetailDto drugOriginSpecificationWithOriginDetailDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugOriginSpecificationWithOriginDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c150d4a9-b7ed-3fd7-9726-b2e5c1e6cd96")
    @Override
    public List<DrugOriginSpecificationWithOriginDetailDto> getByDrugOriginCode(
            String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationWithOriginDetailDto>
                drugOriginSpecificationWithOriginDetailDtoList =
                        getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        return drugOriginSpecificationWithOriginDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
