package com.pulse.drug_dictionary.manager.bo.base;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.converter.InputCodeEoConverter;
import com.pulse.drug_dictionary.common.enums.AntibacterialLevelEnum;
import com.pulse.drug_dictionary.common.enums.AntineoplasticLevelEnum;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.drug_dictionary.common.enums.SkinTestLevelEnum;
import com.pulse.drug_dictionary.common.enums.SkinTestTypeEnum;
import com.pulse.drug_dictionary.manager.bo.DrugDictionaryBO;
import com.pulse.drug_dictionary.manager.bo.DrugOriginBO;
import com.pulse.drug_dictionary.manager.bo.DrugSpecificationDetailBO;
import com.pulse.drug_dictionary.manager.bo.DrugSpecificationDictionaryBO;
import com.pulse.drug_dictionary.manager.bo.DrugSpecificationExtensionBO;
import com.pulse.drug_dictionary.persist.dos.DrugSpecificationDictionary;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.Valid;

@DoNotModify
@Table(name = "drug_specification_dictionary")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "2f2f8607-0414-3107-bd38-a05288c43f99")
public abstract class BaseDrugSpecificationDictionaryBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 帐簿类别 西药、成药、草药类型下得一个细分类型，比如：草药下面含有饮片和颗粒剂，在草药房发药时会根据饮片和颗粒剂区分为两个菜单进行发药 */
    @Column(name = "account_type")
    @AutoGenerated(locked = true, uuid = "********-f443-47fa-bf3c-0bd5255b0e52")
    private String accountType;

    /** 每箱数量 */
    @Column(name = "amount_per_box")
    @AutoGenerated(locked = true, uuid = "1e989d54-2c65-45a2-9d12-226a7cf483f6")
    private Long amountPerBox;

    /** 包装量 */
    @Column(name = "amount_per_package")
    @AutoGenerated(locked = true, uuid = "37e5b8a9-ccf6-458e-991f-222cbc8326f6")
    private Long amountPerPackage;

    /** 麻醉用药标识 */
    @Column(name = "anesthesia_flag")
    @AutoGenerated(locked = true, uuid = "79b39b5d-bc1f-4a1e-a4ae-0b7028836084")
    private Boolean anesthesiaFlag;

    /** 抗菌药标志 */
    @Column(name = "antibacterial_flag")
    @AutoGenerated(locked = true, uuid = "5cdfb1b6-fd38-4b97-b54d-b969d90dab83")
    private Boolean antibacterialFlag;

    /** 抗菌药限级 */
    @Column(name = "antibacterial_level")
    @AutoGenerated(locked = true, uuid = "3fbc491f-37b2-48cf-8253-31c6f2e48318")
    @Enumerated(EnumType.STRING)
    private AntibacterialLevelEnum antibacterialLevel;

    /** 抗生素标识 */
    @Column(name = "antibiotic_flag")
    @AutoGenerated(locked = true, uuid = "05c49140-3918-433b-9519-a6c112acf9cc")
    private Boolean antibioticFlag;

    /** 抗生素级别 非限制级、限制级、特殊使用级 */
    @Column(name = "antibiotic_level")
    @AutoGenerated(locked = true, uuid = "3eeab281-7649-493e-b524-d507933a5532")
    private String antibioticLevel;

    /** 抗肿瘤药物级别 普通级、限制级 */
    @Column(name = "antineoplastic_level")
    @AutoGenerated(locked = true, uuid = "21d7c7b3-ef84-47ff-89ec-5fa0d6b64ebb")
    @Enumerated(EnumType.STRING)
    private AntineoplasticLevelEnum antineoplasticLevel;

    /**
     * 基药标识 非基药、基药 （1、基药是满足基本医疗需求的药物，是用于预防、诊断和治疗最重要的疾病的药物。
     * 2、非基药是相对于基药而言的，并不是满足基本医疗需求的药物，可能用于一些特定或专科的治疗。）
     */
    @Column(name = "basic_flag")
    @AutoGenerated(locked = true, uuid = "4715bce6-4b4b-4f2b-b2ef-9efbf14210ff")
    private Boolean basicFlag;

    /** 大输液标志 医生站开立医嘱时，会通过大输液标志判断流向哪个药房 */
    @Column(name = "big_infusion_flag")
    @AutoGenerated(locked = true, uuid = "097dd1e3-e0e1-4fdb-bbaa-7d898b3f2226")
    private Boolean bigInfusionFlag;

    /** 化疗药品标识 是否可开化疗处方 */
    @Column(name = "chemotherapy_flag")
    @AutoGenerated(locked = true, uuid = "906761b6-24cc-4418-b62b-879df48b0958")
    private Boolean chemotherapyFlag;

    /** 冷藏药标识 */
    @Column(name = "cold_flag")
    @AutoGenerated(locked = true, uuid = "13996892-5e63-4410-9ab5-cb7d69b52e24")
    private Boolean coldFlag;

    /** 浓度 */
    @Column(name = "concentration")
    @AutoGenerated(locked = true, uuid = "82ecdb31-f00b-488c-a865-97bc4dc0d13c")
    private BigDecimal concentration;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "bf84e39c-ad89-51b1-980c-c2ab2228281c")
    private Date createdAt;

    /** 日常用量 */
    @Column(name = "daily_usage")
    @AutoGenerated(locked = true, uuid = "a083edfc-56e7-41c1-88ae-365e963afe97")
    private BigDecimal dailyUsage;

    /** 高危药标识 */
    @Column(name = "danger_flag")
    @AutoGenerated(locked = true, uuid = "3103c2ad-567b-4339-9e6c-8584f8a5382b")
    private Boolean dangerFlag;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "d7aee05b-d675-5cce-937e-fb5be8cde481")
    private Long deletedAt = 0L;

    /** 出院带药标识 是否可开出院带药处方 */
    @Column(name = "discharge_flag")
    @AutoGenerated(locked = true, uuid = "4e494f8d-596e-4dea-b437-5ecf22aaa800")
    private Boolean dischargeFlag;

    /** 剂量 剂量 */
    @Column(name = "dosage")
    @AutoGenerated(locked = true, uuid = "5d91b5a4-1606-40dd-abd4-feb03c951ddd")
    private BigDecimal dosage;

    /** 剂量单位 */
    @Column(name = "dosage_unit")
    @AutoGenerated(locked = true, uuid = "3c8bd076-618a-4fe1-a1f2-9d3dd6f489a7")
    private String dosageUnit;

    @ManyToOne
    @JoinColumn(name = "drug_code", referencedColumnName = "drug_code")
    @AutoGenerated(locked = true)
    private DrugDictionaryBO drugDictionaryBO;

    /** 剂型 */
    @Column(name = "drug_form")
    @AutoGenerated(locked = true, uuid = "d2e5fe3f-e618-4293-9f50-5cc40226652a")
    private String drugForm;

    @JoinColumn(name = "drug_specification_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugOriginBO> drugOriginBOSet = new HashSet<>();

    @JoinColumn(name = "drug_specification_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugSpecificationDetailBO> drugSpecificationDetailBOSet = new HashSet<>();

    @JoinColumn(name = "drug_specification_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugSpecificationExtensionBO> drugSpecificationExtensionBOSet = new HashSet<>();

    /** 药品类型 */
    @Column(name = "drug_type")
    @AutoGenerated(locked = true, uuid = "f1dc6583-bf0a-4c26-9562-20cb72d8ade8")
    @Enumerated(EnumType.STRING)
    private DrugTypeEnum drugType;

    /** EN药品标识 */
    @Column(name = "en_flag")
    @AutoGenerated(locked = true, uuid = "c1c68162-db37-4de6-be61-cbe50c4c3bad")
    private Boolean enFlag;

    /** 每包克数 */
    @Column(name = "grams_per_package")
    @AutoGenerated(locked = true, uuid = "d22871d3-e86c-411e-8813-e66f20859fe6")
    private BigDecimal gramsPerPackage;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "ef10f96c-15f5-4dd8-b647-7e348dbdc12c")
    @Id
    private String id;

    /** 输入码 */
    @Column(name = "input_code")
    @Valid
    @AutoGenerated(locked = true, uuid = "b870e6fa-b780-4ff6-b570-fe322a0f9874")
    @Convert(converter = InputCodeEoConverter.class)
    private InputCodeEo inputCode;

    /** 医保等级 */
    @Column(name = "insurance_level")
    @AutoGenerated(locked = true, uuid = "376a8c83-4a84-4556-bd04-43ecc5fce7a2")
    private String insuranceLevel;

    /** 作废标志 */
    @Column(name = "invalid_flag")
    @AutoGenerated(locked = true, uuid = "3a55d22a-5f71-4f3f-be10-3cb652aab443")
    private Boolean invalidFlag;

    /** 标签单位 */
    @Column(name = "label_unit")
    @AutoGenerated(locked = true, uuid = "f250d27e-e8c3-4eee-974f-500d58cc5c10")
    private String labelUnit;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 管理模式 */
    @Column(name = "manage_mode")
    @AutoGenerated(locked = true, uuid = "93820759-0cf5-4daa-8a47-32d27ac45516")
    @Enumerated(EnumType.STRING)
    private ManageModelEnum manageMode;

    /** 最小规格 最小规格 */
    @Column(name = "min_specification")
    @AutoGenerated(locked = true, uuid = "1fe26260-fe1f-4668-9360-a700915fdef7")
    private String minSpecification;

    /** 最小单位 */
    @Column(name = "min_unit")
    @AutoGenerated(locked = true, uuid = "c8d28a7a-f51f-4e75-8ee0-42c6a4aee11b")
    private String minUnit;

    /** 眼科剂量单位 */
    @Column(name = "ophthalmic_dosage_unit")
    @AutoGenerated(locked = true, uuid = "b2b6939a-51fe-4cff-ab4d-5b52ea4a72d9")
    private String ophthalmicDosageUnit;

    /** 眼科剂量 */
    @Column(name = "ophthalmology_dosage")
    @AutoGenerated(locked = true, uuid = "19b8d391-880c-4820-835b-7134a3a44c5f")
    private Long ophthalmologyDosage;

    /** 包装规格 包装规格 */
    @Column(name = "package_specification")
    @AutoGenerated(locked = true, uuid = "91fcf913-7b46-45a0-8c3d-538c3d42ee64")
    private String packageSpecification;

    /** 包装单位 包装单位 */
    @Column(name = "package_unit")
    @AutoGenerated(locked = true, uuid = "92c12028-42f7-4775-9dc8-9facae7038df")
    private String packageUnit;

    /** 皮试有效天数 皮试结果有效时间 */
    @Column(name = "skin_test_efficient_days")
    @AutoGenerated(locked = true, uuid = "9c804252-309b-4c3e-a9e1-80d1c5700886")
    private Long skinTestEfficientDays;

    /** 皮试标志 */
    @Column(name = "skin_test_flag")
    @AutoGenerated(locked = true, uuid = "94a7104f-63e4-490d-b086-85cc09b64c54")
    private Boolean skinTestFlag;

    /** 皮试等级 强制皮试，建议皮试 */
    @Column(name = "skin_test_level")
    @AutoGenerated(locked = true, uuid = "8e9d6978-153e-4b7b-a1c9-6fd4a09e6d3a")
    @Enumerated(EnumType.STRING)
    private SkinTestLevelEnum skinTestLevel;

    /** 皮试类型 原液皮试，非原液皮试，二者皆可 */
    @Column(name = "skin_test_type")
    @AutoGenerated(locked = true, uuid = "a5415b13-0ee2-484d-84ff-efec8ca9bed9")
    @Enumerated(EnumType.STRING)
    private SkinTestTypeEnum skinTestType;

    /** 特殊剂量 */
    @Column(name = "special_dosage")
    @AutoGenerated(locked = true, uuid = "0165d44c-e6b5-4c38-9a55-c788be7d0d09")
    private BigDecimal specialDosage;

    /** 特殊剂量单位 */
    @Column(name = "special_dosage_unit")
    @AutoGenerated(locked = true, uuid = "45a92cf1-70f9-4bc3-a33d-16505e77dc7c")
    private String specialDosageUnit;

    /** 药品规格名称 */
    @Column(name = "specification_drug_name")
    @AutoGenerated(locked = true, uuid = "68fff012-0975-4770-91f3-e58cfb0ca728")
    private String specificationDrugName;

    /** 存储条件 */
    @Column(name = "store_condition")
    @AutoGenerated(locked = true, uuid = "d0114fba-3ba0-429e-bff5-5207e3c4d80a")
    private String storeCondition;

    /** 手术用药标识 */
    @Column(name = "surgery_flag")
    @AutoGenerated(locked = true, uuid = "a021d1b4-af40-476c-90d6-bf6ffbddbe67")
    private Boolean surgeryFlag;

    /** 毒性类型 */
    @Column(name = "toxic_type")
    @AutoGenerated(locked = true, uuid = "9fc0a921-1b0b-4a38-b845-617acd785220")
    private String toxicType;

    /** TPN药品标识 */
    @Column(name = "tpn_flag")
    @AutoGenerated(locked = true, uuid = "4db5c44f-546e-4216-942e-d0910cee285e")
    private Boolean tpnFlag;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "aa4f427a-20f4-51eb-aa06-a0f3f7c14f39")
    private Date updatedAt;

    /** 体积 */
    @Column(name = "volume")
    @AutoGenerated(locked = true, uuid = "5b27646b-1a22-43cd-b521-264eb92490c9")
    private BigDecimal volume;

    /** 体积单位 */
    @Column(name = "volume_unit")
    @AutoGenerated(locked = true, uuid = "083b2f7c-69a6-4103-b976-b2e1cd7a3688")
    private String volumeUnit;

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionary convertToDrugSpecificationDictionary() {
        DrugSpecificationDictionary entity = new DrugSpecificationDictionary();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "specificationDrugName",
                "packageSpecification",
                "packageUnit",
                "amountPerPackage",
                "minSpecification",
                "minUnit",
                "dosage",
                "dosageUnit",
                "concentration",
                "volume",
                "volumeUnit",
                "basicFlag",
                "drugForm",
                "accountType",
                "bigInfusionFlag",
                "specialDosage",
                "antibioticFlag",
                "antibioticLevel",
                "antineoplasticLevel",
                "skinTestFlag",
                "skinTestLevel",
                "skinTestType",
                "skinTestEfficientDays",
                "dischargeFlag",
                "chemotherapyFlag",
                "enFlag",
                "tpnFlag",
                "surgeryFlag",
                "anesthesiaFlag",
                "coldFlag",
                "dangerFlag",
                "inputCode",
                "ophthalmologyDosage",
                "ophthalmicDosageUnit",
                "storeCondition",
                "amountPerBox",
                "gramsPerPackage",
                "manageMode",
                "invalidFlag",
                "insuranceLevel",
                "specialDosageUnit",
                "drugType",
                "toxicType",
                "antibacterialFlag",
                "antibacterialLevel",
                "dailyUsage",
                "labelUnit",
                "createdAt",
                "updatedAt",
                "deletedAt");
        DrugDictionaryBO drugDictionaryBO = this.getDrugDictionaryBO();
        entity.setDrugCode(drugDictionaryBO.getDrugCode());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getAccountType() {
        return this.accountType;
    }

    @AutoGenerated(locked = true)
    public Long getAmountPerBox() {
        return this.amountPerBox;
    }

    @AutoGenerated(locked = true)
    public Long getAmountPerPackage() {
        return this.amountPerPackage;
    }

    @AutoGenerated(locked = true)
    public Boolean getAnesthesiaFlag() {
        return this.anesthesiaFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getAntibacterialFlag() {
        return this.antibacterialFlag;
    }

    @AutoGenerated(locked = true)
    public AntibacterialLevelEnum getAntibacterialLevel() {
        return this.antibacterialLevel;
    }

    @AutoGenerated(locked = true)
    public Boolean getAntibioticFlag() {
        return this.antibioticFlag;
    }

    @AutoGenerated(locked = true)
    public String getAntibioticLevel() {
        return this.antibioticLevel;
    }

    @AutoGenerated(locked = true)
    public AntineoplasticLevelEnum getAntineoplasticLevel() {
        return this.antineoplasticLevel;
    }

    @AutoGenerated(locked = true)
    public Boolean getBasicFlag() {
        return this.basicFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getBigInfusionFlag() {
        return this.bigInfusionFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getChemotherapyFlag() {
        return this.chemotherapyFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getColdFlag() {
        return this.coldFlag;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getConcentration() {
        return this.concentration;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getDailyUsage() {
        return this.dailyUsage;
    }

    @AutoGenerated(locked = true)
    public Boolean getDangerFlag() {
        return this.dangerFlag;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public Boolean getDischargeFlag() {
        return this.dischargeFlag;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getDosage() {
        return this.dosage;
    }

    @AutoGenerated(locked = true)
    public String getDosageUnit() {
        return this.dosageUnit;
    }

    @AutoGenerated(locked = true)
    public String getDrugCode() {
        return this.getDrugDictionaryBO().getDrugCode();
    }

    @AutoGenerated(locked = true)
    public DrugDictionaryBO getDrugDictionaryBO() {
        return this.drugDictionaryBO;
    }

    @AutoGenerated(locked = true)
    public String getDrugForm() {
        return this.drugForm;
    }

    @AutoGenerated(locked = true)
    public Set<DrugOriginBO> getDrugOriginBOSet() {
        return this.drugOriginBOSet;
    }

    @AutoGenerated(locked = true)
    public Set<DrugSpecificationDetailBO> getDrugSpecificationDetailBOSet() {
        return this.drugSpecificationDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationExtensionBO getDrugSpecificationExtensionBO() {
        return this.drugSpecificationExtensionBOSet.isEmpty()
                ? null
                : this.drugSpecificationExtensionBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<DrugSpecificationExtensionBO> getDrugSpecificationExtensionBOSet() {
        return this.drugSpecificationExtensionBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugTypeEnum getDrugType() {
        return this.drugType;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnFlag() {
        return this.enFlag;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getGramsPerPackage() {
        return this.gramsPerPackage;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public InputCodeEo getInputCode() {
        return this.inputCode;
    }

    @AutoGenerated(locked = true)
    public String getInsuranceLevel() {
        return this.insuranceLevel;
    }

    @AutoGenerated(locked = true)
    public Boolean getInvalidFlag() {
        return this.invalidFlag;
    }

    @AutoGenerated(locked = true)
    public String getLabelUnit() {
        return this.labelUnit;
    }

    @AutoGenerated(locked = true)
    public ManageModelEnum getManageMode() {
        return this.manageMode;
    }

    @AutoGenerated(locked = true)
    public String getMinSpecification() {
        return this.minSpecification;
    }

    @AutoGenerated(locked = true)
    public String getMinUnit() {
        return this.minUnit;
    }

    @AutoGenerated(locked = true)
    public String getOphthalmicDosageUnit() {
        return this.ophthalmicDosageUnit;
    }

    @AutoGenerated(locked = true)
    public Long getOphthalmologyDosage() {
        return this.ophthalmologyDosage;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationExtensionBO getOrCreateDrugSpecificationExtensionBO() {
        if (this.getDrugSpecificationExtensionBO() == null) {
            DrugSpecificationExtensionBO obj = new DrugSpecificationExtensionBO();
            obj.setDrugSpecificationDictionaryBO((DrugSpecificationDictionaryBO) this);
            setDrugSpecificationExtensionBO(obj);
            return obj;
        } else {
            return this.getDrugSpecificationExtensionBO();
        }
    }

    @AutoGenerated(locked = true)
    public String getPackageSpecification() {
        return this.packageSpecification;
    }

    @AutoGenerated(locked = true)
    public String getPackageUnit() {
        return this.packageUnit;
    }

    @AutoGenerated(locked = true)
    public Long getSkinTestEfficientDays() {
        return this.skinTestEfficientDays;
    }

    @AutoGenerated(locked = true)
    public Boolean getSkinTestFlag() {
        return this.skinTestFlag;
    }

    @AutoGenerated(locked = true)
    public SkinTestLevelEnum getSkinTestLevel() {
        return this.skinTestLevel;
    }

    @AutoGenerated(locked = true)
    public SkinTestTypeEnum getSkinTestType() {
        return this.skinTestType;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getSpecialDosage() {
        return this.specialDosage;
    }

    @AutoGenerated(locked = true)
    public String getSpecialDosageUnit() {
        return this.specialDosageUnit;
    }

    @AutoGenerated(locked = true)
    public String getSpecificationDrugName() {
        return this.specificationDrugName;
    }

    @AutoGenerated(locked = true)
    public String getStoreCondition() {
        return this.storeCondition;
    }

    @AutoGenerated(locked = true)
    public Boolean getSurgeryFlag() {
        return this.surgeryFlag;
    }

    @AutoGenerated(locked = true)
    public String getToxicType() {
        return this.toxicType;
    }

    @AutoGenerated(locked = true)
    public Boolean getTpnFlag() {
        return this.tpnFlag;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getVolume() {
        return this.volume;
    }

    @AutoGenerated(locked = true)
    public String getVolumeUnit() {
        return this.volumeUnit;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAccountType(String accountType) {
        this.accountType = accountType;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAmountPerBox(Long amountPerBox) {
        this.amountPerBox = amountPerBox;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAmountPerPackage(Long amountPerPackage) {
        this.amountPerPackage = amountPerPackage;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAnesthesiaFlag(Boolean anesthesiaFlag) {
        this.anesthesiaFlag = anesthesiaFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAntibacterialFlag(Boolean antibacterialFlag) {
        this.antibacterialFlag = antibacterialFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAntibacterialLevel(
            AntibacterialLevelEnum antibacterialLevel) {
        this.antibacterialLevel = antibacterialLevel;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAntibioticFlag(Boolean antibioticFlag) {
        this.antibioticFlag = antibioticFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAntibioticLevel(String antibioticLevel) {
        this.antibioticLevel = antibioticLevel;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setAntineoplasticLevel(
            AntineoplasticLevelEnum antineoplasticLevel) {
        this.antineoplasticLevel = antineoplasticLevel;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setBasicFlag(Boolean basicFlag) {
        this.basicFlag = basicFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setBigInfusionFlag(Boolean bigInfusionFlag) {
        this.bigInfusionFlag = bigInfusionFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setChemotherapyFlag(Boolean chemotherapyFlag) {
        this.chemotherapyFlag = chemotherapyFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setColdFlag(Boolean coldFlag) {
        this.coldFlag = coldFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setConcentration(BigDecimal concentration) {
        this.concentration = concentration;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDailyUsage(BigDecimal dailyUsage) {
        this.dailyUsage = dailyUsage;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDangerFlag(Boolean dangerFlag) {
        this.dangerFlag = dangerFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDischargeFlag(Boolean dischargeFlag) {
        this.dischargeFlag = dischargeFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDosage(BigDecimal dosage) {
        this.dosage = dosage;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public BaseDrugSpecificationDictionaryBO setDrugDictionaryBO(
            DrugDictionaryBO drugDictionaryBO) {
        this.drugDictionaryBO = drugDictionaryBO;
        return (BaseDrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDrugForm(String drugForm) {
        this.drugForm = drugForm;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugOriginBOSet(Set<DrugOriginBO> drugOriginBOSet) {
        this.drugOriginBOSet = drugOriginBOSet;
    }

    @AutoGenerated(locked = true)
    private void setDrugSpecificationDetailBOSet(
            Set<DrugSpecificationDetailBO> drugSpecificationDetailBOSet) {
        this.drugSpecificationDetailBOSet = drugSpecificationDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDrugSpecificationExtensionBO(
            DrugSpecificationExtensionBO drugSpecificationExtensionBO) {
        if (this.drugSpecificationExtensionBOSet.size() > 0) {
            this.drugSpecificationExtensionBOSet.clear();
        }
        if (drugSpecificationExtensionBO != null) {
            this.drugSpecificationExtensionBOSet.add(drugSpecificationExtensionBO);
        }
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugSpecificationExtensionBOSet(
            Set<DrugSpecificationExtensionBO> drugSpecificationExtensionBOSet) {
        this.drugSpecificationExtensionBOSet = drugSpecificationExtensionBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setDrugType(DrugTypeEnum drugType) {
        this.drugType = drugType;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setEnFlag(Boolean enFlag) {
        this.enFlag = enFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setGramsPerPackage(BigDecimal gramsPerPackage) {
        this.gramsPerPackage = gramsPerPackage;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setId(String id) {
        this.id = id;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setInputCode(InputCodeEo inputCode) {
        this.inputCode = inputCode;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setInsuranceLevel(String insuranceLevel) {
        this.insuranceLevel = insuranceLevel;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setInvalidFlag(Boolean invalidFlag) {
        this.invalidFlag = invalidFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setLabelUnit(String labelUnit) {
        this.labelUnit = labelUnit;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setManageMode(ManageModelEnum manageMode) {
        this.manageMode = manageMode;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setMinSpecification(String minSpecification) {
        this.minSpecification = minSpecification;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setMinUnit(String minUnit) {
        this.minUnit = minUnit;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setOphthalmicDosageUnit(String ophthalmicDosageUnit) {
        this.ophthalmicDosageUnit = ophthalmicDosageUnit;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setOphthalmologyDosage(Long ophthalmologyDosage) {
        this.ophthalmologyDosage = ophthalmologyDosage;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setPackageSpecification(String packageSpecification) {
        this.packageSpecification = packageSpecification;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSkinTestEfficientDays(Long skinTestEfficientDays) {
        this.skinTestEfficientDays = skinTestEfficientDays;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSkinTestFlag(Boolean skinTestFlag) {
        this.skinTestFlag = skinTestFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSkinTestLevel(SkinTestLevelEnum skinTestLevel) {
        this.skinTestLevel = skinTestLevel;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSkinTestType(SkinTestTypeEnum skinTestType) {
        this.skinTestType = skinTestType;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSpecialDosage(BigDecimal specialDosage) {
        this.specialDosage = specialDosage;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSpecialDosageUnit(String specialDosageUnit) {
        this.specialDosageUnit = specialDosageUnit;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSpecificationDrugName(String specificationDrugName) {
        this.specificationDrugName = specificationDrugName;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setStoreCondition(String storeCondition) {
        this.storeCondition = storeCondition;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setSurgeryFlag(Boolean surgeryFlag) {
        this.surgeryFlag = surgeryFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setToxicType(String toxicType) {
        this.toxicType = toxicType;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setTpnFlag(Boolean tpnFlag) {
        this.tpnFlag = tpnFlag;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setVolume(BigDecimal volume) {
        this.volume = volume;
        return (DrugSpecificationDictionaryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugSpecificationDictionaryBO setVolumeUnit(String volumeUnit) {
        this.volumeUnit = volumeUnit;
        return (DrugSpecificationDictionaryBO) this;
    }
}
