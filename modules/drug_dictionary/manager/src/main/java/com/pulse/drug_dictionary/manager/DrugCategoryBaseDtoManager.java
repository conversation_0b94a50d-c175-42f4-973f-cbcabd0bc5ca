package com.pulse.drug_dictionary.manager;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "fefdfe45-6076-4072-bb54-7ccda091bb7e|DTO|MANAGER")
public interface DrugCategoryBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "2d8f1d77-d8eb-3709-bb07-26e59d86d52e")
    List<DrugCategoryBaseDto> getByRootId(String rootId);

    @AutoGenerated(locked = true, uuid = "468fe785-83f7-3765-b679-55587cefeb15")
    List<DrugCategoryBaseDto> getByStandardCode(String standardCode);

    @AutoGenerated(locked = true, uuid = "7cf4e841-a40e-356e-a818-34cf1c3e032d")
    List<DrugCategoryBaseDto> getByStandardCodes(List<String> standardCode);

    @AutoGenerated(locked = true, uuid = "8591a389-b4af-3a80-998d-592e609229ed")
    List<DrugCategoryBaseDto> getByParentId(String parentId);

    @AutoGenerated(locked = true, uuid = "b6a7251f-309e-3c89-b518-188057ca0d5c")
    DrugCategoryBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "bdc3321c-05d3-3911-826c-9c218aa954da")
    List<DrugCategoryBaseDto> getByParentIds(List<String> parentId);

    @AutoGenerated(locked = true, uuid = "c9ed0d22-5b89-3416-8299-e37e58d8a457")
    List<DrugCategoryBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "f1f010b0-de52-35a9-9549-95e3ee00dbca")
    List<DrugCategoryBaseDto> getByRootIds(List<String> rootId);
}
