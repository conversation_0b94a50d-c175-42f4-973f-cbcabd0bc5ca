package com.pulse.drug_permission.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> DrugOriginSpecificationSupply
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "4edaf34b-6a97-4849-980b-290deafb31ce|BTO|DEFINITION")
public class MergeDrugOriginSpecificationSupplyBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 创建人id */
    @AutoGenerated(locked = true, uuid = "ab2e5a51-51ab-457d-89f0-ed6f968fcda6")
    private String createdBy;

    /** 出院带药可供标识 */
    @AutoGenerated(locked = true, uuid = "6d87879e-ca25-4f0d-9f33-8a1d30783b7b")
    private Boolean dischargeWithMedicationSupplyFlag;

    /** 药品产地编码 冗余存 */
    @AutoGenerated(locked = true, uuid = "98cfffd2-40b3-4485-8b86-9248de35391a")
    private String drugOriginCode;

    /** 药品产地规格id 标识可供状态控制哪个规格 */
    @AutoGenerated(locked = true, uuid = "6ab5520d-45ba-4ae0-ab29-12f1c66340de")
    private String drugOriginSpecificationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "fa3317b7-d51a-4eb9-8886-f8ce4985c5ea")
    private String id;

    /** 住院静配可供标识 */
    @AutoGenerated(locked = true, uuid = "a2842dad-accd-436c-b788-2c38f4f813cc")
    private Boolean inpatientPivasSupplyFlag;

    /** 住院可供标识 */
    @AutoGenerated(locked = true, uuid = "796c4d1e-70ad-46bf-b5d4-f3d3eb3bbf07")
    private Boolean inpatientSupplyFlag;

    /** 门诊静配可供标识 */
    @AutoGenerated(locked = true, uuid = "4c83856d-8552-48ac-b094-61763077905e")
    private Boolean outpatientPivasSupplyFlag;

    /** 门诊可供标识 */
    @AutoGenerated(locked = true, uuid = "f6247d43-752c-4579-af83-9b92f900d673")
    private Boolean outpatientSupplyFlag;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "1b788ffd-d1bc-412b-91dd-103879ac660a")
    private String storageCode;

    /** 修改人id */
    @AutoGenerated(locked = true, uuid = "3bc8cc02-44a1-4ed7-92cd-f4c4f8d14e39")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDischargeWithMedicationSupplyFlag(Boolean dischargeWithMedicationSupplyFlag) {
        this.__$validPropertySet.add("dischargeWithMedicationSupplyFlag");
        this.dischargeWithMedicationSupplyFlag = dischargeWithMedicationSupplyFlag;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginCode(String drugOriginCode) {
        this.__$validPropertySet.add("drugOriginCode");
        this.drugOriginCode = drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginSpecificationId(String drugOriginSpecificationId) {
        this.__$validPropertySet.add("drugOriginSpecificationId");
        this.drugOriginSpecificationId = drugOriginSpecificationId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInpatientPivasSupplyFlag(Boolean inpatientPivasSupplyFlag) {
        this.__$validPropertySet.add("inpatientPivasSupplyFlag");
        this.inpatientPivasSupplyFlag = inpatientPivasSupplyFlag;
    }

    @AutoGenerated(locked = true)
    public void setInpatientSupplyFlag(Boolean inpatientSupplyFlag) {
        this.__$validPropertySet.add("inpatientSupplyFlag");
        this.inpatientSupplyFlag = inpatientSupplyFlag;
    }

    @AutoGenerated(locked = true)
    public void setOutpatientPivasSupplyFlag(Boolean outpatientPivasSupplyFlag) {
        this.__$validPropertySet.add("outpatientPivasSupplyFlag");
        this.outpatientPivasSupplyFlag = outpatientPivasSupplyFlag;
    }

    @AutoGenerated(locked = true)
    public void setOutpatientSupplyFlag(Boolean outpatientSupplyFlag) {
        this.__$validPropertySet.add("outpatientSupplyFlag");
        this.outpatientSupplyFlag = outpatientSupplyFlag;
    }

    @AutoGenerated(locked = true)
    public void setStorageCode(String storageCode) {
        this.__$validPropertySet.add("storageCode");
        this.storageCode = storageCode;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
