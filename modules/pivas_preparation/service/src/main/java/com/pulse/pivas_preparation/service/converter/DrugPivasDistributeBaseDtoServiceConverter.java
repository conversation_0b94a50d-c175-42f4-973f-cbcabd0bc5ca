package com.pulse.pivas_preparation.service.converter;

import com.pulse.pivas_preparation.manager.dto.DrugPivasDistributeBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "1c9584c2-7701-324e-9f4a-14cde91ab67c")
public class DrugPivasDistributeBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugPivasDistributeBaseDto> DrugPivasDistributeBaseDtoConverter(
            List<DrugPivasDistributeBaseDto> drugPivasDistributeBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugPivasDistributeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
