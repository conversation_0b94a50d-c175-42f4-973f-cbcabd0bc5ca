package com.pulse.pivas_preparation.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.pivas_preparation.manager.dto.DrugPivasBatchSettingBaseDto;
import com.pulse.pivas_preparation.persist.dos.DrugPivasBatchSetting;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "04f5151b-d73b-40cb-9af4-5da1375b3978|DTO|BASE_CONVERTER")
public class DrugPivasBatchSettingBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugPivasBatchSettingBaseDto
            convertFromDrugPivasBatchSettingToDrugPivasBatchSettingBaseDto(
                    DrugPivasBatchSetting drugPivasBatchSetting) {
        return convertFromDrugPivasBatchSettingToDrugPivasBatchSettingBaseDto(
                        List.of(drugPivasBatchSetting))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugPivasBatchSettingBaseDto>
            convertFromDrugPivasBatchSettingToDrugPivasBatchSettingBaseDto(
                    List<DrugPivasBatchSetting> drugPivasBatchSettingList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugPivasBatchSettingList)) {
            return new ArrayList<>();
        }
        List<DrugPivasBatchSettingBaseDto> drugPivasBatchSettingBaseDtoList = new ArrayList<>();
        for (DrugPivasBatchSetting drugPivasBatchSetting : drugPivasBatchSettingList) {
            if (drugPivasBatchSetting == null) {
                continue;
            }
            DrugPivasBatchSettingBaseDto drugPivasBatchSettingBaseDto =
                    new DrugPivasBatchSettingBaseDto();
            drugPivasBatchSettingBaseDto.setId(drugPivasBatchSetting.getId());
            drugPivasBatchSettingBaseDto.setSortOrder(drugPivasBatchSetting.getSortOrder());
            drugPivasBatchSettingBaseDto.setName(drugPivasBatchSetting.getName());
            drugPivasBatchSettingBaseDto.setStartTime(drugPivasBatchSetting.getStartTime());
            drugPivasBatchSettingBaseDto.setEndTime(drugPivasBatchSetting.getEndTime());
            drugPivasBatchSettingBaseDto.setInvalidFlag(drugPivasBatchSetting.getInvalidFlag());
            drugPivasBatchSettingBaseDto.setMaxDosage(drugPivasBatchSetting.getMaxDosage());
            drugPivasBatchSettingBaseDto.setAdmixtureType(drugPivasBatchSetting.getAdmixtureType());
            drugPivasBatchSettingBaseDto.setRepeatFlag(drugPivasBatchSetting.getRepeatFlag());
            drugPivasBatchSettingBaseDto.setTemporaryFlag(drugPivasBatchSetting.getTemporaryFlag());
            drugPivasBatchSettingBaseDto.setTpnFlag(drugPivasBatchSetting.getTpnFlag());
            drugPivasBatchSettingBaseDto.setChemotherapyFlag(
                    drugPivasBatchSetting.getChemotherapyFlag());
            drugPivasBatchSettingBaseDto.setClinicType(drugPivasBatchSetting.getClinicType());
            drugPivasBatchSettingBaseDto.setFrequencyList(drugPivasBatchSetting.getFrequencyList());
            drugPivasBatchSettingBaseDto.setAdministrationList(
                    drugPivasBatchSetting.getAdministrationList());
            drugPivasBatchSettingBaseDto.setUpdatedBy(drugPivasBatchSetting.getUpdatedBy());
            drugPivasBatchSettingBaseDto.setRemark(drugPivasBatchSetting.getRemark());
            drugPivasBatchSettingBaseDto.setInstitutionId(drugPivasBatchSetting.getInstitutionId());
            drugPivasBatchSettingBaseDto.setCreatedBy(drugPivasBatchSetting.getCreatedBy());
            drugPivasBatchSettingBaseDto.setLockVersion(drugPivasBatchSetting.getLockVersion());
            drugPivasBatchSettingBaseDto.setCreatedAt(drugPivasBatchSetting.getCreatedAt());
            drugPivasBatchSettingBaseDto.setUpdatedAt(drugPivasBatchSetting.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugPivasBatchSettingBaseDtoList.add(drugPivasBatchSettingBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugPivasBatchSettingBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
