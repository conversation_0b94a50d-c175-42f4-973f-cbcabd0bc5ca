package com.pulse.orders.persist.mapper;

import com.pulse.orders.persist.dos.OrderLab;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "1291a482-b8a9-4145-b3fe-e055e552ec1e|ENTITY|IDAO")
public interface OrderLabDao {

    @AutoGenerated(locked = true, uuid = "7ae4cc56-e40e-3916-acaa-560fd03c5117")
    OrderLab getById(String id);

    @AutoGenerated(locked = true, uuid = "7ec4a974-211c-34ed-91c6-65909ce98d10")
    List<OrderLab> getByOrderInfoIds(List<String> orderInfoId);

    @AutoGenerated(locked = true, uuid = "97d8e586-d7ac-3a85-9c03-097f3c0ce8e4")
    List<OrderLab> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "c9d7c16a-**************-f6b944031812")
    OrderLab getByOrderInfoId(String orderInfoId);
}
