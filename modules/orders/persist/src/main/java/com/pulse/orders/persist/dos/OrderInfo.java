package com.pulse.orders.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.orders.common.enums.OrderClassEnum;
import com.pulse.orders.common.enums.OrderSourceTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "order_info", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "8ed03a8b-8cdc-44df-8b18-82c540c54c10|ENTITY|DEFINITION")
public class OrderInfo {
    @AutoGenerated(locked = true, uuid = "330102b0-c90a-4f0d-8460-bdd5e9c262fc")
    @TableField(value = "billing_attribute")
    private String billingAttribute;

    @AutoGenerated(locked = true, uuid = "dbae6283-757e-492d-9baf-41dd87880e5e")
    @TableField(value = "billing_flag")
    private Boolean billingFlag;

    @AutoGenerated(locked = true, uuid = "1d2d95b3-0568-4491-8079-db9a1e91e046")
    @TableField(value = "cancel_by")
    private String cancelBy;

    @AutoGenerated(locked = true, uuid = "f0255cde-f6e3-4cee-9599-aa602556ac4a")
    @TableField(value = "cancel_date")
    private Date cancelDate;

    @AutoGenerated(locked = true, uuid = "0fbb28ac-3d2f-4326-91ce-23ae03a5da2e")
    @TableField(value = "charge_status")
    private String chargeStatus;

    @AutoGenerated(locked = true, uuid = "ed7d4929-91ea-5a2d-b885-c1f86f751ee7")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "7113fb4c-ca61-5e45-b406-fc3ef68e82ab")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "0ff99a2e-bbfc-4f42-a89b-ad159093b8af")
    @TableField(value = "disease_type")
    private String diseaseType;

    @AutoGenerated(locked = true, uuid = "c3115de6-0694-44cd-ba8a-2884289491fd")
    @TableField(value = "display_id")
    private String displayId;

    @AutoGenerated(locked = true, uuid = "260737ba-deae-4f45-ad8c-4a5c5068a395")
    @TableField(value = "doctor_instruction")
    private String doctorInstruction;

    @AutoGenerated(locked = true, uuid = "07acc92d-0e42-4c0e-a65f-6fbc2feece66")
    @TableField(value = "enter_date")
    private Date enterDate;

    @AutoGenerated(locked = true, uuid = "9ae0195c-99c1-4c3f-a4fa-fbcd09ac3e02")
    @TableField(value = "erp_visit_id")
    private String erpVisitId;

    @AutoGenerated(locked = true, uuid = "afe78fb1-77d7-4771-a8be-a8066af3635a")
    @TableField(value = "exclusion_type")
    private String exclusionType;

    @AutoGenerated(locked = true, uuid = "655a8552-e3e3-496f-88e9-c07e71b4e1e8")
    @TableField(value = "force_self_payment_flag")
    private String forceSelfPaymentFlag;

    @AutoGenerated(locked = true, uuid = "97e1bcc1-6691-4398-8b61-2df2e4ab29ed")
    @TableField(value = "gcp_id")
    private String gcpId;

    @AutoGenerated(locked = true, uuid = "75fe1248-7d11-4e3f-8d6c-787dc1e93f1f")
    @TableField(value = "group_number")
    private Long groupNumber;

    @AutoGenerated(locked = true, uuid = "80453c31-0582-429b-b74c-dbc587775120")
    @TableField(value = "group_sort_number")
    private Long groupSortNumber;

    @AutoGenerated(locked = true, uuid = "d2cc1b68-25c7-4279-892d-73f453c826e5")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "48b7a094-1288-4f9a-aeee-f927a20970e1")
    @TableField(value = "inp_visit_id")
    private String inpVisitId;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "cea3c8e7-6ce2-463f-9d17-827e2e8363ee")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "8d04a1ff-5cc0-4def-9995-b2e031ce92e3")
    @TableField(value = "order_class")
    private OrderClassEnum orderClass;

    @AutoGenerated(locked = true, uuid = "287eee3b-5d1c-4fbd-9194-ef5620b54d22")
    @TableField(value = "order_print_count")
    private Long orderPrintCount;

    @AutoGenerated(locked = true, uuid = "6bccc839-8848-44ce-b07e-16ab09f600f5")
    @TableField(value = "order_source_id")
    private String orderSourceId;

    @AutoGenerated(locked = true, uuid = "6867a087-2365-4a62-815e-54889fc34133")
    @TableField(value = "order_source_type")
    private OrderSourceTypeEnum orderSourceType;

    @AutoGenerated(locked = true, uuid = "706c3e33-b518-4b39-b638-81e4910cf4fd")
    @TableField(value = "order_status")
    private String orderStatus;

    @AutoGenerated(locked = true, uuid = "08cc417a-5cda-4cd3-a667-2852454ff9ab")
    @TableField(value = "order_text")
    private String orderText;

    @AutoGenerated(locked = true, uuid = "c6e92055-a8b7-4575-b602-2a543fe7f4cc")
    @TableField(value = "ordering_application_id")
    private String orderingApplicationId;

    @AutoGenerated(locked = true, uuid = "193be4fa-3e3a-4a48-9d5a-da7c53b42fd0")
    @TableField(value = "ordering_by")
    private String orderingBy;

    @AutoGenerated(locked = true, uuid = "91a55c77-18a5-4927-a253-1e037c926fcb")
    @TableField(value = "ordering_campus_id")
    private String orderingCampusId;

    @AutoGenerated(locked = true, uuid = "5dc67cf9-72da-4fb3-9f8b-b05f778ff406")
    @TableField(value = "ordering_department_id")
    private String orderingDepartmentId;

    @AutoGenerated(locked = true, uuid = "d828b880-3674-4145-baf4-a518feb716b4")
    @TableField(value = "ordering_medical_group_id")
    private String orderingMedicalGroupId;

    @AutoGenerated(locked = true, uuid = "3866cb57-728c-4c01-a415-7d23562caa9c")
    @TableField(value = "outp_visit_encounter_id")
    private String outpVisitEncounterId;

    @AutoGenerated(locked = true, uuid = "340afca3-b4f5-4d87-bc7f-2bccf31fd070")
    @TableField(value = "outp_visit_id")
    private String outpVisitId;

    @AutoGenerated(locked = true, uuid = "aef32687-628b-451e-96a1-c46878e5c7e3")
    @TableField(value = "parent_order_id")
    private String parentOrderId;

    @AutoGenerated(locked = true, uuid = "c2f5b6a1-8991-4e2a-a3f4-990fad9f1115")
    @TableField(value = "patient_bed_id")
    private String patientBedId;

    @AutoGenerated(locked = true, uuid = "05fb939f-0a9a-42ea-a812-66f4b8da5056")
    @TableField(value = "patient_bed_number")
    private String patientBedNumber;

    @AutoGenerated(locked = true, uuid = "5966af93-a315-48c5-a59e-274f9b9fb091")
    @TableField(value = "patient_department_id")
    private String patientDepartmentId;

    @AutoGenerated(locked = true, uuid = "d2095c69-06a0-47f8-b858-18f5bdf6a2e8")
    @TableField(value = "patient_id")
    private String patientId;

    @AutoGenerated(locked = true, uuid = "9ad31e78-db48-4c9b-a9af-************")
    @TableField(value = "patient_ward_id")
    private String patientWardId;

    @AutoGenerated(locked = true, uuid = "85210c60-4ec5-4c11-9154-08af90f0a4e9")
    @TableField(value = "perform_department_id")
    private String performDepartmentId;

    @AutoGenerated(locked = true, uuid = "e6b6282a-9fc7-4a21-ab98-c7c0d99ca381")
    @TableField(value = "perform_print_date")
    private Date performPrintDate;

    @AutoGenerated(locked = true, uuid = "687893f7-cca3-4574-833a-ea60f403fd87")
    @TableField(value = "pre_hospital_order_flag")
    private Boolean preHospitalOrderFlag;

    @AutoGenerated(locked = true, uuid = "f2490030-28ca-4a34-8897-efb2baf899bd")
    @TableField(value = "prescription_id")
    private String prescriptionId;

    @AutoGenerated(locked = true, uuid = "a50bd1d9-7d10-43b2-a473-e9bfb039c5e7")
    @TableField(value = "proofread_by")
    private String proofreadBy;

    @AutoGenerated(locked = true, uuid = "291c095d-1151-495c-bdc3-54a3ed788f2c")
    @TableField(value = "proofread_date")
    private Date proofreadDate;

    @AutoGenerated(locked = true, uuid = "470bd910-539d-4d56-a55f-d280dea94752")
    @TableField(value = "repeat_order_flag")
    private Boolean repeatOrderFlag;

    @AutoGenerated(locked = true, uuid = "e9f73fe9-e0c7-48f3-9cf0-0a2d25934505")
    @TableField(value = "review_by")
    private String reviewBy;

    @AutoGenerated(locked = true, uuid = "3d24652d-a3d4-4aca-ad31-dc3967635fdf")
    @TableField(value = "review_date")
    private Date reviewDate;

    @AutoGenerated(locked = true, uuid = "701a64b1-d79a-4e08-a748-1ed84777161c")
    @TableField(value = "revoke_by")
    private String revokeBy;

    @AutoGenerated(locked = true, uuid = "8c0a0f2e-cf60-4a8d-899c-d3f3d074c8e1")
    @TableField(value = "revoke_date")
    private Date revokeDate;

    @AutoGenerated(locked = true, uuid = "87f0f0de-d391-4562-97f2-7e3752f91ae4")
    @TableField(value = "revoke_reason")
    private String revokeReason;

    @AutoGenerated(locked = true, uuid = "7bd9c8e1-2138-42e7-95a7-c0098b27d1ff")
    @TableField(value = "revoke_type")
    private String revokeType;

    @AutoGenerated(locked = true, uuid = "ae0e6f88-3877-4185-a517-a72732f5b284")
    @TableField(value = "sort_number")
    private Long sortNumber;

    @AutoGenerated(locked = true, uuid = "f74315e8-cb34-44f9-81e5-6ecda4941180")
    @TableField(value = "start_date")
    private Date startDate;

    @AutoGenerated(locked = true, uuid = "8ed90462-9cea-4bd7-93ad-3eaa44749e9c")
    @TableField(value = "stop_by")
    private String stopBy;

    @AutoGenerated(locked = true, uuid = "47f9dbee-f558-4903-9f0f-f179da231eaa")
    @TableField(value = "stop_date")
    private Date stopDate;

    @AutoGenerated(locked = true, uuid = "9c2e796f-e39e-49f2-bec0-b7af8416fa0c")
    @TableField(value = "submit_date")
    private Date submitDate;

    @AutoGenerated(locked = true, uuid = "2c13a48f-e5b1-5512-b219-28fc3469ebbb")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
