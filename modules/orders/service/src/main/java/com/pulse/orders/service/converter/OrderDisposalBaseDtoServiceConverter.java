package com.pulse.orders.service.converter;

import com.pulse.orders.manager.dto.OrderDisposalBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "d1178c23-3ff3-3baa-a81f-c6bf65535aae")
public class OrderDisposalBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OrderDisposalBaseDto> OrderDisposalBaseDtoConverter(
            List<OrderDisposalBaseDto> orderDisposalBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return orderDisposalBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
