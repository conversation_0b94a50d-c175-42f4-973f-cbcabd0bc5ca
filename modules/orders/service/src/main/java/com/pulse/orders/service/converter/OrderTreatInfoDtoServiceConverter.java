package com.pulse.orders.service.converter;

import com.pulse.orders.manager.dto.OrderTreatInfoDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "73771e7a-e514-32a9-bdc0-a62179bd663c")
public class OrderTreatInfoDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OrderTreatInfoDto> OrderTreatInfoDtoConverter(
            List<OrderTreatInfoDto> orderTreatInfoDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return orderTreatInfoDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
