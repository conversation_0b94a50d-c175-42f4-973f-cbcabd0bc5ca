package com.pulse.orders.service.converter;

import com.pulse.orders.manager.dto.OrderOperationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "c99841f2-ab05-312b-b03a-44c656336b23")
public class OrderOperationBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OrderOperationBaseDto> OrderOperationBaseDtoConverter(
            List<OrderOperationBaseDto> orderOperationBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return orderOperationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
