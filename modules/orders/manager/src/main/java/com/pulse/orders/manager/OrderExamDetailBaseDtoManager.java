package com.pulse.orders.manager;

import com.pulse.orders.manager.dto.OrderExamDetailBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "02ab0bc9-97b3-4ac3-bf3b-1f68f441cd85|DTO|MANAGER")
public interface OrderExamDetailBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "282982ba-ec65-34b7-98b3-e53f94945ba8")
    List<OrderExamDetailBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "621d4e74-a7e5-3b40-9463-a0e40bd7bfec")
    List<OrderExamDetailBaseDto> getByOrderExamIds(List<String> orderExamId);

    @AutoGenerated(locked = true, uuid = "72be2dc5-359b-3538-a0b2-8e90de335cce")
    List<OrderExamDetailBaseDto> getByOrderExamId(String orderExamId);

    @AutoGenerated(locked = true, uuid = "e70a8334-aa27-399f-a694-b2f8551df8dc")
    OrderExamDetailBaseDto getById(String id);
}
