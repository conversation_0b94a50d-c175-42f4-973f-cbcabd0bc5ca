package com.pulse.orders.manager.bo;

import com.pulse.orders.persist.dos.OrderLab;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "order_lab")
@Entity
@AutoGenerated(locked = true, uuid = "27d6071d-5b7d-4a93-8e28-e83b8ba4b2f6|BO|DEFINITION")
public class OrderLabBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 条形码 */
    @Column(name = "barcode")
    @AutoGenerated(locked = true, uuid = "fa050c5d-2323-4dec-8836-c127704e23d9")
    private String barcode;

    /** 作废条码人 */
    @Column(name = "barcode_cancel_by")
    @AutoGenerated(locked = true, uuid = "eb4b35cf-2942-4215-a682-203ad2e455bd")
    private String barcodeCancelBy;

    /** 作废条码时间 */
    @Column(name = "barcode_cancel_date")
    @AutoGenerated(locked = true, uuid = "09aea183-**************-197fff92a0ee")
    private Date barcodeCancelDate;

    /** 作废原因 */
    @Column(name = "barcode_cancel_reason")
    @AutoGenerated(locked = true, uuid = "ff6de962-57fc-4d03-9143-8797e81041b4")
    private String barcodeCancelReason;

    /** 条码打印人 */
    @Column(name = "barcode_print_by")
    @AutoGenerated(locked = true, uuid = "c3b14698-5e09-4ef5-b077-b50c3f2baf87")
    private String barcodePrintBy;

    /** 条码打印日期 */
    @Column(name = "barcode_print_flag")
    @AutoGenerated(locked = true, uuid = "0cafde8e-f856-48d8-9ed8-6b48abf3ee23")
    private Boolean barcodePrintFlag;

    /** 条码扫描标志 */
    @Column(name = "barcode_scanning_flag")
    @AutoGenerated(locked = true, uuid = "b97ced90-bc61-46aa-a114-894acc4ac8a1")
    private Boolean barcodeScanningFlag;

    /** 采集完成标志 */
    @Column(name = "complete_sample_flag")
    @AutoGenerated(locked = true, uuid = "bcdc19ff-5067-4952-abe7-13cc3bafa63d")
    private Boolean completeSampleFlag;

    /** 检验完成人 */
    @Column(name = "completed_by")
    @AutoGenerated(locked = true, uuid = "0e90285d-b403-4b20-8443-32f3a56c5c5c")
    private String completedBy;

    /** 检验完成日期 */
    @Column(name = "completed_date")
    @AutoGenerated(locked = true, uuid = "ce5f7aaa-4c97-43a9-bc34-420472f985a8")
    private Date completedDate;

    /** 检验完成标志 */
    @Column(name = "completed_flag")
    @AutoGenerated(locked = true, uuid = "79ec788c-8b1a-408e-99bf-9eaaf3c84f52")
    private Boolean completedFlag;

    /** 上机日期 */
    @Column(name = "computer_operate_date")
    @AutoGenerated(locked = true, uuid = "b61f01a5-c5d2-48b3-aa57-2685387ed62f")
    private Date computerOperateDate;

    /** 上机人 */
    @Column(name = "computer_operator")
    @AutoGenerated(locked = true, uuid = "6233e803-96b2-4da7-8f07-6f80bf2b455f")
    private String computerOperator;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "75b06dc5-8218-527f-8f94-3cd2beec507e")
    private Date createdAt;

    /** 危急值id */
    @Column(name = "crisis_notice_id")
    @AutoGenerated(locked = true, uuid = "712a25a8-00e9-4b0f-aa02-2bec66c80d3e")
    private String crisisNoticeId;

    /** 术中紧急用血标志 */
    @Column(name = "emergency_blood_use_flag")
    @AutoGenerated(locked = true, uuid = "9b13ed9d-fc99-49cf-88b4-49861e69ecb3")
    private Boolean emergencyBloodUseFlag;

    /** 可执行标志 */
    @Column(name = "executable_flag")
    @AutoGenerated(locked = true, uuid = "573b657e-dad2-4259-93c5-ac20fd2f8f52")
    private Boolean executableFlag;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "13505fcd-0f07-47fb-a835-ee71de8a4b29")
    @Id
    private String id;

    /** 项目ID */
    @Column(name = "item_id")
    @AutoGenerated(locked = true, uuid = "0dc0c0b5-6768-44b1-a57b-36115ab5a7fa")
    private String itemId;

    /** 项目名称 */
    @Column(name = "item_name")
    @AutoGenerated(locked = true, uuid = "661d7dbe-d8ee-47d5-8e83-7123082a2713")
    private String itemName;

    /** 检验状态 */
    @Column(name = "lab_status")
    @AutoGenerated(locked = true, uuid = "054c588a-17d0-475a-8d71-f89f7c660fed")
    private String labStatus;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    @ManyToOne
    @JoinColumn(name = "order_info_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private OrderInfoBO orderInfoBO;

    /** 医嘱接口id */
    @Column(name = "order_interface_id")
    @AutoGenerated(locked = true, uuid = "c60dc208-cb59-4a58-9b7c-eac20e8b9689")
    private String orderInterfaceId;

    /** 执行人员ID */
    @Column(name = "perform_by")
    @AutoGenerated(locked = true, uuid = "60cf58d0-281f-4a30-92b0-72c8cbf4f8c2")
    private String performBy;

    /** 执行日期 */
    @Column(name = "perform_date")
    @AutoGenerated(locked = true, uuid = "37010bb7-d9e8-4b96-93fc-2d8742986ef3")
    private Date performDate;

    /** 执行科室ID */
    @Column(name = "perform_department_id")
    @AutoGenerated(locked = true, uuid = "81c44a51-bfa9-44e9-9586-5587fdb99426")
    private String performDepartmentId;

    /** 生理周期 */
    @Column(name = "physiological_cycle")
    @AutoGenerated(locked = true, uuid = "39a156f1-38ef-42e3-a461-5e2545f436eb")
    private String physiologicalCycle;

    /** 打印人 */
    @Column(name = "print_by")
    @AutoGenerated(locked = true, uuid = "851d1f97-a66a-4ea9-b652-acd19976ff1e")
    private String printBy;

    /** 打印日期 */
    @Column(name = "print_date")
    @AutoGenerated(locked = true, uuid = "456f3295-6db1-4cb9-a53f-613acb5b117b")
    private Date printDate;

    /** 退单人 */
    @Column(name = "refund_by")
    @AutoGenerated(locked = true, uuid = "cbfbb8bd-7147-4831-a777-f078e3bc9562")
    private String refundBy;

    /** 退单日期 */
    @Column(name = "refund_date")
    @AutoGenerated(locked = true, uuid = "bcf6b40a-1d9f-4f41-ad93-bead446c2368")
    private Date refundDate;

    /** 退单标志 */
    @Column(name = "refund_flag")
    @AutoGenerated(locked = true, uuid = "489cb5c0-31e4-4d42-a512-8cb65baeea12")
    private Boolean refundFlag;

    /** 退单原因 */
    @Column(name = "refund_reason")
    @AutoGenerated(locked = true, uuid = "2af5b066-5d5b-46e9-8412-c94abc21bfbc")
    private String refundReason;

    /** 报告人员ID */
    @Column(name = "report_by")
    @AutoGenerated(locked = true, uuid = "1d8c6f4b-1798-4237-8bb7-a66d6f93405d")
    private String reportBy;

    /** 报告日期 */
    @Column(name = "report_date")
    @AutoGenerated(locked = true, uuid = "4307ae8d-c710-4523-873c-22c61bf6b57e")
    private Date reportDate;

    /** 报告查看人 */
    @Column(name = "report_view_by")
    @AutoGenerated(locked = true, uuid = "011038fd-61e4-4dce-9bad-463866c9c3c5")
    private String reportViewBy;

    /** 浏览报告标志 */
    @Column(name = "report_view_flag")
    @AutoGenerated(locked = true, uuid = "97d5db53-9b22-42e1-906c-e358b8590549")
    private Boolean reportViewFlag;

    /** 审核人 */
    @Column(name = "review_by")
    @AutoGenerated(locked = true, uuid = "48804a64-b7d3-4e64-9cf5-6e8fad5766df")
    private String reviewBy;

    /** 审核日期 */
    @Column(name = "review_date")
    @AutoGenerated(locked = true, uuid = "b74909e2-3bc7-4d9f-9d64-f45f49e6fd2b")
    private Date reviewDate;

    /** 审核标志 */
    @Column(name = "review_flag")
    @AutoGenerated(locked = true, uuid = "6f93f0d5-2260-4ad9-8fa4-2a7ac4a9c716")
    private Boolean reviewFlag;

    /** 采集核对人ID */
    @Column(name = "sample_audit_by")
    @AutoGenerated(locked = true, uuid = "abdc701d-0747-4d37-b82d-e191c8eb32c0")
    private String sampleAuditBy;

    /** 采集核对日期 */
    @Column(name = "sample_audit_date")
    @AutoGenerated(locked = true, uuid = "1f3319a9-1f49-4e65-9351-89d813f5fc89")
    private Date sampleAuditDate;

    /** 采集核对标志 */
    @Column(name = "sample_audit_flag")
    @AutoGenerated(locked = true, uuid = "6efd1404-6a7a-4e9f-b33e-6d929278607e")
    private Boolean sampleAuditFlag;

    /** 采集核对操作人 */
    @Column(name = "sample_audit_operator")
    @AutoGenerated(locked = true, uuid = "aedfa099-85cf-4ab1-8ece-b8c89dc8e236")
    private String sampleAuditOperator;

    /** 采集部位 */
    @Column(name = "sample_body")
    @AutoGenerated(locked = true, uuid = "9cfdc05b-c233-474c-a28d-9b86c4c2b758")
    private String sampleBody;

    /** 标本采集人ID */
    @Column(name = "sample_by")
    @AutoGenerated(locked = true, uuid = "04df64e9-5e81-4457-9442-d4a8022fde1d")
    private String sampleBy;

    /** 标本结束采集时间 */
    @Column(name = "sample_end_date")
    @AutoGenerated(locked = true, uuid = "a9079e03-77ff-445c-b3ef-a2ceebcdfa25")
    private Date sampleEndDate;

    /** 采样地点 */
    @Column(name = "sample_location")
    @AutoGenerated(locked = true, uuid = "7391c7a5-17a0-47b6-aba8-7481613feb33")
    private String sampleLocation;

    /** 开始采集操作人ID */
    @Column(name = "sample_operator")
    @AutoGenerated(locked = true, uuid = "aae3a0fd-4606-4d19-8360-5a076d7cbb27")
    private String sampleOperator;

    /** 标本开始采集时间 */
    @Column(name = "sample_start_date")
    @AutoGenerated(locked = true, uuid = "576929a6-01d3-4761-acef-96bd832ebf89")
    private Date sampleStartDate;

    /** 标本开始采集标志 */
    @Column(name = "sample_start_flag")
    @AutoGenerated(locked = true, uuid = "bc6001c1-ff48-44fc-a8e4-85e2a208c577")
    private Boolean sampleStartFlag;

    /** 采集复核人ID */
    @Column(name = "sample_verify_by")
    @AutoGenerated(locked = true, uuid = "9ffb87fb-cd5e-4c93-8627-126f91220c71")
    private String sampleVerifyBy;

    /** 采集复核日期 */
    @Column(name = "sample_verify_date")
    @AutoGenerated(locked = true, uuid = "2e903b67-6dd0-4cfd-908d-122345432010")
    private Date sampleVerifyDate;

    /** 采集复核操作人ID */
    @Column(name = "sample_verify_operator")
    @AutoGenerated(locked = true, uuid = "5c9e23fe-a3e7-4810-9d3f-214557a3b930")
    private String sampleVerifyOperator;

    /** 样本ID */
    @Column(name = "specimen_id")
    @AutoGenerated(locked = true, uuid = "912a2773-be8b-4161-83d2-fecb5a56ed97")
    private String specimenId;

    /** 送检人ID */
    @Column(name = "specimen_inspection_by")
    @AutoGenerated(locked = true, uuid = "91be86a0-0ae0-4b90-b9b5-d62826207005")
    private String specimenInspectionBy;

    /** 送检时间 */
    @Column(name = "specimen_inspection_date")
    @AutoGenerated(locked = true, uuid = "bc411aec-2c70-409d-abd8-716e6d3e94e2")
    private Date specimenInspectionDate;

    /** 送检标志 */
    @Column(name = "specimen_inspection_flag")
    @AutoGenerated(locked = true, uuid = "b01b866b-ed17-4b10-832b-47a6af343fff")
    private Boolean specimenInspectionFlag;

    /** 打包人 */
    @Column(name = "specimen_package_by")
    @AutoGenerated(locked = true, uuid = "56464a93-ae11-4ddb-ae0a-0c97d24a6563")
    private String specimenPackageBy;

    /** 打包日期 */
    @Column(name = "specimen_package_date")
    @AutoGenerated(locked = true, uuid = "6cdf4a37-3be9-492d-ae6f-1548b834f8d4")
    private Date specimenPackageDate;

    /** 打包标志 */
    @Column(name = "specimen_packaging_flag")
    @AutoGenerated(locked = true, uuid = "a3ac63ba-9ce5-48c2-8b37-fc3250b56315")
    private Boolean specimenPackagingFlag;

    /** 标本接收人 */
    @Column(name = "specimen_receive_by")
    @AutoGenerated(locked = true, uuid = "75b6e36d-ba0d-4166-878c-64353342b752")
    private String specimenReceiveBy;

    /** 标本接收日期 */
    @Column(name = "specimen_receive_date")
    @AutoGenerated(locked = true, uuid = "de555f80-d732-4065-b59c-54b17bcd3e88")
    private Date specimenReceiveDate;

    /** 标本接收科室ID */
    @Column(name = "specimen_receive_department_id")
    @AutoGenerated(locked = true, uuid = "3f3e7aef-57ed-4eab-aa54-9b83fee3c0b9")
    private String specimenReceiveDepartmentId;

    /** 标本回退时间 */
    @Column(name = "specimen_return_date")
    @AutoGenerated(locked = true, uuid = "92043924-f3a9-4090-b2a6-91ae896305b7")
    private Date specimenReturnDate;

    /** 标本回退操作人 */
    @Column(name = "specimen_return_operator")
    @AutoGenerated(locked = true, uuid = "6cf0a3c6-9e08-4a8e-8fd8-554bd8a695b0")
    private String specimenReturnOperator;

    /** 标本回退原因 */
    @Column(name = "specimen_return_reason")
    @AutoGenerated(locked = true, uuid = "f6d8f8f4-9497-4ed8-a6c7-b92f0c1280f7")
    private String specimenReturnReason;

    /** 标本采集方法 */
    @Column(name = "specimen_sample_method")
    @AutoGenerated(locked = true, uuid = "02f5c320-c5cf-4057-b5cb-fb47deada55b")
    private String specimenSampleMethod;

    /** 标本发送人员ID */
    @Column(name = "specimen_send_by")
    @AutoGenerated(locked = true, uuid = "ceb25bdb-ac2d-451f-ae14-806f8488ffc3")
    private String specimenSendBy;

    /** 标本发送时间 */
    @Column(name = "specimen_send_date")
    @AutoGenerated(locked = true, uuid = "052bee71-d965-4131-aec1-689f658f801e")
    private Date specimenSendDate;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "64f2976d-f60e-5eca-9e17-ffd3295178d4")
    private Date updatedAt;

    /** 加急类型 */
    @Column(name = "urgency_type")
    @AutoGenerated(locked = true, uuid = "9352c36c-ffff-4356-9bb1-04da05f81876")
    private String urgencyType;

    /** 容器类型 */
    @Column(name = "vessel_type")
    @AutoGenerated(locked = true, uuid = "48e9e268-2c7a-4fe0-97d8-5abfc1be52db")
    private String vesselType;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "ae1abaf8-c70f-4601-b994-20eb9036cdfa|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public OrderLab convertToOrderLab() {
        OrderLab entity = new OrderLab();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "urgencyType",
                "labStatus",
                "itemId",
                "itemName",
                "crisisNoticeId",
                "executableFlag",
                "vesselType",
                "physiologicalCycle",
                "specimenId",
                "barcode",
                "specimenSampleMethod",
                "sampleBody",
                "sampleLocation",
                "sampleStartFlag",
                "sampleStartDate",
                "sampleOperator",
                "sampleBy",
                "sampleEndDate",
                "completeSampleFlag",
                "sampleAuditDate",
                "sampleAuditBy",
                "sampleAuditOperator",
                "sampleAuditFlag",
                "sampleVerifyDate",
                "sampleVerifyBy",
                "sampleVerifyOperator",
                "barcodeScanningFlag",
                "barcodePrintFlag",
                "barcodePrintBy",
                "barcodeCancelDate",
                "barcodeCancelBy",
                "barcodeCancelReason",
                "specimenReturnDate",
                "specimenReturnReason",
                "specimenReturnOperator",
                "specimenSendBy",
                "specimenSendDate",
                "specimenPackagingFlag",
                "specimenPackageDate",
                "specimenPackageBy",
                "specimenInspectionFlag",
                "specimenInspectionBy",
                "specimenInspectionDate",
                "specimenReceiveBy",
                "specimenReceiveDate",
                "specimenReceiveDepartmentId",
                "performBy",
                "performDate",
                "performDepartmentId",
                "completedFlag",
                "completedDate",
                "completedBy",
                "reviewDate",
                "reviewBy",
                "reviewFlag",
                "reportDate",
                "reportBy",
                "reportViewFlag",
                "reportViewBy",
                "printDate",
                "printBy",
                "refundFlag",
                "refundDate",
                "refundBy",
                "refundReason",
                "orderInterfaceId",
                "emergencyBloodUseFlag",
                "computerOperator",
                "computerOperateDate",
                "createdAt",
                "updatedAt");
        OrderInfoBO orderInfoBO = this.getOrderInfoBO();
        entity.setOrderInfoId(orderInfoBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getBarcode() {
        return this.barcode;
    }

    @AutoGenerated(locked = true)
    public String getBarcodeCancelBy() {
        return this.barcodeCancelBy;
    }

    @AutoGenerated(locked = true)
    public Date getBarcodeCancelDate() {
        return this.barcodeCancelDate;
    }

    @AutoGenerated(locked = true)
    public String getBarcodeCancelReason() {
        return this.barcodeCancelReason;
    }

    @AutoGenerated(locked = true)
    public String getBarcodePrintBy() {
        return this.barcodePrintBy;
    }

    @AutoGenerated(locked = true)
    public Boolean getBarcodePrintFlag() {
        return this.barcodePrintFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getBarcodeScanningFlag() {
        return this.barcodeScanningFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getCompleteSampleFlag() {
        return this.completeSampleFlag;
    }

    @AutoGenerated(locked = true)
    public String getCompletedBy() {
        return this.completedBy;
    }

    @AutoGenerated(locked = true)
    public Date getCompletedDate() {
        return this.completedDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getCompletedFlag() {
        return this.completedFlag;
    }

    @AutoGenerated(locked = true)
    public Date getComputerOperateDate() {
        return this.computerOperateDate;
    }

    @AutoGenerated(locked = true)
    public String getComputerOperator() {
        return this.computerOperator;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCrisisNoticeId() {
        return this.crisisNoticeId;
    }

    @AutoGenerated(locked = true)
    public Boolean getEmergencyBloodUseFlag() {
        return this.emergencyBloodUseFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getExecutableFlag() {
        return this.executableFlag;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getItemId() {
        return this.itemId;
    }

    @AutoGenerated(locked = true)
    public String getItemName() {
        return this.itemName;
    }

    @AutoGenerated(locked = true)
    public String getLabStatus() {
        return this.labStatus;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO getOrderInfoBO() {
        return this.orderInfoBO;
    }

    @AutoGenerated(locked = true)
    public String getOrderInfoId() {
        return this.getOrderInfoBO().getId();
    }

    @AutoGenerated(locked = true)
    public String getOrderInterfaceId() {
        return this.orderInterfaceId;
    }

    @AutoGenerated(locked = true)
    public String getPerformBy() {
        return this.performBy;
    }

    @AutoGenerated(locked = true)
    public Date getPerformDate() {
        return this.performDate;
    }

    @AutoGenerated(locked = true)
    public String getPerformDepartmentId() {
        return this.performDepartmentId;
    }

    @AutoGenerated(locked = true)
    public String getPhysiologicalCycle() {
        return this.physiologicalCycle;
    }

    @AutoGenerated(locked = true)
    public String getPrintBy() {
        return this.printBy;
    }

    @AutoGenerated(locked = true)
    public Date getPrintDate() {
        return this.printDate;
    }

    @AutoGenerated(locked = true)
    public String getRefundBy() {
        return this.refundBy;
    }

    @AutoGenerated(locked = true)
    public Date getRefundDate() {
        return this.refundDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getRefundFlag() {
        return this.refundFlag;
    }

    @AutoGenerated(locked = true)
    public String getRefundReason() {
        return this.refundReason;
    }

    @AutoGenerated(locked = true)
    public String getReportBy() {
        return this.reportBy;
    }

    @AutoGenerated(locked = true)
    public Date getReportDate() {
        return this.reportDate;
    }

    @AutoGenerated(locked = true)
    public String getReportViewBy() {
        return this.reportViewBy;
    }

    @AutoGenerated(locked = true)
    public Boolean getReportViewFlag() {
        return this.reportViewFlag;
    }

    @AutoGenerated(locked = true)
    public String getReviewBy() {
        return this.reviewBy;
    }

    @AutoGenerated(locked = true)
    public Date getReviewDate() {
        return this.reviewDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getReviewFlag() {
        return this.reviewFlag;
    }

    @AutoGenerated(locked = true)
    public String getSampleAuditBy() {
        return this.sampleAuditBy;
    }

    @AutoGenerated(locked = true)
    public Date getSampleAuditDate() {
        return this.sampleAuditDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getSampleAuditFlag() {
        return this.sampleAuditFlag;
    }

    @AutoGenerated(locked = true)
    public String getSampleAuditOperator() {
        return this.sampleAuditOperator;
    }

    @AutoGenerated(locked = true)
    public String getSampleBody() {
        return this.sampleBody;
    }

    @AutoGenerated(locked = true)
    public String getSampleBy() {
        return this.sampleBy;
    }

    @AutoGenerated(locked = true)
    public Date getSampleEndDate() {
        return this.sampleEndDate;
    }

    @AutoGenerated(locked = true)
    public String getSampleLocation() {
        return this.sampleLocation;
    }

    @AutoGenerated(locked = true)
    public String getSampleOperator() {
        return this.sampleOperator;
    }

    @AutoGenerated(locked = true)
    public Date getSampleStartDate() {
        return this.sampleStartDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getSampleStartFlag() {
        return this.sampleStartFlag;
    }

    @AutoGenerated(locked = true)
    public String getSampleVerifyBy() {
        return this.sampleVerifyBy;
    }

    @AutoGenerated(locked = true)
    public Date getSampleVerifyDate() {
        return this.sampleVerifyDate;
    }

    @AutoGenerated(locked = true)
    public String getSampleVerifyOperator() {
        return this.sampleVerifyOperator;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenId() {
        return this.specimenId;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenInspectionBy() {
        return this.specimenInspectionBy;
    }

    @AutoGenerated(locked = true)
    public Date getSpecimenInspectionDate() {
        return this.specimenInspectionDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getSpecimenInspectionFlag() {
        return this.specimenInspectionFlag;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenPackageBy() {
        return this.specimenPackageBy;
    }

    @AutoGenerated(locked = true)
    public Date getSpecimenPackageDate() {
        return this.specimenPackageDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getSpecimenPackagingFlag() {
        return this.specimenPackagingFlag;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenReceiveBy() {
        return this.specimenReceiveBy;
    }

    @AutoGenerated(locked = true)
    public Date getSpecimenReceiveDate() {
        return this.specimenReceiveDate;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenReceiveDepartmentId() {
        return this.specimenReceiveDepartmentId;
    }

    @AutoGenerated(locked = true)
    public Date getSpecimenReturnDate() {
        return this.specimenReturnDate;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenReturnOperator() {
        return this.specimenReturnOperator;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenReturnReason() {
        return this.specimenReturnReason;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenSampleMethod() {
        return this.specimenSampleMethod;
    }

    @AutoGenerated(locked = true)
    public String getSpecimenSendBy() {
        return this.specimenSendBy;
    }

    @AutoGenerated(locked = true)
    public Date getSpecimenSendDate() {
        return this.specimenSendDate;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUrgencyType() {
        return this.urgencyType;
    }

    @AutoGenerated(locked = true)
    public String getVesselType() {
        return this.vesselType;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcode(String barcode) {
        this.barcode = barcode;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcodeCancelBy(String barcodeCancelBy) {
        this.barcodeCancelBy = barcodeCancelBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcodeCancelDate(Date barcodeCancelDate) {
        this.barcodeCancelDate = barcodeCancelDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcodeCancelReason(String barcodeCancelReason) {
        this.barcodeCancelReason = barcodeCancelReason;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcodePrintBy(String barcodePrintBy) {
        this.barcodePrintBy = barcodePrintBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcodePrintFlag(Boolean barcodePrintFlag) {
        this.barcodePrintFlag = barcodePrintFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setBarcodeScanningFlag(Boolean barcodeScanningFlag) {
        this.barcodeScanningFlag = barcodeScanningFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCompleteSampleFlag(Boolean completeSampleFlag) {
        this.completeSampleFlag = completeSampleFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCompletedBy(String completedBy) {
        this.completedBy = completedBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCompletedFlag(Boolean completedFlag) {
        this.completedFlag = completedFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setComputerOperateDate(Date computerOperateDate) {
        this.computerOperateDate = computerOperateDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setComputerOperator(String computerOperator) {
        this.computerOperator = computerOperator;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCrisisNoticeId(String crisisNoticeId) {
        this.crisisNoticeId = crisisNoticeId;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setEmergencyBloodUseFlag(Boolean emergencyBloodUseFlag) {
        this.emergencyBloodUseFlag = emergencyBloodUseFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setExecutableFlag(Boolean executableFlag) {
        this.executableFlag = executableFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setId(String id) {
        this.id = id;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setItemId(String itemId) {
        this.itemId = itemId;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setItemName(String itemName) {
        this.itemName = itemName;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setLabStatus(String labStatus) {
        this.labStatus = labStatus;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setOrderInfoBO(OrderInfoBO orderInfoBO) {
        this.orderInfoBO = orderInfoBO;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setOrderInterfaceId(String orderInterfaceId) {
        this.orderInterfaceId = orderInterfaceId;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setPerformBy(String performBy) {
        this.performBy = performBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setPerformDate(Date performDate) {
        this.performDate = performDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setPerformDepartmentId(String performDepartmentId) {
        this.performDepartmentId = performDepartmentId;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setPhysiologicalCycle(String physiologicalCycle) {
        this.physiologicalCycle = physiologicalCycle;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setPrintBy(String printBy) {
        this.printBy = printBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setPrintDate(Date printDate) {
        this.printDate = printDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setRefundBy(String refundBy) {
        this.refundBy = refundBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setRefundDate(Date refundDate) {
        this.refundDate = refundDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setRefundFlag(Boolean refundFlag) {
        this.refundFlag = refundFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setRefundReason(String refundReason) {
        this.refundReason = refundReason;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReportBy(String reportBy) {
        this.reportBy = reportBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReportDate(Date reportDate) {
        this.reportDate = reportDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReportViewBy(String reportViewBy) {
        this.reportViewBy = reportViewBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReportViewFlag(Boolean reportViewFlag) {
        this.reportViewFlag = reportViewFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReviewBy(String reviewBy) {
        this.reviewBy = reviewBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setReviewFlag(Boolean reviewFlag) {
        this.reviewFlag = reviewFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleAuditBy(String sampleAuditBy) {
        this.sampleAuditBy = sampleAuditBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleAuditDate(Date sampleAuditDate) {
        this.sampleAuditDate = sampleAuditDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleAuditFlag(Boolean sampleAuditFlag) {
        this.sampleAuditFlag = sampleAuditFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleAuditOperator(String sampleAuditOperator) {
        this.sampleAuditOperator = sampleAuditOperator;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleBody(String sampleBody) {
        this.sampleBody = sampleBody;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleBy(String sampleBy) {
        this.sampleBy = sampleBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleEndDate(Date sampleEndDate) {
        this.sampleEndDate = sampleEndDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleLocation(String sampleLocation) {
        this.sampleLocation = sampleLocation;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleOperator(String sampleOperator) {
        this.sampleOperator = sampleOperator;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleStartDate(Date sampleStartDate) {
        this.sampleStartDate = sampleStartDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleStartFlag(Boolean sampleStartFlag) {
        this.sampleStartFlag = sampleStartFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleVerifyBy(String sampleVerifyBy) {
        this.sampleVerifyBy = sampleVerifyBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleVerifyDate(Date sampleVerifyDate) {
        this.sampleVerifyDate = sampleVerifyDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSampleVerifyOperator(String sampleVerifyOperator) {
        this.sampleVerifyOperator = sampleVerifyOperator;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenId(String specimenId) {
        this.specimenId = specimenId;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenInspectionBy(String specimenInspectionBy) {
        this.specimenInspectionBy = specimenInspectionBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenInspectionDate(Date specimenInspectionDate) {
        this.specimenInspectionDate = specimenInspectionDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenInspectionFlag(Boolean specimenInspectionFlag) {
        this.specimenInspectionFlag = specimenInspectionFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenPackageBy(String specimenPackageBy) {
        this.specimenPackageBy = specimenPackageBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenPackageDate(Date specimenPackageDate) {
        this.specimenPackageDate = specimenPackageDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenPackagingFlag(Boolean specimenPackagingFlag) {
        this.specimenPackagingFlag = specimenPackagingFlag;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenReceiveBy(String specimenReceiveBy) {
        this.specimenReceiveBy = specimenReceiveBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenReceiveDate(Date specimenReceiveDate) {
        this.specimenReceiveDate = specimenReceiveDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenReceiveDepartmentId(String specimenReceiveDepartmentId) {
        this.specimenReceiveDepartmentId = specimenReceiveDepartmentId;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenReturnDate(Date specimenReturnDate) {
        this.specimenReturnDate = specimenReturnDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenReturnOperator(String specimenReturnOperator) {
        this.specimenReturnOperator = specimenReturnOperator;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenReturnReason(String specimenReturnReason) {
        this.specimenReturnReason = specimenReturnReason;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenSampleMethod(String specimenSampleMethod) {
        this.specimenSampleMethod = specimenSampleMethod;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenSendBy(String specimenSendBy) {
        this.specimenSendBy = specimenSendBy;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setSpecimenSendDate(Date specimenSendDate) {
        this.specimenSendDate = specimenSendDate;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setUrgencyType(String urgencyType) {
        this.urgencyType = urgencyType;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setVesselType(String vesselType) {
        this.vesselType = vesselType;
        return (OrderLabBO) this;
    }
}
