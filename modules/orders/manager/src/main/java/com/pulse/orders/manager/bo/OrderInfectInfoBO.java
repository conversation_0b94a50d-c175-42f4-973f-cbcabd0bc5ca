package com.pulse.orders.manager.bo;

import com.pulse.orders.persist.dos.OrderInfectInfo;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "order_infect_info")
@Entity
@AutoGenerated(locked = true, uuid = "27d6071d-5b7d-4a93-8e28-e83b8ba4b2f6|BO|DEFINITION")
public class OrderInfectInfoBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "a74d55a3-db1d-50af-9665-e56a06f6d331")
    private Date createdAt;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "cc3f86d6-3bc6-404f-8e30-56c0430992fb")
    @Id
    private String id;

    /** 传染病分类ID */
    @Column(name = "infect_class_id")
    @AutoGenerated(locked = true, uuid = "ab652992-2f14-4602-860d-a0590c8f40bb")
    private String infectClassId;

    /** 传染病结果 */
    @Column(name = "infect_result")
    @AutoGenerated(locked = true, uuid = "7a173322-92a0-4fdd-a12a-edb29f005f6c")
    private String infectResult;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    @ManyToOne
    @JoinColumn(name = "order_info_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private OrderInfoBO orderInfoBO;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "af65b5a5-0ffd-5d3b-9188-7259741eadb1")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "c60d1fa7-1109-45a0-b90c-b976cba70ef0|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public OrderInfectInfo convertToOrderInfectInfo() {
        OrderInfectInfo entity = new OrderInfectInfo();
        BoUtil.copyProperties(
                this, entity, "id", "infectClassId", "infectResult", "createdAt", "updatedAt");
        OrderInfoBO orderInfoBO = this.getOrderInfoBO();
        entity.setOrderInfoId(orderInfoBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getInfectClassId() {
        return this.infectClassId;
    }

    @AutoGenerated(locked = true)
    public String getInfectResult() {
        return this.infectResult;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO getOrderInfoBO() {
        return this.orderInfoBO;
    }

    @AutoGenerated(locked = true)
    public String getOrderInfoId() {
        return this.getOrderInfoBO().getId();
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public OrderInfectInfoBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (OrderInfectInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfectInfoBO setId(String id) {
        this.id = id;
        return (OrderInfectInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfectInfoBO setInfectClassId(String infectClassId) {
        this.infectClassId = infectClassId;
        return (OrderInfectInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfectInfoBO setInfectResult(String infectResult) {
        this.infectResult = infectResult;
        return (OrderInfectInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfectInfoBO setOrderInfoBO(OrderInfoBO orderInfoBO) {
        this.orderInfoBO = orderInfoBO;
        return (OrderInfectInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfectInfoBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (OrderInfectInfoBO) this;
    }
}
