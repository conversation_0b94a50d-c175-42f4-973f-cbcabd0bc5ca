package com.pulse.orders.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "913927c7-342b-4eaf-91a8-d2aa193ef75f|DTO|DEFINITION")
public class OrderOperationDetailBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "c9222956-b851-4a33-9d39-3c63a48712f4")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "50deb6e5-1f8b-4b93-bd43-5038a4233ab0")
    private String id;

    /** 手术医嘱ID */
    @AutoGenerated(locked = true, uuid = "5f164d44-0ec6-44c1-9548-2d4ef89d2251")
    private String orderOperationId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "49bcb23c-718d-4a48-860a-5c0795872bff")
    private Date updatedAt;
}
