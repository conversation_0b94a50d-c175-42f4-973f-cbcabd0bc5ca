package com.pulse.patient_information.service.mq.consumer;

import com.pulse.patient_information.manager.mo.UpdatePatientMo;
import com.pulse.patient_information.service.mq.consumer.base.BaseUpdatePatientMoConsumer;
import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/** 消息UpdatePatientMo的消费 */
@Slf4j
@Component("PatientInformation_UpdatePatientMoConsumer")
@AutoGenerated(locked = false, uuid = "c30ffd94-dd42-3f6d-8e9d-004a97369ab8")
public class UpdatePatientMoConsumer extends BaseUpdatePatientMoConsumer {

    /** 实现At Least Once 的语义， 实现消费功能。返回False，进入重试队列，消费失败超过重试次数，进入死信队列 */
    @AutoGenerated(
            locked = false,
            uuid = "401a13c9-0cf3-4e0a-8ab7-e94842a29d74_78aed3e4-658c-4d41-9b14-fac2ab4166fd")
    public Boolean handleMessage(UpdatePatientMo message) {
        return true;
    }
}
