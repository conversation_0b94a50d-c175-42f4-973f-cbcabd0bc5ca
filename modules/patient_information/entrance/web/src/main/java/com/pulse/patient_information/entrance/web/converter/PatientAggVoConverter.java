package com.pulse.patient_information.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.patient_information.entrance.web.query.assembler.PatientAggVoDataAssembler;
import com.pulse.patient_information.entrance.web.query.assembler.PatientAggVoDataAssembler.PatientAggVoDataHolder;
import com.pulse.patient_information.entrance.web.query.collector.PatientAggVoDataCollector;
import com.pulse.patient_information.entrance.web.vo.PatientAggVo;
import com.pulse.patient_information.entrance.web.vo.PatientIdentificationBaseVo;
import com.pulse.patient_information.entrance.web.vo.PatientProfileBaseVo;
import com.pulse.patient_information.entrance.web.vo.PatientRelationshipBaseVo;
import com.pulse.patient_information.manager.dto.PatientAggDto;
import com.pulse.patient_information.manager.dto.PatientIdentificationBaseDto;
import com.pulse.patient_information.manager.dto.PatientProfileBaseDto;
import com.pulse.patient_information.manager.dto.PatientRelationshipBaseDto;
import com.pulse.patient_information.service.PatientBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到PatientAggVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "0ca7644d-d2fa-47dc-a8a1-c4ffbd6f41a3|VO|CONVERTER")
public class PatientAggVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private PatientAggVoDataAssembler patientAggVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private PatientAggVoDataCollector patientAggVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private PatientBaseDtoService patientBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private PatientIdentificationBaseVoConverter patientIdentificationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private PatientProfileBaseVoConverter patientProfileBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private PatientRelationshipBaseVoConverter patientRelationshipBaseVoConverter;

    /** 把PatientAggDto转换成PatientAggVo */
    @AutoGenerated(locked = false, uuid = "0ca7644d-d2fa-47dc-a8a1-c4ffbd6f41a3-converter-Map")
    public Map<PatientAggDto, PatientAggVo> convertToPatientAggVoMap(List<PatientAggDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<PatientRelationshipBaseDto, PatientRelationshipBaseVo> patientRelationshipListMap =
                patientRelationshipBaseVoConverter.convertToPatientRelationshipBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getPatientRelationshipList()))
                                .flatMap(dto -> dto.getPatientRelationshipList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<PatientIdentificationBaseDto, PatientIdentificationBaseVo>
                patientIdentificationListMap =
                        patientIdentificationBaseVoConverter
                                .convertToPatientIdentificationBaseVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getPatientIdentificationList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getPatientIdentificationList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<PatientProfileBaseDto, PatientProfileBaseVo> patientProfileMap =
                patientProfileBaseVoConverter.convertToPatientProfileBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(PatientAggDto::getPatientProfile)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<PatientAggDto, PatientAggVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            PatientAggVo vo = new PatientAggVo();
                                            vo.setId(dto.getId());
                                            vo.setDisplayId(dto.getDisplayId());
                                            vo.setName(dto.getName());
                                            vo.setNameInputCode(dto.getNameInputCode());
                                            vo.setTranslateName(dto.getTranslateName());
                                            vo.setPublicFundedUnit(dto.getPublicFundedUnit());
                                            vo.setPublicMedicalExpensesCertificateNumber(
                                                    dto
                                                            .getPublicMedicalExpensesCertificateNumber());
                                            vo.setGender(dto.getGender());
                                            vo.setIdNumber(dto.getIdNumber());
                                            vo.setInsuranceTypeId(dto.getInsuranceTypeId());
                                            vo.setInsuranceNumber(dto.getInsuranceNumber());
                                            vo.setInsuranceCardNumber(dto.getInsuranceCardNumber());
                                            vo.setCellphone(dto.getCellphone());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setDefaultChargeType(dto.getDefaultChargeType());
                                            vo.setIdentityCode(dto.getIdentityCode());
                                            vo.setBirthday(dto.getBirthday());
                                            vo.setBirthAddress(dto.getBirthAddress());
                                            vo.setUnknownFlag(dto.getUnknownFlag());
                                            vo.setBloodCardFlag(dto.getBloodCardFlag());
                                            vo.setDisabilityFlag(dto.getDisabilityFlag());
                                            vo.setVipFlag(dto.getVipFlag());
                                            vo.setStatus(dto.getStatus());
                                            vo.setAvatar(dto.getAvatar());
                                            vo.setCommercialInsuranceFlag(
                                                    dto.getCommercialInsuranceFlag());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setIdType(dto.getIdType());
                                            vo.setUnknownType(dto.getUnknownType());
                                            vo.setMainAccountFlag(dto.getMainAccountFlag());
                                            vo.setGreenChannelFlag(dto.getGreenChannelFlag());
                                            vo.setPatientRelationshipList(
                                                    dto.getPatientRelationshipList() == null
                                                            ? null
                                                            : dto
                                                                    .getPatientRelationshipList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    patientRelationshipListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setPatientIdentificationList(
                                                    dto.getPatientIdentificationList() == null
                                                            ? null
                                                            : dto
                                                                    .getPatientIdentificationList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    patientIdentificationListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setPatientProfile(
                                                    dto.getPatientProfile() == null
                                                            ? null
                                                            : patientProfileMap.get(
                                                                    dto.getPatientProfile()));
                                            vo.setPublicFundedLevel(dto.getPublicFundedLevel());
                                            vo.setChildrenCoordinatedFlag(
                                                    dto.getChildrenCoordinatedFlag());
                                            vo.setChildrenCoordinatedValidDate(
                                                    dto.getChildrenCoordinatedValidDate());
                                            vo.setVeteranFlag(dto.getVeteranFlag());
                                            vo.setModelWorkerFlag(dto.getModelWorkerFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把PatientAggDto转换成PatientAggVo */
    @AutoGenerated(locked = true, uuid = "0ca7644d-d2fa-47dc-a8a1-c4ffbd6f41a3-converter-list")
    public List<PatientAggVo> convertToPatientAggVoList(List<PatientAggDto> dtoList) {
        return new ArrayList<>(convertToPatientAggVoMap(dtoList).values());
    }

    /** 使用默认方式组装PatientAggVo数据 */
    @AutoGenerated(locked = true, uuid = "17478e89-d276-34d3-93fb-1e8a8b4dfaef")
    public PatientAggVo convertAndAssembleData(PatientAggDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把PatientAggDto转换成PatientAggVo */
    @AutoGenerated(locked = true, uuid = "7c2fa540-0233-30e3-92fa-5da2e925f9e0")
    public PatientAggVo convertToPatientAggVo(PatientAggDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToPatientAggVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装PatientAggVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fd1a2240-93dc-3aa9-8100-facf21bdb0e2")
    public List<PatientAggVo> convertAndAssembleDataList(List<PatientAggDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        PatientAggVoDataHolder dataHolder = new PatientAggVoDataHolder();
        dataHolder.setRootBaseDtoList(
                patientBaseDtoService.getByIds(
                        dtoList.stream().map(PatientAggDto::getId).collect(Collectors.toList())));
        Map<String, PatientAggVo> voMap =
                convertToPatientAggVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        patientAggVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        patientAggVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
