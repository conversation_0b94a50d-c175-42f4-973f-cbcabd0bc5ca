package com.pulse.patient_information.entrance.web.query.assembler;

import com.pulse.patient_information.entrance.web.vo.PatientSpecialVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** PatientSpecialVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "57c71409-d337-359e-b001-2302d52278ef")
public class PatientSpecialVoDataAssembler {

    /** 组装PatientSpecialVo数据 */
    @AutoGenerated(locked = true, uuid = "23b54669-7254-3c13-81c2-c6a8c42fc476")
    public void assembleData(Map<String, PatientSpecialVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装PatientSpecialVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "625dfb9a-d748-38a6-be40-e21c283123dd")
    public void assembleDataCustomized(List<PatientSpecialVo> dataList) {
        // 自定义数据组装

    }
}
