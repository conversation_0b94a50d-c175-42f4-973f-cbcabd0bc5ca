package com.pulse.patient_information.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.patient_information.entrance.web.query.assembler.PatientRelationshipBaseVoDataAssembler;
import com.pulse.patient_information.entrance.web.vo.PatientRelationshipBaseVo;
import com.pulse.patient_information.manager.dto.PatientRelationshipBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到PatientRelationshipBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "871ddb4c-279c-4cf0-a01c-62e602ba2801|VO|CONVERTER")
public class PatientRelationshipBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private PatientRelationshipBaseVoDataAssembler patientRelationshipBaseVoDataAssembler;

    /** 把PatientRelationshipBaseDto转换成PatientRelationshipBaseVo */
    @AutoGenerated(locked = false, uuid = "871ddb4c-279c-4cf0-a01c-62e602ba2801-converter-Map")
    public Map<PatientRelationshipBaseDto, PatientRelationshipBaseVo>
            convertToPatientRelationshipBaseVoMap(List<PatientRelationshipBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<PatientRelationshipBaseDto, PatientRelationshipBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            PatientRelationshipBaseVo vo =
                                                    new PatientRelationshipBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setWechatAccount(dto.getWechatAccount());
                                            vo.setPatientId(dto.getPatientId());
                                            vo.setName(dto.getName());
                                            vo.setRelationshipType(dto.getRelationshipType());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setIdentificationClass(dto.getIdentificationClass());
                                            vo.setIdentificationNumber(
                                                    dto.getIdentificationNumber());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setGuardianFlag(dto.getGuardianFlag());
                                            vo.setAddress(dto.getAddress());
                                            vo.setFamilyDisplayId(dto.getFamilyDisplayId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把PatientRelationshipBaseDto转换成PatientRelationshipBaseVo */
    @AutoGenerated(locked = true, uuid = "871ddb4c-279c-4cf0-a01c-62e602ba2801-converter-list")
    public List<PatientRelationshipBaseVo> convertToPatientRelationshipBaseVoList(
            List<PatientRelationshipBaseDto> dtoList) {
        return new ArrayList<>(convertToPatientRelationshipBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装PatientRelationshipBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "c1655a8f-9456-3291-a1af-b4735276376a")
    public PatientRelationshipBaseVo convertAndAssembleData(PatientRelationshipBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装PatientRelationshipBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "ca8bc5c4-e879-3112-9728-bcf6991df2cf")
    public List<PatientRelationshipBaseVo> convertAndAssembleDataList(
            List<PatientRelationshipBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, PatientRelationshipBaseVo> voMap =
                convertToPatientRelationshipBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        patientRelationshipBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把PatientRelationshipBaseDto转换成PatientRelationshipBaseVo */
    @AutoGenerated(locked = true, uuid = "d0fe4053-7056-3fa6-b4b8-503169cb3b34")
    public PatientRelationshipBaseVo convertToPatientRelationshipBaseVo(
            PatientRelationshipBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToPatientRelationshipBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
