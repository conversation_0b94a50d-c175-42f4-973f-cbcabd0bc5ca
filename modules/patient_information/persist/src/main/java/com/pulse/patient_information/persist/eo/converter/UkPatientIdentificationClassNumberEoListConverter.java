package com.pulse.patient_information.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.patient_information.persist.eo.UkPatientIdentificationClassNumberEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<UkPatientIdentificationClassNumberEo> */
@Converter
@AutoGenerated(locked = true, uuid = "ba1f175f-fbec-39d1-a847-40d86580c7b4")
public class UkPatientIdentificationClassNumberEoListConverter
        implements AttributeConverter<List<UkPatientIdentificationClassNumberEo>, String> {

    /** convert List<UkPatientIdentificationClassNumberEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            List<UkPatientIdentificationClassNumberEo> ukPatientIdentificationClassNumberEoList) {
        if (ukPatientIdentificationClassNumberEoList == null
                || ukPatientIdentificationClassNumberEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(ukPatientIdentificationClassNumberEoList);
        }
    }

    /** convert DB column to List<UkPatientIdentificationClassNumberEo> */
    @AutoGenerated(locked = true)
    public List<UkPatientIdentificationClassNumberEo> convertToEntityAttribute(
            String ukPatientIdentificationClassNumberEoListJson) {
        if (StrUtil.isEmpty(ukPatientIdentificationClassNumberEoListJson)) {
            return new ArrayList<UkPatientIdentificationClassNumberEo>();
        } else {
            return JsonUtils.readObject(
                    ukPatientIdentificationClassNumberEoListJson,
                    new TypeReference<List<UkPatientIdentificationClassNumberEo>>() {});
        }
    }
}
