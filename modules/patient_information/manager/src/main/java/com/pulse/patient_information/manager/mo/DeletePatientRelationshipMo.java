package com.pulse.patient_information.manager.mo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "9f5bd0d3-b6a4-4e5b-a739-cc5952521fc6|DMO|DEFINITION")
public class DeletePatientRelationshipMo {
    /** 地址 */
    @AutoGenerated(locked = true, uuid = "540f57c9-b9ba-4f66-a3d8-28e827db2a5b")
    private String address;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "374daacb-a740-4fe0-9420-3bb318cf6e98")
    private Date createdAt;

    /** 创作者 */
    @AutoGenerated(locked = true, uuid = "03b58552-2971-4528-a783-71f72cca84da")
    private String createdBy;

    /** 家属病案号 */
    @AutoGenerated(locked = true, uuid = "c820d4c9-d0f2-4839-8ae7-deb4bdb085f9")
    private String familyDisplayId;

    /** 监护人标识 */
    @AutoGenerated(locked = true, uuid = "ceb29749-d368-4f60-9071-1770bd70a3d9")
    private Boolean guardianFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "833a0b6d-02f0-4f0e-8b31-5b0121cdac3b")
    private String id;

    /** 证件类型 */
    @AutoGenerated(locked = true, uuid = "e7a2c430-b2ed-40d9-b174-567c0bfe20ef")
    private String identificationClass;

    /** 证件号 */
    @AutoGenerated(locked = true, uuid = "08fbc706-a519-4b89-a23a-a6c5546a2637")
    private String identificationNumber;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "bdeddee1-5c3e-4f86-a749-114d4b9afd42")
    private String name;

    /** 患者id */
    @AutoGenerated(locked = true, uuid = "17cd8e0b-d065-4809-b8dc-e4cd4a55def7")
    private String patientId;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "1db00815-1154-43d6-9549-f59597bdb727")
    private String phoneNumber;

    /** 关系类型 */
    @AutoGenerated(locked = true, uuid = "be468ef0-2bca-44a5-baaf-9b8e369276bf")
    private String relationshipType;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "9e596a0a-8441-476e-bbf3-47d0b9fb44c8")
    private String sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "68c4ae89-3040-4a6f-8196-2c54a4c632ad")
    private Date updatedAt;

    /** 操作员 */
    @AutoGenerated(locked = true, uuid = "6039e62e-d17d-432a-8a34-c116a1c93845")
    private String updatedBy;

    /** 微信号 */
    @AutoGenerated(locked = true, uuid = "3a250f9c-d49b-442b-a866-c91ff7de3309")
    private String wechatAccount;
}
