package com.pulse.patient_information.manager.mo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "8eda152b-4911-4342-9e7f-69a6d88f8fd7|DMO|DEFINITION")
public class UpdatePatientProfileMo {
    /** 国籍 */
    @AutoGenerated(locked = true, uuid = "d29fc254-8aa1-481e-a3a4-4fe9bdffd6a5")
    private String citizenship;

    /** 国籍 */
    @AutoGenerated(locked = true, uuid = "d29fc254-8aa1-481e-a3a4-4fe9bdffd6a5")
    private String citizenshipOld;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "0ddd4c11-9dae-4048-8579-0b4d570f8e0c")
    private Date createdAt;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "0ddd4c11-9dae-4048-8579-0b4d570f8e0c")
    private Date createdAtOld;

    /** 现住地址 */
    @AutoGenerated(locked = true, uuid = "8c23aab4-6602-4747-bb7e-e961b8a537c0")
    private String currentAddress;

    /** 现住地址 */
    @AutoGenerated(locked = true, uuid = "8c23aab4-6602-4747-bb7e-e961b8a537c0")
    private String currentAddressOld;

    /** 学历 */
    @AutoGenerated(locked = true, uuid = "5eb0e61e-4c2f-4ffa-ab2c-e6b898c41842")
    private String educationLevel;

    /** 学历 */
    @AutoGenerated(locked = true, uuid = "5eb0e61e-4c2f-4ffa-ab2c-e6b898c41842")
    private String educationLevelOld;

    /** 电子邮件 */
    @AutoGenerated(locked = true, uuid = "e0debb81-1f6c-4291-904d-6347c89ea11a")
    private String email;

    /** 电子邮件 */
    @AutoGenerated(locked = true, uuid = "e0debb81-1f6c-4291-904d-6347c89ea11a")
    private String emailOld;

    /** 外部平台ID */
    @AutoGenerated(locked = true, uuid = "ec512d9e-9574-435f-9ea2-8ce817615663")
    private String externalPlatformId;

    /** 外部平台ID */
    @AutoGenerated(locked = true, uuid = "ec512d9e-9574-435f-9ea2-8ce817615663")
    private String externalPlatformIdOld;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "ee26a49b-4a70-4283-aab7-9f66d1629493")
    private String id;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "ee26a49b-4a70-4283-aab7-9f66d1629493")
    private String idOld;

    /** 职业 */
    @AutoGenerated(locked = true, uuid = "209446e3-a735-47c9-b703-da3dc0b26660")
    private String job;

    /** 职业 */
    @AutoGenerated(locked = true, uuid = "209446e3-a735-47c9-b703-da3dc0b26660")
    private String jobOld;

    /** 婚姻状态 */
    @AutoGenerated(locked = true, uuid = "c7d20f2b-8ab5-4c1f-a2d0-8f4c2045927f")
    private String marriageStatus;

    /** 婚姻状态 */
    @AutoGenerated(locked = true, uuid = "c7d20f2b-8ab5-4c1f-a2d0-8f4c2045927f")
    private String marriageStatusOld;

    /** 民族 */
    @AutoGenerated(locked = true, uuid = "59820f0f-fd4e-45e2-a955-576c023962a9")
    private String nation;

    /** 民族 */
    @AutoGenerated(locked = true, uuid = "59820f0f-fd4e-45e2-a955-576c023962a9")
    private String nationOld;

    /** 籍贯 */
    @AutoGenerated(locked = true, uuid = "e85ddba6-f458-4812-ab8c-93814dc6c6ae")
    private String nativeAddress;

    /** 籍贯 */
    @AutoGenerated(locked = true, uuid = "e85ddba6-f458-4812-ab8c-93814dc6c6ae")
    private String nativeAddressOld;

    /** 病人建档来源 */
    @AutoGenerated(locked = true, uuid = "7e831a8f-d63f-4716-9ff5-a519f35a1d72")
    private String patientCaseSource;

    /** 病人建档来源 */
    @AutoGenerated(locked = true, uuid = "7e831a8f-d63f-4716-9ff5-a519f35a1d72")
    private String patientCaseSourceOld;

    /** 患者id */
    @AutoGenerated(locked = true, uuid = "b72c1600-bc17-41f3-82f6-cb43d39581dc")
    private String patientId;

    /** 患者id */
    @AutoGenerated(locked = true, uuid = "b72c1600-bc17-41f3-82f6-cb43d39581dc")
    private String patientIdOld;

    /** 警医共建单位编码 */
    @AutoGenerated(locked = true, uuid = "642a027b-032f-45f6-8a0d-4f56bc155b47")
    private String policeStationName;

    /** 警医共建单位编码 */
    @AutoGenerated(locked = true, uuid = "642a027b-032f-45f6-8a0d-4f56bc155b47")
    private String policeStationNameOld;

    /** 户口地址 */
    @AutoGenerated(locked = true, uuid = "becb31de-71f6-4956-85c7-683a13300074")
    private String registeredResidenceAddress;

    /** 户口地址 */
    @AutoGenerated(locked = true, uuid = "becb31de-71f6-4956-85c7-683a13300074")
    private String registeredResidenceAddressOld;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "64a11c0d-2cba-4062-a6c8-fbf8a0ffa135")
    private String remark;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "64a11c0d-2cba-4062-a6c8-fbf8a0ffa135")
    private String remarkOld;

    /** 保密等级 */
    @AutoGenerated(locked = true, uuid = "965c1c52-ec18-456e-a175-80aeb021aa28")
    private String securityLevel;

    /** 保密等级 */
    @AutoGenerated(locked = true, uuid = "965c1c52-ec18-456e-a175-80aeb021aa28")
    private String securityLevelOld;

    /** 专用病例过期时间 */
    @AutoGenerated(locked = true, uuid = "82c507b5-e91e-4296-8944-94355b1c0d46")
    private String specialMedicalEndDate;

    /** 专用病例过期时间 */
    @AutoGenerated(locked = true, uuid = "82c507b5-e91e-4296-8944-94355b1c0d46")
    private String specialMedicalEndDateOld;

    /** 专用病历号 */
    @AutoGenerated(locked = true, uuid = "ee1477cd-3575-4404-87af-af9a8afc0730")
    private String specialMedicalRecordNumber;

    /** 专用病历号 */
    @AutoGenerated(locked = true, uuid = "ee1477cd-3575-4404-87af-af9a8afc0730")
    private String specialMedicalRecordNumberOld;

    /** 合同单位 */
    @AutoGenerated(locked = true, uuid = "db11afc5-e291-403a-809f-dddc18206392")
    private String unitInContract;

    /** 合同单位 */
    @AutoGenerated(locked = true, uuid = "db11afc5-e291-403a-809f-dddc18206392")
    private String unitInContractOld;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "a0a981a3-49e2-4ef3-86aa-179bd2e68646")
    private Date updatedAt;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "a0a981a3-49e2-4ef3-86aa-179bd2e68646")
    private Date updatedAtOld;

    /** 支付宝账号 */
    @AutoGenerated(locked = true, uuid = "7933237e-733f-4766-8e85-d354954e5cda")
    private String userAccount;

    /** 支付宝账号 */
    @AutoGenerated(locked = true, uuid = "7933237e-733f-4766-8e85-d354954e5cda")
    private String userAccountOld;

    /** 微信号 */
    @AutoGenerated(locked = true, uuid = "19692fdf-7625-4791-a26b-9e96b367d449")
    private String wechatAccount;

    /** 微信号 */
    @AutoGenerated(locked = true, uuid = "19692fdf-7625-4791-a26b-9e96b367d449")
    private String wechatAccountOld;

    /** 工作单位地址 */
    @AutoGenerated(locked = true, uuid = "c7c757ec-606d-4a0b-9ca5-228f58a1d045")
    private String workUnit;

    /** 工作单位地址 */
    @AutoGenerated(locked = true, uuid = "c7c757ec-606d-4a0b-9ca5-228f58a1d045")
    private String workUnitOld;
}
