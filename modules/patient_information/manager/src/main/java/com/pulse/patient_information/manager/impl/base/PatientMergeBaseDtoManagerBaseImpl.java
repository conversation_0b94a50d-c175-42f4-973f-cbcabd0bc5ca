package com.pulse.patient_information.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.patient_information.manager.PatientMergeBaseDtoManager;
import com.pulse.patient_information.manager.converter.PatientMergeBaseDtoConverter;
import com.pulse.patient_information.manager.dto.PatientMergeBaseDto;
import com.pulse.patient_information.persist.dos.PatientMerge;
import com.pulse.patient_information.persist.mapper.PatientMergeDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "454de7cb-9369-4d45-9f95-2f761cc2e32e|DTO|BASE_MANAGER_IMPL")
public abstract class PatientMergeBaseDtoManagerBaseImpl implements PatientMergeBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private PatientMergeBaseDtoConverter patientMergeBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private PatientMergeDao patientMergeDao;

    @AutoGenerated(locked = true, uuid = "155c5e4f-320a-3869-8a97-eff6bb9f76bf")
    @Override
    public List<PatientMergeBaseDto> getByPatientRetain(String patientRetain) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<PatientMergeBaseDto> patientMergeBaseDtoList =
                getByPatientRetains(Arrays.asList(patientRetain));
        return patientMergeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2faa8725-4ed9-34b4-84f9-e25f88694268")
    @Override
    public PatientMergeBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<PatientMergeBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        PatientMergeBaseDto patientMergeBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return patientMergeBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3821d109-318c-34ae-b5ef-c3fed0edeb0d")
    public List<PatientMergeBaseDto> doConvertFromPatientMergeToPatientMergeBaseDto(
            List<PatientMerge> patientMergeList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(patientMergeList)) {
            return Collections.emptyList();
        }

        Map<String, PatientMergeBaseDto> dtoMap =
                patientMergeBaseDtoConverter
                        .convertFromPatientMergeToPatientMergeBaseDto(patientMergeList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        PatientMergeBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<PatientMergeBaseDto> patientMergeBaseDtoList = new ArrayList<>();
        for (PatientMerge i : patientMergeList) {
            PatientMergeBaseDto patientMergeBaseDto = dtoMap.get(i.getId());
            if (patientMergeBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            patientMergeBaseDtoList.add(patientMergeBaseDto);
        }
        return patientMergeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "57b3268a-1d16-35b5-9876-a0d489bd1495")
    @Override
    public List<PatientMergeBaseDto> getByPatientRetains(List<String> patientRetain) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(patientRetain)) {
            return Collections.emptyList();
        }

        List<PatientMerge> patientMergeList = patientMergeDao.getByPatientRetains(patientRetain);
        if (CollectionUtil.isEmpty(patientMergeList)) {
            return Collections.emptyList();
        }

        return doConvertFromPatientMergeToPatientMergeBaseDto(patientMergeList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "eb9f3fd6-3d33-36ab-85af-3032548a17f4")
    @Override
    public List<PatientMergeBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<PatientMerge> patientMergeList = patientMergeDao.getByIds(id);
        if (CollectionUtil.isEmpty(patientMergeList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, PatientMerge> patientMergeMap =
                patientMergeList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        patientMergeList =
                id.stream()
                        .map(i -> patientMergeMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromPatientMergeToPatientMergeBaseDto(patientMergeList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
