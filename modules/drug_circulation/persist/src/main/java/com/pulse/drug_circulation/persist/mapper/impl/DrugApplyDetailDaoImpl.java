package com.pulse.drug_circulation.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.drug_circulation.persist.dos.DrugApplyDetail;
import com.pulse.drug_circulation.persist.mapper.DrugApplyDetailDao;
import com.pulse.drug_circulation.persist.mapper.mybatis.DrugApplyDetailMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "99d9396f-7f65-3ac6-a8b0-6668950a5811|ENTITY|DAO")
public class DrugApplyDetailDaoImpl implements DrugApplyDetailDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugApplyDetailMapper drugApplyDetailMapper;

    @AutoGenerated(locked = true, uuid = "091888ea-e2bd-3c13-88a1-3fd2196b5ec4")
    @Override
    public List<DrugApplyDetail> getByIds(List<String> id) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "59ea9eed-43a1-33ef-af39-528f567b6520")
    @Override
    public List<DrugApplyDetail> getByDrugApplyId(String drugApplyId) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_apply_id", drugApplyId).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "5ff5b0a5-4478-3de5-850c-b5473b6ab9d2")
    @Override
    public List<DrugApplyDetail> getByDrugOriginCode(String drugOriginCode) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_code", drugOriginCode).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9453a4a0-33e0-358b-9a52-456d49d43a7f")
    @Override
    public List<DrugApplyDetail> getByReceiverStaffs(List<String> receiverStaff) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("receiver_staff", receiverStaff).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9c53396d-f3d1-3930-99e6-18260ba7b7d7")
    @Override
    public List<DrugApplyDetail> getByDrugOriginCodes(List<String> drugOriginCode) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_code", drugOriginCode).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a308faf8-40e9-3152-996c-a86813ec41d9")
    @Override
    public List<DrugApplyDetail> getByApplyStaff(String applyStaff) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("apply_staff", applyStaff).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a8e0a446-e632-38d6-9626-0dafbc02bbd8")
    @Override
    public List<DrugApplyDetail> getByDrugOriginSpecificationId(String drugOriginSpecificationId) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b61e85ec-b98e-3d2f-8882-5aec546f7cb0")
    @Override
    public List<DrugApplyDetail> getByDrugApplyIds(List<String> drugApplyId) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_apply_id", drugApplyId).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c32c74b5-75c5-3dc8-b47f-16163dd5f10f")
    @Override
    public DrugApplyDetail getById(String id) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugApplyDetailMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "db4b5c74-bfde-3a43-8283-ca1342b98a3b")
    @Override
    public List<DrugApplyDetail> getByReceiverStaff(String receiverStaff) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("receiver_staff", receiverStaff).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "efe8f264-88b1-3dd6-83f2-6e7f33142b70")
    @Override
    public List<DrugApplyDetail> getByApplyStaffs(List<String> applyStaff) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("apply_staff", applyStaff).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f531b8b7-e3d5-3e44-9bd2-133ee5eab5b3")
    @Override
    public List<DrugApplyDetail> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        QueryWrapper<DrugApplyDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("drug_origin_specification_id", drugOriginSpecificationId).orderByAsc("id");
        return drugApplyDetailMapper.selectList(queryWrapper);
    }
}
