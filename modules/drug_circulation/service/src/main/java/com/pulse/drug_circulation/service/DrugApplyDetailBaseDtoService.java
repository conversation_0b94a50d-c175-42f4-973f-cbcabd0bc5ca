package com.pulse.drug_circulation.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_circulation.manager.DrugApplyDetailBaseDtoManager;
import com.pulse.drug_circulation.manager.dto.DrugApplyDetailBaseDto;
import com.pulse.drug_circulation.service.converter.DrugApplyDetailBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "cd699b99-6595-4633-afd1-d185cebdaecb|DTO|SERVICE")
public class DrugApplyDetailBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugApplyDetailBaseDtoManager drugApplyDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugApplyDetailBaseDtoServiceConverter drugApplyDetailBaseDtoServiceConverter;

    @PublicInterface(
            id = "236dcedf-daf5-4e03-a1f0-c9a80be9fbc3",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647283067")
    @AutoGenerated(locked = false, uuid = "12e1366d-5725-39bd-9a15-ef66db966800")
    public List<DrugApplyDetailBaseDto> getByReceiverStaffs(
            @Valid @NotNull(message = "受理人不能为空") List<String> receiverStaff) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        receiverStaff = new ArrayList<>(new HashSet<>(receiverStaff));
        List<DrugApplyDetailBaseDto> drugApplyDetailBaseDtoList =
                drugApplyDetailBaseDtoManager.getByReceiverStaffs(receiverStaff);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugApplyDetailBaseDtoServiceConverter.DrugApplyDetailBaseDtoConverter(
                drugApplyDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "ab5417be-c9b8-4844-8d38-a2fd7363c3e8",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217072")
    @AutoGenerated(locked = false, uuid = "25e2ce2e-c853-3321-89d3-d97f11db4924")
    public List<DrugApplyDetailBaseDto> getByDrugApplyIds(
            @Valid @NotNull(message = "药品发放申请主记录ID不能为空") List<String> drugApplyId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugApplyId = new ArrayList<>(new HashSet<>(drugApplyId));
        List<DrugApplyDetailBaseDto> drugApplyDetailBaseDtoList =
                drugApplyDetailBaseDtoManager.getByDrugApplyIds(drugApplyId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugApplyDetailBaseDtoServiceConverter.DrugApplyDetailBaseDtoConverter(
                drugApplyDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "7f271dec-0723-4ba2-b1bd-0b5618574c29",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217103")
    @AutoGenerated(locked = false, uuid = "28c9546d-4795-3421-ad91-85f9c0136700")
    public List<DrugApplyDetailBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugApplyDetailBaseDto> drugApplyDetailBaseDtoList =
                drugApplyDetailBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugApplyDetailBaseDtoServiceConverter.DrugApplyDetailBaseDtoConverter(
                drugApplyDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "de2c98e0-50d3-4ee5-8036-5a3825d02af8",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217088")
    @AutoGenerated(locked = false, uuid = "5aa699ee-1740-3b75-a8ae-9ff45d9e2c58")
    public List<DrugApplyDetailBaseDto> getByDrugOriginSpecificationId(
            @NotNull(message = "药品产地规格id不能为空") String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "5f87f9da-b637-4ebf-97b6-371714314c75",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217093")
    @AutoGenerated(locked = false, uuid = "68a5639e-fa9d-3fd0-b4b8-9e1ee7f84aaa")
    public List<DrugApplyDetailBaseDto> getByDrugOriginSpecificationIds(
            @Valid @NotNull(message = "药品产地规格id不能为空") List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginSpecificationId = new ArrayList<>(new HashSet<>(drugOriginSpecificationId));
        List<DrugApplyDetailBaseDto> drugApplyDetailBaseDtoList =
                drugApplyDetailBaseDtoManager.getByDrugOriginSpecificationIds(
                        drugOriginSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugApplyDetailBaseDtoServiceConverter.DrugApplyDetailBaseDtoConverter(
                drugApplyDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "f8dacde6-fce2-4ea9-ad88-0efc0d970cd0",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647269731")
    @AutoGenerated(locked = false, uuid = "6ddabe7c-2b83-3f80-9d47-5b2e536159b0")
    public List<DrugApplyDetailBaseDto> getByApplyStaff(
            @NotNull(message = "请领人不能为空") String applyStaff) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByApplyStaffs(Arrays.asList(applyStaff));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "53af2401-aefe-4fd9-b7e7-c52b03c2282e",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217077")
    @AutoGenerated(locked = false, uuid = "7e389d9d-d259-3a87-9c9b-583c5d97ccf9")
    public List<DrugApplyDetailBaseDto> getByDrugOriginCode(
            @NotNull(message = "药品产地编码不能为空") String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "57bac16a-4df5-4b61-b640-81375bcf6048",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217083")
    @AutoGenerated(locked = false, uuid = "b9eed695-850f-3ac5-a661-2f964b6ac8ef")
    public List<DrugApplyDetailBaseDto> getByDrugOriginCodes(
            @Valid @NotNull(message = "药品产地编码不能为空") List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginCode = new ArrayList<>(new HashSet<>(drugOriginCode));
        List<DrugApplyDetailBaseDto> drugApplyDetailBaseDtoList =
                drugApplyDetailBaseDtoManager.getByDrugOriginCodes(drugOriginCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugApplyDetailBaseDtoServiceConverter.DrugApplyDetailBaseDtoConverter(
                drugApplyDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "a8f227a9-8fd7-4b1f-988e-05894e3f931d",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217098")
    @AutoGenerated(locked = false, uuid = "c84ea738-b7ed-349b-bc5b-39bc37689a1b")
    public DrugApplyDetailBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "cb4a9361-2628-4502-81cf-5fc307476f92",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647217067")
    @AutoGenerated(locked = false, uuid = "d099ba56-0ab0-33cb-9665-6bf1b2e9dc07")
    public List<DrugApplyDetailBaseDto> getByDrugApplyId(
            @NotNull(message = "药品发放申请主记录ID不能为空") String drugApplyId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugApplyIds(Arrays.asList(drugApplyId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "1dc9357f-ef75-4290-9aa4-d9a9125d4bc9",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647269735")
    @AutoGenerated(locked = false, uuid = "d62a1380-bdd6-3366-b7e5-fbf8a867a027")
    public List<DrugApplyDetailBaseDto> getByApplyStaffs(
            @Valid @NotNull(message = "请领人不能为空") List<String> applyStaff) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        applyStaff = new ArrayList<>(new HashSet<>(applyStaff));
        List<DrugApplyDetailBaseDto> drugApplyDetailBaseDtoList =
                drugApplyDetailBaseDtoManager.getByApplyStaffs(applyStaff);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugApplyDetailBaseDtoServiceConverter.DrugApplyDetailBaseDtoConverter(
                drugApplyDetailBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "7215ce24-0b73-4cdd-bad7-72645aa5191b",
            module = "drug_circulation",
            moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686",
            pubRpc = true,
            version = "1747647283063")
    @AutoGenerated(locked = false, uuid = "fedbdf1f-3696-3140-a2af-6e1829ebb55f")
    public List<DrugApplyDetailBaseDto> getByReceiverStaff(
            @NotNull(message = "受理人不能为空") String receiverStaff) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByReceiverStaffs(Arrays.asList(receiverStaff));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
