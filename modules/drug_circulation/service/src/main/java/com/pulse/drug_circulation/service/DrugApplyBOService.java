package com.pulse.drug_circulation.service;

import com.pulse.drug_circulation.common.enums.ApplyStatusEnum;
import com.pulse.drug_circulation.manager.bo.*;
import com.pulse.drug_circulation.manager.dto.DrugApplyBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugApplyDetailBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugApplyDetailExtendDto;
import com.pulse.drug_circulation.persist.dos.DrugApply;
import com.pulse.drug_circulation.persist.dos.DrugApplyDetail;
import com.pulse.drug_circulation.service.base.BaseDrugApplyBOService;
import com.pulse.drug_circulation.service.bto.CreateApplyBto;
import com.pulse.drug_circulation.service.bto.CreateApplyDetailBto;
import com.pulse.drug_circulation.service.bto.CreateDrugApplyBto;
import com.pulse.drug_circulation.service.bto.DeleteApplyBto;
import com.pulse.drug_circulation.service.bto.DeleteApplyDetailBto;
import com.pulse.drug_circulation.service.bto.MergeDrugApplyBto;
import com.pulse.drug_circulation.service.bto.UpdateApplyBto;
import com.pulse.drug_circulation.service.bto.UpdateApplyDetailBto;
import com.pulse.pulse.common.utils.IdGeneratorUtils;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "2f43a9d4-2140-431a-9c26-************|BO|SERVICE")
public class DrugApplyBOService extends BaseDrugApplyBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugApplyBaseDtoService drugApplyBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugApplyDetailBaseDtoService drugApplyDetailBaseDtoService;

    @Resource private DrugApplyDtoService drugApplyDtoService;

    /** 删除公用请领 */
    @PublicInterface(id = "f3b86df8-4a20-43ff-a80b-ce0202f19e8a", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "1b930719-b0de-455f-a770-3b5e1126b4bf")
    public String deleteApply(@Valid @NotNull DeleteApplyBto deleteApplyBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugApplyBaseDto drugApplyBaseDto = drugApplyBaseDtoService.getById(deleteApplyBto.getId());
        DeleteApplyBoResult boResult = super.deleteApplyBase(deleteApplyBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteApplyBto */
        {
            DeleteApplyBto bto =
                    boResult.<DeleteApplyBto>getBtoOfType(DeleteApplyBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteApplyBto, DrugApply> deletedBto = boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 新增公用请领 */
    @PublicInterface(id = "f15e8917-c7c8-45bf-91dd-0a16d81a99a1", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "53cf1e83-2600-4e43-bd86-2cadb1202630")
    public String createDrugApply(@Valid @NotNull CreateDrugApplyBto createDrugApplyBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateDrugApplyBoResult boResult = super.createDrugApplyBase(createDrugApplyBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateDrugApplyBto.DrugApplyDetailBto */
        {
            for (CreateDrugApplyBto.DrugApplyDetailBto bto :
                    boResult.<CreateDrugApplyBto.DrugApplyDetailBto>getBtoOfType(
                            CreateDrugApplyBto.DrugApplyDetailBto.class)) {
                AddedBto<CreateDrugApplyBto.DrugApplyDetailBto, DrugApplyDetailBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugApplyDetailBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 CreateDrugApplyBto */
        {
            CreateDrugApplyBto bto =
                    boResult.<CreateDrugApplyBto>getBtoOfType(CreateDrugApplyBto.class).stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateDrugApplyBto, DrugApplyBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                DrugApplyBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 请领批量修改 */
    @PublicInterface(id = "19a4b7f7-1d9e-4794-87b6-979a816eb6af", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "7643b8f5-47d5-4094-8696-48e466ff1203")
    public String mergeDrugApply(@Valid @NotNull MergeDrugApplyBto mergeDrugApplyBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugApplyBaseDto drugApplyBaseDto = null;
        if (mergeDrugApplyBto.getId() != null) {
            drugApplyBaseDto = drugApplyBaseDtoService.getById(mergeDrugApplyBto.getId());
        }
        MergeDrugApplyBoResult boResult = super.mergeDrugApplyBase(mergeDrugApplyBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeDrugApplyBto */
        {
            MergeDrugApplyBto bto =
                    boResult.<MergeDrugApplyBto>getBtoOfType(MergeDrugApplyBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeDrugApplyBto, DrugApply, DrugApplyBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeDrugApplyBto, DrugApplyBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugApplyBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugApply entity = updatedBto.getEntity();
                // 其他自定义操作...
                // TODO:校验公用请领申请单的状态，若明细都是拒绝状态，则修改状态为拒绝，如果部分已执行，则部分出库
                // 获取该申请单含明细
                var apply = drugApplyDtoService.getById(entity.getId());
                if (apply != null) {
                    var listDtail = apply.getDrugApplyDetailList();
                    int cnt = 0;
                    for (DrugApplyDetailExtendDto item : listDtail) {
                        if (item.getStatus() == ApplyStatusEnum.REJECT) {
                            cnt++;
                        }
                    }
                    if (cnt != 0 && cnt == listDtail.size()) {
                        entity.setApplyStatus(ApplyStatusEnum.REJECT);
                    }
                }
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DrugApplyBO bo = addedBto.getBo();
                // 其他自定义操作...
                // 生成单号
                if (bo.getApplyNumber() == "" || bo.getApplyNumber() == null) {
                    bo.setApplyNumber(
                            IdGeneratorUtils.generateNextSequenceId("DrugApply", "id", 20));
                }
            }
        }
        /** 处理 MergeDrugApplyBto.DrugApplyDetailBto */
        {
            for (MergeDrugApplyBto.DrugApplyDetailBto bto :
                    boResult.<MergeDrugApplyBto.DrugApplyDetailBto>getBtoOfType(
                            MergeDrugApplyBto.DrugApplyDetailBto.class)) {
                UpdatedBto<MergeDrugApplyBto.DrugApplyDetailBto, DrugApplyDetail, DrugApplyDetailBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeDrugApplyBto.DrugApplyDetailBto, DrugApplyDetailBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugApplyDetailBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugApplyDetail entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugApplyDetailBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 新建请领单(不带明细) */
    @PublicInterface(id = "10686aba-6df9-4ff4-b8f5-774fe711350e", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "76c61142-f730-4d84-a06f-c2a264b4575b")
    public String createApply(@Valid @NotNull CreateApplyBto createApplyBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateApplyBoResult boResult = super.createApplyBase(createApplyBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateApplyBto */
        {
            CreateApplyBto bto =
                    boResult.<CreateApplyBto>getBtoOfType(CreateApplyBto.class).stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateApplyBto, DrugApplyBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                DrugApplyBO bo = addedBto.getBo();
                // 其他自定义操作...
                // 生成单号
                if (bo.getApplyNumber() == "" || bo.getApplyNumber() == null) {
                    bo.setApplyNumber(
                            IdGeneratorUtils.generateNextSequenceId("DrugApply", "id", 20));
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 删除公用请领明细 */
    @PublicInterface(id = "029387fe-9273-400b-9f1e-79a75c50ebe3", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "7e52e296-2863-4f7d-9124-5b222d1cc50f")
    public String deleteApplyDetail(
            @Valid @NotNull DeleteApplyDetailBto.DrugApplyDetailBto drugApplyDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DeleteApplyDetailBto deleteApplyDetailBto = new DeleteApplyDetailBto();
        deleteApplyDetailBto.setDrugApplyDetailBtoList(List.of(drugApplyDetailBto));
        DrugApplyDetailBaseDto drugApplyDetailBaseDto =
                drugApplyDetailBaseDtoService.getById(drugApplyDetailBto.getId());
        if (drugApplyDetailBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        DrugApplyBaseDto drugApplyBaseDto =
                drugApplyBaseDtoService.getById(drugApplyDetailBaseDto.getDrugApplyId());
        deleteApplyDetailBto.setId(drugApplyBaseDto.getId());
        DeleteApplyDetailBoResult boResult = super.deleteApplyDetailBase(deleteApplyDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteApplyDetailBto.DrugApplyDetailBto */
        {
            for (DeleteApplyDetailBto.DrugApplyDetailBto bto :
                    boResult.<DeleteApplyDetailBto.DrugApplyDetailBto>getBtoOfType(
                            DeleteApplyDetailBto.DrugApplyDetailBto.class)) {
                DeletedBto<DeleteApplyDetailBto.DrugApplyDetailBto, DrugApplyDetail> deletedBto =
                        boResult.getDeletedResult(bto);
                boolean deleted = (deletedBto != null);
                if (deleted) { // getDeletedResult
                    Object entity = deletedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 新增公用请领单明细 */
    @PublicInterface(id = "45321fa5-d981-4086-ad45-23d471a6fd41", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "82f4d278-a033-415c-917b-6e4d07d67374")
    public String createApplyDetail(
            @Valid @NotNull CreateApplyDetailBto.DrugApplyDetailBto drugApplyDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateApplyDetailBto createApplyDetailBto = new CreateApplyDetailBto();
        createApplyDetailBto.setDrugApplyDetailBtoList(List.of(drugApplyDetailBto));
        DrugApplyBaseDto drugApplyBaseDto =
                drugApplyBaseDtoService.getById(drugApplyDetailBto.getDrugApply().getId());
        createApplyDetailBto.setId(drugApplyDetailBto.getDrugApply().getId());
        CreateApplyDetailBoResult boResult = super.createApplyDetailBase(createApplyDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateApplyDetailBto.DrugApplyDetailBto */
        {
            for (CreateApplyDetailBto.DrugApplyDetailBto bto :
                    boResult.<CreateApplyDetailBto.DrugApplyDetailBto>getBtoOfType(
                            CreateApplyDetailBto.DrugApplyDetailBto.class)) {
                AddedBto<CreateApplyDetailBto.DrugApplyDetailBto, DrugApplyDetailBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugApplyDetailBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改公用请领明细 */
    @PublicInterface(id = "7b03228e-6390-452d-99c2-75d208f8511c", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "8466393a-83e0-4ea5-9cb7-42b5f0ac8e02")
    public String updateApplyDetail(
            @Valid @NotNull UpdateApplyDetailBto.DrugApplyDetailBto drugApplyDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        UpdateApplyDetailBto updateApplyDetailBto = new UpdateApplyDetailBto();
        updateApplyDetailBto.setDrugApplyDetailBtoList(List.of(drugApplyDetailBto));
        DrugApplyDetailBaseDto drugApplyDetailBaseDto =
                drugApplyDetailBaseDtoService.getById(drugApplyDetailBto.getId());
        if (drugApplyDetailBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        DrugApplyBaseDto drugApplyBaseDto =
                drugApplyBaseDtoService.getById(drugApplyDetailBaseDto.getDrugApplyId());
        updateApplyDetailBto.setId(drugApplyBaseDto.getId());
        UpdateApplyDetailBoResult boResult = super.updateApplyDetailBase(updateApplyDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateApplyDetailBto.DrugApplyDetailBto */
        {
            for (UpdateApplyDetailBto.DrugApplyDetailBto bto :
                    boResult.<UpdateApplyDetailBto.DrugApplyDetailBto>getBtoOfType(
                            UpdateApplyDetailBto.DrugApplyDetailBto.class)) {
                UpdatedBto<
                                UpdateApplyDetailBto.DrugApplyDetailBto,
                                DrugApplyDetail,
                                DrugApplyDetailBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugApplyDetailBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugApplyDetail entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改公用请领 */
    @PublicInterface(id = "087f5c23-289f-4c3e-9845-0726dd2c9f95", module = "drug_circulation")
    @Transactional
    @AutoGenerated(locked = false, uuid = "d6b9f1f4-cd16-41af-b3b2-30a5fd259d75")
    public String updateApply(@Valid @NotNull UpdateApplyBto updateApplyBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugApplyBaseDto drugApplyBaseDto = drugApplyBaseDtoService.getById(updateApplyBto.getId());
        UpdateApplyBoResult boResult = super.updateApplyBase(updateApplyBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateApplyBto */
        {
            UpdateApplyBto bto =
                    boResult.<UpdateApplyBto>getBtoOfType(UpdateApplyBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateApplyBto, DrugApply, DrugApplyBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugApplyBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugApply entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
