package com.pulse.drug_circulation.manager.facade.drug_dictionary.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "bbebe786-3ad0-332d-a5b8-6e003bf2d876")
public class DrugOriginExtensionBaseDtoServiceInDrugCirculationBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "9009cf4c-e6b5-4172-980c-6f5ecc2a6850|RPC|BASE_ADAPTER")
    public List<DrugOriginExtensionBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/9009cf4c-e6b5-4172-980c-6f5ecc2a6850/DrugOriginExtensionBaseDtoService-getByIds",
                        "com.pulse.drug_dictionary.service.DrugOriginExtensionBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "f7024bce-ba2c-493b-88a1-0404a9b21686",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "dd7eeb0b-4b5f-48cf-aed7-c9f10c4f144f|RPC|BASE_ADAPTER")
    public List<DrugOriginExtensionBaseDto> getByDrugOriginCodes(List<String> drugOriginCode) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("drug_origin_code", drugOriginCode);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("drug_origin_code", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/dd7eeb0b-4b5f-48cf-aed7-c9f10c4f144f/DrugOriginExtensionBaseDtoService-getByDrugOriginCodes",
                        "com.pulse.drug_dictionary.service.DrugOriginExtensionBaseDtoService",
                        "getByDrugOriginCodes",
                        paramMap,
                        paramTypeMap,
                        "f7024bce-ba2c-493b-88a1-0404a9b21686",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }
}
