package com.pulse.drug_circulation.manager.facade.drug_dictionary.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryWithCatalogDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "81a02037-c674-3f99-90c3-60c16a6a0e4b")
public class DrugDictionaryWithCatalogDtoServiceInDrugCirculationBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "2bcf7f70-f302-4cf8-b5cf-a4cbab7034cf|RPC|BASE_ADAPTER")
    public List<DrugDictionaryWithCatalogDto> getByDrugCodes(List<String> drugCode) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("drug_code", drugCode);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("drug_code", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/2bcf7f70-f302-4cf8-b5cf-a4cbab7034cf/DrugDictionaryWithCatalogDtoService-getByDrugCodes",
                        "com.pulse.drug_dictionary.service.DrugDictionaryWithCatalogDtoService",
                        "getByDrugCodes",
                        paramMap,
                        paramTypeMap,
                        "f7024bce-ba2c-493b-88a1-0404a9b21686",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }
}
