package com.pulse.drug_circulation.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_circulation.manager.DrugApplyBaseDtoManager;
import com.pulse.drug_circulation.manager.converter.DrugApplyBaseDtoConverter;
import com.pulse.drug_circulation.manager.dto.DrugApplyBaseDto;
import com.pulse.drug_circulation.persist.dos.DrugApply;
import com.pulse.drug_circulation.persist.mapper.DrugApplyDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "8420b6d2-c3b0-45a8-80d3-37b15218346d|DTO|BASE_MANAGER_IMPL")
public abstract class DrugApplyBaseDtoManagerBaseImpl implements DrugApplyBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugApplyBaseDtoConverter drugApplyBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugApplyDao drugApplyDao;

    @AutoGenerated(locked = true, uuid = "13aa9a15-bd75-3773-919b-fff0c47ecec8")
    @Override
    public List<DrugApplyBaseDto> getByAuditStaffId(String auditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyBaseDto> drugApplyBaseDtoList =
                getByAuditStaffIds(Arrays.asList(auditStaffId));
        return drugApplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1879cc37-8090-3017-b87e-269d1458aa49")
    @Override
    public List<DrugApplyBaseDto> getByExecuteStaffId(String executeStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyBaseDto> drugApplyBaseDtoList =
                getByExecuteStaffIds(Arrays.asList(executeStaffId));
        return drugApplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "262f80d8-28be-3d87-b340-17b390d0ea8c")
    @Override
    public List<DrugApplyBaseDto> getByApplyStaffIds(List<String> applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applyStaffId)) {
            return Collections.emptyList();
        }

        List<DrugApply> drugApplyList = drugApplyDao.getByApplyStaffIds(applyStaffId);
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugApplyToDrugApplyBaseDto(drugApplyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2e3a14da-dffc-3275-b44f-07df90d0c8df")
    public List<DrugApplyBaseDto> doConvertFromDrugApplyToDrugApplyBaseDto(
            List<DrugApply> drugApplyList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        Map<String, DrugApplyBaseDto> dtoMap =
                drugApplyBaseDtoConverter
                        .convertFromDrugApplyToDrugApplyBaseDto(drugApplyList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugApplyBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugApplyBaseDto> drugApplyBaseDtoList = new ArrayList<>();
        for (DrugApply i : drugApplyList) {
            DrugApplyBaseDto drugApplyBaseDto = dtoMap.get(i.getId());
            if (drugApplyBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugApplyBaseDtoList.add(drugApplyBaseDto);
        }
        return drugApplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "5beb02ac-0632-32bc-9587-7421748d7d43")
    @Override
    public List<DrugApplyBaseDto> getByProvideStorageCode(String provideStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyBaseDto> drugApplyBaseDtoList =
                getByProvideStorageCodes(Arrays.asList(provideStorageCode));
        return drugApplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "61b77438-f523-3fa2-8489-72a5601c0d55")
    @Override
    public List<DrugApplyBaseDto> getByExecuteStaffIds(List<String> executeStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(executeStaffId)) {
            return Collections.emptyList();
        }

        List<DrugApply> drugApplyList = drugApplyDao.getByExecuteStaffIds(executeStaffId);
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugApplyToDrugApplyBaseDto(drugApplyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "6399a65f-a903-39e3-be7f-26993ca6c054")
    @Override
    public List<DrugApplyBaseDto> getByApplyDepartmentCode(String applyDepartmentCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyBaseDto> drugApplyBaseDtoList =
                getByApplyDepartmentCodes(Arrays.asList(applyDepartmentCode));
        return drugApplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9ee9b58b-9bd5-3811-b3a1-cbaa4d1b50a5")
    @Override
    public List<DrugApplyBaseDto> getByApplyStaffId(String applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyBaseDto> drugApplyBaseDtoList =
                getByApplyStaffIds(Arrays.asList(applyStaffId));
        return drugApplyBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b30a281d-0e6b-39f6-9815-658d1bf2add5")
    @Override
    public List<DrugApplyBaseDto> getByAuditStaffIds(List<String> auditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(auditStaffId)) {
            return Collections.emptyList();
        }

        List<DrugApply> drugApplyList = drugApplyDao.getByAuditStaffIds(auditStaffId);
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugApplyToDrugApplyBaseDto(drugApplyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bd93edb4-7a20-3d28-acbc-99f8bc1d1f16")
    @Override
    public List<DrugApplyBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugApply> drugApplyList = drugApplyDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugApply> drugApplyMap =
                drugApplyList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugApplyList =
                id.stream()
                        .map(i -> drugApplyMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugApplyToDrugApplyBaseDto(drugApplyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bdf1f2c8-d139-3f37-8028-c82c54dc6e52")
    @Override
    public List<DrugApplyBaseDto> getByApplyDepartmentCodes(List<String> applyDepartmentCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applyDepartmentCode)) {
            return Collections.emptyList();
        }

        List<DrugApply> drugApplyList = drugApplyDao.getByApplyDepartmentCodes(applyDepartmentCode);
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugApplyToDrugApplyBaseDto(drugApplyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c134464b-7e02-312b-ace1-95ebd829b1a3")
    @Override
    public List<DrugApplyBaseDto> getByProvideStorageCodes(List<String> provideStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(provideStorageCode)) {
            return Collections.emptyList();
        }

        List<DrugApply> drugApplyList = drugApplyDao.getByProvideStorageCodes(provideStorageCode);
        if (CollectionUtil.isEmpty(drugApplyList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugApplyToDrugApplyBaseDto(drugApplyList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e8367dec-b8b5-3a82-9095-d8dbb9432bb6")
    @Override
    public DrugApplyBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugApplyBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugApplyBaseDto drugApplyBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugApplyBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
