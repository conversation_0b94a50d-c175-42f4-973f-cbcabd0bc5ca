package com.pulse.drug_circulation.manager.facade.drug_dictionary;

import com.pulse.drug_circulation.manager.facade.drug_dictionary.base.DrugOriginSpecificationBaseDtoServiceInDrugCirculationBaseRpcAdapter;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686")
@AutoGenerated(locked = false, uuid = "171bac04-2367-3e08-898f-f238223cacf6")
public class DrugOriginSpecificationBaseDtoServiceInDrugCirculationRpcAdapter
        extends DrugOriginSpecificationBaseDtoServiceInDrugCirculationBaseRpcAdapter {

    @RpcRefer(id = "4e5f0238-0b8c-4359-adca-8e6156d8fe8b", version = "1745385746208")
    @AutoGenerated(locked = false, uuid = "4e5f0238-0b8c-4359-adca-8e6156d8fe8b|RPC|ADAPTER")
    public DrugOriginSpecificationBaseDto getById(String id) {
        return super.getById(id);
    }

    @RpcRefer(id = "e7234d99-43d1-49b8-b8f1-a5249308735c", version = "1745385746210")
    @AutoGenerated(locked = false, uuid = "e7234d99-43d1-49b8-b8f1-a5249308735c|RPC|ADAPTER")
    public List<DrugOriginSpecificationBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
