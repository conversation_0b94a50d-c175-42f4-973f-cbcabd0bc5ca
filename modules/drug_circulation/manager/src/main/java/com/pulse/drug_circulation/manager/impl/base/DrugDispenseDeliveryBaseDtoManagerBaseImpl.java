package com.pulse.drug_circulation.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_circulation.manager.DrugDispenseDeliveryBaseDtoManager;
import com.pulse.drug_circulation.manager.converter.DrugDispenseDeliveryBaseDtoConverter;
import com.pulse.drug_circulation.manager.dto.DrugDispenseDeliveryBaseDto;
import com.pulse.drug_circulation.persist.dos.DrugDispenseDelivery;
import com.pulse.drug_circulation.persist.mapper.DrugDispenseDeliveryDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "96886664-a79d-420a-bdc7-47f88e56d5f4|DTO|BASE_MANAGER_IMPL")
public abstract class DrugDispenseDeliveryBaseDtoManagerBaseImpl
        implements DrugDispenseDeliveryBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugDispenseDeliveryBaseDtoConverter drugDispenseDeliveryBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugDispenseDeliveryDao drugDispenseDeliveryDao;

    @AutoGenerated(locked = true, uuid = "0ed79080-5550-3dcb-9cd0-aad003b8dbdb")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveredStaffIds(List<String> deliveredStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(deliveredStaffId)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByDeliveredStaffIds(deliveredStaffId);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "24ced961-e5d0-3dd5-8dd2-6e1a3805779e")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveryDepartmentCodes(
            List<String> deliveryDepartmentCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(deliveryDepartmentCode)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByDeliveryDepartmentCodes(deliveryDepartmentCode);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "300d7e25-3490-3c26-8430-44572a67ec75")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDispenseStorageCodes(
            List<String> dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dispenseStorageCode)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByDispenseStorageCodes(dispenseStorageCode);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "44e4c0d5-df14-3f99-878a-fa42c64dda04")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveryAuditStaffId(
            String deliveryAuditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByDeliveryAuditStaffIds(Arrays.asList(deliveryAuditStaffId));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "6c65fefa-bc77-3dd3-b607-73d6ae01cc1e")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByPrintStaffId(String printStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByPrintStaffIds(Arrays.asList(printStaffId));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "83724d1d-1b16-3abc-bdeb-cb33cf5f4cda")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveryAuditStaffIds(
            List<String> deliveryAuditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(deliveryAuditStaffId)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByDeliveryAuditStaffIds(deliveryAuditStaffId);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a5d52947-fe67-3ce9-9c7d-97a9a2dda466")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList = drugDispenseDeliveryDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugDispenseDelivery> drugDispenseDeliveryMap =
                drugDispenseDeliveryList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugDispenseDeliveryList =
                id.stream()
                        .map(i -> drugDispenseDeliveryMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a6b8c704-e13d-310c-bac2-2dc038a9d157")
    @Override
    public DrugDispenseDeliveryBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugDispenseDeliveryBaseDto drugDispenseDeliveryBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugDispenseDeliveryBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b1269ba9-9606-3090-9d65-8107b51012b3")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveryDepartmentCode(
            String deliveryDepartmentCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByDeliveryDepartmentCodes(Arrays.asList(deliveryDepartmentCode));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b1c5dd8f-063a-3e7d-9859-343293fe0bcf")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveredStaffId(String deliveredStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByDeliveredStaffIds(Arrays.asList(deliveredStaffId));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b30f8815-d4db-3b55-8dff-86aa1f9002e9")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByPrintStaffIds(List<String> printStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(printStaffId)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByPrintStaffIds(printStaffId);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b7d7f30c-61ba-3af4-b0fb-b4419cadd8d8")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByReceiveStaffId(String receiveStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByReceiveStaffIds(Arrays.asList(receiveStaffId));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bc1b7a25-b3a1-3aa8-8703-ccc4288cd7c5")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveryStaffId(String deliveryStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByDeliveryStaffIds(Arrays.asList(deliveryStaffId));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c265b1ee-dfd4-3c23-a1ca-22f54e5df7e9")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDispenseStorageCode(String dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList =
                getByDispenseStorageCodes(Arrays.asList(dispenseStorageCode));
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cc8d8957-77b0-3dab-adf8-5f16cf9a26eb")
    public List<DrugDispenseDeliveryBaseDto>
            doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                    List<DrugDispenseDelivery> drugDispenseDeliveryList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        Map<String, DrugDispenseDeliveryBaseDto> dtoMap =
                drugDispenseDeliveryBaseDtoConverter
                        .convertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                                drugDispenseDeliveryList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugDispenseDeliveryBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugDispenseDeliveryBaseDto> drugDispenseDeliveryBaseDtoList = new ArrayList<>();
        for (DrugDispenseDelivery i : drugDispenseDeliveryList) {
            DrugDispenseDeliveryBaseDto drugDispenseDeliveryBaseDto = dtoMap.get(i.getId());
            if (drugDispenseDeliveryBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugDispenseDeliveryBaseDtoList.add(drugDispenseDeliveryBaseDto);
        }
        return drugDispenseDeliveryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "ed44c93b-1ae0-3277-adc0-546768491a5d")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByDeliveryStaffIds(List<String> deliveryStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(deliveryStaffId)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByDeliveryStaffIds(deliveryStaffId);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f1995b8f-026f-3c01-ab0d-e6ec10d0fc19")
    @Override
    public List<DrugDispenseDeliveryBaseDto> getByReceiveStaffIds(List<String> receiveStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(receiveStaffId)) {
            return Collections.emptyList();
        }

        List<DrugDispenseDelivery> drugDispenseDeliveryList =
                drugDispenseDeliveryDao.getByReceiveStaffIds(receiveStaffId);
        if (CollectionUtil.isEmpty(drugDispenseDeliveryList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugDispenseDeliveryToDrugDispenseDeliveryBaseDto(
                drugDispenseDeliveryList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
