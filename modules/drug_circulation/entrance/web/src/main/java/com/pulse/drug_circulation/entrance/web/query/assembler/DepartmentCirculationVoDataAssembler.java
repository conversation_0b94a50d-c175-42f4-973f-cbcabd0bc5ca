package com.pulse.drug_circulation.entrance.web.query.assembler;

import com.pulse.drug_circulation.entrance.web.vo.DepartmentCirculationVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** DepartmentCirculationVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "63947068-6e04-3f20-8aaa-a6df032477e9")
public class DepartmentCirculationVoDataAssembler {

    /** 组装DepartmentCirculationVo数据 */
    @AutoGenerated(locked = true, uuid = "ab463498-ef87-38df-8998-ea9d52c1399b")
    public void assembleData(Map<String, DepartmentCirculationVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装DepartmentCirculationVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "d8113140-40a4-37b8-b8f1-692c0d848250")
    public void assembleDataCustomized(List<DepartmentCirculationVo> dataList) {
        // 自定义数据组装

    }
}
