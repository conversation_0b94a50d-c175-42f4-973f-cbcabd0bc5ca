package com.pulse.file.service;

import com.pulse.file.manager.dto.FileMetadataBaseDto;
import com.pulse.file.manager.service.FileStorageService;
import com.pulse.file.persist.eo.FileBusinessEo;
import com.pulse.file.persist.eo.IdxBusinessEo;
import com.pulse.file.service.bto.CreateFileMetadataBto;
import com.pulse.file.service.bto.DeleteFileMetadataBto;
import com.pulse.pulse.common.context.UserContext;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import com.pulse.file.common.config.FileStorageConfig;
import com.pulse.file.common.enums.FileStorageTypeEnum;
import com.pulse.file.common.enums.FileTypeEnum;
import com.pulse.file.common.enums.FileBusinessTypeEnum;

/**
 * 文件服务类
 *
 * <p>提供文件的上传、下载、删除和获取URL等功能。该服务类封装了对底层存储的访问， 并维护文件元数据与业务数据之间的关联。默认使用配置的存储类型进行文件操作。
 */
@Controller
@Validated
@Slf4j
@AutoGenerated(locked = false, uuid = "a5054bbe-c73b-355b-97b6-d3e86a641d55")
public class FileService {

    /** 文件URL默认有效期（秒）：7天 */
    private static final long DEFAULT_FILE_URL_EXPIRY_SECONDS = 7 * 24 * 60 * 60L;

    /** 系统默认用户ID */
    private static final String SYSTEM_USER_ID = "system";

    /** 文件元数据键：原始文件名 */
    private static final String METADATA_KEY_ORIGINAL_FILENAME = "originalFileName";

    @Resource private FileMetadataBaseDtoService fileMetadataBaseDtoService;

    @Resource private FileMetadataBOService fileMetadataBOService;

    @Resource private FileStorageService fileStorageService;

    @Resource private FileStorageConfig fileStorageConfig;

    /** 根据单个业务信息获取文件URL列表：可根据业务ID+业务类型、文件ID（可选）获取文件URL列表 */
    @PublicInterface(
            id = "20cf75bf-8f45-4f47-ba00-95c3174b2005",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748322504089",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "20cf75bf-8f45-4f47-ba00-95c3174b2005")
    public List<String> listFileUrlByBusiness(@Valid @NotNull FileBusinessEo fileBusiness) {
        try {
            // 校验业务参数
            validateBusinessParameters(fileBusiness);

            // 获取文件元数据列表
            List<FileMetadataBaseDto> fileMetadataList =
                    getFileMetadataListByBusiness(fileBusiness);

            // 生成文件URL列表
            return generateFileUrlList(fileMetadataList);

        } catch (IgnoredException e) {
            throw e;
        } catch (Exception e) {
            log.error(
                    "Failed to list file URLs by business. BusinessId: {}, BusinessType: {},"
                            + " FileId: {}",
                    fileBusiness.getBusinessId(),
                    fileBusiness.getBusinessType(),
                    fileBusiness.getFileId(),
                    e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "获取文件URL列表失败: " + e.getMessage());
        }
    }

    /** 批量删除文件及其元数据（使用默认存储类型） */
    @PublicInterface(
            id = "23d6ce47-7d9e-4c68-9435-f39ea392f17a",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748322068474",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "23d6ce47-7d9e-4c68-9435-f39ea392f17a")
    public List<String> batchDeleteFile(@Valid @NotNull List<FileBusinessEo> fileBusinessList) {
        // 校验输入参数
        validateBatchDeleteFileInput(fileBusinessList);

        List<String> successFileIdList = new ArrayList<>();
        List<String> failedFileIdList = new ArrayList<>();

        // 逐个删除文件
        processBatchFileDelete(fileBusinessList, successFileIdList, failedFileIdList);

        // 记录批量删除结果
        logBatchDeleteResult(fileBusinessList.size(), successFileIdList.size(), failedFileIdList);

        return successFileIdList;
    }

    /** 上传文件（不绑定业务信息），返回完整的文件元数据信息（除业务信息外的所有属性）。需传入MultipartFile file参数 */
    @PublicInterface(
            id = "33b37235-6a95-4541-8dbd-b001de659aa1",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748321488423",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "33b37235-6a95-4541-8dbd-b001de659aa1")
    public FileMetadataBaseDto uploadFileWithoutBusiness(MultipartFile file) {
        // 校验上传文件
        validateUploadFile(file);

        // 上传文件到存储服务
        String filePath = uploadFileToStorage(file);

        // 构建不包含业务信息的文件元数据
        return buildFileMetadataWithoutBusiness(file, filePath);
    }

    /** 删除文件及其元数据（使用默认存储类型） */
    @PublicInterface(
            id = "d8209bfb-4743-4c41-822c-7b0b7a2d1746",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748322744430",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "d8209bfb-4743-4c41-822c-7b0b7a2d1746")
    @Transactional
    public String deleteFile(@Valid @NotNull FileBusinessEo fileBusiness) {
        // 获取并验证文件元数据
        FileMetadataBaseDto fileMetadataBaseDto = getAndValidateFileMetadata(fileBusiness);

        try {
            // 先删除物理文件，再删除元数据记录
            // 如果物理文件删除成功但元数据删除失败，事务会回滚，但物理文件已被删除
            // 这种情况下会有孤立的元数据记录，但不会有孤立的物理文件
            fileStorageService.deleteFile(fileMetadataBaseDto.getPath());

            // 创建删除文件元数据的BTO对象
            DeleteFileMetadataBto deleteFileMetadataBto = new DeleteFileMetadataBto();
            deleteFileMetadataBto.setId(fileBusiness.getFileId());

            // 调用服务删除文件元数据
            fileMetadataBOService.deleteFileMetadata(deleteFileMetadataBto);

            log.info(
                    "File and metadata deleted successfully. FileId: {}", fileBusiness.getFileId());

            return fileMetadataBaseDto.getId();
        } catch (Exception e) {
            log.error("Failed to delete file or metadata. FileId: {}", fileBusiness.getFileId(), e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "删除文件失败: " + e.getMessage());
        }
    }

    /** 上传文件并关联业务信息，返回新创建的文件ID。需传入MultipartFile file参数 */
    @PublicInterface(
            id = "d970b2a5-86c2-43d8-ba81-e18a9b5123bd",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748409686697",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "d970b2a5-86c2-43d8-ba81-e18a9b5123bd")
    public String uploadFileWithBusiness(MultipartFile file, @Valid FileBusinessEo fileBusiness) {
        // 校验上传文件
        validateUploadFile(file);

        // 上传文件到存储服务
        String filePath = uploadFileToStorage(file);

        // 构建文件元数据
        CreateFileMetadataBto createFileMetadataBto =
                getCreateFileMetadataBto(file, fileBusiness, filePath);

        // 调用 FileMetadataBOService.createFileMetadata 创建文件元数据，获取文件ID
        return fileMetadataBOService.createFileMetadata(createFileMetadataBto);
    }

    /** 下载文件内容（使用默认存储类型），文件ID必传，仅支持单个文件下载 */
    @PublicInterface(
            id = "ff386911-2125-4de5-912a-f3793d9ab4f1",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748322841807",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "ff386911-2125-4de5-912a-f3793d9ab4f1")
    public byte[] downloadFile(@Valid @NotNull FileBusinessEo fileBusiness) {
        // 获取并验证文件元数据
        FileMetadataBaseDto fileMetadataBaseDto = getAndValidateFileMetadata(fileBusiness);

        try {
            // 使用默认存储类型下载文件
            return fileStorageService.downloadFile(
                    fileMetadataBaseDto.getPath(), fileMetadataBaseDto.getEncryptionKey());
        } catch (Exception e) {
            log.error("Failed to download file", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "下载文件失败");
        }
    }

    /**
     * 构建不包含业务信息的文件元数据
     *
     * <p>该方法专门用于 {@code uploadFileWithoutBusiness} 场景，从上传的文件和存储路径中提取必要信息， 构造不包含业务关联信息的 {@link
     * FileMetadataBaseDto} 对象。具体处理包括：
     *
     * <ul>
     *   <li>设置文件路径、名称和大小等基本属性
     *   <li>根据文件扩展名自动判断文件类型
     *   <li>设置与fileStorageService.uploadFile中使用的存储类型一致的存储类型
     *   <li>不设置业务ID和业务类型（适用于先上传后关联业务的场景）
     * </ul>
     *
     * @param file 上传的文件对象，包含文件名、大小等信息
     * @param filePath 文件存储路径，由存储服务生成
     * @return {@link FileMetadataBaseDto} 不包含业务信息的文件元数据
     * @throws IllegalArgumentException 如果文件名无效（如无扩展名）或文件扩展名不受支持
     */
    private FileMetadataBaseDto buildFileMetadataWithoutBusiness(
            MultipartFile file, String filePath) {
        FileMetadataBaseDto fileMetadataBaseDto = new FileMetadataBaseDto();

        // 设置文件基本信息
        fileMetadataBaseDto.setName(file.getOriginalFilename());
        fileMetadataBaseDto.setPath(filePath);
        fileMetadataBaseDto.setFileSize(file.getSize());

        // 设置文件类型
        FileTypeEnum fileType = FileTypeEnum.fromExtension(file.getOriginalFilename());
        fileMetadataBaseDto.setFileType(fileType);

        // 设置存储类型，与fileStorageService.uploadFile中使用的默认存储类型保持一致
        String defaultType = fileStorageConfig.getDefaultType();
        FileStorageTypeEnum storageType = FileStorageTypeEnum.valueOf(defaultType);
        fileMetadataBaseDto.setStorageType(storageType);

        return fileMetadataBaseDto;
    }

    /**
     * 构建文件元数据的写服务模型，用于创建文件元数据实体
     *
     * <p>该方法从上传的文件、业务上下文和存储路径中提取必要信息，构造 {@link CreateFileMetadataBto} 对象。 具体处理包括：
     *
     * <ul>
     *   <li>设置业务ID和业务类型，关联文件与业务数据
     *   <li>设置文件路径、名称和大小等基本属性
     *   <li>根据配置确定默认存储类型
     *   <li>根据文件扩展名自动判断文件类型
     *   <li>默认设置不加密
     * </ul>
     *
     * <p>该方法支持系统中的文件管理功能（如电子病历、医学影像、报告等）。
     *
     * @param file 上传的文件对象，包含文件名、大小等信息
     * @param fileBusiness 文件业务信息，包含业务ID和业务类型
     * @param filePath 文件存储路径，由存储服务生成
     * @return {@link CreateFileMetadataBto} 写服务模型对象，包含完整的文件元数据
     * @throws IllegalArgumentException 如果文件名无效（如无扩展名）或文件扩展名不受支持
     */
    private CreateFileMetadataBto getCreateFileMetadataBto(
            MultipartFile file, FileBusinessEo fileBusiness, String filePath) {
        CreateFileMetadataBto createFileMetadataBto = new CreateFileMetadataBto();
        createFileMetadataBto.setBusinessId(fileBusiness.getBusinessId());
        createFileMetadataBto.setBusinessType(fileBusiness.getBusinessType());
        createFileMetadataBto.setPath(filePath);
        createFileMetadataBto.setName(file.getOriginalFilename());
        createFileMetadataBto.setFileSize(file.getSize());
        createFileMetadataBto.setEncrypted(false);

        // 设置上传者ID为当前登录用户，如果为空则设置为系统用户
        String userId = UserContext.getUserId();
        if (userId == null || userId.trim().isEmpty()) {
            userId = SYSTEM_USER_ID;
        }
        createFileMetadataBto.setUploadBy(userId);

        String defaultType = fileStorageConfig.getDefaultType();
        FileStorageTypeEnum storageType = FileStorageTypeEnum.valueOf(defaultType);
        createFileMetadataBto.setStorageType(storageType);

        // 使用 FileTypeEnum 的静态方法设置文件类型
        FileTypeEnum fileType = FileTypeEnum.fromExtension(file.getOriginalFilename());
        createFileMetadataBto.setFileType(fileType);

        return createFileMetadataBto;
    }

    /**
     * 获取并验证文件元数据
     *
     * <p>该方法根据文件业务信息获取文件元数据，并进行以下验证：
     *
     * <ul>
     *   <li>检查文件元数据是否存在
     *   <li>验证业务ID和业务类型是否与文件元数据中的信息一致
     * </ul>
     *
     * @param fileBusiness 文件业务信息，包含文件ID、业务ID和业务类型
     * @return 文件元数据对象
     * @throws IgnoredException 如果文件元数据不存在或业务信息不匹配
     */
    private FileMetadataBaseDto getAndValidateFileMetadata(
            @Valid @NotNull FileBusinessEo fileBusiness) {
        // 调用 FileMetadataBaseDtoService.getById 获取文件元数据
        FileMetadataBaseDto fileMetadataBaseDto =
                fileMetadataBaseDtoService.getById(fileBusiness.getFileId());

        // 如果文件元数据为空，则抛出异常
        if (fileMetadataBaseDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "文件元数据不存在");
        }

        // 校验入参中的业务信息与文件元数据中的业务信息是否一致，如果不一致则抛出异常
        if (!fileBusiness.getBusinessId().equals(fileMetadataBaseDto.getBusinessId())
                || !fileBusiness.getBusinessType().equals(fileMetadataBaseDto.getBusinessType())) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "业务信息与文件元数据中的业务信息不一致");
        }

        return fileMetadataBaseDto;
    }

    /**
     * 校验批量删除文件的输入参数
     *
     * @param fileBusinessList 文件业务信息列表
     * @throws IgnoredException 如果输入参数不合法
     */
    private void validateBatchDeleteFileInput(List<FileBusinessEo> fileBusinessList) {
        if (fileBusinessList == null || fileBusinessList.isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "文件业务信息列表不能为空");
        }
    }

    /**
     * 处理批量文件删除的核心逻辑
     *
     * @param fileBusinessList 文件业务信息列表
     * @param successFileIdList 成功删除的文件ID列表
     * @param failedFileIdList 删除失败的文件ID列表
     */
    private void processBatchFileDelete(
            List<FileBusinessEo> fileBusinessList,
            List<String> successFileIdList,
            List<String> failedFileIdList) {
        for (int i = 0; i < fileBusinessList.size(); i++) {
            FileBusinessEo fileBusiness = fileBusinessList.get(i);

            try {
                // 校验单个文件业务信息
                if (!isValidFileBusinessForDelete(fileBusiness, i + 1)) {
                    continue;
                }

                // 调用单个文件删除方法，该方法内部有事务保证
                String deletedFileId = deleteFile(fileBusiness);
                successFileIdList.add(deletedFileId);

                log.debug("文件删除成功. FileId: {}", deletedFileId);

            } catch (Exception e) {
                // 记录失败的文件ID，但不中断整个批量删除流程
                handleSingleFileDeleteFailure(fileBusiness, failedFileIdList, e);
            }
        }
    }

    /**
     * 校验单个文件业务信息是否有效
     *
     * @param fileBusiness 文件业务信息
     * @param index 文件在列表中的位置（从1开始）
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidFileBusinessForDelete(FileBusinessEo fileBusiness, int index) {
        if (fileBusiness == null) {
            log.warn("第{}个文件业务信息为空，跳过删除", index);
            return false;
        }
        if (fileBusiness.getFileId() == null || fileBusiness.getFileId().trim().isEmpty()) {
            log.warn("第{}个文件的文件ID为空，跳过删除", index);
            return false;
        }
        if (fileBusiness.getBusinessId() == null || fileBusiness.getBusinessId().trim().isEmpty()) {
            log.warn("第{}个文件的业务ID为空，跳过删除", index);
            return false;
        }
        if (fileBusiness.getBusinessType() == null) {
            log.warn("第{}个文件的业务类型为空，跳过删除", index);
            return false;
        }
        return true;
    }

    /**
     * 处理单个文件删除失败的情况
     *
     * @param fileBusiness 文件业务信息
     * @param failedFileIdList 删除失败的文件ID列表
     * @param e 异常信息
     */
    private void handleSingleFileDeleteFailure(
            FileBusinessEo fileBusiness, List<String> failedFileIdList, Exception e) {
        String fileId = fileBusiness != null ? fileBusiness.getFileId() : "unknown";
        failedFileIdList.add(fileId);

        if (fileBusiness != null) {
            log.error(
                    "删除文件失败. FileId: {}, BusinessId: {}, BusinessType: {}",
                    fileBusiness.getFileId(),
                    fileBusiness.getBusinessId(),
                    fileBusiness.getBusinessType(),
                    e);
        } else {
            log.error("删除文件失败，文件业务信息为空", e);
        }
    }

    /**
     * 记录批量删除操作的结果
     *
     * @param totalCount 总请求数量
     * @param successCount 成功删除数量
     * @param failedFileIdList 删除失败的文件ID列表
     */
    private void logBatchDeleteResult(
            int totalCount, int successCount, List<String> failedFileIdList) {
        int failedCount = failedFileIdList.size();

        log.info("批量删除完成. 总请求数: {}, 成功删除: {}, 失败数: {}", totalCount, successCount, failedCount);

        if (!failedFileIdList.isEmpty()) {
            log.warn("以下文件删除失败: {}", failedFileIdList);
        }
    }

    /**
     * 校验业务参数
     *
     * @param fileBusiness 文件业务信息
     * @throws IgnoredException 如果业务参数不合法
     */
    private void validateBusinessParameters(FileBusinessEo fileBusiness) {
        if (fileBusiness.getBusinessId() == null || fileBusiness.getBusinessId().trim().isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "业务ID不能为空");
        }
        if (fileBusiness.getBusinessType() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "业务类型不能为空");
        }
    }

    /**
     * 根据业务信息获取文件元数据列表
     *
     * @param fileBusiness 文件业务信息
     * @return 文件元数据列表
     * @throws IgnoredException 如果获取失败
     */
    private List<FileMetadataBaseDto> getFileMetadataListByBusiness(FileBusinessEo fileBusiness) {
        if (fileBusiness.getFileId() != null && !fileBusiness.getFileId().trim().isEmpty()) {
            // 按文件ID查询单个文件
            return getSingleFileMetadata(fileBusiness);
        } else {
            // 按业务ID和业务类型查询文件列表
            return getFileMetadataByBusinessInfo(fileBusiness);
        }
    }

    /**
     * 获取单个文件元数据并校验业务信息
     *
     * @param fileBusiness 文件业务信息
     * @return 包含单个文件元数据的列表
     * @throws IgnoredException 如果文件不存在或业务信息不匹配
     */
    private List<FileMetadataBaseDto> getSingleFileMetadata(FileBusinessEo fileBusiness) {
        FileMetadataBaseDto fileMetadataBaseDto =
                fileMetadataBaseDtoService.getById(fileBusiness.getFileId());
        if (fileMetadataBaseDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "文件不存在");
        }

        // 校验业务信息是否匹配
        if (!fileBusiness.getBusinessId().equals(fileMetadataBaseDto.getBusinessId())
                || !fileBusiness.getBusinessType().equals(fileMetadataBaseDto.getBusinessType())) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "业务信息与文件元数据中的业务信息不一致");
        }

        return List.of(fileMetadataBaseDto);
    }

    /**
     * 根据业务ID和业务类型获取文件元数据列表
     *
     * @param fileBusiness 文件业务信息
     * @return 文件元数据列表
     */
    private List<FileMetadataBaseDto> getFileMetadataByBusinessInfo(FileBusinessEo fileBusiness) {
        IdxBusinessEo idxBusinessEo =
                IdxBusinessEo.builder()
                        .businessId(fileBusiness.getBusinessId())
                        .businessType(fileBusiness.getBusinessType())
                        .build();
        return fileMetadataBaseDtoService.getByBusinessIdAndBusinessType(idxBusinessEo);
    }

    /**
     * 生成文件URL列表
     *
     * @param fileMetadataList 文件元数据列表
     * @return 文件URL列表
     */
    private List<String> generateFileUrlList(List<FileMetadataBaseDto> fileMetadataList) {
        List<String> fileUrlList = new ArrayList<>();
        for (FileMetadataBaseDto fileMetadata : fileMetadataList) {
            try {
                String fileUrl =
                        fileStorageService.getFileUrl(
                                fileMetadata.getPath(),
                                DEFAULT_FILE_URL_EXPIRY_SECONDS,
                                fileMetadata.getStorageType());
                fileUrlList.add(fileUrl);
            } catch (Exception e) {
                log.warn(
                        "Failed to generate URL for file: {}, path: {}",
                        fileMetadata.getId(),
                        fileMetadata.getPath(),
                        e);
                // 继续处理其他文件，不因单个文件URL生成失败而中断整个流程
            }
        }
        return fileUrlList;
    }

    /**
     * 校验上传文件
     *
     * @param file 上传的文件
     * @throws IgnoredException 如果文件不合法
     */
    private void validateUploadFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "文件名不能为空");
        }

        // 校验文件类型（通过扩展名）
        try {
            FileTypeEnum.fromExtension(fileName);
        } catch (IllegalArgumentException e) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "不支持的文件类型: " + e.getMessage());
        }
    }

    /**
     * 上传文件到存储服务
     *
     * @param file 上传的文件
     * @return 文件存储路径
     * @throws IgnoredException 如果上传失败
     */
    private String uploadFileToStorage(MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            String contentType = file.getContentType();

            // 准备元数据
            Map<String, String> metadata = new HashMap<>();
            metadata.put(METADATA_KEY_ORIGINAL_FILENAME, fileName);

            // 上传文件
            return fileStorageService.uploadFile(
                    file.getInputStream(), fileName, contentType, null, metadata);
        } catch (Exception e) {
            log.error("Failed to upload file", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "上传文件失败");
        }
    }
}
