package com.pulse.file.manager.facade.impl;

import com.pulse.file.common.config.FileStorageConfig;
import com.pulse.file.common.enums.FileStorageTypeEnum;
import com.pulse.file.manager.facade.FileStorageAdapter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** 文件存储适配器工厂类 根据存储类型创建对应的存储适配器实例 */
@Component
public class FileStorageAdapterFactory {

    @Autowired private FileStorageConfig fileStorageConfig;

    /**
     * 获取默认的存储适配器
     *
     * @return 默认的存储适配器
     */
    public FileStorageAdapter getDefaultAdapter() {
        String defaultType = fileStorageConfig.getDefaultType();
        FileStorageTypeEnum storageType = FileStorageTypeEnum.valueOf(defaultType);
        return getAdapter(storageType);
    }

    /**
     * 根据存储类型获取对应的存储适配器
     *
     * @param storageType 存储类型
     * @return 存储适配器
     */
    public FileStorageAdapter getAdapter(FileStorageTypeEnum storageType) {
        switch (storageType) {
            case LOCAL:
                return new LocalFileStorageAdapter(fileStorageConfig.getLocal());
            case MINIO:
                return new MinIOStorageAdapter(fileStorageConfig.getMinIo());
            case AWS_S3:
                return new AWSS3StorageAdapter(fileStorageConfig.getAwsS3());
            case ALIYUN_OSS:
                return new AliyunOSSStorageAdapter(fileStorageConfig.getAliyunOss());
            default:
                throw new IllegalArgumentException("Unsupported storage type: " + storageType);
        }
    }
}
