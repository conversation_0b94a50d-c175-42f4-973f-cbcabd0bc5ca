package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.WardSimpleBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** WardSimpleBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "a6df0780-435e-3a19-80b7-3217942733c5")
public class WardSimpleBaseVoDataAssembler {

    /** 组装WardSimpleBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "a3bf5637-d03c-35f2-8fcd-ba0b55d5c6e2")
    public void assembleData(Map<String, WardSimpleBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装WardSimpleBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f79c9a5a-8f45-3594-94ee-db50487725ca")
    public void assembleDataCustomized(List<WardSimpleBaseVo> dataList) {
        // 自定义数据组装

    }
}
