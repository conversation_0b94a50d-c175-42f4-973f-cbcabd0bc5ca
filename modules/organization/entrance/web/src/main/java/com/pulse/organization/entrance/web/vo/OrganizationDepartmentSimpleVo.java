package com.pulse.organization.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9c0c84c0-ad5b-4ddd-9d6d-e1a3bd303b0e|VO|DEFINITION")
public class OrganizationDepartmentSimpleVo {
    /** 科室 */
    @Valid
    @AutoGenerated(locked = true, uuid = "cfb04e1e-7460-47ce-bbb9-d21282a5895d")
    private DepartmentSimpleVo department;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "a574cb72-f953-4f57-8c6e-df5928819894")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "616015ba-ca7a-4899-a3b0-0a4e4e0dbfc2")
    private InputCodeEo inputCode;

    /** 组织名称 */
    @AutoGenerated(locked = true, uuid = "af6a03a8-1186-47a6-bd1f-71c648f1d1ec")
    private String name;

    /** 组织层级 */
    @AutoGenerated(locked = true, uuid = "2a0dd8b1-c01c-4597-9611-7e047ed14343")
    private Long organizationLevel;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "8d9a6b53-6c7f-4033-b381-22b778bce01f")
    private Long sortNumber;
}
