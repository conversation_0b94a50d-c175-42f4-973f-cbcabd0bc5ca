package com.pulse.organization.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "4a4aa2d5-62db-4a76-8699-29b20e5a70c1|VO|DEFINITION")
public class CampusBaseVo {
    /** 院区编号 */
    @AutoGenerated(locked = true, uuid = "d56ec86c-c6f2-4ef7-9c3f-e90342a50c8f")
    private String campusNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "38f5d054-a719-4a56-a7a7-476bd80a129d")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "e4041ed7-9a2a-485f-9012-d38a6e1cf949")
    private String createdBy;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "786820fd-c64a-43bd-8c0f-7fd884c43bea")
    private String deletedBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "19074332-b6ee-442a-bb65-5b6e1ded558d")
    private String id;

    /** 医保编码 */
    @AutoGenerated(locked = true, uuid = "9bfe49bb-a2b3-4c8f-a959-c3c29d53caf1")
    private String medicalInsuranceCode;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "ae7fa42d-ce42-44d7-8339-aab7dc431dfc")
    private String organizationId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "27d9144a-d914-42a3-a35a-0209925600ad")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "e578266b-707b-4ce3-907a-dd1f520b4237")
    private String updatedBy;
}
