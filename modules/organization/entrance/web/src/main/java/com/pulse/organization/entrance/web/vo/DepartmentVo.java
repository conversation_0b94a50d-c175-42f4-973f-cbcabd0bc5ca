package com.pulse.organization.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.TimePeriodEo;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.organization.common.enums.GenderLimitEnum;
import com.pulse.organization.common.enums.MedicalServiceTypeEnum;
import com.pulse.organization.common.enums.OrganizationPropertyEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.StorageTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "6e70fc23-01c8-42b4-b927-6b0e8a0a27f9|VO|DEFINITION")
public class DepartmentVo {
    /** 核算科室名称 */
    @AutoGenerated(locked = true, uuid = "fbf84a05-7f16-4414-90b2-4f82481221cf")
    private String accountingDepartmentName;

    /** 核算科室组织id */
    @AutoGenerated(locked = true, uuid = "6e1bd27b-0e6e-4b04-bb14-93165e1d9d2c")
    private String accountingDepartmentOrganizationId;

    /** 年龄下限 */
    @AutoGenerated(locked = true, uuid = "5f9d1542-133a-4c74-badf-1aa2a9a18d10")
    private Long ageLowerLimit;

    /** 年龄上限 */
    @AutoGenerated(locked = true, uuid = "eb6abca0-2c2b-4d33-a005-02a01ceaa06a")
    private Long ageUpperLimit;

    /** 平均处方限额 */
    @AutoGenerated(locked = true, uuid = "842ffbc7-9b98-4bca-ad2b-0a7054ce85ea")
    private BigDecimal averagePrescriptionLimit;

    /** 院区名称 */
    @AutoGenerated(locked = true, uuid = "35275c13-e634-4acd-b808-7f3490271c3a")
    private String campusName;

    /** 院区组织id */
    @AutoGenerated(locked = true, uuid = "650019ce-3633-419d-b7f1-ebeb7aa0cf46")
    private String campusOrganizationId;

    /** 成本科室名称 */
    @AutoGenerated(locked = true, uuid = "de2d845b-3526-4f8e-a084-f1be189610ed")
    private String costDepartmentName;

    /** 成本科室组织id */
    @AutoGenerated(locked = true, uuid = "f1db7fc8-ae30-454e-8586-405af36a28d3")
    private String costDepartmentOrganizationId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "3f5412ce-8f02-4dd5-a4e7-0693fbbddea3")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "cd112601-3b8e-4e35-96d4-0ba48504428e")
    private String createdBy;

    /** 默认处方类型 */
    @AutoGenerated(locked = true, uuid = "ed042cb0-f26d-4f25-991d-1d25c4334478")
    private String defaultPrescriptionType;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "38f084f2-2149-4cd9-93bb-8ddff7b6e153")
    private String deletedBy;

    /** 科室层级 */
    @AutoGenerated(locked = true, uuid = "87c5729f-8cb7-4f83-9646-7c58c8aa5616")
    private Long departmentLevel;

    /** 科室性质 */
    @AutoGenerated(locked = true, uuid = "769250c0-52cc-4f21-aa8f-df0e1775593c")
    private OrganizationPropertyEnum departmentProperty;

    /** 药品分类列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d7199e5c-dfea-4dd2-a3d2-4e98b2181007")
    private List<String> drugCatalogList;

    /** 药物类别列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "13b487d8-299c-4439-86ce-666851e83d68")
    private List<DrugTypeEnum> drugTypeList;

    /** 性别限制 */
    @AutoGenerated(locked = true, uuid = "745d0183-c613-4993-955b-2288c0a1dc6b")
    private GenderLimitEnum genderLimit;

    /** 人事科室名称 */
    @AutoGenerated(locked = true, uuid = "d9cb33cc-3a8c-4b41-8dac-f52456a03c7e")
    private String hrDepartmentName;

    /** 人事科室组织id */
    @AutoGenerated(locked = true, uuid = "c986631e-e82b-438f-9c2a-93dff1c720c2")
    private String hrDepartmentOrganizationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "8684d3e9-e8fc-4965-a432-1eb1323543ac")
    private String id;

    /** 针剂领第二天量 */
    @AutoGenerated(locked = true, uuid = "cb8b2d43-68b5-47e0-bfe9-6459be4a2a91")
    private BigDecimal injectionSecondDayAmount;

    /** 管理科室名称 */
    @AutoGenerated(locked = true, uuid = "a87ed960-1bdf-449f-9519-35ae435ed949")
    private String manageDepartmentName;

    /** 管理科室组织id */
    @AutoGenerated(locked = true, uuid = "117b9527-1953-47b7-9e10-a76766cae6a5")
    private String manageDepartmentOrganizationId;

    /** 病案科室代码 */
    @AutoGenerated(locked = true, uuid = "89db2a1d-0af8-4e62-a968-3135cf05b04f")
    private String medicalRecordDepartmentCode;

    /** 医疗服务类型 */
    @AutoGenerated(locked = true, uuid = "3e30b257-2c2d-4a31-8ddf-05d7d16ed933")
    private MedicalServiceTypeEnum medicalServiceType;

    /** 开放时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "04905ccd-9a9a-4561-8469-311b841f3295")
    private TimePeriodEo openTimePeriod;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "63f6e81a-b767-4b47-9ffa-f80dfa6ff487")
    private String organizationId;

    /** 上级科室名称 */
    @AutoGenerated(locked = true, uuid = "1c095122-aa0f-40bb-97f9-4972de24fd91")
    private String parentDepartmentName;

    /** 上级科室组织id */
    @AutoGenerated(locked = true, uuid = "18ee8565-1a04-41ca-9a14-87e670e36d95")
    private String parentDepartmentOrganizationId;

    /** 省平台作废标志 */
    @AutoGenerated(locked = true, uuid = "5af61371-db3d-4786-a42c-43bc6edefdb4")
    private Boolean provincePlatformCancelFlag;

    /** 挂号科室启用标志 */
    @AutoGenerated(locked = true, uuid = "3ac6e67d-c1ee-4a57-953f-e0b4f98884cb")
    private Boolean registerDepartmentEnableFlag;

    /** 挂号科室标志 */
    @AutoGenerated(locked = true, uuid = "a1bd1b18-99c5-48f9-86e2-1bfbe7bc6b60")
    private Boolean registerDepartmentFlag;

    /** 专科标志 */
    @AutoGenerated(locked = true, uuid = "ed491410-76b0-4f98-8843-ed5e55f39144")
    private Boolean specialtyFlag;

    /** 标准科室目录id */
    @AutoGenerated(locked = true, uuid = "eea2ea4f-ae15-45d8-a015-69960ae29e35")
    private String standardDepartmentCatalogId;

    /** 库房类型 */
    @AutoGenerated(locked = true, uuid = "111d2ab6-b2c5-48dd-8b1d-b52bc3109f26")
    private StorageTypeEnum storageType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ddf17f80-c26a-4225-8eb7-1861bc979c2c")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "dd20f3b6-45ba-469a-9e62-767cfac2be5b")
    private String updatedBy;
}
