package com.pulse.organization.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "7901952d-c02f-4f04-abc1-f0dd8c3de18d|VO|DEFINITION")
public class AssetOrganizationVo {
    /** 应用信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4b369dfc-a9eb-4280-9390-061efbaeca1e")
    private OrganizationRefApplicationBaseVo application;

    /** 资产ID */
    @AutoGenerated(locked = true, uuid = "f201ff78-8464-4ccb-9bd6-16843aa27cb3")
    private String assetId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "48b4a335-7883-49f1-8aa3-b2949e95dec0")
    private Date createdAt;

    /** 创建者ID */
    @AutoGenerated(locked = true, uuid = "ef1e90d8-4d16-4467-b7ed-5df7084110c1")
    private String createdBy;

    /** 删除者ID */
    @AutoGenerated(locked = true, uuid = "9491bf29-407d-4346-bff4-ff3bda395f43")
    private String deletedBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b89e4f85-cab0-44d4-89e6-fd365cc09b0f")
    private String id;

    /** 组织信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "283de87a-73bf-4147-bfbf-f6cf34743786")
    private OrganizationSimpleVo organization;
}
