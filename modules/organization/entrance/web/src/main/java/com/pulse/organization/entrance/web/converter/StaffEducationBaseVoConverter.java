package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffEducationBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.StaffEducationBaseVo;
import com.pulse.organization.manager.dto.StaffEducationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffEducationBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "61116e47-f5e4-4035-a89b-0139f6ad16a6|VO|CONVERTER")
public class StaffEducationBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffEducationBaseVoDataAssembler staffEducationBaseVoDataAssembler;

    /** 使用默认方式组装StaffEducationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "3af2ec4f-1bca-3bfb-9a4b-18d506eddc1b")
    public StaffEducationBaseVo convertAndAssembleData(StaffEducationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffEducationBaseDto转换成StaffEducationBaseVo */
    @AutoGenerated(locked = false, uuid = "61116e47-f5e4-4035-a89b-0139f6ad16a6-converter-Map")
    public Map<StaffEducationBaseDto, StaffEducationBaseVo> convertToStaffEducationBaseVoMap(
            List<StaffEducationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffEducationBaseDto, StaffEducationBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffEducationBaseVo vo = new StaffEducationBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setStartDate(dto.getStartDate());
                                            vo.setEndDate(dto.getEndDate());
                                            vo.setGraduatedInstitution(
                                                    dto.getGraduatedInstitution());
                                            vo.setQualification(dto.getQualification());
                                            vo.setStudyMode(dto.getStudyMode());
                                            vo.setMajor(dto.getMajor());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setStaffId(dto.getStaffId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffEducationBaseDto转换成StaffEducationBaseVo */
    @AutoGenerated(locked = true, uuid = "61116e47-f5e4-4035-a89b-0139f6ad16a6-converter-list")
    public List<StaffEducationBaseVo> convertToStaffEducationBaseVoList(
            List<StaffEducationBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffEducationBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装StaffEducationBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "6a7268ed-52c3-360e-b0c9-13e96dd47cc7")
    public List<StaffEducationBaseVo> convertAndAssembleDataList(
            List<StaffEducationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, StaffEducationBaseVo> voMap =
                convertToStaffEducationBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffEducationBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffEducationBaseDto转换成StaffEducationBaseVo */
    @AutoGenerated(locked = true, uuid = "a12e0bd0-579c-3dec-9a05-01c6fdb507c6")
    public StaffEducationBaseVo convertToStaffEducationBaseVo(StaffEducationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffEducationBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
