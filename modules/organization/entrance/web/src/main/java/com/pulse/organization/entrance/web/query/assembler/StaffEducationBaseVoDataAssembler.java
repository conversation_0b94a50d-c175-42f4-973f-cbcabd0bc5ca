package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.StaffEducationBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** StaffEducationBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "b92ad742-ae44-38ac-b332-755e3cd51671")
public class StaffEducationBaseVoDataAssembler {

    /** 组装StaffEducationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "378f6f23-88b6-3e4f-8666-65cf9c3063b3")
    public void assembleData(Map<String, StaffEducationBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装StaffEducationBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "518203ed-9b11-3e26-8c29-94e03af75fa2")
    public void assembleDataCustomized(List<StaffEducationBaseVo> dataList) {
        // 自定义数据组装

    }
}
