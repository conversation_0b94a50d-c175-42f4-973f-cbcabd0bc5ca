package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.WardBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** WardBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "8db6f60a-580f-33ea-8d3e-3fb7c1bf5079")
public class WardBaseVoDataAssembler {

    /** 组装WardBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "6a57bf3a-aee2-3948-b57a-675c2b708f81")
    public void assembleData(Map<String, WardBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装WardBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "921e01bd-88f9-3a53-b4b7-2475700c5df1")
    public void assembleDataCustomized(List<WardBaseVo> dataList) {
        // 自定义数据组装

    }
}
