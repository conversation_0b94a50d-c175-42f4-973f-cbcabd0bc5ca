package com.pulse.organization.service;

import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.pulse.organization.manager.bo.*;
import com.pulse.organization.manager.dto.OrganizationRelationshipBaseDto;
import com.pulse.organization.manager.dto.OrganizationRelationshipDto;
import com.pulse.organization.persist.dos.OrganizationRelationship;
import com.pulse.organization.persist.qto.SearchOrganizationRelationshipQto;
import com.pulse.organization.service.base.BaseOrganizationRelationshipBOService;
import com.pulse.organization.service.bto.CreateOrganizationRelationshipBto;
import com.pulse.organization.service.bto.DeleteOrganizationRelationshipBto;
import com.pulse.organization.service.bto.MergeOrganizationRelationshipBto;
import com.pulse.organization.service.query.OrganizationRelationshipDtoQueryService;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "13fad27b-6b12-4816-86ed-9a8f88cc7152|BO|SERVICE")
public class OrganizationRelationshipBOService extends BaseOrganizationRelationshipBOService {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipBaseDtoService organizationRelationshipBaseDtoService;

    @Resource
    private OrganizationRelationshipDtoQueryService organizationRelationshipDtoQueryService;

    /** 创建组织关系 */
    @PublicInterface(id = "b90587b7-b4e0-4e8f-a20e-f32823bfa374", module = "organization")
    @Transactional
    @AutoGenerated(locked = false, uuid = "16d19411-2291-4302-9d8a-1993cb9e53fb")
    public String createOrganizationRelationship(
            @Valid @NotNull CreateOrganizationRelationshipBto createOrganizationRelationshipBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateOrganizationRelationshipBoResult boResult =
                super.createOrganizationRelationshipBase(createOrganizationRelationshipBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateOrganizationRelationshipBto */
        {
            CreateOrganizationRelationshipBto bto =
                    boResult
                            .<CreateOrganizationRelationshipBto>getBtoOfType(
                                    CreateOrganizationRelationshipBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateOrganizationRelationshipBto, OrganizationRelationshipBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                OrganizationRelationshipBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 删除组织关系 */
    @PublicInterface(id = "a3d664c2-76b7-46d7-ac32-7589b3e714f9", module = "organization")
    @Transactional
    @AutoGenerated(locked = false, uuid = "7e9e62ae-6b17-4745-a0ac-3a86bff7d785")
    public String deleteOrganizationRelationship(
            @Valid @NotNull DeleteOrganizationRelationshipBto deleteOrganizationRelationshipBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        OrganizationRelationshipBaseDto organizationRelationshipBaseDto =
                organizationRelationshipBaseDtoService.getById(
                        deleteOrganizationRelationshipBto.getId());
        DeleteOrganizationRelationshipBoResult boResult =
                super.deleteOrganizationRelationshipBase(deleteOrganizationRelationshipBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteOrganizationRelationshipBto */
        {
            DeleteOrganizationRelationshipBto bto =
                    boResult
                            .<DeleteOrganizationRelationshipBto>getBtoOfType(
                                    DeleteOrganizationRelationshipBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteOrganizationRelationshipBto, OrganizationRelationship> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "c7bae032-254f-445d-9a7e-605aec32da00",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            version = "1743572436463",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "c7bae032-254f-445d-9a7e-605aec32da00")
    public List<String> mergeOrganizationRelationshipList(
            @Valid List<MergeOrganizationRelationshipBto> mergeOrganizationRelationshipBtoList,
            String sourceOrganizationId,
            OrganizationTypeEnum sourceOrganizationType,
            OrganizationTypeEnum targetOrganizationType) {
        // TODO implement method
        // 必须确定是保存那种关系
        if (Strings.isEmpty(sourceOrganizationId)
                || sourceOrganizationType == null
                || targetOrganizationType == null) {
            return null;
        }

        List<String> resultList = new ArrayList<>();
        // 查询当前库中组织的特定组织关系list
        SearchOrganizationRelationshipQto searchOrganizationRelationshipQto =
                new SearchOrganizationRelationshipQto();
        searchOrganizationRelationshipQto.setSourceOrganizationIdIs(sourceOrganizationId);
        searchOrganizationRelationshipQto.setSourceOrganizationTypeIs(sourceOrganizationType);
        searchOrganizationRelationshipQto.setTargetOrganizationTypeIs(targetOrganizationType);
        List<OrganizationRelationshipDto> dbOrganizationRelationshipDtoList =
                organizationRelationshipDtoQueryService.searchOrganizationRelationship(
                        searchOrganizationRelationshipQto);
        // 对比数据并处理
        List<String> mergeIds =
                mergeOrganizationRelationshipBtoList.stream()
                        .map(MergeOrganizationRelationshipBto::getId)
                        .filter(id -> !Strings.isNullOrEmpty(id))
                        .collect(Collectors.toList());

        // merge此次数据
        for (MergeOrganizationRelationshipBto mergeOrganizationRelationshipBto :
                mergeOrganizationRelationshipBtoList) {
            // 这里做一次数据设置，防止错误
            mergeOrganizationRelationshipBto.setSourceOrganizationId(sourceOrganizationId);
            mergeOrganizationRelationshipBto.setSourceOrganizationType(sourceOrganizationType);
            mergeOrganizationRelationshipBto.setTargetOrganizationType(targetOrganizationType);
            resultList.add(mergeOrganizationRelationship(mergeOrganizationRelationshipBto));
        }

        // 删除库中存在，此次保存没有的数据
        for (OrganizationRelationshipDto dto : dbOrganizationRelationshipDtoList) {
            if (!mergeIds.contains(dto.getId())) {
                DeleteOrganizationRelationshipBto deleteOrganizationRelationshipBto =
                        new DeleteOrganizationRelationshipBto();
                deleteOrganizationRelationshipBto.setId(dto.getId());
                deleteOrganizationRelationship(deleteOrganizationRelationshipBto);
            }
        }
        return resultList;
    }

    @PublicInterface(id = "e1d6117b-3ea1-4cc3-8084-5d9d45a02f55", module = "organization")
    @Transactional
    @AutoGenerated(locked = false, uuid = "f01c71cb-10b3-4f03-8d31-4f2dacf16161")
    public String mergeOrganizationRelationship(
            @Valid @NotNull MergeOrganizationRelationshipBto mergeOrganizationRelationshipBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        OrganizationRelationshipBaseDto organizationRelationshipBaseDto = null;
        if (mergeOrganizationRelationshipBto.getId() != null) {
            organizationRelationshipBaseDto =
                    organizationRelationshipBaseDtoService.getById(
                            mergeOrganizationRelationshipBto.getId());
        }
        MergeOrganizationRelationshipBoResult boResult =
                super.mergeOrganizationRelationshipBase(mergeOrganizationRelationshipBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeOrganizationRelationshipBto */
        {
            MergeOrganizationRelationshipBto bto =
                    boResult
                            .<MergeOrganizationRelationshipBto>getBtoOfType(
                                    MergeOrganizationRelationshipBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            MergeOrganizationRelationshipBto,
                            OrganizationRelationship,
                            OrganizationRelationshipBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeOrganizationRelationshipBto, OrganizationRelationshipBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                OrganizationRelationshipBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                OrganizationRelationship entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                OrganizationRelationshipBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
