package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.TeamDetailDtoConverter;
import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamDetailDto;
import com.pulse.organization.persist.qto.SearchTeamDetailQto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.pulse.organization.service.index.entity.SearchTeamDetailQtoService;
import com.pulse.organization.service.query.assembler.TeamDetailDtoDataAssembler;
import com.pulse.organization.service.query.assembler.TeamDetailDtoDataAssembler.TeamDetailDtoDataHolder;
import com.pulse.organization.service.query.collector.TeamDetailDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** TeamDetailDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "e36699d1-7d93-35d2-b9c8-f85a7a808a18")
public class TeamDetailDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchTeamDetailQtoService searchTeamDetailQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailDtoConverter teamDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailDtoDataAssembler teamDetailDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailDtoDataCollector teamDetailDtoDataCollector;

    /** 根据SearchTeamDetailQto查询TeamDetailDto列表,上限500 */
    @PublicInterface(id = "92f0f025-dd89-4ebf-89f3-c008ca75b798", module = "organization")
    @AutoGenerated(locked = false, uuid = "96489347-476d-3a12-9577-81367c95769f")
    public List<TeamDetailDto> searchTeamDetailList(@Valid @NotNull SearchTeamDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchTeamDetailQtoService.query(qto);
        TeamDetailDtoDataHolder dataHolder = new TeamDetailDtoDataHolder();
        List<TeamDetailDto> result = toDtoList(ids, dataHolder);
        teamDetailDtoDataCollector.collectDataDefault(dataHolder);
        teamDetailDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "b0e679c6-09d6-38fd-a5dd-6f08ba1f1258")
    private List<TeamDetailDto> toDtoList(List<String> ids, TeamDetailDtoDataHolder dataHolder) {
        List<TeamBaseDto> baseDtoList = teamBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, TeamDetailDto> dtoMap =
                teamDetailDtoConverter.convertFromTeamBaseDtoToTeamDetailDto(baseDtoList).stream()
                        .collect(Collectors.toMap(TeamDetailDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchTeamDetailQto查询TeamDetailDto列表,分页 */
    @PublicInterface(id = "772d2279-eb33-483f-886d-52ff0f52f0de", module = "organization")
    @AutoGenerated(locked = false, uuid = "c3ff90aa-463a-30bf-bf3e-ee89b7228391")
    public VSQueryResult<TeamDetailDto> searchTeamDetailPaged(
            @Valid @NotNull SearchTeamDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchTeamDetailQtoService.queryPaged(qto);
        TeamDetailDtoDataHolder dataHolder = new TeamDetailDtoDataHolder();
        List<TeamDetailDto> dtoList = toDtoList(ids, dataHolder);
        teamDetailDtoDataCollector.collectDataDefault(dataHolder);
        teamDetailDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchTeamDetailQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchTeamDetailQto查询数量 */
    @PublicInterface(id = "8419abf8-e755-4c67-8c34-ac8ac8e733dc", module = "organization")
    @AutoGenerated(locked = false, uuid = "d570809c-148c-34da-948d-32fc28c35656")
    public Integer searchTeamDetailListCount(@Valid @NotNull SearchTeamDetailQto qto) {
        return searchTeamDetailQtoService.count(qto);
    }
}
