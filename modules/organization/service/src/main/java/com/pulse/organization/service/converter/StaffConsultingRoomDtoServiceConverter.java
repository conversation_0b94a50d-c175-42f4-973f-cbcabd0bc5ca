package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.StaffConsultingRoomDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "716dfb99-f2a1-3d55-90cc-62728ab846e9")
public class StaffConsultingRoomDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<StaffConsultingRoomDto> StaffConsultingRoomDtoConverter(
            List<StaffConsultingRoomDto> staffConsultingRoomDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return staffConsultingRoomDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
