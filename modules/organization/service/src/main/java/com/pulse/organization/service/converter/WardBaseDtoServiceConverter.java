package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.WardBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "d438a259-4a0b-33ab-90ed-6e90155599a8")
public class WardBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<WardBaseDto> WardBaseDtoConverter(List<WardBaseDto> wardBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return wardBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
