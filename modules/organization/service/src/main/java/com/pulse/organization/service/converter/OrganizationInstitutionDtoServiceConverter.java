package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.OrganizationInstitutionDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "cf8a4b4d-a901-34a6-a866-189b7b2fcd82")
public class OrganizationInstitutionDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OrganizationInstitutionDto> OrganizationInstitutionDtoConverter(
            List<OrganizationInstitutionDto> organizationInstitutionDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return organizationInstitutionDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
