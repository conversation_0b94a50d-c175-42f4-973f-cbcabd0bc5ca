package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** StaffUserBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "3e6d2afc-06b9-30c0-ac83-3925148f2004")
public class StaffUserBaseDtoDataAssembler {

    /** 组装StaffUserBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "3c48dd39-db47-3d0d-812e-27a11689cf28")
    public void assembleData(List<StaffUserBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装StaffUserBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "c44c940f-d04f-3a86-aa69-e40c5eaba2c5")
    public void assembleDataCustomized(List<StaffUserBaseDto> dataList) {
        // 自定义数据组装

    }
}
