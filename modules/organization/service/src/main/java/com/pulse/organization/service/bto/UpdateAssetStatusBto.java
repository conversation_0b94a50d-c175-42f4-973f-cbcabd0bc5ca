package com.pulse.organization.service.bto;

import com.pulse.organization.common.enums.AssetStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Asset
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "2b5dd470-44f3-4322-86fc-01aa6d4fa1f2|BTO|DEFINITION")
public class UpdateAssetStatusBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "6f3ab67c-3175-4422-a2cc-e9e5a2d2d1b5")
    private String id;

    /** 标签列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2e8dbbde-e85e-4b4f-b6e9-46960a553534")
    private List<String> labelList;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "cf7a3053-3bec-44d9-936e-edb64cb6788c")
    private AssetStatusEnum status;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setLabelList(List<String> labelList) {
        this.__$validPropertySet.add("labelList");
        this.labelList = labelList;
    }

    @AutoGenerated(locked = true)
    public void setStatus(AssetStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }
}
