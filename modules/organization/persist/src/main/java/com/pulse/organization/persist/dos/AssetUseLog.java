package com.pulse.organization.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.organization.common.enums.AssetUseStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "asset_use_log", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "8c5d11f0-d122-3425-ba7c-1a6cb39de184|ENTITY|DEFINITION")
public class AssetUseLog {
    @AutoGenerated(locked = true, uuid = "2ee26b6d-a849-333a-84a7-d3b0e9846bb1")
    @TableField(value = "asset_id")
    private String assetId;

    @AutoGenerated(locked = true, uuid = "073b55b4-905f-306f-a4cd-d5ea9fa70f79")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "02e01ec2-d20a-3741-b957-229b0ba3509d")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "9b486da3-230c-3b97-87f7-cc505fc94043")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "c930545c-6e97-3b49-b09c-9bb2adebfa21")
    @TableField(value = "status")
    private AssetUseStatusEnum status;

    @AutoGenerated(locked = true, uuid = "9bda0f8d-954d-3252-a395-1e3f2d29cb5f")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "7987de52-3cb0-398c-a74a-70593345cd64")
    @TableField(value = "use_organization_id")
    private String useOrganizationId;

    @AutoGenerated(locked = true, uuid = "151941a0-ded6-3755-b491-b1f2f7aeab90")
    @TableField(value = "use_time_end")
    private Date useTimeEnd;

    @AutoGenerated(locked = true, uuid = "60a0af54-e510-3132-aec6-dca0eb47dc16")
    @TableField(value = "use_time_start")
    private Date useTimeStart;

    @AutoGenerated(locked = true, uuid = "b38ae05f-28fa-3519-adc4-3a898368b2a1")
    @TableField(value = "user_id")
    private String userId;
}
