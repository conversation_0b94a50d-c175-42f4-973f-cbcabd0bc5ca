package com.pulse.organization.persist.mapper;

import com.pulse.organization.persist.dos.StaffEducation;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "50eb75e9-773b-443d-a07f-c5a3ffd646f8|ENTITY|IDAO")
public interface StaffEducationDao {

    @AutoGenerated(locked = true, uuid = "14452f11-3441-3aa2-a06f-98e427d2b9c2")
    List<StaffEducation> getByStaffId(String staffId);

    @AutoGenerated(locked = true, uuid = "15ee20fb-3a2e-3ba5-84ea-8a5fec944b93")
    List<StaffEducation> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "28ccb2eb-1f04-31a3-a5ef-a664f1d72548")
    StaffEducation getById(String id);

    @AutoGenerated(locked = true, uuid = "a40a454e-348f-3140-8dac-2dbb77c421b3")
    List<StaffEducation> getByStaffIds(List<String> staffId);
}
