package com.pulse.dictionary_basic.persist.dos.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;
import com.vs.sqlmapper.convert.Convert;

@AutoGenerated(locked = true, uuid = "d4c8ef92-91e5-31f0-9975-b2bf87bdbd99")
public class InputCodeEoDeSerializer implements Convert<InputCodeEo> {

    @AutoGenerated(locked = true)
    @Override
    public InputCodeEo convert(Object src) {
        if (src == null) {
            return null;
        } else {
            return JsonUtils.readObject((String) src, new TypeReference<InputCodeEo>() {});
        }
    }

    @AutoGenerated(locked = true)
    @Override
    public boolean support(Object src) {
        return src instanceof String;
    }
}
