package com.pulse.organization.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.organization.persist.dos.StaffPractice;
import com.pulse.organization.persist.mapper.StaffPracticeDao;
import com.pulse.organization.persist.mapper.mybatis.StaffPracticeMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "83f895ee-306c-4200-b261-443586d89065|ENTITY|DAO")
public class StaffPracticeDaoImpl implements StaffPracticeDao {
    @AutoGenerated(locked = true)
    @Resource
    private StaffPracticeMapper staffPracticeMapper;

    @AutoGenerated(locked = true, uuid = "45680e06-b746-334d-890e-************")
    @Override
    public StaffPractice getById(String id) {
        QueryWrapper<StaffPractice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return staffPracticeMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "5cb47523-bd6e-3c26-a4f1-4b1994163441")
    @Override
    public List<StaffPractice> getByStaffIds(List<String> staffId) {
        QueryWrapper<StaffPractice> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("staff_id", staffId).eq("deleted_at", 0L).orderByAsc("id");
        return staffPracticeMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "991df8bb-510f-3805-b9f1-e19ec736411c")
    @Override
    public List<StaffPractice> getByStaffId(String staffId) {
        QueryWrapper<StaffPractice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("staff_id", staffId).eq("deleted_at", 0L).orderByAsc("id");
        return staffPracticeMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f9bd5287-3ffc-3859-a2bd-9d0bda0099cf")
    @Override
    public List<StaffPractice> getByIds(List<String> id) {
        QueryWrapper<StaffPractice> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return staffPracticeMapper.selectList(queryWrapper);
    }
}
