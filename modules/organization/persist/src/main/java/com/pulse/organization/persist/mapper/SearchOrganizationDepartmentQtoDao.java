package com.pulse.organization.persist.mapper;

import com.pulse.organization.persist.qto.SearchOrganizationDepartmentQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "3bc07113-46ef-4474-9c78-ec0e81ecfa93|QTO|DAO")
public class SearchOrganizationDepartmentQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询组织科室列表(按院区、专科标志、状态、关键词等) */
    @AutoGenerated(locked = false, uuid = "3bc07113-46ef-4474-9c78-ec0e81ecfa93-count")
    public Integer count(SearchOrganizationDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(organization.id) FROM organization LEFT JOIN department"
                    + " \"department\" on organization.id = \"department\".organization_id WHERE"
                    + " organization.type = 'DEPARTMENT' AND \"department\".campus_organization_id"
                    + " = #departmentCampusOrganizationIdIs AND \"department\".specialty_flag ="
                    + " #departmentSpecialtyFlagIs AND organization.status = #statusIs AND"
                    + " \"department\".medical_service_type = #departmentMedicalServiceTypeIs AND"
                    + " \"department\".department_property = #departmentDpdepartmentPropertyIs AND"
                    + " ( organization.name like #nameLike OR organization.alias like #aliasLike OR"
                    + " organization.abbreviation like #abbreviationLike OR"
                    + " JSON_VALUE(organization.input_code, '$.pinyin') like #inputCodePinyinLike"
                    + " OR JSON_VALUE(organization.input_code, '$.wubi') like #inputCodeWubiLike OR"
                    + " JSON_VALUE(organization.input_code, '$.custom') like #inputCodeCustomLike )"
                    + " ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDepartmentCampusOrganizationIdIs() == null) {
            conditionToRemove.add("#departmentCampusOrganizationIdIs");
        }
        if (qto.getDepartmentDpdepartmentPropertyIs() == null) {
            conditionToRemove.add("#departmentDpdepartmentPropertyIs");
        }
        if (qto.getDepartmentMedicalServiceTypeIs() == null) {
            conditionToRemove.add("#departmentMedicalServiceTypeIs");
        }
        if (qto.getDepartmentSpecialtyFlagIs() == null) {
            conditionToRemove.add("#departmentSpecialtyFlagIs");
        }
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getAbbreviationLike() == null) {
            conditionToRemove.add("#abbreviationLike");
        }
        if (qto.getAliasLike() == null) {
            conditionToRemove.add("#aliasLike");
        }
        if (qto.getStatusIs() == null) {
            conditionToRemove.add("#statusIs");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("organization");
        softDeleteTableAlias.add("\"department\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#departmentCampusOrganizationIdIs", "?")
                        .replace("#departmentDpdepartmentPropertyIs", "?")
                        .replace("#departmentMedicalServiceTypeIs", "?")
                        .replace("#departmentSpecialtyFlagIs", "?")
                        .replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#abbreviationLike", "?")
                        .replace("#aliasLike", "?")
                        .replace("#statusIs", "?")
                        .replace("#nameLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#departmentCampusOrganizationIdIs")) {
                sqlParams.add(qto.getDepartmentCampusOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#departmentDpdepartmentPropertyIs")) {
                sqlParams.add(qto.getDepartmentDpdepartmentPropertyIs().toString());
            } else if (paramName.equalsIgnoreCase("#departmentMedicalServiceTypeIs")) {
                sqlParams.add(qto.getDepartmentMedicalServiceTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#departmentSpecialtyFlagIs")) {
                sqlParams.add(qto.getDepartmentSpecialtyFlagIs());
            } else if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#abbreviationLike")) {
                sqlParams.add("%" + qto.getAbbreviationLike() + "%");
            } else if (paramName.equalsIgnoreCase("#aliasLike")) {
                sqlParams.add("%" + qto.getAliasLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIs")) {
                sqlParams.add(qto.getStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询组织科室列表(按院区、专科标志、状态、关键词等) */
    @AutoGenerated(locked = false, uuid = "3bc07113-46ef-4474-9c78-ec0e81ecfa93-query-all")
    public List<String> query(SearchOrganizationDepartmentQto qto) {
        qto.setSize(2000);
        qto.setFrom(0);
        return this.queryPaged(qto);
    }

    /** 查询组织科室列表(按院区、专科标志、状态、关键词等) */
    @AutoGenerated(locked = false, uuid = "3bc07113-46ef-4474-9c78-ec0e81ecfa93-query-paginate")
    public List<String> queryPaged(SearchOrganizationDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT organization.id FROM organization LEFT JOIN department \"department\" on"
                    + " organization.id = \"department\".organization_id WHERE organization.type ="
                    + " 'DEPARTMENT' AND \"department\".campus_organization_id ="
                    + " #departmentCampusOrganizationIdIs AND \"department\".specialty_flag ="
                    + " #departmentSpecialtyFlagIs AND organization.status = #statusIs AND"
                    + " \"department\".medical_service_type = #departmentMedicalServiceTypeIs AND"
                    + " \"department\".department_property = #departmentDpdepartmentPropertyIs AND"
                    + " ( organization.name like #nameLike OR organization.alias like #aliasLike OR"
                    + " organization.abbreviation like #abbreviationLike OR"
                    + " JSON_VALUE(organization.input_code, '$.pinyin') like #inputCodePinyinLike"
                    + " OR JSON_VALUE(organization.input_code, '$.wubi') like #inputCodeWubiLike OR"
                    + " JSON_VALUE(organization.input_code, '$.custom') like #inputCodeCustomLike )"
                    + " ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDepartmentCampusOrganizationIdIs() == null) {
            conditionToRemove.add("#departmentCampusOrganizationIdIs");
        }
        if (qto.getDepartmentDpdepartmentPropertyIs() == null) {
            conditionToRemove.add("#departmentDpdepartmentPropertyIs");
        }
        if (qto.getDepartmentMedicalServiceTypeIs() == null) {
            conditionToRemove.add("#departmentMedicalServiceTypeIs");
        }
        if (qto.getDepartmentSpecialtyFlagIs() == null) {
            conditionToRemove.add("#departmentSpecialtyFlagIs");
        }
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getAbbreviationLike() == null) {
            conditionToRemove.add("#abbreviationLike");
        }
        if (qto.getAliasLike() == null) {
            conditionToRemove.add("#aliasLike");
        }
        if (qto.getStatusIs() == null) {
            conditionToRemove.add("#statusIs");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("organization");
        softDeleteTableAlias.add("\"department\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#departmentCampusOrganizationIdIs", "?")
                        .replace("#departmentDpdepartmentPropertyIs", "?")
                        .replace("#departmentMedicalServiceTypeIs", "?")
                        .replace("#departmentSpecialtyFlagIs", "?")
                        .replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#abbreviationLike", "?")
                        .replace("#aliasLike", "?")
                        .replace("#statusIs", "?")
                        .replace("#nameLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#departmentCampusOrganizationIdIs")) {
                sqlParams.add(qto.getDepartmentCampusOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#departmentDpdepartmentPropertyIs")) {
                sqlParams.add(qto.getDepartmentDpdepartmentPropertyIs().toString());
            } else if (paramName.equalsIgnoreCase("#departmentMedicalServiceTypeIs")) {
                sqlParams.add(qto.getDepartmentMedicalServiceTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#departmentSpecialtyFlagIs")) {
                sqlParams.add(qto.getDepartmentSpecialtyFlagIs());
            } else if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#abbreviationLike")) {
                sqlParams.add("%" + qto.getAbbreviationLike() + "%");
            } else if (paramName.equalsIgnoreCase("#aliasLike")) {
                sqlParams.add("%" + qto.getAliasLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIs")) {
                sqlParams.add(qto.getStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql +=
                " order by  organization.organization_level asc , organization.sort_number asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
