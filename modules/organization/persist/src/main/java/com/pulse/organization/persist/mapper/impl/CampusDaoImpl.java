package com.pulse.organization.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.organization.persist.dos.Campus;
import com.pulse.organization.persist.mapper.CampusDao;
import com.pulse.organization.persist.mapper.mybatis.CampusMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "966fdd34-0bc5-456d-8e8a-1e6e7a0295ac|ENTITY|DAO")
public class CampusDaoImpl implements CampusDao {
    @AutoGenerated(locked = true)
    @Resource
    private CampusMapper campusMapper;

    @AutoGenerated(locked = true, uuid = "1661e102-59c1-3677-8488-c0c4470022ff")
    @Override
    public Campus getById(String id) {
        QueryWrapper<Campus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return campusMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "4c4c1598-0aad-3658-8723-043dd21f1d3d")
    @Override
    public Campus getByCampusNumber(String campusNumber) {
        QueryWrapper<Campus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("campus_number", campusNumber).eq("deleted_at", 0L);
        return campusMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b3e434ab-9895-3d23-a75d-8dbcd88a23e0")
    @Override
    public Campus getByOrganizationId(String organizationId) {
        QueryWrapper<Campus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("organization_id", organizationId).eq("deleted_at", 0L);
        return campusMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "bb992e62-cc2e-3464-9943-84654a05f6b6")
    @Override
    public List<Campus> getByOrganizationIds(List<String> organizationId) {
        QueryWrapper<Campus> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("organization_id", organizationId).eq("deleted_at", 0L).orderByAsc("id");
        return campusMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d8037c79-e355-374d-b1cb-00e718bfaa6b")
    @Override
    public List<Campus> getByIds(List<String> id) {
        QueryWrapper<Campus> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return campusMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f36b7abd-a65b-3716-8d54-0afdca6fd87d")
    @Override
    public List<Campus> getByCampusNumbers(List<String> campusNumber) {
        QueryWrapper<Campus> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("campus_number", campusNumber).eq("deleted_at", 0L).orderByAsc("id");
        return campusMapper.selectList(queryWrapper);
    }
}
