package com.pulse.organization.persist.mapper;

import com.pulse.organization.persist.qto.SearchTeamDetailQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "3fc43e1e-bf2e-44d7-ba6e-d593630a349a|QTO|DAO")
public class SearchTeamDetailQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询团队及其团队明细 */
    @AutoGenerated(locked = false, uuid = "3fc43e1e-bf2e-44d7-ba6e-d593630a349a-count")
    public Integer count(SearchTeamDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(team.id) FROM team WHERE team.type = #typeIs AND team.status ="
                    + " #statusIs AND ( JSON_VALUE(team.input_code, '$.pinyin') like"
                    + " #inputCodePinyinLike OR JSON_VALUE(team.input_code, '$.wubi') like"
                    + " #inputCodeWubiLike OR JSON_VALUE(team.input_code, '$.custom') like"
                    + " #inputCodeCustomLike OR team.name like #nameLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getStatusIs() == null) {
            conditionToRemove.add("#statusIs");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        if (qto.getTypeIs() == null) {
            conditionToRemove.add("#typeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add(
                "\"teamCompositionOrganizationWithOrganizationList_organization\"");
        softDeleteTableAlias.add("\"leadOrganization\"");
        softDeleteTableAlias.add("\"teamCompositionOrganizationWithOrganizationList\"");
        softDeleteTableAlias.add("team");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#statusIs", "?")
                        .replace("#nameLike", "?")
                        .replace("#typeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIs")) {
                sqlParams.add(qto.getStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#typeIs")) {
                sqlParams.add(qto.getTypeIs().toString());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询团队及其团队明细 */
    @AutoGenerated(locked = false, uuid = "3fc43e1e-bf2e-44d7-ba6e-d593630a349a-query-all")
    public List<String> query(SearchTeamDetailQto qto) {
        qto.setSize(500);
        qto.setFrom(0);
        return this.queryPaged(qto);
    }

    /** 查询团队及其团队明细 */
    @AutoGenerated(locked = false, uuid = "3fc43e1e-bf2e-44d7-ba6e-d593630a349a-query-paginate")
    public List<String> queryPaged(SearchTeamDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT team.id FROM team WHERE team.type = #typeIs AND team.status = #statusIs AND"
                    + " ( JSON_VALUE(team.input_code, '$.pinyin') like #inputCodePinyinLike OR"
                    + " JSON_VALUE(team.input_code, '$.wubi') like #inputCodeWubiLike OR"
                    + " JSON_VALUE(team.input_code, '$.custom') like #inputCodeCustomLike OR"
                    + " team.name like #nameLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getStatusIs() == null) {
            conditionToRemove.add("#statusIs");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        if (qto.getTypeIs() == null) {
            conditionToRemove.add("#typeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add(
                "\"teamCompositionOrganizationWithOrganizationList_organization\"");
        softDeleteTableAlias.add("\"leadOrganization\"");
        softDeleteTableAlias.add("\"teamCompositionOrganizationWithOrganizationList\"");
        softDeleteTableAlias.add("team");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#statusIs", "?")
                        .replace("#nameLike", "?")
                        .replace("#typeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIs")) {
                sqlParams.add(qto.getStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#typeIs")) {
                sqlParams.add(qto.getTypeIs().toString());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  team.sort_number asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
