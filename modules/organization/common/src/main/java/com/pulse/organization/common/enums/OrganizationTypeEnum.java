package com.pulse.organization.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "2cb83f14-e034-4d3a-ae06-4ff3e094976d|ENUM|DEFINITION")
public enum OrganizationTypeEnum {

    /** 机构 */
    INSTITUTION(),

    /** 院区 */
    CAMPUS(),

    /** 科室 */
    DEPARTMENT(),

    /** 病区 */
    WARD(),

    /** 病区分组 */
    WARD_GROUPING(),

    /** 诊区 */
    CLINIC_AREA();

    @AutoGenerated(locked = true)
    OrganizationTypeEnum() {}
}
