package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.pulse.organization.common.enums.StaffStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "8509b51f-2d9b-4fa1-8c26-d55ecbdb2889|DTO|DEFINITION")
public class StaffDetailDto {
    /** 核算组织 */
    @Valid
    @AutoGenerated(locked = true, uuid = "3858fbcf-87f3-461e-b45b-ed51addd9b77")
    private OrganizationBaseDto accountingOrganization;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "92b85825-809b-49bf-8c3c-af4d04fbf67f")
    private Date birthDate;

    /** 所属院区组织ID */
    @AutoGenerated(locked = true, uuid = "4b16efb4-c1d5-469b-88a4-88a0fbd16da6")
    private String campusOrganizationId;

    /** 证件编号 */
    @AutoGenerated(locked = true, uuid = "f38bfdda-a1df-4364-bdab-2e5e622a09b4")
    private String certificateNumber;

    /** 证件类型 */
    @AutoGenerated(locked = true, uuid = "0a82976b-6a88-41b1-83f1-756adf1f4504")
    private String certificateTypeId;

    /** 下乡结束日期 */
    @AutoGenerated(locked = true, uuid = "0134cd45-e13e-4334-bf08-0933abc5c0a7")
    private Date countrysideEndDate;

    /** 下乡开始日期 */
    @AutoGenerated(locked = true, uuid = "1ce668f4-e0e0-44dd-908d-8e6c6b4c4aa0")
    private Date countrysideStartDate;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "55ee7170-eaab-45d9-9f75-bf7c8504bfe5")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "0ba89fe2-2ed0-49ef-a7ba-bf38d8ffcdcf")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "be004876-e647-4b49-96d7-753c700ee3e9")
    private Long deletedAt;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "1fadf277-66df-4cf7-92d3-f4c27c870426")
    private String deletedBy;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "d83ad23f-f1f3-437c-a400-c0182cd6bdb9")
    private String description;

    /** 医生备注 */
    @AutoGenerated(locked = true, uuid = "8272ac24-27a6-4798-bec2-62c7fe283340")
    private String doctorRemark;

    /** 电子邮件地址 */
    @AutoGenerated(locked = true, uuid = "0c589d1c-4478-463d-ab40-ffcdbe32f0cf")
    private String emailAddress;

    /** 专家标志 */
    @AutoGenerated(locked = true, uuid = "75648c05-5fed-4400-81a5-ca439fa67726")
    private Boolean expertFlag;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "323e7802-8925-4fe8-9926-5e48b987aaee")
    private GenderEnum gender;

    /** 人事组织 */
    @Valid
    @AutoGenerated(locked = true, uuid = "6eda8e69-5bf1-4252-a353-86f891f64924")
    private OrganizationBaseDto hrOrganization;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "8acaf32c-561e-406b-a309-84e7b15cab84")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "af048932-a974-4364-9692-aa8837bfd9bf")
    private InputCodeEo inputCode;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "971bab03-e1da-42a8-ae5a-c07ac097cbff")
    private String name;

    /** 所属组织 */
    @Valid
    @AutoGenerated(locked = true, uuid = "bd57dade-0fd1-4142-8a7e-b28e8f8e37f2")
    private OrganizationBaseDto organization;

    /** 电话号码 */
    @AutoGenerated(locked = true, uuid = "12bd5323-9ad3-4a1b-bf22-2287c5e4146a")
    private String phoneNumber;

    /** 职务id */
    @AutoGenerated(locked = true, uuid = "52e01d7c-6751-40e1-a8a7-f6f16ea2c424")
    private String positionId;

    /** 职称 */
    @AutoGenerated(locked = true, uuid = "5b912c1c-317d-471b-b1df-46468cd8fe35")
    private String professionalTitleId;

    /** 晋升日期 */
    @AutoGenerated(locked = true, uuid = "1613918e-917c-4550-86cc-948c5e478906")
    private Date promotionDate;

    /** 省平台作废标识 */
    @AutoGenerated(locked = true, uuid = "8d5c6677-911b-4168-8539-e743b6c94a71")
    private Boolean provincePlatformCancelFlag;

    /** 挂号医生启用标志 */
    @AutoGenerated(locked = true, uuid = "a9ac16b0-8181-4851-bbc2-57f12279f780")
    private Boolean registerDoctorEnableFlag;

    /** 挂号医生标志 */
    @AutoGenerated(locked = true, uuid = "64d01c22-d546-4374-9d4b-159a34c83737")
    private Boolean registerDoctorFlag;

    /** 挂号类别ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b70243cf-eda3-4667-9a73-60324e05d816")
    private List<String> registerTypeList;

    /** 手机短号 */
    @AutoGenerated(locked = true, uuid = "c5144a1b-e17a-4841-b6a2-04e5b0c2f59d")
    private String shortPhoneNumber;

    /** 序号 */
    @AutoGenerated(locked = true, uuid = "6d1d1b5e-42e8-4009-a485-922d011643c2")
    private Long sortNumber;

    /** 教育信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a5e289ec-034b-4e9f-bb61-437a0d95e726")
    private List<StaffEducationBaseDto> staffEducationList;

    /** 聘用信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "da166827-9218-43fb-8c3a-8f70d7ba3217")
    private List<StaffEmploymentBaseDto> staffEmploymentList;

    /** 扩展信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b5d4a536-1e45-484e-85c6-dda8882cabfb")
    private StaffExtensionBaseDto staffExtension;

    /** 员工登录应用组织 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a160d232-453f-42ad-9b98-013281932514")
    private List<StaffOrganizationDto> staffLoginOrganizationDetailList;

    /** 员工编号 */
    @AutoGenerated(locked = true, uuid = "18fa6c27-434b-4141-87d5-704b6e95227a")
    private String staffNumber;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7a4c6785-1024-4fe8-8ce3-c4c7ca143420")
    private List<StaffPracticeBaseDto> staffPracticeList;

    /** 职工类别 */
    @AutoGenerated(locked = true, uuid = "fd40b451-c4b7-4f83-b452-c38c4d3840c0")
    private String staffTypeId;

    /** 员工用户对应 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f96ea835-c8e3-4268-85fc-c4a20bb07dda")
    private List<StaffUserDto> staffUserList;

    /** 工作信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b7c0c8a1-897d-4a00-af92-bdc48d33cfeb")
    private List<StaffWorkExperienceBaseDto> staffWorkExperienceList;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "ec3f77df-8eb3-480f-9996-de2ce7f2b639")
    private StaffStatusEnum status;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "6e6de1f2-cd34-4e81-86bb-aaa7ddf97bad")
    private String updateBy;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b94b678c-eba9-408c-8653-33364e8967ac")
    private Date updatedAt;
}
