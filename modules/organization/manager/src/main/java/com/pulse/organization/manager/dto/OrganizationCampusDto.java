package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "095ef1e0-8810-4698-b705-c1e3c2a18e85|DTO|DEFINITION")
public class OrganizationCampusDto {
    /** 简称 */
    @AutoGenerated(locked = true, uuid = "7046df77-3097-4ccc-b1a4-43ad5cd2d5d2")
    private String abbreviation;

    /** 组织地址 */
    @AutoGenerated(locked = true, uuid = "c9a527cc-4b07-4514-bc0a-c5936769a6f3")
    private String address;

    /** 别名 */
    @AutoGenerated(locked = true, uuid = "bb7688bf-69fb-4bd5-ae9f-ea517a9ee397")
    private String alias;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "bee4ef79-0cd5-4d1a-8814-55bf39ce2cef")
    private CampusBaseDto campus;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "5c341d7b-3a28-49ba-8147-c24bd654cb38")
    private String contactNumber;

    /** 联系人 */
    @AutoGenerated(locked = true, uuid = "460c281e-ce62-454c-83cb-8dd5949dbe9d")
    private String contactPerson;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "f5e185ce-8cdb-411e-9b29-7dfd573375b3")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "0f67ff64-468c-4dab-bd19-ff62e4dddb42")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "bd7c4712-d505-4009-986b-e2874c3638af")
    private Long deletedAt;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "d8368256-019e-45d4-95ce-e60de975309c")
    private String deletedBy;

    /** 组织描述 */
    @AutoGenerated(locked = true, uuid = "707f7ee7-a251-4a99-a2ab-1462b225b899")
    private String description;

    /** 英文名 */
    @AutoGenerated(locked = true, uuid = "af3e92f8-322e-4b89-a825-35be4aa9906d")
    private String englishName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "8def07f7-6480-46b5-a598-fc97cb27db66")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "764baf60-296e-4453-9863-b8fc1e059924")
    private InputCodeEo inputCode;

    /** 作废标记 */
    @AutoGenerated(locked = true, uuid = "011c74bf-8bec-47d4-8430-8b28379dd38b")
    private Boolean invalidFlag;

    /** 组织名称 */
    @AutoGenerated(locked = true, uuid = "0cc28b6f-9651-4dd6-941f-1b3dfdc81eab")
    private String name;

    /** 组织层级 */
    @AutoGenerated(locked = true, uuid = "0458d5de-2766-48ec-8d9b-953de0a8ba7b")
    private Long organizationLevel;

    /** 上级组织ID */
    @AutoGenerated(locked = true, uuid = "f3a16385-4fbf-49c3-81e3-72f53e057e83")
    private String parentId;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "f2149aa8-cd29-431a-8686-011e2081a580")
    private Long sortNumber;

    /** 组织状态 */
    @AutoGenerated(locked = true, uuid = "b7fde70f-e55f-4b5e-858e-8aa8236907f5")
    private OrganizationStatusEnum status;

    /** 组织类型 */
    @AutoGenerated(locked = true, uuid = "b7f19f62-1eea-49bf-b881-ade0b7580c9c")
    private OrganizationTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "bab35c05-00e8-45aa-9fc3-195d3ef7614a")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "e552981e-62be-4997-bf6c-3b9a905f947c")
    private String updatedBy;
}
