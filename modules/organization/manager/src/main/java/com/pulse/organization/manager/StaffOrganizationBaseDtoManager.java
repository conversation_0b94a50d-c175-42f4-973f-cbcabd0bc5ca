package com.pulse.organization.manager;

import com.pulse.organization.manager.dto.StaffOrganizationBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "1ea19df9-f0f1-4871-ae21-a8c04a8dc17c|DTO|MANAGER")
public interface StaffOrganizationBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "1be440ff-0e53-348c-b166-922aafd4ec99")
    List<StaffOrganizationBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "212df5dc-1b68-3734-8d20-a2844a413430")
    List<StaffOrganizationBaseDto> getByOrganizationIds(List<String> organizationId);

    @AutoGenerated(locked = true, uuid = "23742029-e36e-3b35-833c-b55875e76f86")
    StaffOrganizationBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "3274c367-2fe5-36d9-b12a-f9fa4d173162")
    List<StaffOrganizationBaseDto> getByStaffId(String staffId);

    @AutoGenerated(locked = true, uuid = "4cd8ec44-41fe-3152-9694-74160d0ebd53")
    List<StaffOrganizationBaseDto> getByApplicationIds(List<String> applicationId);

    @AutoGenerated(locked = true, uuid = "8974e862-f0bf-301f-a944-f6571d1e9994")
    List<StaffOrganizationBaseDto> getByStaffIds(List<String> staffId);

    @AutoGenerated(locked = true, uuid = "96308389-d81c-397a-9ca0-259ae0fe8008")
    List<StaffOrganizationBaseDto> getByOrganizationId(String organizationId);

    @AutoGenerated(locked = true, uuid = "af312683-c965-3dcc-bf86-d4777c4cec83")
    List<StaffOrganizationBaseDto> getByApplicationId(String applicationId);
}
