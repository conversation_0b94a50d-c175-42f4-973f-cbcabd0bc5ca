package com.pulse.organization.manager;

import com.pulse.organization.manager.dto.StaffEmploymentBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "e12688cd-5c2b-45e5-97a7-9883e1c84123|DTO|MANAGER")
public interface StaffEmploymentBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "005e08f4-74ac-3e8f-82d4-965833890d44")
    List<StaffEmploymentBaseDto> getByStaffId(String staffId);

    @AutoGenerated(locked = true, uuid = "62f7e98b-5b42-37a4-932c-526694267da1")
    List<StaffEmploymentBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "ca597d0a-1fa0-35a2-a760-b5aaeb6514b9")
    StaffEmploymentBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "f426909f-c15a-3fb8-b6bf-190bf6eb2409")
    List<StaffEmploymentBaseDto> getByStaffIds(List<String> staffId);
}
