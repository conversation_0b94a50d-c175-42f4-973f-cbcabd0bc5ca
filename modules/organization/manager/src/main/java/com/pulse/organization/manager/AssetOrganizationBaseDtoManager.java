package com.pulse.organization.manager;

import com.pulse.organization.manager.dto.AssetOrganizationBaseDto;
import com.pulse.organization.persist.dos.AssetOrganization;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "2152819c-1350-4651-b9f5-8e13f071700e|DTO|MANAGER")
public interface AssetOrganizationBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "52d2bed1-681f-3fa8-958d-4912d2c34c39")
    List<AssetOrganizationBaseDto> getByOrganizationIds(List<String> organizationId);

    @AutoGenerated(locked = true, uuid = "588416fb-54f5-373a-84d8-0939d663aabb")
    List<AssetOrganizationBaseDto> getByOrganizationId(String organizationId);

    @AutoGenerated(locked = true, uuid = "7361b1ff-4aac-3c67-b855-098932aba93b")
    List<AssetOrganizationBaseDto> getByAssetId(String assetId);

    @AutoGenerated(locked = true, uuid = "7a12a9eb-9312-3571-a765-22bb8bf39962")
    List<AssetOrganizationBaseDto> getByAssetIdAndOrganizationIdAndApplicationId(
            AssetOrganization.ApplicationIdAndAssetIdAndOrganizationId var);

    @AutoGenerated(locked = true, uuid = "80d07e03-7cfa-35f0-9c03-4019ce2f85db")
    List<AssetOrganizationBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "a2928f4c-805a-34b4-b29a-f8523b5683d8")
    List<AssetOrganizationBaseDto> getByApplicationId(String applicationId);

    @AutoGenerated(locked = true, uuid = "b2d14224-6341-3c7f-9d1d-fb5d34b5aa23")
    List<AssetOrganizationBaseDto> getByAssetIds(List<String> assetId);

    @AutoGenerated(locked = true, uuid = "d2cf3329-ef1e-3591-a1a1-74fc17cb96eb")
    AssetOrganizationBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "df9ce7e4-ee85-34af-a6ab-1b7edbd9706c")
    List<AssetOrganizationBaseDto> getByApplicationIds(List<String> applicationId);

    @AutoGenerated(locked = true, uuid = "e57a67e9-d7d3-3eba-9ea3-c28df3934bb5")
    List<AssetOrganizationBaseDto> getByAssetIdsAndOrganizationIdsAndApplicationIds(
            List<AssetOrganization.ApplicationIdAndAssetIdAndOrganizationId> var);
}
