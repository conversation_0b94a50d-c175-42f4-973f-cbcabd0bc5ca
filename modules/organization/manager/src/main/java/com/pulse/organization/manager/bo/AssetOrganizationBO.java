package com.pulse.organization.manager.bo;

import com.pulse.organization.persist.dos.AssetOrganization;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE asset_organization  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ?")
@Getter
@Setter
@Table(name = "asset_organization")
@Entity
@AutoGenerated(locked = true, uuid = "ec979f46-9b1c-4e5f-a36d-9cae673b8fd1|BO|DEFINITION")
public class AssetOrganizationBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 应用ID */
    @Column(name = "application_id")
    @AutoGenerated(locked = true, uuid = "5e3fa914-c54b-47b2-b2d1-8ba752bd2752")
    private String applicationId;

    @ManyToOne
    @JoinColumn(name = "asset_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private AssetBO assetBO;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "6d62c5ab-bbba-57e3-9c09-d3f529d64915")
    private Date createdAt;

    /** 创建者ID */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "a7b1a7db-ebbf-4579-81a9-b425efb1f27a")
    private String createdBy;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "d26a08d6-1849-5d76-869b-9744aa4c6f42")
    private Long deletedAt = 0L;

    /** 删除者ID */
    @Column(name = "deleted_by")
    @AutoGenerated(locked = true, uuid = "b7a74c43-0384-4a4e-b503-1107dbfc3be4")
    private String deletedBy;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "ce39f44b-5760-4e89-818a-af7d84257f77")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 组织ID */
    @Column(name = "organization_id")
    @AutoGenerated(locked = true, uuid = "2b5f9e8d-76b9-4d15-85e3-9d3c9904f746")
    private String organizationId;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "8e49e49a-93ba-43d3-ae8c-e878e52efbee|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public AssetOrganization convertToAssetOrganization() {
        AssetOrganization entity = new AssetOrganization();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "organizationId",
                "applicationId",
                "createdBy",
                "deletedBy",
                "createdAt",
                "deletedAt");
        AssetBO assetBO = this.getAssetBO();
        entity.setAssetId(assetBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getApplicationId() {
        return this.applicationId;
    }

    @AutoGenerated(locked = true)
    public AssetBO getAssetBO() {
        return this.assetBO;
    }

    @AutoGenerated(locked = true)
    public String getAssetId() {
        return this.getAssetBO().getId();
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDeletedBy() {
        return this.deletedBy;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getOrganizationId() {
        return this.organizationId;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setApplicationId(String applicationId) {
        this.applicationId = applicationId;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setAssetBO(AssetBO assetBO) {
        this.assetBO = assetBO;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setDeletedBy(String deletedBy) {
        this.deletedBy = deletedBy;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setId(String id) {
        this.id = id;
        return (AssetOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public AssetOrganizationBO setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
        return (AssetOrganizationBO) this;
    }
}
