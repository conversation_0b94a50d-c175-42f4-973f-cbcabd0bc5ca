package com.pulse.organization.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.OrganizationBaseDtoManager;
import com.pulse.organization.manager.TeamOrganizationBaseDtoManager;
import com.pulse.organization.manager.TeamOrganizationDtoManager;
import com.pulse.organization.manager.converter.TeamOrganizationBaseDtoConverter;
import com.pulse.organization.manager.converter.TeamOrganizationDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.persist.dos.TeamOrganization;
import com.pulse.organization.persist.mapper.TeamOrganizationDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "2c68ecb9-24e6-49af-9ffb-7174879beeed|DTO|BASE_MANAGER_IMPL")
public abstract class TeamOrganizationDtoManagerBaseImpl implements TeamOrganizationDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private OrganizationBaseDtoManager organizationBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamOrganizationBaseDtoConverter teamOrganizationBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamOrganizationBaseDtoManager teamOrganizationBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamOrganizationDao teamOrganizationDao;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamOrganizationDtoConverter teamOrganizationDtoConverter;

    @AutoGenerated(locked = true, uuid = "3b416943-86e0-3a8d-a44a-56fdd257f00c")
    @Override
    public List<TeamOrganizationDto> getByOrganizationId(String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamOrganizationDto> teamOrganizationDtoList =
                getByOrganizationIds(Arrays.asList(organizationId));
        return teamOrganizationDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "500c4556-01b8-3bf9-94fa-f2106ab4e1c0")
    @Override
    public List<TeamOrganizationDto> getByOrganizationIds(List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(organizationId)) {
            return Collections.emptyList();
        }

        List<TeamOrganization> teamOrganizationList =
                teamOrganizationDao.getByOrganizationIds(organizationId);
        if (CollectionUtil.isEmpty(teamOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromTeamOrganizationToTeamOrganizationDto(teamOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "97675b79-c482-393d-b058-17a1c5c1c6a0")
    @Override
    public List<TeamOrganizationDto> getByTeamId(String teamId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamOrganizationDto> teamOrganizationDtoList = getByTeamIds(Arrays.asList(teamId));
        return teamOrganizationDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bf6cfdd2-fe71-3ea7-aac1-28d0dffe9e99")
    @Override
    public List<TeamOrganizationDto> getByTeamIds(List<String> teamId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(teamId)) {
            return Collections.emptyList();
        }

        List<TeamOrganization> teamOrganizationList = teamOrganizationDao.getByTeamIds(teamId);
        if (CollectionUtil.isEmpty(teamOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromTeamOrganizationToTeamOrganizationDto(teamOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "dfa62d35-2a84-3922-9f07-2dd8c3eacff4")
    @Override
    public TeamOrganizationDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamOrganizationDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        TeamOrganizationDto teamOrganizationDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return teamOrganizationDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f161ebb4-d8b3-3fe4-8ad9-2ae870b6abc6")
    public List<TeamOrganizationDto> doConvertFromTeamOrganizationToTeamOrganizationDto(
            List<TeamOrganization> teamOrganizationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(teamOrganizationList)) {
            return Collections.emptyList();
        }

        Map<String, String> organizationIdMap =
                teamOrganizationList.stream()
                        .filter(i -> i.getOrganizationId() != null)
                        .collect(
                                Collectors.toMap(
                                        TeamOrganization::getId,
                                        TeamOrganization::getOrganizationId));
        List<OrganizationBaseDto> organizationIdOrganizationBaseDtoList =
                organizationBaseDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(organizationIdMap.values())));
        Map<String, OrganizationBaseDto> organizationIdOrganizationBaseDtoMapRaw =
                organizationIdOrganizationBaseDtoList.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, i -> i));
        Map<String, OrganizationBaseDto> organizationIdOrganizationBaseDtoMap =
                organizationIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        organizationIdOrganizationBaseDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                organizationIdOrganizationBaseDtoMapRaw.get(
                                                        i.getValue())));

        List<TeamOrganizationBaseDto> baseDtoList =
                teamOrganizationBaseDtoConverter
                        .convertFromTeamOrganizationToTeamOrganizationBaseDto(teamOrganizationList);
        Map<String, TeamOrganizationDto> dtoMap =
                teamOrganizationDtoConverter
                        .convertFromTeamOrganizationBaseDtoToTeamOrganizationDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        TeamOrganizationDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<TeamOrganizationDto> teamOrganizationDtoList = new ArrayList<>();
        for (TeamOrganization i : teamOrganizationList) {
            TeamOrganizationDto teamOrganizationDto = dtoMap.get(i.getId());
            if (teamOrganizationDto == null) {
                continue;
            }

            if (null != i.getOrganizationId()) {
                teamOrganizationDto.setOrganization(
                        organizationIdOrganizationBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            teamOrganizationDtoList.add(teamOrganizationDto);
        }
        return teamOrganizationDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "f386d231-4068-3dbe-a1e0-0960f599cf7a")
    @Override
    public List<TeamOrganizationDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<TeamOrganization> teamOrganizationList = teamOrganizationDao.getByIds(id);
        if (CollectionUtil.isEmpty(teamOrganizationList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, TeamOrganization> teamOrganizationMap =
                teamOrganizationList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        teamOrganizationList =
                id.stream()
                        .map(i -> teamOrganizationMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromTeamOrganizationToTeamOrganizationDto(teamOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
