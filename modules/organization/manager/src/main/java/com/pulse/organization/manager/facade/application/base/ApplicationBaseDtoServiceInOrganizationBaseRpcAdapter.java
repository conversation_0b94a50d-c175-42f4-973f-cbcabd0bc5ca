package com.pulse.organization.manager.facade.application.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "33357e00-bbea-3587-8c75-041455a83c5b")
public class ApplicationBaseDtoServiceInOrganizationBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "451a9e57-de85-4ee7-89b9-5e5ba5fa6fb2|RPC|BASE_ADAPTER")
    public ApplicationBaseDto getById(String id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/application/451a9e57-de85-4ee7-89b9-5e5ba5fa6fb2/ApplicationBaseDtoService-getById",
                        "com.pulse.application.service.ApplicationBaseDtoService",
                        "getById",
                        paramMap,
                        paramTypeMap,
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c",
                        "2490b347-7947-4b60-afc4-0054306a277c"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "babb9774-8e31-45d9-aa4b-9f535dcb4877|RPC|BASE_ADAPTER")
    public List<ApplicationBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/application/babb9774-8e31-45d9-aa4b-9f535dcb4877/ApplicationBaseDtoService-getByIds",
                        "com.pulse.application.service.ApplicationBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c",
                        "2490b347-7947-4b60-afc4-0054306a277c"),
                new TypeReference<>() {});
    }
}
