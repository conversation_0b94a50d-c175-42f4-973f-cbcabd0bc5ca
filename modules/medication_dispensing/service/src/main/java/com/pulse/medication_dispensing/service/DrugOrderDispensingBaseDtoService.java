package com.pulse.medication_dispensing.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.DrugOrderDispensingBaseDtoManager;
import com.pulse.medication_dispensing.manager.dto.DrugOrderDispensingBaseDto;
import com.pulse.medication_dispensing.service.converter.DrugOrderDispensingBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "41bcf3fd-3c5d-4ce7-aa93-8fde7e2639f7|DTO|SERVICE")
public class DrugOrderDispensingBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugOrderDispensingBaseDtoManager drugOrderDispensingBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOrderDispensingBaseDtoServiceConverter drugOrderDispensingBaseDtoServiceConverter;

    @PublicInterface(
            id = "6206a29a-d1db-4331-9dea-0e066430f497",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577094242")
    @AutoGenerated(locked = false, uuid = "5999aa71-ebab-33d2-b754-d62a1e33d1a7")
    public List<DrugOrderDispensingBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugOrderDispensingBaseDto> drugOrderDispensingBaseDtoList =
                drugOrderDispensingBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispensingBaseDtoServiceConverter.DrugOrderDispensingBaseDtoConverter(
                drugOrderDispensingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "575a873d-4141-40e3-b501-c9f462279c12",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577094238")
    @AutoGenerated(locked = false, uuid = "66c93197-803c-329d-ab73-4e2a5a6f220f")
    public DrugOrderDispensingBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOrderDispensingBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "24391b18-848b-46fb-b6f9-ee382296407f",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577094247")
    @AutoGenerated(locked = false, uuid = "6835c5b8-5410-3bcb-9562-5f291c9c7fc9")
    public List<DrugOrderDispensingBaseDto> getByDrugProductSpecificationId(
            @NotNull(message = "商品规格id不能为空") String drugProductSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugProductSpecificationIds(Arrays.asList(drugProductSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "e1d0261d-1b50-45aa-9d51-538823020f82",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577094251")
    @AutoGenerated(locked = false, uuid = "74298af0-3c59-3cb9-b044-a05430fafd03")
    public List<DrugOrderDispensingBaseDto> getByDrugProductSpecificationIds(
            @Valid @NotNull(message = "商品规格id不能为空") List<String> drugProductSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugProductSpecificationId = new ArrayList<>(new HashSet<>(drugProductSpecificationId));
        List<DrugOrderDispensingBaseDto> drugOrderDispensingBaseDtoList =
                drugOrderDispensingBaseDtoManager.getByDrugProductSpecificationIds(
                        drugProductSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispensingBaseDtoServiceConverter.DrugOrderDispensingBaseDtoConverter(
                drugOrderDispensingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "2e182f10-a758-4309-8582-8804aa885656",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577094229")
    @AutoGenerated(locked = false, uuid = "8bb4d71c-e707-3e46-ad5c-26e8f4766bae")
    public List<DrugOrderDispensingBaseDto> getByDispenseStorageCode(
            @NotNull(message = "摆药药房编码不能为空") String dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDispenseStorageCodes(Arrays.asList(dispenseStorageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "07342994-7298-426e-b892-123d53e2692f",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577094233")
    @AutoGenerated(locked = false, uuid = "dbf951ca-edd8-3024-8bb6-e110cfbd801a")
    public List<DrugOrderDispensingBaseDto> getByDispenseStorageCodes(
            @Valid @NotNull(message = "摆药药房编码不能为空") List<String> dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        dispenseStorageCode = new ArrayList<>(new HashSet<>(dispenseStorageCode));
        List<DrugOrderDispensingBaseDto> drugOrderDispensingBaseDtoList =
                drugOrderDispensingBaseDtoManager.getByDispenseStorageCodes(dispenseStorageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispensingBaseDtoServiceConverter.DrugOrderDispensingBaseDtoConverter(
                drugOrderDispensingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
