package com.pulse.medication_dispensing.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.DrugOrderDispenseBaseDtoManager;
import com.pulse.medication_dispensing.manager.dto.DrugOrderDispenseBaseDto;
import com.pulse.medication_dispensing.service.converter.DrugOrderDispenseBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "b82a7662-6764-419d-8d15-d0daffe17ee1|DTO|SERVICE")
public class DrugOrderDispenseBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugOrderDispenseBaseDtoManager drugOrderDispenseBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOrderDispenseBaseDtoServiceConverter drugOrderDispenseBaseDtoServiceConverter;

    @PublicInterface(
            id = "a398c1d4-b55b-42e5-a066-793297689ac8",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119938")
    @AutoGenerated(locked = false, uuid = "06dea60d-2291-3a4f-a2bf-ab45ace2f0f2")
    public List<DrugOrderDispenseBaseDto> getByOperatorIds(
            @Valid @NotNull(message = "摆药操作员id不能为空") List<String> operatorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        operatorId = new ArrayList<>(new HashSet<>(operatorId));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getByOperatorIds(operatorId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "66e89c0f-ef16-404a-ad0d-87615309d1de",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119929")
    @AutoGenerated(locked = false, uuid = "3fb8287c-e856-3745-acdc-dc2f240735fa")
    public List<DrugOrderDispenseBaseDto> getByDeliveryIds(
            @Valid @NotNull(message = "配送id不能为空") List<String> deliveryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        deliveryId = new ArrayList<>(new HashSet<>(deliveryId));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getByDeliveryIds(deliveryId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "c98bcd5d-de2a-42a8-82c9-339aa56fecba",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119946")
    @AutoGenerated(locked = false, uuid = "51693f13-c054-3554-b512-bdbee1be8557")
    public List<DrugOrderDispenseBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "7294a4ee-4025-4f82-931c-e4b6e37314f0",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119940")
    @AutoGenerated(locked = false, uuid = "53654dd6-ddf6-37eb-af83-0622695f248a")
    public List<DrugOrderDispenseBaseDto> getByAuditStaffId(
            @NotNull(message = "核对人不能为空") String auditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAuditStaffIds(Arrays.asList(auditStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "a6b954f5-5846-4847-867c-328317a9b8d1",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119934")
    @AutoGenerated(locked = false, uuid = "694152fc-809b-361d-ac7f-f9304ef161a9")
    public List<DrugOrderDispenseBaseDto> getByPrepareStaffIds(
            @Valid @NotNull(message = "配药人不能为空") List<String> prepareStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        prepareStaffId = new ArrayList<>(new HashSet<>(prepareStaffId));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getByPrepareStaffIds(prepareStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "edc7b000-ec8d-48a9-9ec3-77813f7a8424",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119944")
    @AutoGenerated(locked = false, uuid = "7c75148d-e99b-321c-a1bf-cb381f71c559")
    public DrugOrderDispenseBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOrderDispenseBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "41ef5d13-5ea6-4d8c-9c61-bf019f52b2c8",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119956")
    @AutoGenerated(locked = false, uuid = "8d0f3c81-435d-37d9-83e4-429c5fca7fce")
    public List<DrugOrderDispenseBaseDto> getBySecondAuditStaffIds(
            @Valid @NotNull(message = "第二核对人不能为空") List<String> secondAuditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        secondAuditStaffId = new ArrayList<>(new HashSet<>(secondAuditStaffId));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getBySecondAuditStaffIds(secondAuditStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "b39f26df-b515-4c59-8ac7-105245da12ef",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119936")
    @AutoGenerated(locked = false, uuid = "959c33b7-8d0e-3308-82e0-5dc14925cf80")
    public List<DrugOrderDispenseBaseDto> getByOperatorId(
            @NotNull(message = "摆药操作员id不能为空") String operatorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOperatorIds(Arrays.asList(operatorId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "f38c268c-b5c8-44db-a15d-7cc2e3230db9",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119950")
    @AutoGenerated(locked = false, uuid = "9d461ec1-c6b7-3ac1-8be0-ca6a8bfce94d")
    public List<DrugOrderDispenseBaseDto> getByDispenseStorageCodes(
            @Valid @NotNull(message = "药房编码不能为空") List<String> dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        dispenseStorageCode = new ArrayList<>(new HashSet<>(dispenseStorageCode));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getByDispenseStorageCodes(dispenseStorageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "e11d23c5-5bdb-4f80-a921-933997c93659",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119948")
    @AutoGenerated(locked = false, uuid = "d6d1bb1e-c2d3-32c7-958d-0f8e441d7ddb")
    public List<DrugOrderDispenseBaseDto> getByDispenseStorageCode(
            @NotNull(message = "药房编码不能为空") String dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDispenseStorageCodes(Arrays.asList(dispenseStorageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "cfb7cc82-af36-47d3-85f2-6133540d1fbd",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119931")
    @AutoGenerated(locked = false, uuid = "e7c72177-2f96-31ed-879d-a1acb2732086")
    public List<DrugOrderDispenseBaseDto> getByPrepareStaffId(
            @NotNull(message = "配药人不能为空") String prepareStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPrepareStaffIds(Arrays.asList(prepareStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "76f84283-b8b7-41e1-a6a4-bb211ae41c0e",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119942")
    @AutoGenerated(locked = false, uuid = "f2795af1-3ccd-35f0-9c89-1f14f2200ecc")
    public List<DrugOrderDispenseBaseDto> getByAuditStaffIds(
            @Valid @NotNull(message = "核对人不能为空") List<String> auditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        auditStaffId = new ArrayList<>(new HashSet<>(auditStaffId));
        List<DrugOrderDispenseBaseDto> drugOrderDispenseBaseDtoList =
                drugOrderDispenseBaseDtoManager.getByAuditStaffIds(auditStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugOrderDispenseBaseDtoServiceConverter.DrugOrderDispenseBaseDtoConverter(
                drugOrderDispenseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "a24b8ce8-0c1c-4a86-8cb9-c6e3d12a7d34",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119927")
    @AutoGenerated(locked = false, uuid = "f60ffc1e-6eea-32c3-897b-aa3577f9cc74")
    public List<DrugOrderDispenseBaseDto> getByDeliveryId(
            @NotNull(message = "配送id不能为空") String deliveryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDeliveryIds(Arrays.asList(deliveryId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "f8f90e1f-9207-43f4-ac90-b0da1c547236",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743577119954")
    @AutoGenerated(locked = false, uuid = "ff55e185-4bee-310c-bd0c-9ebd09431f1d")
    public List<DrugOrderDispenseBaseDto> getBySecondAuditStaffId(
            @NotNull(message = "第二核对人不能为空") String secondAuditStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySecondAuditStaffIds(Arrays.asList(secondAuditStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
