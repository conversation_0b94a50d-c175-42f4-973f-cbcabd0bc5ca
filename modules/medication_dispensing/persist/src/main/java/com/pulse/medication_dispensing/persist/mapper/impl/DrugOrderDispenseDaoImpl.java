package com.pulse.medication_dispensing.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.medication_dispensing.persist.dos.DrugOrderDispense;
import com.pulse.medication_dispensing.persist.mapper.DrugOrderDispenseDao;
import com.pulse.medication_dispensing.persist.mapper.mybatis.DrugOrderDispenseMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "215b4362-d623-3bb7-9e9e-734ddc0bb2c1|ENTITY|DAO")
public class DrugOrderDispenseDaoImpl implements DrugOrderDispenseDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOrderDispenseMapper drugOrderDispenseMapper;

    @AutoGenerated(locked = true, uuid = "1083973b-4e24-3271-8a0f-ea3f1e4254fe")
    @Override
    public List<DrugOrderDispense> getBySecondAuditStaffIds(List<String> secondAuditStaffId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("second_audit_staff_id", secondAuditStaffId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "1b16aa1d-c6a1-38a5-baea-9c9faed8f2b1")
    @Override
    public List<DrugOrderDispense> getBySecondAuditStaffId(String secondAuditStaffId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("second_audit_staff_id", secondAuditStaffId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "2227308b-a0dc-3130-99c2-7e3d528a5fde")
    @Override
    public List<DrugOrderDispense> getByPrepareStaffIds(List<String> prepareStaffId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("prepare_staff_id", prepareStaffId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "22659b20-2cb0-397d-9869-970fd9e989b9")
    @Override
    public List<DrugOrderDispense> getByAuditStaffId(String auditStaffId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("audit_staff_id", auditStaffId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "25234dfc-a7b4-3848-addf-8e44c7270177")
    @Override
    public List<DrugOrderDispense> getByOperatorIds(List<String> operatorId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("operator_id", operatorId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "3d42b878-7c30-3f6f-926e-0159a44e1852")
    @Override
    public List<DrugOrderDispense> getByPrepareStaffId(String prepareStaffId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prepare_staff_id", prepareStaffId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "868bd2dd-caf9-3721-8660-a0e563c0a36d")
    @Override
    public List<DrugOrderDispense> getByOperatorId(String operatorId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operator_id", operatorId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "8ae40ff2-0cfe-3000-bfd1-73a37f956b23")
    @Override
    public List<DrugOrderDispense> getByAuditStaffIds(List<String> auditStaffId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("audit_staff_id", auditStaffId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "96f940e9-30ed-38a7-9766-2d56bf63b309")
    @Override
    public List<DrugOrderDispense> getByIds(List<String> id) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9b079a38-eddf-3eee-8163-65f2fa2578da")
    @Override
    public DrugOrderDispense getById(String id) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugOrderDispenseMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a54688c0-6d88-38a4-8d97-a0383d66df91")
    @Override
    public List<DrugOrderDispense> getByDispenseStorageCode(String dispenseStorageCode) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dispense_storage_code", dispenseStorageCode).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b2fa0029-ac8a-3513-8770-5449e8ecf40b")
    @Override
    public List<DrugOrderDispense> getByDispenseStorageCodes(List<String> dispenseStorageCode) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("dispense_storage_code", dispenseStorageCode).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "cbaff7a6-41c1-338c-a4d8-b2c7ddd9a6a5")
    @Override
    public List<DrugOrderDispense> getByDeliveryIds(List<String> deliveryId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("delivery_id", deliveryId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "df89acb0-d6e8-3a1c-9272-db9f9c657d49")
    @Override
    public List<DrugOrderDispense> getByDeliveryId(String deliveryId) {
        QueryWrapper<DrugOrderDispense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delivery_id", deliveryId).orderByAsc("id");
        return drugOrderDispenseMapper.selectList(queryWrapper);
    }
}
