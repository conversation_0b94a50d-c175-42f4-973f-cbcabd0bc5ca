package com.pulse.medication_dispensing.manager.dto;

import com.pulse.medication_dispensing.common.enums.RoundingTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "5e8c2dd8-4e56-44f0-b64e-6bf1d4b55e65|DTO|DEFINITION")
public class DrugFormVsDispenseRoundingBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "05734734-f985-4455-8243-8f52fc919006")
    private Date createdAt;

    /** 剂型编码 */
    @AutoGenerated(locked = true, uuid = "51608acb-9420-43fc-a865-7c7065bea6de")
    private String formCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "562746cb-4b45-4382-9598-f118b91ac3e7")
    private String id;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "0fb43e84-6d37-43fb-8431-c3fc12fb4d60")
    private Long lockVersion;

    /** 住院红区取整方式 */
    @AutoGenerated(locked = true, uuid = "e2790f18-822a-4f4d-879f-3dcd1db9bbe7")
    private RoundingTypeEnum roundingTypeInp;

    /** 门诊绿区取整方式 按次、按天、按量 */
    @AutoGenerated(locked = true, uuid = "7f8ae73e-d617-48a6-b2e6-dd74a53a4d02")
    private RoundingTypeEnum roundingTypeOutp;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "7f12c47c-9498-411e-b539-01c747badf7b")
    private Date updatedAt;
}
