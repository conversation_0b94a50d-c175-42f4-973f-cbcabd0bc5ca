package com.pulse.medication_dispensing.manager;

import com.pulse.medication_dispensing.manager.dto.DrugDispenseDailyReportBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "e0a01108-4409-42f3-b25d-da04068f2781|DTO|MANAGER")
public interface DrugDispenseDailyReportBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "17d84d1f-f434-349e-baeb-a04604e49dc3")
    List<DrugDispenseDailyReportBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "34a63968-a00a-3fd1-8dd4-9b8870bb0dca")
    List<DrugDispenseDailyReportBaseDto> getByFinancialReportIds(List<String> financialReportId);

    @AutoGenerated(locked = true, uuid = "40e3088e-520c-3393-bb26-f0edbd8e59d4")
    List<DrugDispenseDailyReportBaseDto> getByFinancialReportId(String financialReportId);

    @AutoGenerated(locked = true, uuid = "5338b6ee-8e88-3692-95eb-9882367bb134")
    List<DrugDispenseDailyReportBaseDto> getByStorageCodes(List<String> storageCode);

    @AutoGenerated(locked = true, uuid = "56f2e3cc-6fa5-39aa-ab0e-5956ee8e06f4")
    List<DrugDispenseDailyReportBaseDto> getByStatisticsStaffId(String statisticsStaffId);

    @AutoGenerated(locked = true, uuid = "710e699a-e5f1-3cd7-a6e8-54b9abd4df62")
    List<DrugDispenseDailyReportBaseDto> getByStatisticsStaffIds(List<String> statisticsStaffId);

    @AutoGenerated(locked = true, uuid = "98cdfd8c-6646-37d1-a75b-64f235179522")
    List<DrugDispenseDailyReportBaseDto> getByStorageCode(String storageCode);

    @AutoGenerated(locked = true, uuid = "a0c7cfac-da1f-3dfd-aa04-05126c45334d")
    DrugDispenseDailyReportBaseDto getById(String id);
}
