package com.pulse.medication_dispensing.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.dto.DrugPrescriptionDispensingBaseDto;
import com.pulse.medication_dispensing.persist.dos.DrugPrescriptionDispensing;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "41e5d1a8-e523-4c0f-9aa8-a6b917fd7858|DTO|BASE_CONVERTER")
public class DrugPrescriptionDispensingBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispensingBaseDto
            convertFromDrugPrescriptionDispensingToDrugPrescriptionDispensingBaseDto(
                    DrugPrescriptionDispensing drugPrescriptionDispensing) {
        return convertFromDrugPrescriptionDispensingToDrugPrescriptionDispensingBaseDto(
                        List.of(drugPrescriptionDispensing))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugPrescriptionDispensingBaseDto>
            convertFromDrugPrescriptionDispensingToDrugPrescriptionDispensingBaseDto(
                    List<DrugPrescriptionDispensing> drugPrescriptionDispensingList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingList)) {
            return new ArrayList<>();
        }
        List<DrugPrescriptionDispensingBaseDto> drugPrescriptionDispensingBaseDtoList =
                new ArrayList<>();
        for (DrugPrescriptionDispensing drugPrescriptionDispensing :
                drugPrescriptionDispensingList) {
            if (drugPrescriptionDispensing == null) {
                continue;
            }
            DrugPrescriptionDispensingBaseDto drugPrescriptionDispensingBaseDto =
                    new DrugPrescriptionDispensingBaseDto();
            drugPrescriptionDispensingBaseDto.setId(drugPrescriptionDispensing.getId());
            drugPrescriptionDispensingBaseDto.setPrescriptionId(
                    drugPrescriptionDispensing.getPrescriptionId());
            drugPrescriptionDispensingBaseDto.setDispenseStorageCode(
                    drugPrescriptionDispensing.getDispenseStorageCode());
            drugPrescriptionDispensingBaseDto.setDispenseWindowId(
                    drugPrescriptionDispensing.getDispenseWindowId());
            drugPrescriptionDispensingBaseDto.setOrderClass(
                    drugPrescriptionDispensing.getOrderClass());
            drugPrescriptionDispensingBaseDto.setStatus(drugPrescriptionDispensing.getStatus());
            drugPrescriptionDispensingBaseDto.setEmergencyFlag(
                    drugPrescriptionDispensing.getEmergencyFlag());
            drugPrescriptionDispensingBaseDto.setClinicType(
                    drugPrescriptionDispensing.getClinicType());
            drugPrescriptionDispensingBaseDto.setPrescriptionDateTime(
                    drugPrescriptionDispensing.getPrescriptionDateTime());
            drugPrescriptionDispensingBaseDto.setHerbDispensingRegimen(
                    drugPrescriptionDispensing.getHerbDispensingRegimen());
            drugPrescriptionDispensingBaseDto.setHerbRefundingRegimen(
                    drugPrescriptionDispensing.getHerbRefundingRegimen());
            drugPrescriptionDispensingBaseDto.setHerbRefundRegimen(
                    drugPrescriptionDispensing.getHerbRefundRegimen());
            drugPrescriptionDispensingBaseDto.setRemark(drugPrescriptionDispensing.getRemark());
            drugPrescriptionDispensingBaseDto.setOutpVisitId(
                    drugPrescriptionDispensing.getOutpVisitId());
            drugPrescriptionDispensingBaseDto.setPatientId(
                    drugPrescriptionDispensing.getPatientId());
            drugPrescriptionDispensingBaseDto.setOutpSettleId(
                    drugPrescriptionDispensing.getOutpSettleId());
            drugPrescriptionDispensingBaseDto.setRefundFlag(
                    drugPrescriptionDispensing.getRefundFlag());
            drugPrescriptionDispensingBaseDto.setHerbType(drugPrescriptionDispensing.getHerbType());
            drugPrescriptionDispensingBaseDto.setLockVersion(
                    drugPrescriptionDispensing.getLockVersion());
            drugPrescriptionDispensingBaseDto.setCreatedAt(
                    drugPrescriptionDispensing.getCreatedAt());
            drugPrescriptionDispensingBaseDto.setUpdatedAt(
                    drugPrescriptionDispensing.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugPrescriptionDispensingBaseDtoList.add(drugPrescriptionDispensingBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugPrescriptionDispensingBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
