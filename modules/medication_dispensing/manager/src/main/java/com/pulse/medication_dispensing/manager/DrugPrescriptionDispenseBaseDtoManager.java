package com.pulse.medication_dispensing.manager;

import com.pulse.medication_dispensing.manager.dto.DrugPrescriptionDispenseBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "819ef128-5e4c-4318-8bce-38cd34df07b9|DTO|MANAGER")
public interface DrugPrescriptionDispenseBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "08aac70b-82f7-3683-8aae-00b35e5ded15")
    List<DrugPrescriptionDispenseBaseDto> getByDispenseStorageCodes(
            List<String> dispenseStorageCode);

    @AutoGenerated(locked = true, uuid = "1f35db3d-be97-334a-867b-52274da2219e")
    List<DrugPrescriptionDispenseBaseDto> getByDispenseStorageCode(String dispenseStorageCode);

    @AutoGenerated(locked = true, uuid = "20a67eba-d80c-36f6-b473-e855af4ead8d")
    List<DrugPrescriptionDispenseBaseDto> getByDispensingPrepareIds(
            List<String> dispensingPrepareId);

    @AutoGenerated(locked = true, uuid = "3f69da05-76b8-30e5-8024-1e008db1fb4e")
    List<DrugPrescriptionDispenseBaseDto> getBySecondPrepareStaffIds(
            List<String> secondPrepareStaffId);

    @AutoGenerated(locked = true, uuid = "574b0a95-8605-39ce-80e0-4ebeb228b47c")
    List<DrugPrescriptionDispenseBaseDto> getByDispenseWindowIds(List<String> dispenseWindowId);

    @AutoGenerated(locked = true, uuid = "57a72a72-02de-3d89-806f-8c65e4fbc7d3")
    List<DrugPrescriptionDispenseBaseDto> getByDispenseStaffId(String dispenseStaffId);

    @AutoGenerated(locked = true, uuid = "6a109e85-9db0-3d37-af0f-80bf8404d9e7")
    List<DrugPrescriptionDispenseBaseDto> getByRefundDispenseId(String refundDispenseId);

    @AutoGenerated(locked = true, uuid = "7ddd7001-a163-3a33-b09a-053a2b60ce5a")
    List<DrugPrescriptionDispenseBaseDto> getByDispenseStaffIds(List<String> dispenseStaffId);

    @AutoGenerated(locked = true, uuid = "820ee6f6-a78e-3e12-9f8a-30e620b4c3c5")
    DrugPrescriptionDispenseBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "85588997-cbca-3882-b1e9-ed2af6135151")
    List<DrugPrescriptionDispenseBaseDto> getByCheckStaffId(String checkStaffId);

    @AutoGenerated(locked = true, uuid = "9aa918eb-8dfc-3c4d-b91e-f5c7925493d8")
    List<DrugPrescriptionDispenseBaseDto> getByDispenseWindowId(String dispenseWindowId);

    @AutoGenerated(locked = true, uuid = "a2089b11-1729-3aae-978a-683c2fd6b80c")
    List<DrugPrescriptionDispenseBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "d17f9d03-2144-3d4d-9d1c-3f60541cd811")
    List<DrugPrescriptionDispenseBaseDto> getByAuditStaffIds(List<String> auditStaffId);

    @AutoGenerated(locked = true, uuid = "d2d8709e-6eb3-3081-9c44-980f99ca4b7a")
    List<DrugPrescriptionDispenseBaseDto> getByDispensingPrepareId(String dispensingPrepareId);

    @AutoGenerated(locked = true, uuid = "d4fdab7e-c946-35b4-b685-581decffdd91")
    List<DrugPrescriptionDispenseBaseDto> getBySecondPrepareStaffId(String secondPrepareStaffId);

    @AutoGenerated(locked = true, uuid = "d8d88382-b745-3def-9da6-c3cb60e6c349")
    List<DrugPrescriptionDispenseBaseDto> getByAuditStaffId(String auditStaffId);

    @AutoGenerated(locked = true, uuid = "dc413b70-047e-37df-8780-b717d118a7b2")
    List<DrugPrescriptionDispenseBaseDto> getByRefundDispenseIds(List<String> refundDispenseId);

    @AutoGenerated(locked = true, uuid = "e841dc27-6c0f-3902-b0b4-0c6bb0020ce0")
    List<DrugPrescriptionDispenseBaseDto> getByCheckStaffIds(List<String> checkStaffId);

    @AutoGenerated(locked = true, uuid = "ee14da90-e08a-33de-ab3a-c4d515089ccb")
    List<DrugPrescriptionDispenseBaseDto> getByPrepareStaffId(String prepareStaffId);

    @AutoGenerated(locked = true, uuid = "ee5dec92-1112-3539-95e4-dda9fb55a516")
    List<DrugPrescriptionDispenseBaseDto> getByPrepareStaffIds(List<String> prepareStaffId);
}
