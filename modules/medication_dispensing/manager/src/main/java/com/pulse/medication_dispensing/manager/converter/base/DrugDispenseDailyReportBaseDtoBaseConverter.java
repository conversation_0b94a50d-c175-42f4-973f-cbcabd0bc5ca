package com.pulse.medication_dispensing.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.dto.DrugDispenseDailyReportBaseDto;
import com.pulse.medication_dispensing.persist.dos.DrugDispenseDailyReport;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "e0a01108-4409-42f3-b25d-da04068f2781|DTO|BASE_CONVERTER")
public class DrugDispenseDailyReportBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugDispenseDailyReportBaseDto
            convertFromDrugDispenseDailyReportToDrugDispenseDailyReportBaseDto(
                    DrugDispenseDailyReport drugDispenseDailyReport) {
        return convertFromDrugDispenseDailyReportToDrugDispenseDailyReportBaseDto(
                        List.of(drugDispenseDailyReport))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugDispenseDailyReportBaseDto>
            convertFromDrugDispenseDailyReportToDrugDispenseDailyReportBaseDto(
                    List<DrugDispenseDailyReport> drugDispenseDailyReportList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugDispenseDailyReportList)) {
            return new ArrayList<>();
        }
        List<DrugDispenseDailyReportBaseDto> drugDispenseDailyReportBaseDtoList = new ArrayList<>();
        for (DrugDispenseDailyReport drugDispenseDailyReport : drugDispenseDailyReportList) {
            if (drugDispenseDailyReport == null) {
                continue;
            }
            DrugDispenseDailyReportBaseDto drugDispenseDailyReportBaseDto =
                    new DrugDispenseDailyReportBaseDto();
            drugDispenseDailyReportBaseDto.setId(drugDispenseDailyReport.getId());
            drugDispenseDailyReportBaseDto.setStorageCode(drugDispenseDailyReport.getStorageCode());
            drugDispenseDailyReportBaseDto.setStatisticsStaffId(
                    drugDispenseDailyReport.getStatisticsStaffId());
            drugDispenseDailyReportBaseDto.setStartDateTime(
                    drugDispenseDailyReport.getStartDateTime());
            drugDispenseDailyReportBaseDto.setEndDateTime(drugDispenseDailyReport.getEndDateTime());
            drugDispenseDailyReportBaseDto.setTotalCharge(drugDispenseDailyReport.getTotalCharge());
            drugDispenseDailyReportBaseDto.setDrugCount(drugDispenseDailyReport.getDrugCount());
            drugDispenseDailyReportBaseDto.setFinancialReportId(
                    drugDispenseDailyReport.getFinancialReportId());
            drugDispenseDailyReportBaseDto.setLockVersion(drugDispenseDailyReport.getLockVersion());
            drugDispenseDailyReportBaseDto.setCreatedAt(drugDispenseDailyReport.getCreatedAt());
            drugDispenseDailyReportBaseDto.setUpdatedAt(drugDispenseDailyReport.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugDispenseDailyReportBaseDtoList.add(drugDispenseDailyReportBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugDispenseDailyReportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
