package com.pulse.medication_dispensing.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.DrugPrescriptionDispenseDetailBaseDtoManager;
import com.pulse.medication_dispensing.manager.converter.DrugPrescriptionDispenseDetailBaseDtoConverter;
import com.pulse.medication_dispensing.manager.dto.DrugPrescriptionDispenseDetailBaseDto;
import com.pulse.medication_dispensing.persist.dos.DrugPrescriptionDispenseDetail;
import com.pulse.medication_dispensing.persist.mapper.DrugPrescriptionDispenseDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "8e46264d-8237-4241-a765-d681077d3294|DTO|BASE_MANAGER_IMPL")
public abstract class DrugPrescriptionDispenseDetailBaseDtoManagerBaseImpl
        implements DrugPrescriptionDispenseDetailBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPrescriptionDispenseDetailBaseDtoConverter
            drugPrescriptionDispenseDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPrescriptionDispenseDetailDao drugPrescriptionDispenseDetailDao;

    @AutoGenerated(locked = true, uuid = "00f24367-6a34-3fc3-9ec0-d05e1810645c")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByDrugProductSpecificationIds(
            List<String> drugProductSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugProductSpecificationId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailList =
                drugPrescriptionDispenseDetailDao.getByDrugProductSpecificationIds(
                        drugProductSpecificationId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispenseDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                drugPrescriptionDispenseDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "120601e5-d6eb-304e-9e39-766a30c7345b")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByDispenseId(String dispenseId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispenseDetailBaseDto> drugPrescriptionDispenseDetailBaseDtoList =
                getByDispenseIds(Arrays.asList(dispenseId));
        return drugPrescriptionDispenseDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "35e20919-a9e0-3b11-be61-555ae3d1748d")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByFirmIds(List<String> firmId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(firmId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailList =
                drugPrescriptionDispenseDetailDao.getByFirmIds(firmId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispenseDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                drugPrescriptionDispenseDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3c9b72be-f142-3e6f-a113-f87657febf8e")
    @Override
    public DrugPrescriptionDispenseDetailBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispenseDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugPrescriptionDispenseDetailBaseDto drugPrescriptionDispenseDetailBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugPrescriptionDispenseDetailBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3d66be25-6863-3db0-bb21-f1f682315e91")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByRefundDetailIds(
            List<String> refundDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(refundDetailId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailList =
                drugPrescriptionDispenseDetailDao.getByRefundDetailIds(refundDetailId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispenseDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                drugPrescriptionDispenseDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "524441ca-ec84-32fb-8102-a9ced91e0d7c")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByDrugProductSpecificationId(
            String drugProductSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispenseDetailBaseDto> drugPrescriptionDispenseDetailBaseDtoList =
                getByDrugProductSpecificationIds(Arrays.asList(drugProductSpecificationId));
        return drugPrescriptionDispenseDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5b6630b5-be41-3a29-9f50-0a0d51b1446f")
    public List<DrugPrescriptionDispenseDetailBaseDto>
            doConvertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                    List<DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugPrescriptionDispenseDetailList)) {
            return Collections.emptyList();
        }

        Map<String, DrugPrescriptionDispenseDetailBaseDto> dtoMap =
                drugPrescriptionDispenseDetailBaseDtoConverter
                        .convertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                                drugPrescriptionDispenseDetailList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugPrescriptionDispenseDetailBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugPrescriptionDispenseDetailBaseDto> drugPrescriptionDispenseDetailBaseDtoList =
                new ArrayList<>();
        for (DrugPrescriptionDispenseDetail i : drugPrescriptionDispenseDetailList) {
            DrugPrescriptionDispenseDetailBaseDto drugPrescriptionDispenseDetailBaseDto =
                    dtoMap.get(i.getId());
            if (drugPrescriptionDispenseDetailBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugPrescriptionDispenseDetailBaseDtoList.add(drugPrescriptionDispenseDetailBaseDto);
        }
        return drugPrescriptionDispenseDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "6a3dc24d-1c34-3d0f-844c-f3ec443fb819")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByFirmId(String firmId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispenseDetailBaseDto> drugPrescriptionDispenseDetailBaseDtoList =
                getByFirmIds(Arrays.asList(firmId));
        return drugPrescriptionDispenseDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d41ef3d2-c504-3942-8236-225d0fdd9487")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByRefundDetailId(String refundDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispenseDetailBaseDto> drugPrescriptionDispenseDetailBaseDtoList =
                getByRefundDetailIds(Arrays.asList(refundDetailId));
        return drugPrescriptionDispenseDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d91e2af5-eab3-3074-ba67-d906ad215405")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByDispenseIds(List<String> dispenseId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dispenseId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailList =
                drugPrescriptionDispenseDetailDao.getByDispenseIds(dispenseId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispenseDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                drugPrescriptionDispenseDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f175aa4b-7763-3a41-b541-a5778efc6424")
    @Override
    public List<DrugPrescriptionDispenseDetailBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailList =
                drugPrescriptionDispenseDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugPrescriptionDispenseDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugPrescriptionDispenseDetail> drugPrescriptionDispenseDetailMap =
                drugPrescriptionDispenseDetailList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugPrescriptionDispenseDetailList =
                id.stream()
                        .map(i -> drugPrescriptionDispenseDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugPrescriptionDispenseDetailToDrugPrescriptionDispenseDetailBaseDto(
                drugPrescriptionDispenseDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
