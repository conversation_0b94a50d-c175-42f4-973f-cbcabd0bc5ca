package com.pulse.dictionary_basic.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> Category
 *
 * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "b8f79982-1f6b-4507-81e3-5b69b78c791d|BTO|DEFINITION")
public class DeleteCategoryBaseCodeBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "11c2a620-a2c4-46bb-ae98-350ea8b4cf10")
    private String id;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
