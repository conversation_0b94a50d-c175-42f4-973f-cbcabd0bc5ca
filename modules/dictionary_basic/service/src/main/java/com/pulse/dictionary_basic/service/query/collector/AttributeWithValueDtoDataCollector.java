package com.pulse.dictionary_basic.service.query.collector;

import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeValueBaseDto;
import com.pulse.dictionary_basic.persist.qto.SearchAttributeWithValueQto;
import com.pulse.dictionary_basic.service.AttributeDefinitionBaseDtoService;
import com.pulse.dictionary_basic.service.AttributeValueBaseDtoService;
import com.pulse.dictionary_basic.service.index.entity.SearchAttributeWithValueQtoService;
import com.pulse.dictionary_basic.service.query.assembler.AttributeWithValueDtoDataAssembler.AttributeWithValueDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装AttributeWithValueDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "4ed5867d-9f82-3058-b9bc-8d8a6d582d10")
public class AttributeWithValueDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private AttributeDefinitionBaseDtoService attributeDefinitionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueBaseDtoService attributeValueBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeWithValueDtoDataCollector attributeWithValueDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchAttributeWithValueQtoService searchAttributeWithValueQtoService;

    /** 根据SearchAttributeWithValueQto采集Dto数据 */
    @AutoGenerated(locked = true, uuid = "d23d328a-7f1f-3991-a7f3-54aa130296f5")
    public void collectDataBySearchAttributeWithValueQto(
            AttributeWithValueDtoDataHolder dataHolder, SearchAttributeWithValueQto qto) {
        List<String> ids =
                dataHolder.getRootBaseDtoList().stream()
                        .map(AttributeDefinitionBaseDto::getId)
                        .collect(Collectors.toList());
        List<Map> dtoData = searchAttributeWithValueQtoService.filter(ids, qto);
        Set<String> attributeValueListIdSet = new HashSet<>();
        for (Map map : dtoData) {
            String attributeValueListId = (String) map.get("attributeValueList");
            if (attributeValueListId != null) {
                attributeValueListIdSet.add(attributeValueListId);
            }
        }

        // access attributeValueList
        List<AttributeValueBaseDto> attributeValueListList =
                attributeValueBaseDtoService
                        .getByIds(new ArrayList<>(attributeValueListIdSet))
                        .stream()
                        .sorted(Comparator.comparing(AttributeValueBaseDto::getId))
                        .collect(Collectors.toList());
        dataHolder.attributeValueList = attributeValueListList;

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "d8b566e8-7b0c-3974-8810-bc7e8a5ddb8c")
    public void collectDataDefault(AttributeWithValueDtoDataHolder dataHolder) {
        attributeWithValueDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "dde489f5-5e42-3322-b738-938e50f876cf")
    private void fillDataWhenNecessary(AttributeWithValueDtoDataHolder dataHolder) {
        List<AttributeDefinitionBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.attributeValueList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AttributeDefinitionBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<AttributeValueBaseDto> baseDtoList =
                    attributeValueBaseDtoService.getByAttributeIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(AttributeValueBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<AttributeValueBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(AttributeValueBaseDto::getAttributeId));
            dataHolder.attributeValueList =
                    rootDtoList.stream()
                            .map(AttributeDefinitionBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
