package com.pulse.dictionary_basic.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;

/**
 * <b>[源自]</b> AttributeValue
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "3c829c41-4075-4872-9b86-cfe057371942|BTO|DEFINITION")
public class UpdateAttributeValueBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 属性ID */
    @AutoGenerated(locked = true, uuid = "96b36fd1-844c-46cb-9858-9208547b2b34")
    private String attributeId;

    /** 布尔类型值 */
    @AutoGenerated(locked = true, uuid = "8a6a044f-4628-4386-b32d-a2c94b94f718")
    private Boolean booleanValue;

    /** 日期类型值 */
    @AutoGenerated(locked = true, uuid = "c0d91ceb-fea3-4983-ad21-35f049773c62")
    private Date dateValue;

    /** 日期时间类型值 */
    @AutoGenerated(locked = true, uuid = "0b6f63e4-39a6-42ff-a04d-b48f795eac5a")
    private Date datetimeValue;

    /** 实体ID */
    @AutoGenerated(locked = true, uuid = "66302282-d6e1-4ccc-a398-6d1855f329f3")
    private String entityId;

    /** 关联实体类型 */
    @AutoGenerated(locked = true, uuid = "eda34870-1186-4b46-93e7-4bb3ee27c20d")
    private String entityType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "77fc23c1-**************-f735284795ef")
    private String id;

    /** 复杂结构值 */
    @AutoGenerated(locked = true, uuid = "*************-4eec-b788-a49773a242d3")
    private String jsonValue;

    /** 数字类型值 */
    @AutoGenerated(locked = true, uuid = "de8f4925-eebf-44a2-8e10-30f866a6b59c")
    private Long numberValue;

    /** 字符串类型值 */
    @AutoGenerated(locked = true, uuid = "3f94bb2b-1bbc-42e5-a733-a959e19a0b49")
    private String stringValue;

    /** 值 通用的值字段，和类型无关，统一为字符串形式 */
    @AutoGenerated(locked = true, uuid = "26cd4d4c-0446-4da7-92ab-cb437bacdef1")
    private String value;

    /** 版本 */
    @AutoGenerated(locked = true, uuid = "37a857bb-dbf9-4f39-abd7-26d7ae1e0be8")
    private String version;

    @AutoGenerated(locked = true)
    public void setAttributeId(String attributeId) {
        this.__$validPropertySet.add("attributeId");
        this.attributeId = attributeId;
    }

    @AutoGenerated(locked = true)
    public void setBooleanValue(Boolean booleanValue) {
        this.__$validPropertySet.add("booleanValue");
        this.booleanValue = booleanValue;
    }

    @AutoGenerated(locked = true)
    public void setDateValue(Date dateValue) {
        this.__$validPropertySet.add("dateValue");
        this.dateValue = dateValue;
    }

    @AutoGenerated(locked = true)
    public void setDatetimeValue(Date datetimeValue) {
        this.__$validPropertySet.add("datetimeValue");
        this.datetimeValue = datetimeValue;
    }

    @AutoGenerated(locked = true)
    public void setEntityId(String entityId) {
        this.__$validPropertySet.add("entityId");
        this.entityId = entityId;
    }

    @AutoGenerated(locked = true)
    public void setEntityType(String entityType) {
        this.__$validPropertySet.add("entityType");
        this.entityType = entityType;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setJsonValue(String jsonValue) {
        this.__$validPropertySet.add("jsonValue");
        this.jsonValue = jsonValue;
    }

    @AutoGenerated(locked = true)
    public void setNumberValue(Long numberValue) {
        this.__$validPropertySet.add("numberValue");
        this.numberValue = numberValue;
    }

    @AutoGenerated(locked = true)
    public void setStringValue(String stringValue) {
        this.__$validPropertySet.add("stringValue");
        this.stringValue = stringValue;
    }

    @AutoGenerated(locked = true)
    public void setValue(String value) {
        this.__$validPropertySet.add("value");
        this.value = value;
    }

    @AutoGenerated(locked = true)
    public void setVersion(String version) {
        this.__$validPropertySet.add("version");
        this.version = version;
    }
}
