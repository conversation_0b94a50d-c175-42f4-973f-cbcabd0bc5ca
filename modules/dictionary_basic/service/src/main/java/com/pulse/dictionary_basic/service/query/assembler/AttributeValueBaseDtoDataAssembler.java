package com.pulse.dictionary_basic.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_basic.manager.dto.AttributeValueBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** AttributeValueBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "b3ef8da8-3c29-3e6c-8346-7e061235b1dc")
public class AttributeValueBaseDtoDataAssembler {

    /** 批量自定义组装AttributeValueBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "10da07a2-dbd8-32ee-a06c-2f2169b16b66")
    public void assembleDataCustomized(List<AttributeValueBaseDto> dataList) {
        // 自定义数据组装

    }

    /** 组装AttributeValueBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "7e095176-eeee-317a-8b96-f9fa336aea89")
    public void assembleData(List<AttributeValueBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }
}
