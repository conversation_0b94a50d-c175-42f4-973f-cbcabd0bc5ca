package com.pulse.dictionary_basic.manager.bo.base;

import com.pulse.dictionary_basic.common.enums.CategoryTypeEnum;
import com.pulse.dictionary_basic.common.enums.SourceCategoryTypeEnum;
import com.pulse.dictionary_basic.manager.bo.CategoryBO;
import com.pulse.dictionary_basic.persist.dos.Category;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.converter.InputCodeEoConverter;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.eo.StringListConverter;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.Valid;

@DoNotModify
@Table(name = "dictionary_basic_category")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "0fd8507b-a4e4-3853-aa71-d670121c9bbf")
public abstract class BaseCategoryBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 编码 */
    @Column(name = "code")
    @AutoGenerated(locked = true, uuid = "ebdfb59c-ee1f-496a-9bd2-8b134a4bbd05")
    private String code;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "9c30b609-457b-340f-9a82-ff7f617eb6d2")
    private Date createdAt;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "ac19f169-24e0-36f4-9dae-ad97b6524ee3")
    private Long deletedAt = 0L;

    /** 描述 */
    @Column(name = "description")
    @AutoGenerated(locked = true, uuid = "d2023f18-1f93-3d86-8b86-8a04c7466c81")
    private String description;

    /** 禁用原因 */
    @Column(name = "disabled_reason")
    @AutoGenerated(locked = true, uuid = "aa65f7e6-d531-4d26-8d86-00c6c09c9319")
    private String disabledReason;

    /** 启用标志 */
    @Column(name = "enable_flag")
    @AutoGenerated(locked = true, uuid = "56913ec8-469f-3d14-8b30-9baa8ef4d81d")
    private Boolean enableFlag;

    /** 扩展信息 */
    @Column(name = "extension_info")
    @AutoGenerated(locked = true, uuid = "d4adfad1-8e63-37fd-803d-130625bd30c7")
    private String extensionInfo;

    /** 末级标志 */
    @Column(name = "final_flag")
    @AutoGenerated(locked = true, uuid = "6cfe5bd7-2272-49c7-b87e-2ed13e859a17")
    private Boolean finalFlag;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "461f4fd4-a0da-3d2a-8223-e9bd684ef514")
    @Id
    private String id;

    /** 输入码 */
    @Column(name = "input_code")
    @Valid
    @AutoGenerated(locked = true, uuid = "c9e8053f-2b13-395d-b6f9-01c42c6e715c")
    @Convert(converter = InputCodeEoConverter.class)
    private InputCodeEo inputCode;

    /** 机构ID */
    @Column(name = "institution_id")
    @AutoGenerated(locked = true, uuid = "0b502743-df73-39d2-a3b0-df0fad9b73a4")
    private String institutionId;

    /** 层级计数 */
    @Column(name = "level_count")
    @AutoGenerated(locked = true, uuid = "ce15e932-3ef1-3349-b615-53eaa1463a7c")
    private Long levelCount;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "48a7c44c-deed-4e9b-b9ba-c331087b0df8")
    @Version
    private Long lockVersion;

    /** 名称 */
    @Column(name = "name")
    @AutoGenerated(locked = true, uuid = "ac2101f5-6750-33f3-b28e-4a08999fa185")
    private String name;

    /** 父ID */
    @Column(name = "parent_id")
    @AutoGenerated(locked = true, uuid = "9e6a7c26-ab5b-35a7-bd5f-8f1d5ec939d6")
    private String parentId;

    /** 根ID */
    @Column(name = "root_id")
    @AutoGenerated(locked = true, uuid = "a24ab2f6-8373-4380-a035-4bc7ce027b0b")
    private String rootId;

    /** 场景 */
    @Column(name = "scene")
    @AutoGenerated(locked = true, uuid = "6bff0680-0945-3d08-96d6-ac717680fc95")
    private String scene;

    /** 排序编号 */
    @Column(name = "sort_number")
    @AutoGenerated(locked = true, uuid = "a585e91f-07a9-3e28-9340-f12dcce2edd2")
    private Long sortNumber;

    /** 来源类型 */
    @Column(name = "source_type")
    @AutoGenerated(locked = true, uuid = "79d571af-bcc2-3ca6-a362-9c9a3b473590")
    @Enumerated(EnumType.STRING)
    private SourceCategoryTypeEnum sourceType;

    /** 类型 */
    @Column(name = "type")
    @AutoGenerated(locked = true, uuid = "da35f9ed-0872-4035-bd95-fe6128ff2a2e")
    @Enumerated(EnumType.STRING)
    private CategoryTypeEnum type;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "df08d2c6-3d3a-3713-99a2-48c64e1ee17d")
    private Date updatedAt;

    /** 使用范围 */
    @Column(name = "use_scope")
    @Valid
    @AutoGenerated(locked = true, uuid = "59752240-54b0-4b6a-9447-776213e4270b")
    @Convert(converter = StringListConverter.class)
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public Category convertToCategory() {
        Category entity = new Category();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "code",
                "name",
                "inputCode",
                "sortNumber",
                "parentId",
                "rootId",
                "levelCount",
                "type",
                "sourceType",
                "scene",
                "extensionInfo",
                "description",
                "enableFlag",
                "disabledReason",
                "institutionId",
                "finalFlag",
                "useScopeList",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static CategoryBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        CategoryBO category =
                (CategoryBO)
                        session.createQuery("from CategoryBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return category;
    }

    @AutoGenerated(locked = true)
    public static CategoryBO getByRootIdAndSourceTypeAndCodeAndParentId(
            String rootId, SourceCategoryTypeEnum sourceType, String code, String parentId) {
        Session session = TransactionalSessionFactory.getSession();
        CategoryBO category =
                (CategoryBO)
                        session.createQuery(
                                        "from CategoryBO where rootId =: rootId  and sourceType =:"
                                            + " sourceType  and code =: code  and parentId =:"
                                            + " parentId ")
                                .setParameter("rootId", rootId)
                                .setParameter("sourceType", sourceType)
                                .setParameter("code", code)
                                .setParameter("parentId", parentId)
                                .uniqueResult();
        return category;
    }

    @AutoGenerated(locked = true)
    public String getCode() {
        return this.code;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDescription() {
        return this.description;
    }

    @AutoGenerated(locked = true)
    public String getDisabledReason() {
        return this.disabledReason;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnableFlag() {
        return this.enableFlag;
    }

    @AutoGenerated(locked = true)
    public String getExtensionInfo() {
        return this.extensionInfo;
    }

    @AutoGenerated(locked = true)
    public Boolean getFinalFlag() {
        return this.finalFlag;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public InputCodeEo getInputCode() {
        return this.inputCode;
    }

    @AutoGenerated(locked = true)
    public String getInstitutionId() {
        return this.institutionId;
    }

    @AutoGenerated(locked = true)
    public Long getLevelCount() {
        return this.levelCount;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getName() {
        return this.name;
    }

    @AutoGenerated(locked = true)
    public String getParentId() {
        return this.parentId;
    }

    @AutoGenerated(locked = true)
    public String getRootId() {
        return this.rootId;
    }

    @AutoGenerated(locked = true)
    public String getScene() {
        return this.scene;
    }

    @AutoGenerated(locked = true)
    public Long getSortNumber() {
        return this.sortNumber;
    }

    @AutoGenerated(locked = true)
    public SourceCategoryTypeEnum getSourceType() {
        return this.sourceType;
    }

    @AutoGenerated(locked = true)
    public CategoryTypeEnum getType() {
        return this.type;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public List<String> getUseScopeList() {
        return this.useScopeList;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public CategoryBO setCode(String code) {
        this.code = code;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setDescription(String description) {
        this.description = description;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setDisabledReason(String disabledReason) {
        this.disabledReason = disabledReason;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setEnableFlag(Boolean enableFlag) {
        this.enableFlag = enableFlag;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setExtensionInfo(String extensionInfo) {
        this.extensionInfo = extensionInfo;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setFinalFlag(Boolean finalFlag) {
        this.finalFlag = finalFlag;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setId(String id) {
        this.id = id;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setInputCode(InputCodeEo inputCode) {
        this.inputCode = inputCode;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setInstitutionId(String institutionId) {
        this.institutionId = institutionId;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setLevelCount(Long levelCount) {
        this.levelCount = levelCount;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setName(String name) {
        this.name = name;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setParentId(String parentId) {
        this.parentId = parentId;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setRootId(String rootId) {
        this.rootId = rootId;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setScene(String scene) {
        this.scene = scene;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setSourceType(SourceCategoryTypeEnum sourceType) {
        this.sourceType = sourceType;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setType(CategoryTypeEnum type) {
        this.type = type;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (CategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public CategoryBO setUseScopeList(List<String> useScopeList) {
        this.useScopeList = useScopeList;
        return (CategoryBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
