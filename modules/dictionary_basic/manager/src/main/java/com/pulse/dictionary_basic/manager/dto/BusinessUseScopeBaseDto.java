package com.pulse.dictionary_basic.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "ce13313e-6bfd-46c7-be4c-47e357844161|DTO|DEFINITION")
public class BusinessUseScopeBaseDto {
    /** 范围编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4af73445-2772-44cd-aefc-b116e7b15e3f")
    private List<String> codeList;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "1e04cd91-a457-42e5-a112-6d1705ef5f7b")
    private Date createdAt;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "ddbe486e-c25d-47aa-929c-21e7578b2be2")
    private Long deletedAt;

    /** 实体ID */
    @AutoGenerated(locked = true, uuid = "dc43620b-4669-49cd-9be6-b41d81c1d0c5")
    private String entityId;

    /** 实体类型 */
    @AutoGenerated(locked = true, uuid = "cd3f4542-a1b5-401a-ae26-d9c272dc16d3")
    private String entityType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9bbe39ae-d8c0-4541-91d3-b28205fd5ad7")
    private Long id;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "2c9f4d72-38fc-4b64-9c2c-27e9735e7409")
    private Long lockVersion;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "5ef95c1c-bee6-425e-83a9-2f3fa010075d")
    private Date updatedAt;
}
