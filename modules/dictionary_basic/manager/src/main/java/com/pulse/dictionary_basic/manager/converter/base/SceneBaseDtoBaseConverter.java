package com.pulse.dictionary_basic.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_basic.manager.dto.SceneBaseDto;
import com.pulse.dictionary_basic.persist.dos.Scene;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "1d088c77-3edb-4245-92de-0be8d230719c|DTO|BASE_CONVERTER")
public class SceneBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public SceneBaseDto convertFromSceneToSceneBaseDto(Scene scene) {
        return convertFromSceneToSceneBaseDto(List.of(scene)).stream().findAny().orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<SceneBaseDto> convertFromSceneToSceneBaseDto(List<Scene> sceneList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(sceneList)) {
            return new ArrayList<>();
        }
        List<SceneBaseDto> sceneBaseDtoList = new ArrayList<>();
        for (Scene scene : sceneList) {
            if (scene == null) {
                continue;
            }
            SceneBaseDto sceneBaseDto = new SceneBaseDto();
            sceneBaseDto.setId(scene.getId());
            sceneBaseDto.setCode(scene.getCode());
            sceneBaseDto.setName(scene.getName());
            sceneBaseDto.setAlias(scene.getAlias());
            sceneBaseDto.setAliasFlag(scene.getAliasFlag());
            sceneBaseDto.setSortNumber(scene.getSortNumber());
            sceneBaseDto.setLockVersion(scene.getLockVersion());
            sceneBaseDto.setCreatedAt(scene.getCreatedAt());
            sceneBaseDto.setUpdatedAt(scene.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            sceneBaseDtoList.add(sceneBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return sceneBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
