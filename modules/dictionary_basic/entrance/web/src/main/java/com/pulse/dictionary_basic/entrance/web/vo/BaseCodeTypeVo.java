package com.pulse.dictionary_basic.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "34edab1b-8712-4efc-ab41-891f5afbaa2b|VO|DEFINITION")
public class BaseCodeTypeVo {
    /** 码表编码 */
    @AutoGenerated(locked = true, uuid = "3fa2dfc1-3da9-4eb8-a441-89492a7f78d0")
    private String code;

    /** 字典类别名称 */
    @AutoGenerated(locked = true, uuid = "57ddca8e-f08c-4596-ae03-1dd093cd58ab")
    private String name;

    /** 父节点ID */
    @AutoGenerated(locked = true, uuid = "caca0085-2ab7-4937-9540-2f51b873ad13")
    private String parentId;
}
