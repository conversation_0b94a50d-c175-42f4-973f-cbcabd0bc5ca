package com.pulse.dictionary_basic.entrance.web.controller;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_basic.common.enums.SourceCategoryTypeEnum;
import com.pulse.dictionary_basic.entrance.web.converter.AttributeDefinitionBaseVoConverter;
import com.pulse.dictionary_basic.entrance.web.converter.AttributeWithValueVoConverter;
import com.pulse.dictionary_basic.entrance.web.vo.*;
import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeWithValueDto;
import com.pulse.dictionary_basic.manager.dto.CategoryWithSceneDto;
import com.pulse.dictionary_basic.manager.dto.SceneBaseDto;
import com.pulse.dictionary_basic.persist.qto.SearchAttributeDefinitionQto;
import com.pulse.dictionary_basic.persist.qto.SearchAttributeWithValueQto;
import com.pulse.dictionary_basic.persist.qto.SearchCategoryWithSceneQto;
import com.pulse.dictionary_basic.persist.qto.SearchSceneQto;
import com.pulse.dictionary_basic.service.AttributeDefinitionBOService;
import com.pulse.dictionary_basic.service.AttributeValueBOService;
import com.pulse.dictionary_basic.service.bto.CreateAttributeDefinitionBto;
import com.pulse.dictionary_basic.service.bto.CreateAttributeValueBto;
import com.pulse.dictionary_basic.service.bto.EnableAttributeDefinitionBto;
import com.pulse.dictionary_basic.service.bto.UpdateAttributeDefinitionBto;
import com.pulse.dictionary_basic.service.query.AttributeDefinitionBaseDtoQueryService;
import com.pulse.dictionary_basic.service.query.AttributeWithValueDtoQueryService;
import com.pulse.dictionary_basic.service.query.CategoryWithSceneDtoQueryService;
import com.pulse.dictionary_basic.service.query.SceneBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "33e7e07e-ac92-341a-821c-432b15e79417")
public class AttributeController {
    @AutoGenerated(locked = true)
    @Resource
    private AttributeDefinitionBOService attributeDefinitionBOService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeDefinitionBaseDtoQueryService attributeDefinitionBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeDefinitionBaseVoConverter attributeDefinitionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueBOService attributeValueBOService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeWithValueDtoQueryService attributeWithValueDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeWithValueVoConverter attributeWithValueVoConverter;

    @Resource private CategoryWithSceneDtoQueryService categoryWithSceneDtoQueryService;
    @Resource private SceneBaseDtoQueryService sceneBaseDtoQueryService;

    /** 修改属性定义 */
    @PublicInterface(id = "2f6d543d-5138-458c-af8d-6dd676cd00bf", version = "1742354134011")
    @AutoGenerated(locked = false, uuid = "2f6d543d-5138-458c-af8d-6dd676cd00bf")
    @RequestMapping(
            value = {"/api/dictionary-basic/update-attribute-definition"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateAttributeDefinition(
            @Valid UpdateAttributeDefinitionBto updateAttributeDefinitionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                attributeDefinitionBOService.updateAttributeDefinition(
                        updateAttributeDefinitionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新建属性值 */
    @PublicInterface(id = "3bb03221-c3d2-49a0-abd4-3945aaf2529d", version = "1741598208936")
    @AutoGenerated(locked = false, uuid = "3bb03221-c3d2-49a0-abd4-3945aaf2529d")
    @RequestMapping(
            value = {"/api/dictionary-basic/create-attribute-value"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createAttributeValue(@Valid CreateAttributeValueBto createAttributeValueBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = attributeValueBOService.createAttributeValue(createAttributeValueBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询属性定义 */
    @PublicInterface(id = "49ffb1a3-7cde-481a-b604-03cefb3f0e37", version = "1742354146217")
    @AutoGenerated(locked = false, uuid = "49ffb1a3-7cde-481a-b604-03cefb3f0e37")
    @RequestMapping(
            value = {"/api/dictionary-basic/search-attribute-definition"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<AttributeDefinitionBaseVo> searchAttributeDefinition(
            @Valid SearchAttributeDefinitionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AttributeDefinitionBaseDto> rpcResult =
                attributeDefinitionBaseDtoQueryService.searchAttributeDefinition(qto);
        List<AttributeDefinitionBaseVo> result =
                attributeDefinitionBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询属性定义含对应实体值 */
    @PublicInterface(id = "5ed89f1b-abbc-4bef-b416-fa52fbb251c5", version = "1743581552622")
    @AutoGenerated(locked = false, uuid = "5ed89f1b-abbc-4bef-b416-fa52fbb251c5")
    @RequestMapping(
            value = {"/api/dictionary-basic/search-attribute-with-value"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<AttributeWithValueVo> searchAttributeWithValue(
            @Valid SearchAttributeWithValueQto qto) {
        qto.setAttributeValueListEntityTypeIs(
                StringUtils.defaultString(
                        qto.getEntityTypeIs(), qto.getAttributeValueListEntityTypeIs()));
        SearchAttributeWithValueQto.Filter filter = new SearchAttributeWithValueQto.Filter();
        filter.setFilterEntityType(qto.getAttributeValueListEntityTypeIs());
        filter.setFilterEntityId(qto.getAttributeValueListEntityIdIs());
        qto.setFilter(filter);
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AttributeWithValueDto> rpcResult =
                attributeWithValueDtoQueryService.searchAttribute(qto);
        List<AttributeWithValueVo> result =
                attributeWithValueVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 兼容返回属性定义
        SearchAttributeWithValueQto definitionQto = new SearchAttributeWithValueQto();
        definitionQto.setEntityTypeIs(qto.getEntityTypeIs());
        definitionQto.setSubEntityTypeIs(qto.getSubEntityTypeIs());
        List<AttributeWithValueDto> rpcDefinitionResult =
                attributeWithValueDtoQueryService.searchAttribute(definitionQto);
        for (AttributeWithValueDto attributeWithValueDto : rpcDefinitionResult) {
            attributeWithValueDto.setAttributeValueList(new ArrayList<>());
        }
        List<AttributeWithValueVo> definitionResult =
                attributeWithValueVoConverter.convertAndAssembleDataList(rpcDefinitionResult);
        if (!Strings.isNullOrEmpty(qto.getAttributeValueListEntityIdIs())) {
            for (AttributeWithValueVo definition : definitionResult) {
                for (AttributeWithValueVo definitionWithValue : result) {
                    if (StringUtils.equals(definition.getId(), definitionWithValue.getId())) {
                        definition.setAttributeValueList(
                                CollectionUtil.isEmpty(definitionWithValue.getAttributeValueList())
                                        ? new ArrayList<>()
                                        : definitionWithValue.getAttributeValueList());
                    }
                }
            }
        }

        return definitionResult;
    }

    /** 启/停用属性定义 */
    @PublicInterface(id = "5fa78d38-cfd9-4817-b885-8619e3bb79c8", version = "1744013782006")
    @AutoGenerated(locked = false, uuid = "5fa78d38-cfd9-4817-b885-8619e3bb79c8")
    @RequestMapping(
            value = {"/api/dictionary-basic/enable-attribute-definition"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String enableAttributeDefinition(
            @Valid EnableAttributeDefinitionBto enableAttributeDefinitionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                attributeDefinitionBOService.enableAttributeDefinition(
                        enableAttributeDefinitionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 模拟模块列表 */
    @PublicInterface(id = "7195217f-37d0-451f-98f7-79a702be6db9", version = "1745213863245")
    @AutoGenerated(locked = false, uuid = "7195217f-37d0-451f-98f7-79a702be6db9")
    @RequestMapping(
            value = {"/api/dictionay-basic/list-module-by-mock"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ModuleVo> listModuleByMock() {
        // TODO implement method
        List<ModuleVo> res = ModuleVo.createModuleList();
        // 获取基础字典
        SearchCategoryWithSceneQto qto = new SearchCategoryWithSceneQto();
        qto.setSceneCodeIs("BASE_CODE");
        qto.setSourceTypeIs(SourceCategoryTypeEnum.MASTER);
        List<CategoryWithSceneDto> categoryWithSceneDtoList =
                categoryWithSceneDtoQueryService.searchCategoryWithScene(qto);
        List<SceneBaseDto> sceneBaseDtoList =
                sceneBaseDtoQueryService.searchScene(new SearchSceneQto());

        List<BaseCodeTypeVo> baseCodeTypeVoList = convertToBaseCodeTypeVo(categoryWithSceneDtoList);
        for (ModuleVo moduleVo : res) {
            if (moduleVo.getModuleName().equals("分类管理")) {
                moduleVo.setEntityList(
                        new ArrayList<>(
                                sceneBaseDtoList.stream()
                                        .map(
                                                sceneBaseDto ->
                                                        new EntityVo(
                                                                sceneBaseDto.getName(),
                                                                sceneBaseDto.getCode(),
                                                                "CATEGORY"))
                                        .collect(Collectors.toList())));
            }
        }
        for (ModuleVo moduleVo : res) {
            for (EntityVo entityVo : moduleVo.getEntityList()) {
                if (entityVo.getEntityEnName().equals("BASE_CODE")) {
                    entityVo.setBaseCodeTypeVoList(baseCodeTypeVoList);
                }
            }
        }

        return res;
    }

    /**
     * 转换基础字典类别VO
     *
     * @param categoryList
     * @return
     */
    private List<BaseCodeTypeVo> convertToBaseCodeTypeVo(List<CategoryWithSceneDto> categoryList) {
        return categoryList.stream()
                // 筛选 sourceType 为 MASTER 的对象
                .filter(category -> category.getSourceType() == SourceCategoryTypeEnum.MASTER)
                // 映射到新的 BaseCodeTypeVo 对象
                .map(
                        category -> {
                            BaseCodeTypeVo vo = new BaseCodeTypeVo();
                            vo.setCode(category.getCode());
                            vo.setName(category.getName());
                            return vo;
                        })
                // 去重，基于 code 和 name
                .distinct()
                // 收集为 List
                .collect(Collectors.toList());
    }

    /** 新建属性定义 */
    @PublicInterface(id = "b97e4709-978c-488b-9bc2-ded329b41fa1", version = "1742354160377")
    @AutoGenerated(locked = false, uuid = "b97e4709-978c-488b-9bc2-ded329b41fa1")
    @RequestMapping(
            value = {"/api/dictionary-basic/create-attribute-definition"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createAttributeDefinition(
            @Valid CreateAttributeDefinitionBto createAttributeDefinitionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                attributeDefinitionBOService.createAttributeDefinition(
                        createAttributeDefinitionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
