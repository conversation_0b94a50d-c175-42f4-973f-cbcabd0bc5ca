package com.pulse.dictionary_basic.entrance.web.controller;

import cn.hutool.core.collection.CollUtil;

import com.pulse.dictionary_basic.common.enums.SourceCategoryTypeEnum;
import com.pulse.dictionary_basic.entrance.web.converter.CategoryBaseCodeSimpleVoConverter;
import com.pulse.dictionary_basic.entrance.web.converter.CategoryBaseCodeVoConverter;
import com.pulse.dictionary_basic.entrance.web.converter.CategorySelectVoConverter;
import com.pulse.dictionary_basic.entrance.web.converter.CategorySimpleVoConverter;
import com.pulse.dictionary_basic.entrance.web.utils.TreeUtils;
import com.pulse.dictionary_basic.entrance.web.vo.CategoryBaseCodeSimpleVo;
import com.pulse.dictionary_basic.entrance.web.vo.CategoryBaseCodeVo;
import com.pulse.dictionary_basic.entrance.web.vo.CategorySelectVo;
import com.pulse.dictionary_basic.entrance.web.vo.CategorySimpleVo;
import com.pulse.dictionary_basic.manager.dto.BaseCodeDto;
import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.dictionary_basic.persist.qto.SearchCategoryBaseCodeQto;
import com.pulse.dictionary_basic.persist.qto.SearchCategoryQto;
import com.pulse.dictionary_basic.service.AttributeValueBOService;
import com.pulse.dictionary_basic.service.CategoryBOService;
import com.pulse.dictionary_basic.service.bto.MergeAttributeValueBto;
import com.pulse.dictionary_basic.service.bto.MergeCategoryBaseCodeBto;
import com.pulse.dictionary_basic.service.query.BaseCodeDtoQueryService;
import com.pulse.dictionary_basic.service.query.CategoryBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.opensearch.common.Strings;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "61ea226d-7735-3772-a33c-867d6cade8ae")
public class CategoryCustomController {
    @AutoGenerated(locked = true)
    @Resource
    private BaseCodeDtoQueryService baseCodeDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private CategoryBOService categoryBOService;

    @AutoGenerated(locked = true)
    @Resource
    private CategoryBaseCodeSimpleVoConverter categoryBaseCodeSimpleVoConverter;

    @Resource private AttributeValueBOService attributeValueBOService;

    @Resource private CategoryBaseCodeVoConverter categoryBaseCodeVoConverter;
    @Resource private CategorySimpleVoConverter categorySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private CategoryBaseDtoQueryService categoryBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private CategorySelectVoConverter categorySelectVoConverter;

    /** 查询分类选项 */
    @PublicInterface(id = "07c6a24e-d127-406f-8c91-9a53688ee96e", version = "1743651957273")
    @AutoGenerated(locked = false, uuid = "07c6a24e-d127-406f-8c91-9a53688ee96e")
    @RequestMapping(
            value = {"/api/dictionary-basic/search-category-select"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<CategorySelectVo> searchCategorySelect(@Valid SearchCategoryQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CategoryBaseDto> rpcResult = categoryBaseDtoQueryService.searchCategory(qto);
        List<CategorySelectVo> result =
                categorySelectVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        if (!Strings.isNullOrEmpty(qto.getCodeIs())) {
            SearchCategoryQto newQto = new SearchCategoryQto();
            // todo:fix
            newQto.setRootIdIn(Arrays.asList(qto.getCodeIs()));
            List<CategoryBaseDto> fullResult = categoryBaseDtoQueryService.searchCategory(newQto);
            Map<String, List<CategoryBaseDto>> parentMap = TreeUtils.buildParentMap(fullResult);
            result =
                    categorySelectVoConverter.convertAndAssembleDataList(
                            TreeUtils.getAllDescendants(qto.getCodeIs(), parentMap));
        }
        return result;
    }

    /** 查询分类码表（含路径） */
    @PublicInterface(id = "66655ca0-d418-43c8-bf43-b6fb8114bf91", version = "1742809229251")
    @AutoGenerated(locked = false, uuid = "66655ca0-d418-43c8-bf43-b6fb8114bf91")
    @RequestMapping(
            value = {"/api/dictionary-basic/search-category-base-code-with-path"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<CategoryBaseCodeVo> searchCategoryBaseCodeWithPath(
            @Valid SearchCategoryBaseCodeQto searchCategoryBaseCodeQto) {
        // TODO implement method
        List<BaseCodeDto> rpcResult =
                baseCodeDtoQueryService.searchCategoryBaseCodeWithPath(searchCategoryBaseCodeQto);
        List<CategoryBaseCodeVo> result =
                categoryBaseCodeVoConverter.convertAndAssembleDataList(rpcResult);
        return result;
    }

    /** 查询分类（含路径 ） */
    @PublicInterface(id = "692b4616-aaad-4c7e-bec5-b5f9473b7096", version = "1744349185460")
    @AutoGenerated(locked = false, uuid = "692b4616-aaad-4c7e-bec5-b5f9473b7096")
    @RequestMapping(
            value = {"/api/dictionary-basic/search-category-with-path"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<CategorySimpleVo> searchCategoryWithPath(
            @Valid SearchCategoryQto searchCategoryQto) {
        // TODO implement method
        List<CategoryBaseDto> rpcResult =
                categoryBaseDtoQueryService.searchCategoryWithPath(searchCategoryQto);
        List<CategorySimpleVo> result =
                categorySimpleVoConverter.convertAndAssembleDataList(rpcResult);
        return result;
    }

    /** 查询基础字典选项 */
    @PublicInterface(id = "6e610744-7f2d-4ddf-bad0-e1bc4c15d1d4", version = "1743070330432")
    @AutoGenerated(locked = false, uuid = "6e610744-7f2d-4ddf-bad0-e1bc4c15d1d4")
    @RequestMapping(
            value = {"/api/dictionary-basic/search-category-base-code-select"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<CategoryBaseCodeSimpleVo> searchCategoryBaseCodeSelect(
            @Valid SearchCategoryBaseCodeQto qto) {
        qto.setSourceTypeIs(SourceCategoryTypeEnum.MASTER);
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<BaseCodeDto> rpcResult = baseCodeDtoQueryService.searchCategoryBaseCode(qto);
        List<CategoryBaseCodeSimpleVo> result =
                categoryBaseCodeSimpleVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        if (CollUtil.isNotEmpty(rpcResult)) {
            BaseCodeDto BaseCodeDto =
                    rpcResult.stream()
                            .findFirst()
                            .orElseThrow(
                                    () -> {
                                        throw new IgnoredException(
                                                ErrorCode.SYS_ERROR,
                                                "未查询到" + qto.getCodeIs() + "字典");
                                    });
            String rootId =
                    Strings.isNullOrEmpty(BaseCodeDto.getRootId())
                            ? BaseCodeDto.getId()
                            : BaseCodeDto.getRootId();
            String parentId = BaseCodeDto.getId();
            SearchCategoryBaseCodeQto qto1 = new SearchCategoryBaseCodeQto();
            qto1.setRootIdIn(Arrays.asList(rootId));
            qto1.setSourceTypeIs(SourceCategoryTypeEnum.VALUE);
            List<BaseCodeDto> rpcRes = baseCodeDtoQueryService.searchCategoryBaseCode(qto1);
            result =
                    categoryBaseCodeSimpleVoConverter.convertAndAssembleDataList(
                            TreeUtils.getAllDescendants(
                                    parentId, TreeUtils.buildParentMap(rpcRes)));
        }

        return result;
    }

    /** 修改分类-码表 基础字典-编辑 */
    @Transactional
    @PublicInterface(id = "b0792849-1a6d-4bc3-ab1c-983cc5e5a0c1", version = "1742434611765")
    @AutoGenerated(locked = false, uuid = "b0792849-1a6d-4bc3-ab1c-983cc5e5a0c1")
    @RequestMapping(
            value = {"/api/dictionary-basic/merge-category-base-code"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeCategoryBaseCode(
            @Valid MergeCategoryBaseCodeBto mergeCategoryBaseCodeBto,
            @Valid List<MergeAttributeValueBto> mergeAttributeValueBtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = categoryBOService.mergeCategoryBaseCode(mergeCategoryBaseCodeBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        attributeValueBOService.mergeAttributeValueList(
                mergeAttributeValueBtoList, "CATEGORY", result);
        return result;
    }
}
