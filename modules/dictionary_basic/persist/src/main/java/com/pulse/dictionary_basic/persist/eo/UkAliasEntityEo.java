package com.pulse.dictionary_basic.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "dafd16b3-4632-30fd-bded-a40211a0f08e|EO|DEFINITION")
public class UkAliasEntityEo {
    @AutoGenerated(locked = true, uuid = "1a85ca7d-f9ce-3597-b4c0-0b38f36c816c")
    private String entityType;

    @AutoGenerated(locked = true, uuid = "79081cbd-938e-3dd3-9770-147e836585f3")
    private String entityId;
}
