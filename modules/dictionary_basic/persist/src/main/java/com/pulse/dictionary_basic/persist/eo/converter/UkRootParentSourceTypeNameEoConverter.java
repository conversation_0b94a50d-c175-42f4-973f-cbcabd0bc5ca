package com.pulse.dictionary_basic.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.UkRootParentSourceTypeNameEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for UkRootParentSourceTypeNameEo */
@Converter
@AutoGenerated(locked = true, uuid = "162d9049-48b7-32c2-b4a8-c98a1c361f0e")
public class UkRootParentSourceTypeNameEoConverter
        implements AttributeConverter<UkRootParentSourceTypeNameEo, String> {

    /** convert DB column to UkRootParentSourceTypeNameEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            UkRootParentSourceTypeNameEo ukRootParentSourceTypeNameEo) {
        if (ukRootParentSourceTypeNameEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(ukRootParentSourceTypeNameEo);
        }
    }

    /** convert DB column to UkRootParentSourceTypeNameEo */
    @AutoGenerated(locked = true)
    public UkRootParentSourceTypeNameEo convertToEntityAttribute(
            String ukRootParentSourceTypeNameEoJson) {
        if (StrUtil.isEmpty(ukRootParentSourceTypeNameEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    ukRootParentSourceTypeNameEoJson,
                    new TypeReference<UkRootParentSourceTypeNameEo>() {});
        }
    }
}
