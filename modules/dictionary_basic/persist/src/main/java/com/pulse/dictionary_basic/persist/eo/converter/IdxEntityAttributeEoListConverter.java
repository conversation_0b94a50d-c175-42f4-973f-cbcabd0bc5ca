package com.pulse.dictionary_basic.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.IdxEntityAttributeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<IdxEntityAttributeEo> */
@Converter
@AutoGenerated(locked = true, uuid = "000d9c3e-84d3-38b7-b62c-77a94232665e")
public class IdxEntityAttributeEoListConverter
        implements AttributeConverter<List<IdxEntityAttributeEo>, String> {

    /** convert List<IdxEntityAttributeEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<IdxEntityAttributeEo> idxEntityAttributeEoList) {
        if (idxEntityAttributeEoList == null || idxEntityAttributeEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(idxEntityAttributeEoList);
        }
    }

    /** convert DB column to List<IdxEntityAttributeEo> */
    @AutoGenerated(locked = true)
    public List<IdxEntityAttributeEo> convertToEntityAttribute(
            String idxEntityAttributeEoListJson) {
        if (StrUtil.isEmpty(idxEntityAttributeEoListJson)) {
            return new ArrayList<IdxEntityAttributeEo>();
        } else {
            return JsonUtils.readObject(
                    idxEntityAttributeEoListJson,
                    new TypeReference<List<IdxEntityAttributeEo>>() {});
        }
    }
}
