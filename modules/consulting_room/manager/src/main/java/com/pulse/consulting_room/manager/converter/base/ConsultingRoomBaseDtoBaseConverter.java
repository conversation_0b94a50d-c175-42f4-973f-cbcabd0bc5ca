package com.pulse.consulting_room.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.pulse.consulting_room.persist.dos.ConsultingRoom;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "3d84e795-2f80-46c7-87f8-d5ddfcdecd73|DTO|BASE_CONVERTER")
public class ConsultingRoomBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ConsultingRoomBaseDto convertFromConsultingRoomToConsultingRoomBaseDto(
            ConsultingRoom consultingRoom) {
        return convertFromConsultingRoomToConsultingRoomBaseDto(List.of(consultingRoom)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ConsultingRoomBaseDto> convertFromConsultingRoomToConsultingRoomBaseDto(
            List<ConsultingRoom> consultingRoomList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(consultingRoomList)) {
            return new ArrayList<>();
        }
        List<ConsultingRoomBaseDto> consultingRoomBaseDtoList = new ArrayList<>();
        for (ConsultingRoom consultingRoom : consultingRoomList) {
            if (consultingRoom == null) {
                continue;
            }
            ConsultingRoomBaseDto consultingRoomBaseDto = new ConsultingRoomBaseDto();
            consultingRoomBaseDto.setId(consultingRoom.getId());
            consultingRoomBaseDto.setShortCode(consultingRoom.getShortCode());
            consultingRoomBaseDto.setCode(consultingRoom.getCode());
            consultingRoomBaseDto.setClinicRoomNumber(consultingRoom.getClinicRoomNumber());
            consultingRoomBaseDto.setCampusOrganizationId(consultingRoom.getCampusOrganizationId());
            consultingRoomBaseDto.setFloorUnit(consultingRoom.getFloorUnit());
            consultingRoomBaseDto.setArea(consultingRoom.getArea());
            consultingRoomBaseDto.setAreaEnglishName(consultingRoom.getAreaEnglishName());
            consultingRoomBaseDto.setIp(consultingRoom.getIp());
            consultingRoomBaseDto.setProperty(consultingRoom.getProperty());
            consultingRoomBaseDto.setDisplayName(consultingRoom.getDisplayName());
            consultingRoomBaseDto.setInputCode(consultingRoom.getInputCode());
            consultingRoomBaseDto.setName(consultingRoom.getName());
            consultingRoomBaseDto.setInvalidFlag(consultingRoom.getInvalidFlag());
            consultingRoomBaseDto.setInvalidReason(consultingRoom.getInvalidReason());
            consultingRoomBaseDto.setCreatedBy(consultingRoom.getCreatedBy());
            consultingRoomBaseDto.setUpdatedBy(consultingRoom.getUpdatedBy());
            consultingRoomBaseDto.setLockVersion(consultingRoom.getLockVersion());
            consultingRoomBaseDto.setCreatedAt(consultingRoom.getCreatedAt());
            consultingRoomBaseDto.setUpdatedAt(consultingRoom.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            consultingRoomBaseDtoList.add(consultingRoomBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return consultingRoomBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
