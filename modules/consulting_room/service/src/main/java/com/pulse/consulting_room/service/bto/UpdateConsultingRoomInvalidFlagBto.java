package com.pulse.consulting_room.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> ConsultingRoom
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "8c7975e8-bc2b-43d2-aff0-183ede5fa952|BTO|DEFINITION")
public class UpdateConsultingRoomInvalidFlagBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3c5aed45-df05-4d1e-bde9-8fc9221ae69b")
    private String id;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "688b3a78-0a3a-40a5-8922-72eab6b1c5a9")
    private Boolean invalidFlag;

    /** 作废原因 */
    @AutoGenerated(locked = true, uuid = "a5bce2e6-ea35-4755-ab59-3ca440d5a40e")
    private String invalidReason;

    /** 修改者id */
    @AutoGenerated(locked = true, uuid = "e0710aba-1014-4dfb-a2e7-31aee5f0abc2")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInvalidFlag(Boolean invalidFlag) {
        this.__$validPropertySet.add("invalidFlag");
        this.invalidFlag = invalidFlag;
    }

    @AutoGenerated(locked = true)
    public void setInvalidReason(String invalidReason) {
        this.__$validPropertySet.add("invalidReason");
        this.invalidReason = invalidReason;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
