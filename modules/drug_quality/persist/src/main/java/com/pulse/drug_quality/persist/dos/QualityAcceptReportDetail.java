package com.pulse.drug_quality.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "quality_accept_report_detail", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "1fa319f6-4b17-3ef1-b667-25a16935d193|ENTITY|DEFINITION")
public class QualityAcceptReportDetail {
    @AutoGenerated(locked = true, uuid = "75e4a32b-61bb-3753-bb1b-fc449d4c091d")
    @TableField(value = "accept_amount")
    private Long acceptAmount;

    @AutoGenerated(locked = true, uuid = "d5f8c437-8744-38a6-a9b5-fb5d3bdadc18")
    @TableField(value = "accept_conclusions")
    private String acceptConclusions;

    @AutoGenerated(locked = true, uuid = "5f0c3da9-bbab-3103-a94f-6ec97d89b563")
    @TableField(value = "accept_report_number")
    private String acceptReportNumber;

    @AutoGenerated(locked = true, uuid = "404fbeb9-ba19-3a4a-8a5a-4203d236eed2")
    @TableField(value = "appearance_quality")
    private String appearanceQuality;

    @AutoGenerated(locked = true, uuid = "e36b728a-649c-3872-9d08-b51ba3580999")
    @TableField(value = "approval_number")
    private String approvalNumber;

    @AutoGenerated(locked = true, uuid = "b7091fb1-4649-330f-9c90-3a11ae824561")
    @TableField(value = "arrival_amount")
    private Long arrivalAmount;

    @AutoGenerated(locked = true, uuid = "74b2e785-9964-31c1-9f4c-21f31f57ce8f")
    @TableField(value = "arrival_temperature")
    private String arrivalTemperature;

    @AutoGenerated(locked = true, uuid = "13538f62-3af5-3dd1-951e-316b5fbd4a83")
    @TableField(value = "batch_issuance_certificate_number")
    private String batchIssuanceCertificateNumber;

    @AutoGenerated(locked = true, uuid = "44bc165b-2efd-30e6-a1a4-d68ab4147fa4")
    @TableField(value = "batch_number")
    private String batchNumber;

    @AutoGenerated(locked = true, uuid = "0ceb6405-c5b5-35da-91af-02c2f979ee05")
    @TableField(value = "clarity_check_results")
    private String clarityCheckResults;

    @AutoGenerated(locked = true, uuid = "b5656a92-e4e0-31c2-99ab-560dbd009366")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "515ffab0-0977-399b-ba57-1604345517c8")
    @TableField(value = "cultivation_place")
    private String cultivationPlace;

    @AutoGenerated(locked = true, uuid = "18282940-9c81-3ee8-97a6-aa478a9c1077")
    @TableField(value = "damage_amount")
    private Long damageAmount;

    @AutoGenerated(locked = true, uuid = "81aff012-c963-3064-b00a-6ed1e7decba9")
    @TableField(value = "dispose_condition")
    private String disposeCondition;

    @AutoGenerated(locked = true, uuid = "a415f5f5-8840-3aff-a8f8-3742c1b58d52")
    @TableField(value = "drug_batch_inventory_id")
    private String drugBatchInventoryId;

    @AutoGenerated(locked = true, uuid = "4ae1f3fc-aed3-3eae-9747-b1088404cf0a")
    @TableField(value = "drug_import_detail_id")
    private String drugImportDetailId;

    @AutoGenerated(locked = true, uuid = "dbfbc78e-a321-312d-9524-8972889d05d2")
    @TableField(value = "drug_product_name")
    private String drugProductName;

    @AutoGenerated(locked = true, uuid = "514191b3-b404-348e-bb07-8a50b8b4bfbb")
    @TableField(value = "drug_product_specification_id")
    private String drugProductSpecificationId;

    @AutoGenerated(locked = true, uuid = "ff4ffb41-5c61-3bfc-8b2b-856a0609fc45")
    @TableField(value = "drug_shape")
    private String drugShape;

    @AutoGenerated(locked = true, uuid = "b9fbbc41-1355-3d55-ada5-4272418ef36c")
    @TableField(value = "expiration_date")
    private Date expirationDate;

    @AutoGenerated(locked = true, uuid = "2a1009e8-56cf-3714-90b2-f25344bb5843")
    @TableField(value = "factory_inspection_certificate")
    private String factoryInspectionCertificate;

    @AutoGenerated(locked = true, uuid = "131df674-b3b3-399e-87e3-aa139efc7aa1")
    @TableField(value = "firm_id")
    private String firmId;

    @AutoGenerated(locked = true, uuid = "508ae93d-6688-36a3-8f85-ad4e33d7b7c9")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "fa66f716-61b9-3866-b464-783c48e3f618")
    @TableField(value = "imported_number")
    private String importedNumber;

    @AutoGenerated(locked = true, uuid = "ee41f0e4-76f3-35a3-9857-0f3acdf338da")
    @TableField(value = "invoice_code")
    private String invoiceCode;

    @AutoGenerated(locked = true, uuid = "65a160e2-d9b8-369d-b03c-493f5a46ba12")
    @TableField(value = "invoice_date_time")
    private Date invoiceDateTime;

    @AutoGenerated(locked = true, uuid = "94f1f761-073e-3a1a-a8a8-f66423a8a7f4")
    @TableField(value = "manufacture_date_time")
    private Date manufactureDateTime;

    @AutoGenerated(locked = true, uuid = "003dedc6-065e-3f3a-8fb4-f8caf7455c90")
    @TableField(value = "origin_province")
    private String originProvince;

    @AutoGenerated(locked = true, uuid = "b84b879d-17a3-355a-9e59-87d5575c0079")
    @TableField(value = "package_condition")
    private String packageCondition;

    @AutoGenerated(locked = true, uuid = "36a3d7af-4aa6-35c8-99b1-6a9f46a72550")
    @TableField(value = "port_inspection_report")
    private String portInspectionReport;

    @AutoGenerated(locked = true, uuid = "6675a171-3c1c-313d-b1e3-ca6d32da5229")
    @TableField(value = "purchase_staff_id")
    private String purchaseStaffId;

    @AutoGenerated(locked = true, uuid = "6c797faf-c240-3831-b313-72d8414a5fc1")
    @TableField(value = "registered_trademark")
    private String registeredTrademark;

    @AutoGenerated(locked = true, uuid = "373f701e-8c43-3f9f-89f8-1b71a1f832ec")
    @TableField(value = "registration_number")
    private String registrationNumber;

    @AutoGenerated(locked = true, uuid = "f413fcec-6143-3965-ac39-585cafd60cd6")
    @TableField(value = "report_id")
    private String reportId;

    @AutoGenerated(locked = true, uuid = "9fc9b733-a7a0-362c-9df2-ed23b5034b6e")
    @TableField(value = "retail_cost")
    private BigDecimal retailCost;

    @AutoGenerated(locked = true, uuid = "044845e2-a6b1-30c5-b0ff-ce4d6af4a535")
    @TableField(value = "retail_price")
    private BigDecimal retailPrice;

    @AutoGenerated(locked = true, uuid = "78f63829-86c2-3eb6-8231-5da965960dd5")
    @TableField(value = "sort_number")
    private Long sortNumber;

    @AutoGenerated(locked = true, uuid = "87b47a2c-270e-32c7-b571-2f6e6b160abf")
    @TableField(value = "stock_cost")
    private BigDecimal stockCost;

    @AutoGenerated(locked = true, uuid = "424631bc-5615-3120-ae03-515d47f6c9d5")
    @TableField(value = "stock_price")
    private BigDecimal stockPrice;

    @AutoGenerated(locked = true, uuid = "e03a8c7f-0b67-3225-a4ab-7a096771c4f1")
    @TableField(value = "supplier_id")
    private String supplierId;

    @AutoGenerated(locked = true, uuid = "b18a125a-0651-355d-a563-d0e5d5d4d239")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
