package com.pulse.drug_quality.persist.mapper;

import com.pulse.drug_quality.persist.dos.DrugMaintenance;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "59e1aa4b-889f-3809-bb89-dd921eade192|ENTITY|IDAO")
public interface DrugMaintenanceDao {

    @AutoGenerated(locked = true, uuid = "05862c42-5d8e-3ff9-82b4-5811888bb21c")
    List<DrugMaintenance> getByDrugProductSpecificationId(String drugProductSpecificationId);

    @AutoGenerated(locked = true, uuid = "07f8a303-0046-3397-b825-4a9018346d17")
    List<DrugMaintenance> getByStaffs(List<String> staff);

    @AutoGenerated(locked = true, uuid = "12773f11-709f-3231-82d4-351e2b0e7347")
    List<DrugMaintenance> getByReviewers(List<String> reviewer);

    @AutoGenerated(locked = true, uuid = "2c32d60b-af5f-355d-935f-0d594517e472")
    List<DrugMaintenance> getByDrugProductSpecificationIds(List<String> drugProductSpecificationId);

    @AutoGenerated(locked = true, uuid = "39bdd0b9-6515-3f3a-a09e-2daa3fe316e7")
    List<DrugMaintenance> getByDrugOriginCodes(List<String> drugOriginCode);

    @AutoGenerated(locked = true, uuid = "43a7f62f-e86c-381b-afc0-3edd1acf1f58")
    List<DrugMaintenance> getByDrugStockId(String drugStockId);

    @AutoGenerated(locked = true, uuid = "4a9404c6-5e31-37e5-8d53-53dc74c435b6")
    List<DrugMaintenance> getByDepartmentId(String departmentId);

    @AutoGenerated(locked = true, uuid = "7c46a03b-139d-3531-91a9-47915132171d")
    List<DrugMaintenance> getByDrugOriginCode(String drugOriginCode);

    @AutoGenerated(locked = true, uuid = "87d378c2-99a5-37e0-a53e-a8aaffc8ef27")
    DrugMaintenance getById(String id);

    @AutoGenerated(locked = true, uuid = "949a8754-bf27-37a6-bbb0-b8aea0008dc4")
    List<DrugMaintenance> getByReviewer(String reviewer);

    @AutoGenerated(locked = true, uuid = "c0d61c4d-9464-3e0f-8f46-e8952edf5247")
    List<DrugMaintenance> getByDrugStockIds(List<String> drugStockId);

    @AutoGenerated(locked = true, uuid = "f5df3076-b6f7-34d3-9171-d309f4ed8d35")
    List<DrugMaintenance> getByStaff(String staff);

    @AutoGenerated(locked = true, uuid = "fb83c532-1b0a-3869-a6c6-09a49c7cf427")
    List<DrugMaintenance> getByDepartmentIds(List<String> departmentId);

    @AutoGenerated(locked = true, uuid = "fe7a6123-376d-3228-a6a8-f929c40f1bca")
    List<DrugMaintenance> getByIds(List<String> id);
}
