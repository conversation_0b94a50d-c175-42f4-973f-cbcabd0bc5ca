package com.pulse.drug_quality.manager.dto;

import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "26982af8-ca65-4622-af22-3b7b33f7ebfd|DTO|DEFINITION")
public class QualityAcceptReportForDetailDto {
    /** 验收日期 */
    @AutoGenerated(locked = true, uuid = "59c997fd-b501-4495-aa6b-7535ef002683")
    private Date acceptDateTime;

    /** 验收单号 */
    @AutoGenerated(locked = true, uuid = "cacfb49c-dd0a-49ff-a817-6b9b761a4b65")
    private String acceptNumber;

    /** 验收人 */
    @AutoGenerated(locked = true, uuid = "9cfd21f6-0d80-45e2-acbf-7ca7875de32e")
    private String acceptStaffId;

    /** 单据类型 */
    @AutoGenerated(locked = true, uuid = "89b38906-4d42-463b-bd0a-49c7aee90736")
    private String acceptType;

    /** 入库记账状态 */
    @AutoGenerated(locked = true, uuid = "74563b5b-f5f8-437a-89f8-ff74c2988c0c")
    private Boolean accountantFlag;

    /** 到货日期 */
    @AutoGenerated(locked = true, uuid = "96b03163-3731-48a9-8e05-d705bfa54c83")
    private Date arrivalDateTime;

    /** 审核日期 */
    @AutoGenerated(locked = true, uuid = "5e06ddf2-0ef9-48e7-90ac-e1b9c99f4ec9")
    private Date auditDateTime;

    /** 审核人 */
    @AutoGenerated(locked = true, uuid = "cdff8da3-21f9-45e9-9a4c-9486ca856fb2")
    private String auditStaffId;

    /** 验收复核人 */
    @AutoGenerated(locked = true, uuid = "861c097c-af64-4c07-9ac7-e3cbd48b9d6a")
    private String checkStaffId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "252df0f3-07f5-403b-84e1-cd35862c4e63")
    private Date createdAt;

    /** 制单人 */
    @AutoGenerated(locked = true, uuid = "191e1bce-7b5e-4086-ad4b-ec931f591450")
    private String createdBy;

    /** 入库单id */
    @AutoGenerated(locked = true, uuid = "c8835178-ff6c-4f07-ad7f-f45f9c7a52db")
    private String drugImportId;

    /** 出入库方式 */
    @AutoGenerated(locked = true, uuid = "*************-45f2-a35b-548cac1b1d5a")
    private String exportImportCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2051f269-c553-451b-850b-3b6e2a3df95a")
    private String id;

    /** 入库次数 */
    @AutoGenerated(locked = true, uuid = "16967f6a-4156-495b-9510-783f623b6ad9")
    private Long importCount;

    /** 入库日期 */
    @AutoGenerated(locked = true, uuid = "18a88c07-af26-4b3d-9415-831b57037912")
    private Date importDateTime;

    /** 付款标志 */
    @AutoGenerated(locked = true, uuid = "ea3d7fee-226c-427c-86c2-2ea1c40b30eb")
    private Boolean payFlag;

    /** 质量验收单明细（详情用） */
    @Valid
    @AutoGenerated(locked = true, uuid = "4ff28d24-921d-43a9-a024-f6fc4f0a719e")
    private List<QualityAcceptReportDetailForDetailDto> qualityAcceptReportDetailForDetailList;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "c17bbdc1-5a28-4d27-850c-01d9e42c507a")
    private String remark;

    /** 零售金额 */
    @AutoGenerated(locked = true, uuid = "6802c52c-346f-4940-9142-abb565f18968")
    private BigDecimal retailCost;

    /** 结算单id */
    @AutoGenerated(locked = true, uuid = "bf19cf91-96f8-49fc-bd94-08606ef3bd5c")
    private String settleId;

    /** 进价金额 */
    @AutoGenerated(locked = true, uuid = "2cdb8d48-d91d-4de3-a0b6-ae6a424ef29b")
    private BigDecimal stockCost;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "59992266-b9fd-451d-8797-69e003ad5895")
    private String storageCode;

    /** 保管员 */
    @AutoGenerated(locked = true, uuid = "d4c0b105-71ce-47a8-b97b-5efb755c12c8")
    private String storekeeperId;

    /** 供货单位 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2d4297ec-fe50-4c31-b5a0-222c5aa78d5e")
    private DrugProducerDictionaryBaseDto supplier;
}
