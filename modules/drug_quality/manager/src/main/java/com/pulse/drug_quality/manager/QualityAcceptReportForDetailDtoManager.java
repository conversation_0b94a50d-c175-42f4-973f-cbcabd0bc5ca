package com.pulse.drug_quality.manager;

import com.pulse.drug_quality.manager.dto.QualityAcceptReportForDetailDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "26982af8-ca65-4622-af22-3b7b33f7ebfd|DTO|MANAGER")
public interface QualityAcceptReportForDetailDtoManager {

    @AutoGenerated(locked = true, uuid = "0d003b3b-8a53-37b2-b26d-511e09b2df9f")
    List<QualityAcceptReportForDetailDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "407658a8-f9df-37fc-b724-b06c0ada3adc")
    QualityAcceptReportForDetailDto getById(String id);

    @AutoGenerated(locked = true, uuid = "bf2e9306-d347-39c7-9592-02603bfde8f9")
    List<QualityAcceptReportForDetailDto> getByStorageCodes(List<String> storageCode);

    @AutoGenerated(locked = true, uuid = "c6f4f915-f474-3274-b03f-d43d25c5401b")
    List<QualityAcceptReportForDetailDto> getBySupplierId(String supplierId);

    @AutoGenerated(locked = true, uuid = "f98c6071-c280-3057-89fe-93d4f7cde8c6")
    List<QualityAcceptReportForDetailDto> getByStorageCode(String storageCode);

    @AutoGenerated(locked = true, uuid = "fadc777b-fba6-30aa-869c-cba459a403d2")
    List<QualityAcceptReportForDetailDto> getBySupplierIds(List<String> supplierId);
}
