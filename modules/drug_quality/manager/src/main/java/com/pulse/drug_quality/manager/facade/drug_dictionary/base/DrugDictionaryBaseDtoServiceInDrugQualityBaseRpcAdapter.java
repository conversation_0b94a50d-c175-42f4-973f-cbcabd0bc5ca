package com.pulse.drug_quality.manager.facade.drug_dictionary.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "05ad19ec-6801-347f-803b-9134fd31aeb1")
public class DrugDictionaryBaseDtoServiceInDrugQualityBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "d2dc39da-0554-44c6-bbeb-f11a2e28fe31|RPC|BASE_ADAPTER")
    public List<DrugDictionaryBaseDto> getByDrugCodes(List<String> drugCode) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("drug_code", drugCode);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("drug_code", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/d2dc39da-0554-44c6-bbeb-f11a2e28fe31/DrugDictionaryBaseDtoService-getByDrugCodes",
                        "com.pulse.drug_dictionary.service.DrugDictionaryBaseDtoService",
                        "getByDrugCodes",
                        paramMap,
                        paramTypeMap,
                        "81481612-491d-48b2-8a02-e63c295e8486",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }
}
