package com.pulse.drug_quality.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> QualityAcceptReport
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "0a793d92-f3dc-4f3a-a1e7-5eb8255546dd|BTO|DEFINITION")
public class CreateQualityAcceptReportBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 验收日期 */
    @AutoGenerated(locked = true, uuid = "f3c89e05-2aee-441d-957f-f197663fcf3f")
    private Date acceptDateTime;

    /** 验收单号 */
    @AutoGenerated(locked = true, uuid = "dfb1a5f2-9d1e-4022-be1c-85ce031c8f1b")
    private String acceptNumber;

    /** 验收人 */
    @AutoGenerated(locked = true, uuid = "5cebd8f9-92ab-4b89-803c-735171f95609")
    private String acceptStaffId;

    /** 单据类型 */
    @AutoGenerated(locked = true, uuid = "9b7be3a8-bb29-49e2-9fa8-41d7d70f5073")
    private String acceptType;

    /** 入库记账状态 */
    @AutoGenerated(locked = true, uuid = "94d16ee6-cea9-4cda-a981-1305eabdd621")
    private Boolean accountantFlag;

    /** 审核人 */
    @AutoGenerated(locked = true, uuid = "ac4e31c3-a67c-4f93-afd0-d53ce53434f1")
    private String auditStaffId;

    /** 验收复核人 */
    @AutoGenerated(locked = true, uuid = "87fed3d2-ab11-4b4d-aab8-df7abb3b5cc3")
    private String checkStaffId;

    /** 制单人 */
    @AutoGenerated(locked = true, uuid = "580f2f0f-09fa-4dcd-b2bd-1c5514abe2db")
    private String createdBy;

    /** 入库单id */
    @AutoGenerated(locked = true, uuid = "ba2cb192-64d0-45f2-bfe1-e47fb41c2357")
    private String drugImportId;

    /** 出入库方式 */
    @AutoGenerated(locked = true, uuid = "2e247fd1-9172-4bbb-9cb5-19bd5860eb12")
    private String exportImportCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "d39db1c5-188d-4089-a514-d0e4de344f4a")
    private String id;

    /** 入库日期 */
    @AutoGenerated(locked = true, uuid = "55e073ae-e697-4459-8a97-4eabb252a3c9")
    private Date importDateTime;

    @Valid
    @AutoGenerated(locked = true, uuid = "da062b6e-f5a9-4b79-baa2-28ae38ec6885")
    private List<CreateQualityAcceptReportBto.QualityAcceptReportDetailBto>
            qualityAcceptReportDetailBtoList;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "01ace3ef-f388-4bdd-b4a9-0bd330fef4cf")
    private String remark;

    /** 零售金额 */
    @AutoGenerated(locked = true, uuid = "056f3b49-7682-44d7-bc89-aa1496dd95a7")
    private BigDecimal retailCost;

    /** 进价金额 */
    @AutoGenerated(locked = true, uuid = "1ed2105e-3f92-4a8f-a3dd-6a1ae07cf071")
    private BigDecimal stockCost;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "4b76231a-c83d-4ed4-b13f-fb71ef879534")
    private String storageCode;

    /** 供货单位 */
    @AutoGenerated(locked = true, uuid = "dd0d2bc8-386c-437f-9a5e-fdc0e8daff7c")
    private String supplierId;

    @AutoGenerated(locked = true)
    public void setAcceptDateTime(Date acceptDateTime) {
        this.__$validPropertySet.add("acceptDateTime");
        this.acceptDateTime = acceptDateTime;
    }

    @AutoGenerated(locked = true)
    public void setAcceptNumber(String acceptNumber) {
        this.__$validPropertySet.add("acceptNumber");
        this.acceptNumber = acceptNumber;
    }

    @AutoGenerated(locked = true)
    public void setAcceptStaffId(String acceptStaffId) {
        this.__$validPropertySet.add("acceptStaffId");
        this.acceptStaffId = acceptStaffId;
    }

    @AutoGenerated(locked = true)
    public void setAcceptType(String acceptType) {
        this.__$validPropertySet.add("acceptType");
        this.acceptType = acceptType;
    }

    @AutoGenerated(locked = true)
    public void setAccountantFlag(Boolean accountantFlag) {
        this.__$validPropertySet.add("accountantFlag");
        this.accountantFlag = accountantFlag;
    }

    @AutoGenerated(locked = true)
    public void setAuditStaffId(String auditStaffId) {
        this.__$validPropertySet.add("auditStaffId");
        this.auditStaffId = auditStaffId;
    }

    @AutoGenerated(locked = true)
    public void setCheckStaffId(String checkStaffId) {
        this.__$validPropertySet.add("checkStaffId");
        this.checkStaffId = checkStaffId;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDrugImportId(String drugImportId) {
        this.__$validPropertySet.add("drugImportId");
        this.drugImportId = drugImportId;
    }

    @AutoGenerated(locked = true)
    public void setExportImportCode(String exportImportCode) {
        this.__$validPropertySet.add("exportImportCode");
        this.exportImportCode = exportImportCode;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setImportDateTime(Date importDateTime) {
        this.__$validPropertySet.add("importDateTime");
        this.importDateTime = importDateTime;
    }

    @AutoGenerated(locked = true)
    public void setQualityAcceptReportDetailBtoList(
            List<CreateQualityAcceptReportBto.QualityAcceptReportDetailBto>
                    qualityAcceptReportDetailBtoList) {
        this.__$validPropertySet.add("qualityAcceptReportDetailBtoList");
        this.qualityAcceptReportDetailBtoList = qualityAcceptReportDetailBtoList;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setRetailCost(BigDecimal retailCost) {
        this.__$validPropertySet.add("retailCost");
        this.retailCost = retailCost;
    }

    @AutoGenerated(locked = true)
    public void setStockCost(BigDecimal stockCost) {
        this.__$validPropertySet.add("stockCost");
        this.stockCost = stockCost;
    }

    @AutoGenerated(locked = true)
    public void setStorageCode(String storageCode) {
        this.__$validPropertySet.add("storageCode");
        this.storageCode = storageCode;
    }

    @AutoGenerated(locked = true)
    public void setSupplierId(String supplierId) {
        this.__$validPropertySet.add("supplierId");
        this.supplierId = supplierId;
    }

    /**
     * <b>[源自]</b> QualityAcceptReportDetail
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class QualityAcceptReportDetailBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "7410e069-7aae-465c-b086-3d8b3d4c40aa")
        private String id;

        /** 药品产地规格id */
        @AutoGenerated(locked = true, uuid = "fd844458-8de4-4dcf-9cf0-2e7e97deff13")
        private String drugProductSpecificationId;

        /** 药品名称 */
        @AutoGenerated(locked = true, uuid = "d04598b6-caef-4d5b-816d-44a417b47940")
        private String drugProductName;

        /** 入库单明细id */
        @AutoGenerated(locked = true, uuid = "9c3bbe3d-b188-4472-bd12-6637fa000bcb")
        private String drugImportDetailId;

        /** 批次库存 */
        @AutoGenerated(locked = true, uuid = "3d5f5070-3b16-4f07-b006-8fe5cdea38f7")
        private String drugBatchInventoryId;

        /** 来货数量 */
        @AutoGenerated(locked = true, uuid = "b2c4d2db-093e-4ed5-b6ba-113052be98d8")
        private Long arrivalAmount;

        /** 验收数量 */
        @AutoGenerated(locked = true, uuid = "30da0404-3943-4168-9fea-476b9092e9e7")
        private Long acceptAmount;

        /** 破损数量 */
        @AutoGenerated(locked = true, uuid = "362ee58c-cc68-49af-baeb-284f265ffd5e")
        private Long damageAmount;

        /** 批号 */
        @AutoGenerated(locked = true, uuid = "19ea2b82-efd4-4b5e-96a8-704aca10a8e4")
        private String batchNumber;

        /** 药品效期 */
        @AutoGenerated(locked = true, uuid = "2b727eb3-4bbf-4c12-9f93-b1e20e73fd07")
        private Date expirationDate;

        /** 批准文号 */
        @AutoGenerated(locked = true, uuid = "db6a3555-da52-40c0-b6ea-578a0fae6590")
        private String approvalNumber;

        /** 生产日期 */
        @AutoGenerated(locked = true, uuid = "00e95b8f-9d07-4aea-acc9-a1b647f250c9")
        private Date manufactureDateTime;

        /** 生产厂商 */
        @AutoGenerated(locked = true, uuid = "7f7ef650-b540-4b4b-abcd-960cba5f9f0b")
        private String firmId;

        /** 供货单位 */
        @AutoGenerated(locked = true, uuid = "0d8677dc-3cf2-433b-9a98-e1594b31c5a7")
        private String supplierId;

        /** 进口药品证号 */
        @AutoGenerated(locked = true, uuid = "42822fe0-4240-4e2c-8c30-c4622062bfc7")
        private String importedNumber;

        /** 注册商标 */
        @AutoGenerated(locked = true, uuid = "c48f7785-c21b-4dbf-b144-22340059514e")
        private String registeredTrademark;

        /** 出厂检验合格单 */
        @AutoGenerated(locked = true, uuid = "8e514c4e-33cf-4e44-8869-10fc595c41d0")
        private String factoryInspectionCertificate;

        /** 包装情况 */
        @AutoGenerated(locked = true, uuid = "1b976526-ed1f-48df-b378-6bcc3cf9f2c9")
        private String packageCondition;

        /** 外观质量 */
        @AutoGenerated(locked = true, uuid = "ead996de-b8db-4348-b32e-3ea7a0ef3bdb")
        private String appearanceQuality;

        /** 验收结论 */
        @AutoGenerated(locked = true, uuid = "f5b54054-9606-4cb2-9c4e-1d60648396bb")
        private String acceptConclusions;

        /** 发票号码 */
        @AutoGenerated(locked = true, uuid = "a62f2f7d-ee5b-4dcd-b732-5371a5d1ef5c")
        private String invoiceCode;

        /** 发票日期 */
        @AutoGenerated(locked = true, uuid = "c74b8993-5fd0-42f0-affd-453f4c147b82")
        private Date invoiceDateTime;

        /** 检验报告书编号 */
        @AutoGenerated(locked = true, uuid = "032d7efd-8103-4e6a-b038-05f3e3835774")
        private String acceptReportNumber;

        /** 注册证号 */
        @AutoGenerated(locked = true, uuid = "c0484ae2-8d4d-4d2c-9450-ef7756fcc5f0")
        private String registrationNumber;

        /** 口岸检验报告 */
        @AutoGenerated(locked = true, uuid = "636434fe-0d05-4e9a-9bb8-650007ea7f08")
        private String portInspectionReport;

        /** 澄明度检查结果 */
        @AutoGenerated(locked = true, uuid = "de1bae7d-fb73-46b3-a7d3-18fbc12843c6")
        private String clarityCheckResults;

        /** 处理情况 */
        @AutoGenerated(locked = true, uuid = "558ba9c1-bfc9-4513-a606-e2329c46ddbd")
        private String disposeCondition;

        /** 采购员 */
        @AutoGenerated(locked = true, uuid = "c2493d7c-c856-4240-97fe-ab32d7d2fbec")
        private String purchaseStaffId;

        /** 种植地 */
        @AutoGenerated(locked = true, uuid = "df4da0f4-e214-4460-a25b-41083cb32ee4")
        private String cultivationPlace;

        /** 药品形状 */
        @AutoGenerated(locked = true, uuid = "0b3d5b03-f1bf-4488-9911-95d3301c0831")
        private String drugShape;

        /** 产地省份 */
        @AutoGenerated(locked = true, uuid = "34406551-3f2f-4008-b4c5-c310038d134b")
        private String originProvince;

        /** 到达温度 */
        @AutoGenerated(locked = true, uuid = "50fbda15-f503-497a-af9c-eee85f593447")
        private String arrivalTemperature;

        /** 批签发合格证编号 */
        @AutoGenerated(locked = true, uuid = "426c8b5b-72f0-480a-a6a7-a5134b3ef947")
        private String batchIssuanceCertificateNumber;

        /** 进价 */
        @AutoGenerated(locked = true, uuid = "1e6a64b9-e726-44b2-8572-5757f5a65ef9")
        private BigDecimal stockPrice;

        /** 进价金额 */
        @AutoGenerated(locked = true, uuid = "52bb05cd-00a1-4158-8c6f-20aefa394e20")
        private BigDecimal stockCost;

        /** 进货零售价 */
        @AutoGenerated(locked = true, uuid = "bf4bca53-4fea-402d-b3c3-9349c60fb88f")
        private BigDecimal retailPrice;

        /** 进货零售金额 */
        @AutoGenerated(locked = true, uuid = "b09810cd-8640-420e-a7b6-64aaaab7f6b8")
        private BigDecimal retailCost;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setDrugProductSpecificationId(String drugProductSpecificationId) {
            this.__$validPropertySet.add("drugProductSpecificationId");
            this.drugProductSpecificationId = drugProductSpecificationId;
        }

        @AutoGenerated(locked = true)
        public void setDrugProductName(String drugProductName) {
            this.__$validPropertySet.add("drugProductName");
            this.drugProductName = drugProductName;
        }

        @AutoGenerated(locked = true)
        public void setDrugImportDetailId(String drugImportDetailId) {
            this.__$validPropertySet.add("drugImportDetailId");
            this.drugImportDetailId = drugImportDetailId;
        }

        @AutoGenerated(locked = true)
        public void setDrugBatchInventoryId(String drugBatchInventoryId) {
            this.__$validPropertySet.add("drugBatchInventoryId");
            this.drugBatchInventoryId = drugBatchInventoryId;
        }

        @AutoGenerated(locked = true)
        public void setArrivalAmount(Long arrivalAmount) {
            this.__$validPropertySet.add("arrivalAmount");
            this.arrivalAmount = arrivalAmount;
        }

        @AutoGenerated(locked = true)
        public void setAcceptAmount(Long acceptAmount) {
            this.__$validPropertySet.add("acceptAmount");
            this.acceptAmount = acceptAmount;
        }

        @AutoGenerated(locked = true)
        public void setDamageAmount(Long damageAmount) {
            this.__$validPropertySet.add("damageAmount");
            this.damageAmount = damageAmount;
        }

        @AutoGenerated(locked = true)
        public void setBatchNumber(String batchNumber) {
            this.__$validPropertySet.add("batchNumber");
            this.batchNumber = batchNumber;
        }

        @AutoGenerated(locked = true)
        public void setExpirationDate(Date expirationDate) {
            this.__$validPropertySet.add("expirationDate");
            this.expirationDate = expirationDate;
        }

        @AutoGenerated(locked = true)
        public void setApprovalNumber(String approvalNumber) {
            this.__$validPropertySet.add("approvalNumber");
            this.approvalNumber = approvalNumber;
        }

        @AutoGenerated(locked = true)
        public void setManufactureDateTime(Date manufactureDateTime) {
            this.__$validPropertySet.add("manufactureDateTime");
            this.manufactureDateTime = manufactureDateTime;
        }

        @AutoGenerated(locked = true)
        public void setFirmId(String firmId) {
            this.__$validPropertySet.add("firmId");
            this.firmId = firmId;
        }

        @AutoGenerated(locked = true)
        public void setSupplierId(String supplierId) {
            this.__$validPropertySet.add("supplierId");
            this.supplierId = supplierId;
        }

        @AutoGenerated(locked = true)
        public void setImportedNumber(String importedNumber) {
            this.__$validPropertySet.add("importedNumber");
            this.importedNumber = importedNumber;
        }

        @AutoGenerated(locked = true)
        public void setRegisteredTrademark(String registeredTrademark) {
            this.__$validPropertySet.add("registeredTrademark");
            this.registeredTrademark = registeredTrademark;
        }

        @AutoGenerated(locked = true)
        public void setFactoryInspectionCertificate(String factoryInspectionCertificate) {
            this.__$validPropertySet.add("factoryInspectionCertificate");
            this.factoryInspectionCertificate = factoryInspectionCertificate;
        }

        @AutoGenerated(locked = true)
        public void setPackageCondition(String packageCondition) {
            this.__$validPropertySet.add("packageCondition");
            this.packageCondition = packageCondition;
        }

        @AutoGenerated(locked = true)
        public void setAppearanceQuality(String appearanceQuality) {
            this.__$validPropertySet.add("appearanceQuality");
            this.appearanceQuality = appearanceQuality;
        }

        @AutoGenerated(locked = true)
        public void setAcceptConclusions(String acceptConclusions) {
            this.__$validPropertySet.add("acceptConclusions");
            this.acceptConclusions = acceptConclusions;
        }

        @AutoGenerated(locked = true)
        public void setInvoiceCode(String invoiceCode) {
            this.__$validPropertySet.add("invoiceCode");
            this.invoiceCode = invoiceCode;
        }

        @AutoGenerated(locked = true)
        public void setInvoiceDateTime(Date invoiceDateTime) {
            this.__$validPropertySet.add("invoiceDateTime");
            this.invoiceDateTime = invoiceDateTime;
        }

        @AutoGenerated(locked = true)
        public void setAcceptReportNumber(String acceptReportNumber) {
            this.__$validPropertySet.add("acceptReportNumber");
            this.acceptReportNumber = acceptReportNumber;
        }

        @AutoGenerated(locked = true)
        public void setRegistrationNumber(String registrationNumber) {
            this.__$validPropertySet.add("registrationNumber");
            this.registrationNumber = registrationNumber;
        }

        @AutoGenerated(locked = true)
        public void setPortInspectionReport(String portInspectionReport) {
            this.__$validPropertySet.add("portInspectionReport");
            this.portInspectionReport = portInspectionReport;
        }

        @AutoGenerated(locked = true)
        public void setClarityCheckResults(String clarityCheckResults) {
            this.__$validPropertySet.add("clarityCheckResults");
            this.clarityCheckResults = clarityCheckResults;
        }

        @AutoGenerated(locked = true)
        public void setDisposeCondition(String disposeCondition) {
            this.__$validPropertySet.add("disposeCondition");
            this.disposeCondition = disposeCondition;
        }

        @AutoGenerated(locked = true)
        public void setPurchaseStaffId(String purchaseStaffId) {
            this.__$validPropertySet.add("purchaseStaffId");
            this.purchaseStaffId = purchaseStaffId;
        }

        @AutoGenerated(locked = true)
        public void setCultivationPlace(String cultivationPlace) {
            this.__$validPropertySet.add("cultivationPlace");
            this.cultivationPlace = cultivationPlace;
        }

        @AutoGenerated(locked = true)
        public void setDrugShape(String drugShape) {
            this.__$validPropertySet.add("drugShape");
            this.drugShape = drugShape;
        }

        @AutoGenerated(locked = true)
        public void setOriginProvince(String originProvince) {
            this.__$validPropertySet.add("originProvince");
            this.originProvince = originProvince;
        }

        @AutoGenerated(locked = true)
        public void setArrivalTemperature(String arrivalTemperature) {
            this.__$validPropertySet.add("arrivalTemperature");
            this.arrivalTemperature = arrivalTemperature;
        }

        @AutoGenerated(locked = true)
        public void setBatchIssuanceCertificateNumber(String batchIssuanceCertificateNumber) {
            this.__$validPropertySet.add("batchIssuanceCertificateNumber");
            this.batchIssuanceCertificateNumber = batchIssuanceCertificateNumber;
        }

        @AutoGenerated(locked = true)
        public void setStockPrice(BigDecimal stockPrice) {
            this.__$validPropertySet.add("stockPrice");
            this.stockPrice = stockPrice;
        }

        @AutoGenerated(locked = true)
        public void setStockCost(BigDecimal stockCost) {
            this.__$validPropertySet.add("stockCost");
            this.stockCost = stockCost;
        }

        @AutoGenerated(locked = true)
        public void setRetailPrice(BigDecimal retailPrice) {
            this.__$validPropertySet.add("retailPrice");
            this.retailPrice = retailPrice;
        }

        @AutoGenerated(locked = true)
        public void setRetailCost(BigDecimal retailCost) {
            this.__$validPropertySet.add("retailCost");
            this.retailCost = retailCost;
        }
    }
}
