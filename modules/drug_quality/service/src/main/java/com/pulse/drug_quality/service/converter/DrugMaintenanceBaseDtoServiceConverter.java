package com.pulse.drug_quality.service.converter;

import com.pulse.drug_quality.manager.dto.DrugMaintenanceBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "d4ab429d-4134-388c-bcd3-5c8a34323cb0")
public class DrugMaintenanceBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugMaintenanceBaseDto> DrugMaintenanceBaseDtoConverter(
            List<DrugMaintenanceBaseDto> drugMaintenanceBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugMaintenanceBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
