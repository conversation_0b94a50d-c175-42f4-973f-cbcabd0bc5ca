package com.pulse.pharmacy_warehouse_setting.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "3d7f6ce0-3f70-4c03-ae8e-2560ec1d2d7f|DTO|DEFINITION")
public class DrugStorageWindowOnlineBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9d7af11f-e4d7-4fcb-bbb3-174a49133197")
    private Date createdAt;

    /** 发药处方数量 */
    @AutoGenerated(locked = true, uuid = "982f5912-07a0-43cd-8d3d-5e820167981e")
    private Long dispensePrescriptionCount;

    /** 药房id 冗余存 */
    @AutoGenerated(locked = true, uuid = "d46735e3-c579-4003-800a-038d7ca9c708")
    private String drugStorageId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "90cd477c-a2d6-4b61-b9a9-5101f93c9fb5")
    private String id;

    /** mac地址 */
    @AutoGenerated(locked = true, uuid = "f9643762-46be-4130-bc53-edd702ba70dd")
    private String macAddress;

    /** 签退时间 */
    @AutoGenerated(locked = true, uuid = "40a19a31-9656-4309-9a7e-d5dd78d0e55a")
    private Date offlineTime;

    /** 联机时间 */
    @AutoGenerated(locked = true, uuid = "63725b36-1618-4bed-bb86-30c5fa2fc553")
    private Date onlineTime;

    /** 操作人id */
    @AutoGenerated(locked = true, uuid = "ee5a7dfb-dc61-4f69-bec7-1b311779611b")
    private String operatorId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "998b6fad-347e-477b-865f-3b486c6eba53")
    private Date updatedAt;

    /** 待发药处方数量 */
    @AutoGenerated(locked = true, uuid = "6b2a4a3d-28c9-4f3c-ae89-72a01bf55a62")
    private Long waitDispensePrescriptionCount;

    /** 窗口id */
    @AutoGenerated(locked = true, uuid = "06a6bb00-927d-4cdf-a8b7-09d6da8be008")
    private String windowId;
}
