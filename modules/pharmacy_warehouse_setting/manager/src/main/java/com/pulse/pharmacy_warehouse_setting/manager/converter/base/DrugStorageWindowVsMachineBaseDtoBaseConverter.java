package com.pulse.pharmacy_warehouse_setting.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.pharmacy_warehouse_setting.manager.dto.DrugStorageWindowVsMachineBaseDto;
import com.pulse.pharmacy_warehouse_setting.persist.dos.DrugStorageWindowVsMachine;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "6fd86182-18ec-45b1-be6a-ce43606296e9|DTO|BASE_CONVERTER")
public class DrugStorageWindowVsMachineBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugStorageWindowVsMachineBaseDto
            convertFromDrugStorageWindowVsMachineToDrugStorageWindowVsMachineBaseDto(
                    DrugStorageWindowVsMachine drugStorageWindowVsMachine) {
        return convertFromDrugStorageWindowVsMachineToDrugStorageWindowVsMachineBaseDto(
                        List.of(drugStorageWindowVsMachine))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugStorageWindowVsMachineBaseDto>
            convertFromDrugStorageWindowVsMachineToDrugStorageWindowVsMachineBaseDto(
                    List<DrugStorageWindowVsMachine> drugStorageWindowVsMachineList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugStorageWindowVsMachineList)) {
            return new ArrayList<>();
        }
        List<DrugStorageWindowVsMachineBaseDto> drugStorageWindowVsMachineBaseDtoList =
                new ArrayList<>();
        for (DrugStorageWindowVsMachine drugStorageWindowVsMachine :
                drugStorageWindowVsMachineList) {
            if (drugStorageWindowVsMachine == null) {
                continue;
            }
            DrugStorageWindowVsMachineBaseDto drugStorageWindowVsMachineBaseDto =
                    new DrugStorageWindowVsMachineBaseDto();
            drugStorageWindowVsMachineBaseDto.setId(drugStorageWindowVsMachine.getId());
            drugStorageWindowVsMachineBaseDto.setDispenseWindowId(
                    drugStorageWindowVsMachine.getDispenseWindowId());
            drugStorageWindowVsMachineBaseDto.setMachineWindowId(
                    drugStorageWindowVsMachine.getMachineWindowId());
            drugStorageWindowVsMachineBaseDto.setCreatedAt(
                    drugStorageWindowVsMachine.getCreatedAt());
            drugStorageWindowVsMachineBaseDto.setUpdatedAt(
                    drugStorageWindowVsMachine.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugStorageWindowVsMachineBaseDtoList.add(drugStorageWindowVsMachineBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugStorageWindowVsMachineBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
