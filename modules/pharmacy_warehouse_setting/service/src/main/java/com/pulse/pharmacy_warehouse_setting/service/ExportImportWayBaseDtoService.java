package com.pulse.pharmacy_warehouse_setting.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.pharmacy_warehouse_setting.manager.ExportImportWayBaseDtoManager;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayBaseDto;
import com.pulse.pharmacy_warehouse_setting.persist.dos.ExportImportWay.StorageCodeAndWayCode;
import com.pulse.pharmacy_warehouse_setting.persist.eo.UkStorageCodeWayCodeEo;
import com.pulse.pharmacy_warehouse_setting.service.converter.ExportImportWayBaseDtoServiceConverter;
import com.pulse.pharmacy_warehouse_setting.service.converter.voConverter.ExportImportWayUkStorageCodeWayCodeConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "de9e46fb-2d3b-4a25-93e8-1b41fb9dca30|DTO|SERVICE")
public class ExportImportWayBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ExportImportWayBaseDtoManager exportImportWayBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ExportImportWayBaseDtoServiceConverter exportImportWayBaseDtoServiceConverter;

    @PublicInterface(
            id = "6b9e748d-63cf-4207-a929-602fece5ee52",
            module = "pharmacy_warehouse_setting",
            moduleId = "c47ef390-e56c-4ad4-bf5b-04a1d408c462",
            pubRpc = true,
            version = "1747725008920")
    @AutoGenerated(locked = false, uuid = "05c0f136-9e91-3918-a6d2-7051101d186b")
    public List<ExportImportWayBaseDto> getByStorageCode(
            @NotNull(message = "库房代码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "dd6784be-c020-4fa9-9582-b9c4120a8ee1",
            module = "pharmacy_warehouse_setting",
            moduleId = "c47ef390-e56c-4ad4-bf5b-04a1d408c462",
            pubRpc = true,
            version = "1747725008906")
    @AutoGenerated(locked = false, uuid = "08c2ddbb-6b6c-3039-b031-595469a2593e")
    public List<ExportImportWayBaseDto> getByStorageCodesAndWayCodes(
            @Valid @NotNull List<UkStorageCodeWayCodeEo> ukStorageCodeWayCodeEo) {
        List<StorageCodeAndWayCode> storageCodeAndWayCode =
                ukStorageCodeWayCodeEo.stream()
                        .map(
                                ExportImportWayUkStorageCodeWayCodeConverter
                                        ::convertFromUkStorageCodeWayCodeToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExportImportWayBaseDto> exportImportWayBaseDtoList =
                exportImportWayBaseDtoManager.getByStorageCodesAndWayCodes(storageCodeAndWayCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return exportImportWayBaseDtoServiceConverter.ExportImportWayBaseDtoConverter(
                exportImportWayBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "c42ce0c9-f807-4e1e-93a7-9285122c8597",
            module = "pharmacy_warehouse_setting",
            moduleId = "c47ef390-e56c-4ad4-bf5b-04a1d408c462",
            pubRpc = true,
            version = "1747725008911")
    @AutoGenerated(locked = false, uuid = "6876c93a-7705-31ac-9640-cad3691fc745")
    public ExportImportWayBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExportImportWayBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "30a01c84-4f34-442b-8927-035854ce1b6e",
            module = "pharmacy_warehouse_setting",
            moduleId = "c47ef390-e56c-4ad4-bf5b-04a1d408c462",
            pubRpc = true,
            version = "1747725008915")
    @AutoGenerated(locked = false, uuid = "7c09e076-87d7-3afe-8d21-a410228f6748")
    public List<ExportImportWayBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ExportImportWayBaseDto> exportImportWayBaseDtoList =
                exportImportWayBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return exportImportWayBaseDtoServiceConverter.ExportImportWayBaseDtoConverter(
                exportImportWayBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "e7f53f58-b333-4c14-88f4-25c320ab5ac5",
            module = "pharmacy_warehouse_setting",
            moduleId = "c47ef390-e56c-4ad4-bf5b-04a1d408c462",
            pubRpc = true,
            version = "1747725008925")
    @AutoGenerated(locked = false, uuid = "cf1e78c3-fc63-3312-9028-7d37dd069faf")
    public List<ExportImportWayBaseDto> getByStorageCodes(
            @Valid @NotNull(message = "库房代码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<ExportImportWayBaseDto> exportImportWayBaseDtoList =
                exportImportWayBaseDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return exportImportWayBaseDtoServiceConverter.ExportImportWayBaseDtoConverter(
                exportImportWayBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "ef31cafc-471d-4291-8c4b-d4a2308a4f9e",
            module = "pharmacy_warehouse_setting",
            moduleId = "c47ef390-e56c-4ad4-bf5b-04a1d408c462",
            pubRpc = true,
            version = "1747725008901")
    @AutoGenerated(locked = false, uuid = "ecbe8238-3242-3cbb-ac50-e55a6a9d36a4")
    public ExportImportWayBaseDto getByStorageCodeAndWayCode(
            @Valid @NotNull UkStorageCodeWayCodeEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExportImportWayBaseDto> ret = getByStorageCodesAndWayCodes(Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
