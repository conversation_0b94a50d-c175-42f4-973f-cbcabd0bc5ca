package com.pulse.pharmacy_warehouse_setting.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> ExportImportWayContrast
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "a205a689-f99d-4a17-8327-2cd83e27f003|BTO|DEFINITION")
public class MergeWayContrastBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 出库方式id */
    @AutoGenerated(locked = true, uuid = "ba5171f6-f4ae-4d2b-92ff-6b0daa27ac3f")
    private String exportId;

    /** 出库库房编码 */
    @AutoGenerated(locked = true, uuid = "d8195d0f-52d2-4474-8cbf-c9b39dde1cae")
    private String exportStorageCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "1063054d-558a-4845-bd53-39b5be125ae5")
    private String id;

    /** 入库方式id */
    @AutoGenerated(locked = true, uuid = "45f87416-9f5e-4d9a-80ed-db5185e90efa")
    private String importId;

    /** 入库库房编码 */
    @AutoGenerated(locked = true, uuid = "e1e78b5c-7fea-46ed-a1f9-7188742e42a9")
    private String importStorageCode;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "ce76af82-f25c-4eae-94f1-ea8c0c6a239e")
    private Long sortNumber;

    @AutoGenerated(locked = true)
    public void setExportId(String exportId) {
        this.__$validPropertySet.add("exportId");
        this.exportId = exportId;
    }

    @AutoGenerated(locked = true)
    public void setExportStorageCode(String exportStorageCode) {
        this.__$validPropertySet.add("exportStorageCode");
        this.exportStorageCode = exportStorageCode;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setImportId(String importId) {
        this.__$validPropertySet.add("importId");
        this.importId = importId;
    }

    @AutoGenerated(locked = true)
    public void setImportStorageCode(String importStorageCode) {
        this.__$validPropertySet.add("importStorageCode");
        this.importStorageCode = importStorageCode;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }
}
