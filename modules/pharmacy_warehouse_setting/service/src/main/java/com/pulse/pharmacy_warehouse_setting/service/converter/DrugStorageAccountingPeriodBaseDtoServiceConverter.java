package com.pulse.pharmacy_warehouse_setting.service.converter;

import com.pulse.pharmacy_warehouse_setting.manager.dto.DrugStorageAccountingPeriodBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "46b21caf-1391-3537-9e51-3c7c7a35d485")
public class DrugStorageAccountingPeriodBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugStorageAccountingPeriodBaseDto> DrugStorageAccountingPeriodBaseDtoConverter(
            List<DrugStorageAccountingPeriodBaseDto> drugStorageAccountingPeriodBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugStorageAccountingPeriodBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
