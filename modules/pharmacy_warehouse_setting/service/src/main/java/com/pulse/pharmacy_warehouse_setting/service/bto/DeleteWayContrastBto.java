package com.pulse.pharmacy_warehouse_setting.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> ExportImportWayContrast
 *
 * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "f8d9a521-eaf3-48f4-a30c-ba383f133591|BTO|DEFINITION")
public class DeleteWayContrastBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c73a152b-1819-479d-a448-39598ff75c81")
    private String id;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
