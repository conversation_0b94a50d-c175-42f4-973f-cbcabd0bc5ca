package com.pulse.pharmacy_warehouse_setting.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "1373186a-4e9b-49b1-b56c-19f007eb4704|QTO|DEFINITION")
public class ListWayContrastByWayIdQto {
    /** 出库方式id export_import_way_contrast.export_id */
    @AutoGenerated(locked = true, uuid = "fd7afac6-a33b-44fe-bb10-fd0318cbec2f")
    private String exportIdIs;

    /** 入库方式id export_import_way_contrast.import_id */
    @AutoGenerated(locked = true, uuid = "561e99a7-f175-4d43-b580-11eab632db09")
    private String importIdIs;
}
