package com.pulse.pharmacy_warehouse_setting.persist.qto;

import com.pulse.pharmacy_warehouse_setting.common.enums.DocumentTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "c1fee292-2241-45eb-8c8f-8d3a21ce850a|QTO|DEFINITION")
public class SearchExportImportWayQto {
    /** 单据类型 export_import_way.document_type */
    @AutoGenerated(locked = true, uuid = "97fb09f6-3218-412b-ada3-cc5190dabc34")
    private DocumentTypeEnum documentTypeIs;

    /** 库房代码 export_import_way.storage_code */
    @AutoGenerated(locked = true, uuid = "b149543d-3feb-420e-8ed2-548c821a88a4")
    private String storageCodeIs;

    /** 方式名称 export_import_way.way_name */
    @AutoGenerated(locked = true, uuid = "fb64cd6a-250a-4351-8e8b-a6af8b1d54dd")
    private String wayNameLike;
}
