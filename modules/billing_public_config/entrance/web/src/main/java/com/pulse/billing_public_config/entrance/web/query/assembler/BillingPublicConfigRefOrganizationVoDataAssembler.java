package com.pulse.billing_public_config.entrance.web.query.assembler;

import com.pulse.billing_public_config.entrance.web.vo.BillingPublicConfigRefOrganizationVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** BillingPublicConfigRefOrganizationVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "36060817-f80a-3534-8be4-37883109251a")
public class BillingPublicConfigRefOrganizationVoDataAssembler {

    /** 组装BillingPublicConfigRefOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "90036aed-e9f6-30aa-8687-ba1327bf257f")
    public void assembleData(Map<String, BillingPublicConfigRefOrganizationVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装BillingPublicConfigRefOrganizationVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "d73de974-40e1-3c67-858d-8cf3b910e239")
    public void assembleDataCustomized(List<BillingPublicConfigRefOrganizationVo> dataList) {
        // 自定义数据组装

    }
}
