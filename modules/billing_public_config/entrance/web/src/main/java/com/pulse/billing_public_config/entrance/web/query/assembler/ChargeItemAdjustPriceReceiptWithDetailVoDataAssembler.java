package com.pulse.billing_public_config.entrance.web.query.assembler;

import com.pulse.billing_public_config.entrance.web.vo.ChargeItemAdjustPriceReceiptDetailChargeExtVo;
import com.pulse.billing_public_config.entrance.web.vo.ChargeItemAdjustPriceReceiptWithDetailVo;
import com.pulse.billing_public_config.entrance.web.vo.ChargeItemSimpleVo;
import com.pulse.billing_public_config.manager.dto.ChargeItemAdjustPriceReceiptAutoCreatedBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemAdjustPriceReceiptBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.service.ChargeItemAdjustPriceReceiptAutoCreatedBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ChargeItemAdjustPriceReceiptWithDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "8a03ee10-df3d-341c-a083-4590bebb9ec3")
public class ChargeItemAdjustPriceReceiptWithDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemAdjustPriceReceiptAutoCreatedBaseDtoService
            chargeItemAdjustPriceReceiptAutoCreatedBaseDtoService;

    /** 组装chargeItemAdjustPriceReceiptDetailChargeExtList数据 */
    @AutoGenerated(locked = true, uuid = "4262b4cd-0e3f-303e-a0a3-ec698adfa4fc")
    private void assembleChargeItemAdjustPriceReceiptDetailChargeExtListData(
            ChargeItemAdjustPriceReceiptWithDetailVoDataAssembler
                            .ChargeItemAdjustPriceReceiptWithDetailVoDataHolder
                    dataHolder) {
        Map<String, Pair<ChargeItemBaseDto, ChargeItemSimpleVo>>
                chargeItemAdjustPriceReceiptDetailChargeExtList2ChargeItem =
                        dataHolder
                                .chargeItemAdjustPriceReceiptDetailChargeExtList2ChargeItem
                                .keySet()
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getItemCode(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder
                                                                        .chargeItemAdjustPriceReceiptDetailChargeExtList2ChargeItem
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<
                        ChargeItemAdjustPriceReceiptBaseDto,
                        ChargeItemAdjustPriceReceiptDetailChargeExtVo>
                chargeItemAdjustPriceReceiptDetailChargeExtList :
                        dataHolder.chargeItemAdjustPriceReceiptDetailChargeExtList.entrySet()) {
            ChargeItemAdjustPriceReceiptBaseDto baseDto =
                    chargeItemAdjustPriceReceiptDetailChargeExtList.getKey();
            ChargeItemAdjustPriceReceiptDetailChargeExtVo vo =
                    chargeItemAdjustPriceReceiptDetailChargeExtList.getValue();
            vo.setChargeItem(
                    Optional.ofNullable(
                                    chargeItemAdjustPriceReceiptDetailChargeExtList2ChargeItem.get(
                                            baseDto.getItemCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 组装ChargeItemAdjustPriceReceiptWithDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "5f5195af-4b9d-3bc5-ab63-1f228e79294f")
    public void assembleData(
            Map<String, ChargeItemAdjustPriceReceiptWithDetailVo> voMap,
            ChargeItemAdjustPriceReceiptWithDetailVoDataAssembler
                            .ChargeItemAdjustPriceReceiptWithDetailVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ChargeItemAdjustPriceReceiptAutoCreatedBaseDto> baseDtoList =
                dataHolder.getRootBaseDtoList();

        Map<
                        String,
                        List<
                                Pair<
                                        ChargeItemAdjustPriceReceiptBaseDto,
                                        ChargeItemAdjustPriceReceiptDetailChargeExtVo>>>
                chargeItemAdjustPriceReceiptDetailChargeExtList =
                        dataHolder.chargeItemAdjustPriceReceiptDetailChargeExtList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getReceiptNumber(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .chargeItemAdjustPriceReceiptDetailChargeExtList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (ChargeItemAdjustPriceReceiptAutoCreatedBaseDto baseDto : baseDtoList) {
            ChargeItemAdjustPriceReceiptWithDetailVo vo = voMap.get(baseDto.getReceiptNumber());
            vo.setChargeItemAdjustPriceReceiptDetailChargeExtList(
                    Optional.ofNullable(
                                    chargeItemAdjustPriceReceiptDetailChargeExtList.get(
                                            baseDto.getReceiptNumber()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleChargeItemAdjustPriceReceiptDetailChargeExtListData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ChargeItemAdjustPriceReceiptWithDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "e24e7702-9777-3694-b2f7-88e4b632e2de")
    public void assembleDataCustomized(List<ChargeItemAdjustPriceReceiptWithDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ChargeItemAdjustPriceReceiptWithDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ChargeItemAdjustPriceReceiptAutoCreatedBaseDto> rootBaseDtoList;

        /** 持有字段chargeItemAdjustPriceReceiptDetailChargeExtList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<
                        ChargeItemAdjustPriceReceiptBaseDto,
                        ChargeItemAdjustPriceReceiptDetailChargeExtVo>
                chargeItemAdjustPriceReceiptDetailChargeExtList;

        /** 持有字段chargeItemAdjustPriceReceiptDetailChargeExtList.chargeItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ChargeItemBaseDto, ChargeItemSimpleVo>
                chargeItemAdjustPriceReceiptDetailChargeExtList2ChargeItem;
    }
}
