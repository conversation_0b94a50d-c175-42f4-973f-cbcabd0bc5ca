package com.pulse.billing_public_config.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.billing_public_config.manager.bo.*;
import com.pulse.billing_public_config.manager.bo.ChargeItemBO;
import com.pulse.billing_public_config.persist.dos.ChargeItem;
import com.pulse.billing_public_config.persist.dos.ChargeItemPerformDepartment;
import com.pulse.billing_public_config.persist.dos.ChargeItemPriceDetail;
import com.pulse.billing_public_config.persist.dos.ChargePackageDetail;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.ChangeChargeItemAuditStatusBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.ChangeChargeItemEnableFlagBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.CreateChargeItemBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.CreateChargeItemPriceDetailBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.CreateChargeItemPriceDetailListBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.PerformChargeItemPriceItemBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.SaveChargeItemPackageBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.SaveChargeItemPerformDepartmentListBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.SaveChargeItemPriceDetailListBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.UpdateChargeItemBoResult;
import com.pulse.billing_public_config.service.base.BaseChargeItemBOService.UpdateChargeItemPriceDetailBoResult;
import com.pulse.billing_public_config.service.bto.ChangeChargeItemAuditStatusBto;
import com.pulse.billing_public_config.service.bto.ChangeChargeItemEnableFlagBto;
import com.pulse.billing_public_config.service.bto.CreateChargeItemBto;
import com.pulse.billing_public_config.service.bto.CreateChargeItemPriceDetailBto;
import com.pulse.billing_public_config.service.bto.CreateChargeItemPriceDetailListBto;
import com.pulse.billing_public_config.service.bto.PerformChargeItemPriceItemBto;
import com.pulse.billing_public_config.service.bto.SaveChargeItemPackageBto;
import com.pulse.billing_public_config.service.bto.SaveChargeItemPerformDepartmentListBto;
import com.pulse.billing_public_config.service.bto.SaveChargeItemPriceDetailListBto;
import com.pulse.billing_public_config.service.bto.UpdateChargeItemBto;
import com.pulse.billing_public_config.service.bto.UpdateChargeItemPriceDetailBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "44adbe87-5f9e-3e47-a621-da71656b9073")
public class BaseChargeItemBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 修改复核状态 */
    @AutoGenerated(locked = true)
    protected ChangeChargeItemAuditStatusBoResult changeChargeItemAuditStatusBase(
            ChangeChargeItemAuditStatusBto changeChargeItemAuditStatusBto) {
        if (changeChargeItemAuditStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeChargeItemAuditStatusBoResult boResult = new ChangeChargeItemAuditStatusBoResult();
        ChargeItemBO chargeItemBO =
                updateChangeChargeItemAuditStatusOnMissThrowEx(
                        boResult, changeChargeItemAuditStatusBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeChargeItemAuditStatusBto, "__$validPropertySet"),
                    "chargeItemPriceDetailBtoList")) {
                updateChargeItemPriceDetailBtoOnMissThrowEx(
                        boResult, changeChargeItemAuditStatusBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 修改收费项目启用标识 */
    @AutoGenerated(locked = true)
    protected ChangeChargeItemEnableFlagBoResult changeChargeItemEnableFlagBase(
            ChangeChargeItemEnableFlagBto changeChargeItemEnableFlagBto) {
        if (changeChargeItemEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeChargeItemEnableFlagBoResult boResult = new ChangeChargeItemEnableFlagBoResult();
        ChargeItemBO chargeItemBO =
                updateChangeChargeItemEnableFlagOnMissThrowEx(
                        boResult, changeChargeItemEnableFlagBto);
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 创建收费项目 */
    @AutoGenerated(locked = true)
    protected CreateChargeItemBoResult createChargeItemBase(
            CreateChargeItemBto createChargeItemBto) {
        if (createChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateChargeItemBoResult boResult = new CreateChargeItemBoResult();
        ChargeItemBO chargeItemBO =
                createCreateChargeItemOnDuplicateThrowEx(boResult, createChargeItemBto);
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 创建对象:ChargeItemPerformDepartmentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createChargeItemPerformDepartmentBtoOnDuplicateUpdate(
            SaveChargeItemPerformDepartmentListBoResult boResult,
            SaveChargeItemPerformDepartmentListBto saveChargeItemPerformDepartmentListBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isEmpty(
                saveChargeItemPerformDepartmentListBto.getChargeItemPerformDepartmentBtoList())) {
            saveChargeItemPerformDepartmentListBto.setChargeItemPerformDepartmentBtoList(List.of());
        }
        chargeItemBO
                .getChargeItemPerformDepartmentBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveChargeItemPerformDepartmentListBto
                                            .getChargeItemPerformDepartmentBtoList()
                                            .stream()
                                            .filter(
                                                    chargeItemPerformDepartmentBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (chargeItemPerformDepartmentBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            chargeItemPerformDepartmentBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToChargeItemPerformDepartment());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveChargeItemPerformDepartmentListBto.getChargeItemPerformDepartmentBtoList())) {
            for (SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto item :
                    saveChargeItemPerformDepartmentListBto
                            .getChargeItemPerformDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ChargeItemPerformDepartmentBO> any =
                        chargeItemBO.getChargeItemPerformDepartmentBOSet().stream()
                                .filter(
                                        chargeItemPerformDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                chargeItemPerformDepartmentBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ChargeItemPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToChargeItemPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentId")) {
                            bo.setDepartmentId(item.getDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                    } else {
                        ChargeItemPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToChargeItemPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentId")) {
                            bo.setDepartmentId(item.getDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                    }
                } else {
                    ChargeItemPerformDepartmentBO subBo = new ChargeItemPerformDepartmentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "departmentId")) {
                        subBo.setDepartmentId(item.getDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultFlag")) {
                        subBo.setDefaultFlag(item.getDefaultFlag());
                    }
                    subBo.setChargeItemBO(chargeItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "charge_item_perform_department")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    chargeItemBO.getChargeItemPerformDepartmentBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建收费项目价格明细 */
    @AutoGenerated(locked = true)
    protected CreateChargeItemPriceDetailBoResult createChargeItemPriceDetailBase(
            CreateChargeItemPriceDetailBto createChargeItemPriceDetailBto) {
        if (createChargeItemPriceDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateChargeItemPriceDetailBoResult boResult = new CreateChargeItemPriceDetailBoResult();
        ChargeItemBO chargeItemBO =
                updateCreateChargeItemPriceDetailOnMissThrowEx(
                        boResult, createChargeItemPriceDetailBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createChargeItemPriceDetailBto, "__$validPropertySet"),
                    "chargeItemPriceDetailBtoList")) {
                createChargeItemPriceDetailBtoOnDuplicateThrowEx(
                        boResult, createChargeItemPriceDetailBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 创建对象:ChargeItemPriceDetailBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createChargeItemPriceDetailBtoOnDuplicateThrowEx(
            CreateChargeItemPriceDetailListBoResult boResult,
            CreateChargeItemPriceDetailListBto createChargeItemPriceDetailListBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isNotEmpty(
                createChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList())) {
            for (CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto item :
                    createChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ChargeItemPriceDetailBO> any =
                        chargeItemBO.getChargeItemPriceDetailBOSet().stream()
                                .filter(
                                        chargeItemPriceDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                chargeItemPriceDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "charge_item_price_detail");
                        throw new IgnoredException(400, "收费项目价格详情已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "charge_item_price_detail",
                                any.get().getId());
                        throw new IgnoredException(400, "收费项目价格详情已存在");
                    }
                } else {
                    ChargeItemPriceDetailBO subBo = new ChargeItemPriceDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "priceType")) {
                        subBo.setPriceType(item.getPriceType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "price")) {
                        subBo.setPrice(item.getPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "increaseRatio")) {
                        subBo.setIncreaseRatio(item.getIncreaseRatio());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "startTime")) {
                        subBo.setStartTime(item.getStartTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "endTime")) {
                        subBo.setEndTime(item.getEndTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditStatus")) {
                        subBo.setAuditStatus(item.getAuditStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditOperatorId")) {
                        subBo.setAuditOperatorId(item.getAuditOperatorId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditTime")) {
                        subBo.setAuditTime(item.getAuditTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditInstruction")) {
                        subBo.setAuditInstruction(item.getAuditInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updateBy")) {
                        subBo.setUpdateBy(item.getUpdateBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performFlag")) {
                        subBo.setPerformFlag(item.getPerformFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performTime")) {
                        subBo.setPerformTime(item.getPerformTime());
                    }
                    subBo.setChargeItemBO(chargeItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("charge_item_price_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    chargeItemBO.getChargeItemPriceDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ChargeItemPriceDetailBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createChargeItemPriceDetailBtoOnDuplicateThrowEx(
            CreateChargeItemPriceDetailBoResult boResult,
            CreateChargeItemPriceDetailBto createChargeItemPriceDetailBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isNotEmpty(
                createChargeItemPriceDetailBto.getChargeItemPriceDetailBtoList())) {
            for (CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto item :
                    createChargeItemPriceDetailBto.getChargeItemPriceDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ChargeItemPriceDetailBO> any =
                        chargeItemBO.getChargeItemPriceDetailBOSet().stream()
                                .filter(
                                        chargeItemPriceDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                chargeItemPriceDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "charge_item_price_detail");
                        throw new IgnoredException(400, "收费项目价格详情已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "charge_item_price_detail",
                                any.get().getId());
                        throw new IgnoredException(400, "收费项目价格详情已存在");
                    }
                } else {
                    ChargeItemPriceDetailBO subBo = new ChargeItemPriceDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "priceType")) {
                        subBo.setPriceType(item.getPriceType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "price")) {
                        subBo.setPrice(item.getPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "increaseRatio")) {
                        subBo.setIncreaseRatio(item.getIncreaseRatio());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "startTime")) {
                        subBo.setStartTime(item.getStartTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "endTime")) {
                        subBo.setEndTime(item.getEndTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditStatus")) {
                        subBo.setAuditStatus(item.getAuditStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditOperatorId")) {
                        subBo.setAuditOperatorId(item.getAuditOperatorId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditTime")) {
                        subBo.setAuditTime(item.getAuditTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditInstruction")) {
                        subBo.setAuditInstruction(item.getAuditInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updateBy")) {
                        subBo.setUpdateBy(item.getUpdateBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performFlag")) {
                        subBo.setPerformFlag(item.getPerformFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performTime")) {
                        subBo.setPerformTime(item.getPerformTime());
                    }
                    subBo.setChargeItemBO(chargeItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("charge_item_price_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    chargeItemBO.getChargeItemPriceDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ChargeItemPriceDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createChargeItemPriceDetailBtoOnDuplicateUpdate(
            SaveChargeItemPriceDetailListBoResult boResult,
            SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isEmpty(
                saveChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList())) {
            saveChargeItemPriceDetailListBto.setChargeItemPriceDetailBtoList(List.of());
        }
        chargeItemBO
                .getChargeItemPriceDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveChargeItemPriceDetailListBto
                                            .getChargeItemPriceDetailBtoList()
                                            .stream()
                                            .filter(
                                                    chargeItemPriceDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (chargeItemPriceDetailBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            chargeItemPriceDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToChargeItemPriceDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList())) {
            for (SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto item :
                    saveChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ChargeItemPriceDetailBO> any =
                        chargeItemBO.getChargeItemPriceDetailBOSet().stream()
                                .filter(
                                        chargeItemPriceDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                chargeItemPriceDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ChargeItemPriceDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToChargeItemPriceDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusId")) {
                            bo.setCampusId(item.getCampusId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priceType")) {
                            bo.setPriceType(item.getPriceType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "increaseRatio")) {
                            bo.setIncreaseRatio(item.getIncreaseRatio());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startTime")) {
                            bo.setStartTime(item.getStartTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endTime")) {
                            bo.setEndTime(item.getEndTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditStatus")) {
                            bo.setAuditStatus(item.getAuditStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditOperatorId")) {
                            bo.setAuditOperatorId(item.getAuditOperatorId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditTime")) {
                            bo.setAuditTime(item.getAuditTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditInstruction")) {
                            bo.setAuditInstruction(item.getAuditInstruction());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updateBy")) {
                            bo.setUpdateBy(item.getUpdateBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performFlag")) {
                            bo.setPerformFlag(item.getPerformFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performTime")) {
                            bo.setPerformTime(item.getPerformTime());
                        }
                    } else {
                        ChargeItemPriceDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToChargeItemPriceDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusId")) {
                            bo.setCampusId(item.getCampusId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priceType")) {
                            bo.setPriceType(item.getPriceType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "increaseRatio")) {
                            bo.setIncreaseRatio(item.getIncreaseRatio());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startTime")) {
                            bo.setStartTime(item.getStartTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endTime")) {
                            bo.setEndTime(item.getEndTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditStatus")) {
                            bo.setAuditStatus(item.getAuditStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditOperatorId")) {
                            bo.setAuditOperatorId(item.getAuditOperatorId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditTime")) {
                            bo.setAuditTime(item.getAuditTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "auditInstruction")) {
                            bo.setAuditInstruction(item.getAuditInstruction());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updateBy")) {
                            bo.setUpdateBy(item.getUpdateBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performFlag")) {
                            bo.setPerformFlag(item.getPerformFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performTime")) {
                            bo.setPerformTime(item.getPerformTime());
                        }
                    }
                } else {
                    ChargeItemPriceDetailBO subBo = new ChargeItemPriceDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "priceType")) {
                        subBo.setPriceType(item.getPriceType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "price")) {
                        subBo.setPrice(item.getPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "increaseRatio")) {
                        subBo.setIncreaseRatio(item.getIncreaseRatio());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "startTime")) {
                        subBo.setStartTime(item.getStartTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "endTime")) {
                        subBo.setEndTime(item.getEndTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditStatus")) {
                        subBo.setAuditStatus(item.getAuditStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditOperatorId")) {
                        subBo.setAuditOperatorId(item.getAuditOperatorId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditTime")) {
                        subBo.setAuditTime(item.getAuditTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "auditInstruction")) {
                        subBo.setAuditInstruction(item.getAuditInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updateBy")) {
                        subBo.setUpdateBy(item.getUpdateBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performFlag")) {
                        subBo.setPerformFlag(item.getPerformFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performTime")) {
                        subBo.setPerformTime(item.getPerformTime());
                    }
                    subBo.setChargeItemBO(chargeItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("charge_item_price_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    chargeItemBO.getChargeItemPriceDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建收费项目价格明细列表 */
    @AutoGenerated(locked = true)
    protected CreateChargeItemPriceDetailListBoResult createChargeItemPriceDetailListBase(
            CreateChargeItemPriceDetailListBto createChargeItemPriceDetailListBto) {
        if (createChargeItemPriceDetailListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateChargeItemPriceDetailListBoResult boResult =
                new CreateChargeItemPriceDetailListBoResult();
        ChargeItemBO chargeItemBO =
                updateCreateChargeItemPriceDetailListOnMissThrowEx(
                        boResult, createChargeItemPriceDetailListBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createChargeItemPriceDetailListBto, "__$validPropertySet"),
                    "chargeItemPriceDetailBtoList")) {
                createChargeItemPriceDetailBtoOnDuplicateThrowEx(
                        boResult, createChargeItemPriceDetailListBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 创建对象:ChargePackageDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createChargePackageDetailBtoOnDuplicateUpdate(
            BaseChargeItemBOService.SaveChargeItemPackageBoResult boResult,
            SaveChargeItemPackageBto saveChargeItemPackageBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isEmpty(saveChargeItemPackageBto.getChargePackageDetailBtoList())) {
            saveChargeItemPackageBto.setChargePackageDetailBtoList(List.of());
        }
        chargeItemBO
                .getChargePackageDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveChargeItemPackageBto
                                            .getChargePackageDetailBtoList()
                                            .stream()
                                            .filter(
                                                    chargePackageDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (chargePackageDetailBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            chargePackageDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToChargePackageDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveChargeItemPackageBto.getChargePackageDetailBtoList())) {
            for (SaveChargeItemPackageBto.ChargePackageDetailBto item :
                    saveChargeItemPackageBto.getChargePackageDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ChargePackageDetailBO> any =
                        chargeItemBO.getChargePackageDetailBOSet().stream()
                                .filter(
                                        chargePackageDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                chargePackageDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ChargePackageDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToChargePackageDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargePackageDetailType")) {
                            bo.setChargePackageDetailType(item.getChargePackageDetailType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicInsuranceCode")) {
                            bo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "insuranceLevelType")) {
                            bo.setInsuranceLevelType(item.getInsuranceLevelType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentDrugFlag")) {
                            bo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "itemSpecification")) {
                            bo.setItemSpecification(item.getItemSpecification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "productionArea")) {
                            bo.setProductionArea(item.getProductionArea());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        ChargePackageDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToChargePackageDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargePackageDetailType")) {
                            bo.setChargePackageDetailType(item.getChargePackageDetailType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicInsuranceCode")) {
                            bo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "insuranceLevelType")) {
                            bo.setInsuranceLevelType(item.getInsuranceLevelType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentDrugFlag")) {
                            bo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "itemSpecification")) {
                            bo.setItemSpecification(item.getItemSpecification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "productionArea")) {
                            bo.setProductionArea(item.getProductionArea());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    ChargePackageDetailBO subBo = new ChargePackageDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargePackageDetailType")) {
                        subBo.setChargePackageDetailType(item.getChargePackageDetailType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "price")) {
                        subBo.setPrice(item.getPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "count")) {
                        subBo.setCount(item.getCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clinicInsuranceCode")) {
                        subBo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "insuranceLevelType")) {
                        subBo.setInsuranceLevelType(item.getInsuranceLevelType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "departmentDrugFlag")) {
                        subBo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "itemSpecification")) {
                        subBo.setItemSpecification(item.getItemSpecification());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "productionArea")) {
                        subBo.setProductionArea(item.getProductionArea());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "unit")) {
                        subBo.setUnit(item.getUnit());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setChargeItemBO(chargeItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("charge_package_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    chargeItemBO.getChargePackageDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO createCreateChargeItemOnDuplicateThrowEx(
            BaseChargeItemBOService.CreateChargeItemBoResult boResult,
            CreateChargeItemBto createChargeItemBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createChargeItemBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(createChargeItemBto.getItemCode());
            if (chargeItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'item_code'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (chargeItemBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!", chargeItemBO.getItemCode(), "charge_item");
                throw new IgnoredException(400, "收费项目已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "charge_item",
                        chargeItemBO.getItemCode(),
                        "charge_item");
                throw new IgnoredException(400, "收费项目已存在");
            }
        } else {
            chargeItemBO = new ChargeItemBO();
            if (pkExist) {
                chargeItemBO.setItemCode(createChargeItemBto.getItemCode());
            } else {
                chargeItemBO.setItemCode(
                        String.valueOf(this.idGenerator.allocateId("charge_item")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "organizationId")) {
                chargeItemBO.setOrganizationId(createChargeItemBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "sortNumber")) {
                chargeItemBO.setSortNumber(createChargeItemBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "itemName")) {
                chargeItemBO.setItemName(createChargeItemBto.getItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "itemSpecification")) {
                chargeItemBO.setItemSpecification(createChargeItemBto.getItemSpecification());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "inputCode")) {
                chargeItemBO.setInputCode(createChargeItemBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "packageType")) {
                chargeItemBO.setPackageType(createChargeItemBto.getPackageType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "leafFlag")) {
                chargeItemBO.setLeafFlag(createChargeItemBto.getLeafFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "itemCategory")) {
                chargeItemBO.setItemCategory(createChargeItemBto.getItemCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "chargeItemCategory")) {
                chargeItemBO.setChargeItemCategory(createChargeItemBto.getChargeItemCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "reckoningCategory")) {
                chargeItemBO.setReckoningCategory(createChargeItemBto.getReckoningCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "tcmMedicalRecordCategory")) {
                chargeItemBO.setTcmMedicalRecordCategory(
                        createChargeItemBto.getTcmMedicalRecordCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "accountingSubjectCategory")) {
                chargeItemBO.setAccountingSubjectCategory(
                        createChargeItemBto.getAccountingSubjectCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "medicalRecordCategory")) {
                chargeItemBO.setMedicalRecordCategory(
                        createChargeItemBto.getMedicalRecordCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "medicalRecordSubCategory")) {
                chargeItemBO.setMedicalRecordSubCategory(
                        createChargeItemBto.getMedicalRecordSubCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "unit")) {
                chargeItemBO.setUnit(createChargeItemBto.getUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "remark")) {
                chargeItemBO.setRemark(createChargeItemBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "insuranceItemLevel")) {
                chargeItemBO.setInsuranceItemLevel(createChargeItemBto.getInsuranceItemLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "insuranceNationCode")) {
                chargeItemBO.setInsuranceNationCode(createChargeItemBto.getInsuranceNationCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "insuranceProvinceCode")) {
                chargeItemBO.setInsuranceProvinceCode(
                        createChargeItemBto.getInsuranceProvinceCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "insuranceNationName")) {
                chargeItemBO.setInsuranceNationName(createChargeItemBto.getInsuranceNationName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "singleSettleFlag")) {
                chargeItemBO.setSingleSettleFlag(createChargeItemBto.getSingleSettleFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "independentPricingFlag")) {
                chargeItemBO.setIndependentPricingFlag(
                        createChargeItemBto.getIndependentPricingFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "synchronizedFlag")) {
                chargeItemBO.setSynchronizedFlag(createChargeItemBto.getSynchronizedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "enableFlag")) {
                chargeItemBO.setEnableFlag(createChargeItemBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "campusIdList")) {
                chargeItemBO.setCampusIdList(createChargeItemBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "source")) {
                chargeItemBO.setSource(createChargeItemBto.getSource());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "itemConnotation")) {
                chargeItemBO.setItemConnotation(createChargeItemBto.getItemConnotation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "firmCode")) {
                chargeItemBO.setFirmCode(createChargeItemBto.getFirmCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "biddingCode")) {
                chargeItemBO.setBiddingCode(createChargeItemBto.getBiddingCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "standardCode")) {
                chargeItemBO.setStandardCode(createChargeItemBto.getStandardCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "extensionField")) {
                chargeItemBO.setExtensionField(createChargeItemBto.getExtensionField());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "parentItemCode")) {
                chargeItemBO.setParentItemCode(createChargeItemBto.getParentItemCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "createdBy")) {
                chargeItemBO.setCreatedBy(createChargeItemBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "updatedBy")) {
                chargeItemBO.setUpdatedBy(createChargeItemBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createChargeItemBto, "__$validPropertySet"),
                    "useScopeList")) {
                chargeItemBO.setUseScopeList(createChargeItemBto.getUseScopeList());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createChargeItemBto);
            addedBto.setBo(chargeItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return chargeItemBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ChargeItemBO createSaveChargeItemPackageOnDuplicateUpdate(
            SaveChargeItemPackageBoResult boResult,
            SaveChargeItemPackageBto saveChargeItemPackageBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveChargeItemPackageBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(saveChargeItemPackageBto.getItemCode());
            if (chargeItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'item_code'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (chargeItemBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(chargeItemBO.convertToChargeItem());
                updatedBto.setBto(saveChargeItemPackageBto);
                updatedBto.setBo(chargeItemBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "organizationId")) {
                    chargeItemBO.setOrganizationId(saveChargeItemPackageBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "sortNumber")) {
                    chargeItemBO.setSortNumber(saveChargeItemPackageBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "itemName")) {
                    chargeItemBO.setItemName(saveChargeItemPackageBto.getItemName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "inputCode")) {
                    chargeItemBO.setInputCode(saveChargeItemPackageBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "packageType")) {
                    chargeItemBO.setPackageType(saveChargeItemPackageBto.getPackageType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "leafFlag")) {
                    chargeItemBO.setLeafFlag(saveChargeItemPackageBto.getLeafFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "itemCategory")) {
                    chargeItemBO.setItemCategory(saveChargeItemPackageBto.getItemCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "remark")) {
                    chargeItemBO.setRemark(saveChargeItemPackageBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "enableFlag")) {
                    chargeItemBO.setEnableFlag(saveChargeItemPackageBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "campusIdList")) {
                    chargeItemBO.setCampusIdList(saveChargeItemPackageBto.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "useScopeList")) {
                    chargeItemBO.setUseScopeList(saveChargeItemPackageBto.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "source")) {
                    chargeItemBO.setSource(saveChargeItemPackageBto.getSource());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "itemConnotation")) {
                    chargeItemBO.setItemConnotation(saveChargeItemPackageBto.getItemConnotation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "extensionField")) {
                    chargeItemBO.setExtensionField(saveChargeItemPackageBto.getExtensionField());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "createdBy")) {
                    chargeItemBO.setCreatedBy(saveChargeItemPackageBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "updatedBy")) {
                    chargeItemBO.setUpdatedBy(saveChargeItemPackageBto.getUpdatedBy());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(chargeItemBO.convertToChargeItem());
                updatedBto.setBto(saveChargeItemPackageBto);
                updatedBto.setBo(chargeItemBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "organizationId")) {
                    chargeItemBO.setOrganizationId(saveChargeItemPackageBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "sortNumber")) {
                    chargeItemBO.setSortNumber(saveChargeItemPackageBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "itemName")) {
                    chargeItemBO.setItemName(saveChargeItemPackageBto.getItemName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "inputCode")) {
                    chargeItemBO.setInputCode(saveChargeItemPackageBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "packageType")) {
                    chargeItemBO.setPackageType(saveChargeItemPackageBto.getPackageType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "leafFlag")) {
                    chargeItemBO.setLeafFlag(saveChargeItemPackageBto.getLeafFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "itemCategory")) {
                    chargeItemBO.setItemCategory(saveChargeItemPackageBto.getItemCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "remark")) {
                    chargeItemBO.setRemark(saveChargeItemPackageBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "enableFlag")) {
                    chargeItemBO.setEnableFlag(saveChargeItemPackageBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "campusIdList")) {
                    chargeItemBO.setCampusIdList(saveChargeItemPackageBto.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "useScopeList")) {
                    chargeItemBO.setUseScopeList(saveChargeItemPackageBto.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "source")) {
                    chargeItemBO.setSource(saveChargeItemPackageBto.getSource());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "itemConnotation")) {
                    chargeItemBO.setItemConnotation(saveChargeItemPackageBto.getItemConnotation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "extensionField")) {
                    chargeItemBO.setExtensionField(saveChargeItemPackageBto.getExtensionField());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "createdBy")) {
                    chargeItemBO.setCreatedBy(saveChargeItemPackageBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveChargeItemPackageBto, "__$validPropertySet"),
                        "updatedBy")) {
                    chargeItemBO.setUpdatedBy(saveChargeItemPackageBto.getUpdatedBy());
                }
            }
        } else {
            chargeItemBO = new ChargeItemBO();
            if (pkExist) {
                chargeItemBO.setItemCode(saveChargeItemPackageBto.getItemCode());
            } else {
                chargeItemBO.setItemCode(
                        String.valueOf(this.idGenerator.allocateId("charge_item")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "organizationId")) {
                chargeItemBO.setOrganizationId(saveChargeItemPackageBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "sortNumber")) {
                chargeItemBO.setSortNumber(saveChargeItemPackageBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "itemName")) {
                chargeItemBO.setItemName(saveChargeItemPackageBto.getItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "inputCode")) {
                chargeItemBO.setInputCode(saveChargeItemPackageBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "packageType")) {
                chargeItemBO.setPackageType(saveChargeItemPackageBto.getPackageType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "leafFlag")) {
                chargeItemBO.setLeafFlag(saveChargeItemPackageBto.getLeafFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "itemCategory")) {
                chargeItemBO.setItemCategory(saveChargeItemPackageBto.getItemCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "remark")) {
                chargeItemBO.setRemark(saveChargeItemPackageBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "enableFlag")) {
                chargeItemBO.setEnableFlag(saveChargeItemPackageBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "campusIdList")) {
                chargeItemBO.setCampusIdList(saveChargeItemPackageBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "useScopeList")) {
                chargeItemBO.setUseScopeList(saveChargeItemPackageBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "source")) {
                chargeItemBO.setSource(saveChargeItemPackageBto.getSource());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "itemConnotation")) {
                chargeItemBO.setItemConnotation(saveChargeItemPackageBto.getItemConnotation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "extensionField")) {
                chargeItemBO.setExtensionField(saveChargeItemPackageBto.getExtensionField());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "createdBy")) {
                chargeItemBO.setCreatedBy(saveChargeItemPackageBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "updatedBy")) {
                chargeItemBO.setUpdatedBy(saveChargeItemPackageBto.getUpdatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveChargeItemPackageBto);
            addedBto.setBo(chargeItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return chargeItemBO;
    }

    /** 执行收费项目价格明细 */
    @AutoGenerated(locked = true)
    protected PerformChargeItemPriceItemBoResult performChargeItemPriceItemBase(
            PerformChargeItemPriceItemBto performChargeItemPriceItemBto) {
        if (performChargeItemPriceItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        PerformChargeItemPriceItemBoResult boResult = new PerformChargeItemPriceItemBoResult();
        ChargeItemBO chargeItemBO =
                updatePerformChargeItemPriceItemOnMissThrowEx(
                        boResult, performChargeItemPriceItemBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    performChargeItemPriceItemBto, "__$validPropertySet"),
                    "chargeItemPriceDetailBtoList")) {
                updateChargeItemPriceDetailBtoOnMissThrowEx(
                        boResult, performChargeItemPriceItemBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 保存收费项目套餐 */
    @AutoGenerated(locked = true)
    protected SaveChargeItemPackageBoResult saveChargeItemPackageBase(
            SaveChargeItemPackageBto saveChargeItemPackageBto) {
        if (saveChargeItemPackageBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveChargeItemPackageBoResult boResult = new SaveChargeItemPackageBoResult();
        ChargeItemBO chargeItemBO =
                createSaveChargeItemPackageOnDuplicateUpdate(boResult, saveChargeItemPackageBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPackageBto, "__$validPropertySet"),
                    "chargePackageDetailBtoList")) {
                createChargePackageDetailBtoOnDuplicateUpdate(
                        boResult, saveChargeItemPackageBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 保存收费项目执行科室列表 */
    @AutoGenerated(locked = true)
    protected SaveChargeItemPerformDepartmentListBoResult saveChargeItemPerformDepartmentListBase(
            SaveChargeItemPerformDepartmentListBto saveChargeItemPerformDepartmentListBto) {
        if (saveChargeItemPerformDepartmentListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveChargeItemPerformDepartmentListBoResult boResult =
                new SaveChargeItemPerformDepartmentListBoResult();
        ChargeItemBO chargeItemBO =
                updateSaveChargeItemPerformDepartmentListOnMissThrowEx(
                        boResult, saveChargeItemPerformDepartmentListBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPerformDepartmentListBto, "__$validPropertySet"),
                    "chargeItemPerformDepartmentBtoList")) {
                createChargeItemPerformDepartmentBtoOnDuplicateUpdate(
                        boResult, saveChargeItemPerformDepartmentListBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 保存收费项目价格明细 */
    @AutoGenerated(locked = true)
    protected SaveChargeItemPriceDetailListBoResult saveChargeItemPriceDetailListBase(
            SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto) {
        if (saveChargeItemPriceDetailListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveChargeItemPriceDetailListBoResult boResult =
                new SaveChargeItemPriceDetailListBoResult();
        ChargeItemBO chargeItemBO =
                updateSaveChargeItemPriceDetailListOnMissThrowEx(
                        boResult, saveChargeItemPriceDetailListBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveChargeItemPriceDetailListBto, "__$validPropertySet"),
                    "chargeItemPriceDetailBtoList")) {
                createChargeItemPriceDetailBtoOnDuplicateUpdate(
                        boResult, saveChargeItemPriceDetailListBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 更新对象:changeChargeItemAuditStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateChangeChargeItemAuditStatusOnMissThrowEx(
            ChangeChargeItemAuditStatusBoResult boResult,
            ChangeChargeItemAuditStatusBto changeChargeItemAuditStatusBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeChargeItemAuditStatusBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(changeChargeItemAuditStatusBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(changeChargeItemAuditStatusBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    /** 更新对象:changeChargeItemEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateChangeChargeItemEnableFlagOnMissThrowEx(
            BaseChargeItemBOService.ChangeChargeItemEnableFlagBoResult boResult,
            ChangeChargeItemEnableFlagBto changeChargeItemEnableFlagBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeChargeItemEnableFlagBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(changeChargeItemEnableFlagBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(changeChargeItemEnableFlagBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeChargeItemEnableFlagBto, "__$validPropertySet"),
                    "enableFlag")) {
                chargeItemBO.setEnableFlag(changeChargeItemEnableFlagBto.getEnableFlag());
            }
            return chargeItemBO;
        }
    }

    /** 更新收费项目 */
    @AutoGenerated(locked = true)
    protected UpdateChargeItemBoResult updateChargeItemBase(
            UpdateChargeItemBto updateChargeItemBto) {
        if (updateChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateChargeItemBoResult boResult = new UpdateChargeItemBoResult();
        ChargeItemBO chargeItemBO =
                updateUpdateChargeItemOnMissThrowEx(boResult, updateChargeItemBto);
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 更新收费项目价格明细 */
    @AutoGenerated(locked = true)
    protected UpdateChargeItemPriceDetailBoResult updateChargeItemPriceDetailBase(
            UpdateChargeItemPriceDetailBto updateChargeItemPriceDetailBto) {
        if (updateChargeItemPriceDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateChargeItemPriceDetailBoResult boResult = new UpdateChargeItemPriceDetailBoResult();
        ChargeItemBO chargeItemBO =
                updateUpdateChargeItemPriceDetailOnMissThrowEx(
                        boResult, updateChargeItemPriceDetailBto);
        if (chargeItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateChargeItemPriceDetailBto, "__$validPropertySet"),
                    "chargeItemPriceDetailBtoList")) {
                updateChargeItemPriceDetailBtoOnMissThrowEx(
                        boResult, updateChargeItemPriceDetailBto, chargeItemBO);
            }
        }
        boResult.setRootBo(chargeItemBO);
        return boResult;
    }

    /** 更新对象:chargeItemPriceDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateChargeItemPriceDetailBtoOnMissThrowEx(
            BaseChargeItemBOService.ChangeChargeItemAuditStatusBoResult boResult,
            ChangeChargeItemAuditStatusBto changeChargeItemAuditStatusBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isNotEmpty(
                changeChargeItemAuditStatusBto.getChargeItemPriceDetailBtoList())) {
            for (ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto bto :
                    changeChargeItemAuditStatusBto.getChargeItemPriceDetailBtoList()) {
                Optional<ChargeItemPriceDetailBO> any =
                        chargeItemBO.getChargeItemPriceDetailBOSet().stream()
                                .filter(
                                        chargeItemPriceDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                chargeItemPriceDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ChargeItemPriceDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToChargeItemPriceDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditStatus")) {
                        bo.setAuditStatus(bto.getAuditStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditOperatorId")) {
                        bo.setAuditOperatorId(bto.getAuditOperatorId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditTime")) {
                        bo.setAuditTime(bto.getAuditTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "reason")) {
                        bo.setReason(bto.getReason());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:chargeItemPriceDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateChargeItemPriceDetailBtoOnMissThrowEx(
            BaseChargeItemBOService.UpdateChargeItemPriceDetailBoResult boResult,
            UpdateChargeItemPriceDetailBto updateChargeItemPriceDetailBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isNotEmpty(
                updateChargeItemPriceDetailBto.getChargeItemPriceDetailBtoList())) {
            for (UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto bto :
                    updateChargeItemPriceDetailBto.getChargeItemPriceDetailBtoList()) {
                Optional<ChargeItemPriceDetailBO> any =
                        chargeItemBO.getChargeItemPriceDetailBOSet().stream()
                                .filter(
                                        chargeItemPriceDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                chargeItemPriceDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ChargeItemPriceDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToChargeItemPriceDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "endTime")) {
                        bo.setEndTime(bto.getEndTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditStatus")) {
                        bo.setAuditStatus(bto.getAuditStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditOperatorId")) {
                        bo.setAuditOperatorId(bto.getAuditOperatorId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditTime")) {
                        bo.setAuditTime(bto.getAuditTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "auditInstruction")) {
                        bo.setAuditInstruction(bto.getAuditInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updateBy")) {
                        bo.setUpdateBy(bto.getUpdateBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "reason")) {
                        bo.setReason(bto.getReason());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:chargeItemPriceDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateChargeItemPriceDetailBtoOnMissThrowEx(
            BaseChargeItemBOService.PerformChargeItemPriceItemBoResult boResult,
            PerformChargeItemPriceItemBto performChargeItemPriceItemBto,
            ChargeItemBO chargeItemBO) {
        if (CollectionUtil.isNotEmpty(
                performChargeItemPriceItemBto.getChargeItemPriceDetailBtoList())) {
            for (PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto bto :
                    performChargeItemPriceItemBto.getChargeItemPriceDetailBtoList()) {
                Optional<ChargeItemPriceDetailBO> any =
                        chargeItemBO.getChargeItemPriceDetailBOSet().stream()
                                .filter(
                                        chargeItemPriceDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                chargeItemPriceDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ChargeItemPriceDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToChargeItemPriceDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "performFlag")) {
                        bo.setPerformFlag(bto.getPerformFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "performTime")) {
                        bo.setPerformTime(bto.getPerformTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updateBy")) {
                        bo.setUpdateBy(bto.getUpdateBy());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:createChargeItemPriceDetailList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateCreateChargeItemPriceDetailListOnMissThrowEx(
            BaseChargeItemBOService.CreateChargeItemPriceDetailListBoResult boResult,
            CreateChargeItemPriceDetailListBto createChargeItemPriceDetailListBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createChargeItemPriceDetailListBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO =
                    ChargeItemBO.getByItemCode(createChargeItemPriceDetailListBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(createChargeItemPriceDetailListBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    /** 更新对象:createChargeItemPriceDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateCreateChargeItemPriceDetailOnMissThrowEx(
            BaseChargeItemBOService.CreateChargeItemPriceDetailBoResult boResult,
            CreateChargeItemPriceDetailBto createChargeItemPriceDetailBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createChargeItemPriceDetailBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(createChargeItemPriceDetailBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(createChargeItemPriceDetailBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    /** 更新对象:performChargeItemPriceItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updatePerformChargeItemPriceItemOnMissThrowEx(
            PerformChargeItemPriceItemBoResult boResult,
            PerformChargeItemPriceItemBto performChargeItemPriceItemBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (performChargeItemPriceItemBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(performChargeItemPriceItemBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(performChargeItemPriceItemBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    /** 更新对象:saveChargeItemPerformDepartmentList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateSaveChargeItemPerformDepartmentListOnMissThrowEx(
            BaseChargeItemBOService.SaveChargeItemPerformDepartmentListBoResult boResult,
            SaveChargeItemPerformDepartmentListBto saveChargeItemPerformDepartmentListBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveChargeItemPerformDepartmentListBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO =
                    ChargeItemBO.getByItemCode(
                            saveChargeItemPerformDepartmentListBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(saveChargeItemPerformDepartmentListBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    /** 更新对象:saveChargeItemPriceDetailList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateSaveChargeItemPriceDetailListOnMissThrowEx(
            BaseChargeItemBOService.SaveChargeItemPriceDetailListBoResult boResult,
            SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveChargeItemPriceDetailListBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO =
                    ChargeItemBO.getByItemCode(saveChargeItemPriceDetailListBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(saveChargeItemPriceDetailListBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    /** 更新对象:updateChargeItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateUpdateChargeItemOnMissThrowEx(
            BaseChargeItemBOService.UpdateChargeItemBoResult boResult,
            UpdateChargeItemBto updateChargeItemBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateChargeItemBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(updateChargeItemBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(updateChargeItemBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "organizationId")) {
                chargeItemBO.setOrganizationId(updateChargeItemBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "sortNumber")) {
                chargeItemBO.setSortNumber(updateChargeItemBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "itemName")) {
                chargeItemBO.setItemName(updateChargeItemBto.getItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "itemSpecification")) {
                chargeItemBO.setItemSpecification(updateChargeItemBto.getItemSpecification());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "inputCode")) {
                chargeItemBO.setInputCode(updateChargeItemBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "packageType")) {
                chargeItemBO.setPackageType(updateChargeItemBto.getPackageType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "leafFlag")) {
                chargeItemBO.setLeafFlag(updateChargeItemBto.getLeafFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "itemCategory")) {
                chargeItemBO.setItemCategory(updateChargeItemBto.getItemCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "chargeItemCategory")) {
                chargeItemBO.setChargeItemCategory(updateChargeItemBto.getChargeItemCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "reckoningCategory")) {
                chargeItemBO.setReckoningCategory(updateChargeItemBto.getReckoningCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "tcmMedicalRecordCategory")) {
                chargeItemBO.setTcmMedicalRecordCategory(
                        updateChargeItemBto.getTcmMedicalRecordCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "accountingSubjectCategory")) {
                chargeItemBO.setAccountingSubjectCategory(
                        updateChargeItemBto.getAccountingSubjectCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "medicalRecordCategory")) {
                chargeItemBO.setMedicalRecordCategory(
                        updateChargeItemBto.getMedicalRecordCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "medicalRecordSubCategory")) {
                chargeItemBO.setMedicalRecordSubCategory(
                        updateChargeItemBto.getMedicalRecordSubCategory());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "unit")) {
                chargeItemBO.setUnit(updateChargeItemBto.getUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "remark")) {
                chargeItemBO.setRemark(updateChargeItemBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "insuranceItemLevel")) {
                chargeItemBO.setInsuranceItemLevel(updateChargeItemBto.getInsuranceItemLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "insuranceNationCode")) {
                chargeItemBO.setInsuranceNationCode(updateChargeItemBto.getInsuranceNationCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "insuranceProvinceCode")) {
                chargeItemBO.setInsuranceProvinceCode(
                        updateChargeItemBto.getInsuranceProvinceCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "insuranceNationName")) {
                chargeItemBO.setInsuranceNationName(updateChargeItemBto.getInsuranceNationName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "singleSettleFlag")) {
                chargeItemBO.setSingleSettleFlag(updateChargeItemBto.getSingleSettleFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "independentPricingFlag")) {
                chargeItemBO.setIndependentPricingFlag(
                        updateChargeItemBto.getIndependentPricingFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "synchronizedFlag")) {
                chargeItemBO.setSynchronizedFlag(updateChargeItemBto.getSynchronizedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "enableFlag")) {
                chargeItemBO.setEnableFlag(updateChargeItemBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "campusIdList")) {
                chargeItemBO.setCampusIdList(updateChargeItemBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "useScopeList")) {
                chargeItemBO.setUseScopeList(updateChargeItemBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "source")) {
                chargeItemBO.setSource(updateChargeItemBto.getSource());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "itemConnotation")) {
                chargeItemBO.setItemConnotation(updateChargeItemBto.getItemConnotation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "firmCode")) {
                chargeItemBO.setFirmCode(updateChargeItemBto.getFirmCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "biddingCode")) {
                chargeItemBO.setBiddingCode(updateChargeItemBto.getBiddingCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "standardCode")) {
                chargeItemBO.setStandardCode(updateChargeItemBto.getStandardCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "extensionField")) {
                chargeItemBO.setExtensionField(updateChargeItemBto.getExtensionField());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "parentItemCode")) {
                chargeItemBO.setParentItemCode(updateChargeItemBto.getParentItemCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "createdBy")) {
                chargeItemBO.setCreatedBy(updateChargeItemBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateChargeItemBto, "__$validPropertySet"),
                    "updatedBy")) {
                chargeItemBO.setUpdatedBy(updateChargeItemBto.getUpdatedBy());
            }
            return chargeItemBO;
        }
    }

    /** 更新对象:updateChargeItemPriceDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ChargeItemBO updateUpdateChargeItemPriceDetailOnMissThrowEx(
            UpdateChargeItemPriceDetailBoResult boResult,
            UpdateChargeItemPriceDetailBto updateChargeItemPriceDetailBto) {
        ChargeItemBO chargeItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateChargeItemPriceDetailBto.getItemCode() == null);
        if (!allNull && !found) {
            chargeItemBO = ChargeItemBO.getByItemCode(updateChargeItemPriceDetailBto.getItemCode());
            found = true;
        }
        if (chargeItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(chargeItemBO.convertToChargeItem());
            updatedBto.setBto(updateChargeItemPriceDetailBto);
            updatedBto.setBo(chargeItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return chargeItemBO;
        }
    }

    public static class CreateChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateChargeItemBto, ChargeItemBO> getCreatedBto(
                CreateChargeItemBto createChargeItemBto) {
            return this.getAddedResult(createChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateChargeItemBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                CreateChargeItemBto createChargeItemBto) {
            return super.getUpdatedResult(createChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateChargeItemBto, ChargeItemBO> getUnmodifiedBto(
                CreateChargeItemBto createChargeItemBto) {
            return super.getUnmodifiedResult(createChargeItemBto);
        }
    }

    public static class CreateChargeItemPriceDetailListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getCreatedBto(
                        CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return this.getAddedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateChargeItemPriceDetailListBto, ChargeItemBO> getCreatedBto(
                CreateChargeItemPriceDetailListBto createChargeItemPriceDetailListBto) {
            return this.getAddedResult(createChargeItemPriceDetailListBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPriceDetail getDeleted_ChargeItemPriceDetail() {
            return (ChargeItemPriceDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItemPriceDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetail,
                        ChargeItemPriceDetailBO>
                getUpdatedBto(
                        CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUpdatedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateChargeItemPriceDetailListBto, ChargeItem, ChargeItemBO>
                getUpdatedBto(
                        CreateChargeItemPriceDetailListBto createChargeItemPriceDetailListBto) {
            return super.getUpdatedResult(createChargeItemPriceDetailListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getUnmodifiedBto(
                        CreateChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateChargeItemPriceDetailListBto, ChargeItemBO> getUnmodifiedBto(
                CreateChargeItemPriceDetailListBto createChargeItemPriceDetailListBto) {
            return super.getUnmodifiedResult(createChargeItemPriceDetailListBto);
        }
    }

    public static class ChangeChargeItemEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeChargeItemEnableFlagBto, ChargeItemBO> getCreatedBto(
                ChangeChargeItemEnableFlagBto changeChargeItemEnableFlagBto) {
            return this.getAddedResult(changeChargeItemEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ChangeChargeItemEnableFlagBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                ChangeChargeItemEnableFlagBto changeChargeItemEnableFlagBto) {
            return super.getUpdatedResult(changeChargeItemEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeChargeItemEnableFlagBto, ChargeItemBO> getUnmodifiedBto(
                ChangeChargeItemEnableFlagBto changeChargeItemEnableFlagBto) {
            return super.getUnmodifiedResult(changeChargeItemEnableFlagBto);
        }
    }

    public static class ChangeChargeItemAuditStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getCreatedBto(
                        ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return this.getAddedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeChargeItemAuditStatusBto, ChargeItemBO> getCreatedBto(
                ChangeChargeItemAuditStatusBto changeChargeItemAuditStatusBto) {
            return this.getAddedResult(changeChargeItemAuditStatusBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPriceDetail getDeleted_ChargeItemPriceDetail() {
            return (ChargeItemPriceDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItemPriceDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetail,
                        ChargeItemPriceDetailBO>
                getUpdatedBto(
                        ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUpdatedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ChangeChargeItemAuditStatusBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                ChangeChargeItemAuditStatusBto changeChargeItemAuditStatusBto) {
            return super.getUpdatedResult(changeChargeItemAuditStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getUnmodifiedBto(
                        ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeChargeItemAuditStatusBto, ChargeItemBO> getUnmodifiedBto(
                ChangeChargeItemAuditStatusBto changeChargeItemAuditStatusBto) {
            return super.getUnmodifiedResult(changeChargeItemAuditStatusBto);
        }
    }

    public static class SaveChargeItemPriceDetailListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getCreatedBto(
                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return this.getAddedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveChargeItemPriceDetailListBto, ChargeItemBO> getCreatedBto(
                SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto) {
            return this.getAddedResult(saveChargeItemPriceDetailListBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPriceDetail getDeleted_ChargeItemPriceDetail() {
            return (ChargeItemPriceDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItemPriceDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetail,
                        ChargeItemPriceDetailBO>
                getUpdatedBto(
                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUpdatedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveChargeItemPriceDetailListBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto) {
            return super.getUpdatedResult(saveChargeItemPriceDetailListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getUnmodifiedBto(
                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveChargeItemPriceDetailListBto, ChargeItemBO> getUnmodifiedBto(
                SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto) {
            return super.getUnmodifiedResult(saveChargeItemPriceDetailListBto);
        }
    }

    public static class SaveChargeItemPerformDepartmentListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto,
                        ChargeItemPerformDepartmentBO>
                getCreatedBto(
                        SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto
                                chargeItemPerformDepartmentBto) {
            return this.getAddedResult(chargeItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveChargeItemPerformDepartmentListBto, ChargeItemBO> getCreatedBto(
                SaveChargeItemPerformDepartmentListBto saveChargeItemPerformDepartmentListBto) {
            return this.getAddedResult(saveChargeItemPerformDepartmentListBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPerformDepartment getDeleted_ChargeItemPerformDepartment() {
            return (ChargeItemPerformDepartment)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ChargeItemPerformDepartment.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto,
                        ChargeItemPerformDepartment,
                        ChargeItemPerformDepartmentBO>
                getUpdatedBto(
                        SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto
                                chargeItemPerformDepartmentBto) {
            return super.getUpdatedResult(chargeItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveChargeItemPerformDepartmentListBto, ChargeItem, ChargeItemBO>
                getUpdatedBto(
                        SaveChargeItemPerformDepartmentListBto
                                saveChargeItemPerformDepartmentListBto) {
            return super.getUpdatedResult(saveChargeItemPerformDepartmentListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto,
                        ChargeItemPerformDepartmentBO>
                getUnmodifiedBto(
                        SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto
                                chargeItemPerformDepartmentBto) {
            return super.getUnmodifiedResult(chargeItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveChargeItemPerformDepartmentListBto, ChargeItemBO> getUnmodifiedBto(
                SaveChargeItemPerformDepartmentListBto saveChargeItemPerformDepartmentListBto) {
            return super.getUnmodifiedResult(saveChargeItemPerformDepartmentListBto);
        }
    }

    public static class UpdateChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateChargeItemBto, ChargeItemBO> getCreatedBto(
                UpdateChargeItemBto updateChargeItemBto) {
            return this.getAddedResult(updateChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateChargeItemBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                UpdateChargeItemBto updateChargeItemBto) {
            return super.getUpdatedResult(updateChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateChargeItemBto, ChargeItemBO> getUnmodifiedBto(
                UpdateChargeItemBto updateChargeItemBto) {
            return super.getUnmodifiedResult(updateChargeItemBto);
        }
    }

    public static class SaveChargeItemPackageBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveChargeItemPackageBto.ChargePackageDetailBto, ChargePackageDetailBO>
                getCreatedBto(
                        SaveChargeItemPackageBto.ChargePackageDetailBto chargePackageDetailBto) {
            return this.getAddedResult(chargePackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveChargeItemPackageBto, ChargeItemBO> getCreatedBto(
                SaveChargeItemPackageBto saveChargeItemPackageBto) {
            return this.getAddedResult(saveChargeItemPackageBto);
        }

        @AutoGenerated(locked = true)
        public ChargePackageDetail getDeleted_ChargePackageDetail() {
            return (ChargePackageDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargePackageDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveChargeItemPackageBto.ChargePackageDetailBto,
                        ChargePackageDetail,
                        ChargePackageDetailBO>
                getUpdatedBto(
                        SaveChargeItemPackageBto.ChargePackageDetailBto chargePackageDetailBto) {
            return super.getUpdatedResult(chargePackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveChargeItemPackageBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                SaveChargeItemPackageBto saveChargeItemPackageBto) {
            return super.getUpdatedResult(saveChargeItemPackageBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveChargeItemPackageBto.ChargePackageDetailBto, ChargePackageDetailBO>
                getUnmodifiedBto(
                        SaveChargeItemPackageBto.ChargePackageDetailBto chargePackageDetailBto) {
            return super.getUnmodifiedResult(chargePackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveChargeItemPackageBto, ChargeItemBO> getUnmodifiedBto(
                SaveChargeItemPackageBto saveChargeItemPackageBto) {
            return super.getUnmodifiedResult(saveChargeItemPackageBto);
        }
    }

    public static class UpdateChargeItemPriceDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getCreatedBto(
                        UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return this.getAddedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateChargeItemPriceDetailBto, ChargeItemBO> getCreatedBto(
                UpdateChargeItemPriceDetailBto updateChargeItemPriceDetailBto) {
            return this.getAddedResult(updateChargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPriceDetail getDeleted_ChargeItemPriceDetail() {
            return (ChargeItemPriceDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItemPriceDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetail,
                        ChargeItemPriceDetailBO>
                getUpdatedBto(
                        UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUpdatedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateChargeItemPriceDetailBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                UpdateChargeItemPriceDetailBto updateChargeItemPriceDetailBto) {
            return super.getUpdatedResult(updateChargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getUnmodifiedBto(
                        UpdateChargeItemPriceDetailBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateChargeItemPriceDetailBto, ChargeItemBO> getUnmodifiedBto(
                UpdateChargeItemPriceDetailBto updateChargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(updateChargeItemPriceDetailBto);
        }
    }

    public static class CreateChargeItemPriceDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getCreatedBto(
                        CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return this.getAddedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateChargeItemPriceDetailBto, ChargeItemBO> getCreatedBto(
                CreateChargeItemPriceDetailBto createChargeItemPriceDetailBto) {
            return this.getAddedResult(createChargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPriceDetail getDeleted_ChargeItemPriceDetail() {
            return (ChargeItemPriceDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItemPriceDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetail,
                        ChargeItemPriceDetailBO>
                getUpdatedBto(
                        CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUpdatedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateChargeItemPriceDetailBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                CreateChargeItemPriceDetailBto createChargeItemPriceDetailBto) {
            return super.getUpdatedResult(createChargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getUnmodifiedBto(
                        CreateChargeItemPriceDetailBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateChargeItemPriceDetailBto, ChargeItemBO> getUnmodifiedBto(
                CreateChargeItemPriceDetailBto createChargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(createChargeItemPriceDetailBto);
        }
    }

    public static class PerformChargeItemPriceItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ChargeItemBO getRootBo() {
            return (ChargeItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getCreatedBto(
                        PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return this.getAddedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<PerformChargeItemPriceItemBto, ChargeItemBO> getCreatedBto(
                PerformChargeItemPriceItemBto performChargeItemPriceItemBto) {
            return this.getAddedResult(performChargeItemPriceItemBto);
        }

        @AutoGenerated(locked = true)
        public ChargeItemPriceDetail getDeleted_ChargeItemPriceDetail() {
            return (ChargeItemPriceDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItemPriceDetail.class));
        }

        @AutoGenerated(locked = true)
        public ChargeItem getDeleted_ChargeItem() {
            return (ChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetail,
                        ChargeItemPriceDetailBO>
                getUpdatedBto(
                        PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUpdatedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<PerformChargeItemPriceItemBto, ChargeItem, ChargeItemBO> getUpdatedBto(
                PerformChargeItemPriceItemBto performChargeItemPriceItemBto) {
            return super.getUpdatedResult(performChargeItemPriceItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto,
                        ChargeItemPriceDetailBO>
                getUnmodifiedBto(
                        PerformChargeItemPriceItemBto.ChargeItemPriceDetailBto
                                chargeItemPriceDetailBto) {
            return super.getUnmodifiedResult(chargeItemPriceDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<PerformChargeItemPriceItemBto, ChargeItemBO> getUnmodifiedBto(
                PerformChargeItemPriceItemBto performChargeItemPriceItemBto) {
            return super.getUnmodifiedResult(performChargeItemPriceItemBto);
        }
    }
}
