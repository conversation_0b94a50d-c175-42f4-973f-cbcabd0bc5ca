package com.pulse.billing_public_config.service.index.entity;

import com.pulse.billing_public_config.persist.mapper.SearchChargeItemPriceQtoDao;
import com.pulse.billing_public_config.persist.qto.SearchChargeItemPriceQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "f464767f-6e56-4f57-9a81-7813eb9215c4|QTO|SERVICE")
public class SearchChargeItemPriceQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchChargeItemPriceQtoDao searchChargeItemPriceMapper;

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchChargeItemPriceQto qto) {
        return searchChargeItemPriceMapper.count(qto);
    }

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "f464767f-6e56-4f57-9a81-7813eb9215c4-query")
    public List<String> query(SearchChargeItemPriceQto qto) {
        return searchChargeItemPriceMapper.query(qto);
    }
}
