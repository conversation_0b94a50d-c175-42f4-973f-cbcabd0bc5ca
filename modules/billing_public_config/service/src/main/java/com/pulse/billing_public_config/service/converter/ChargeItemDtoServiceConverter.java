package com.pulse.billing_public_config.service.converter;

import cn.hutool.core.collection.CollUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailBaseDto;
import com.pulse.billing_public_config.service.ChargeItemPriceDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "a46d477b-05f6-3d14-9d7a-d226c5c21fa2")
public class ChargeItemDtoServiceConverter {

    @Resource private ChargeItemPriceDetailBaseDtoService chargeItemPriceDetailBaseDtoService;

    @AutoGenerated(locked = false)
    public List<ChargeItemDto> ChargeItemDtoConverter(List<ChargeItemDto> chargeItemDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return chargeItemDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    public List<ChargeItemDto> ChargeItemDtoConverter(
            List<ChargeItemDto> chargeItemDtoList, List<String> itemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollUtil.isNotEmpty(itemCode)) {
            // fixme 需要过滤出当前院区有效价格
            Map<String, ChargeItemPriceDetailBaseDto> chargeItemPriceDetailBaseDtoMap =
                    chargeItemPriceDetailBaseDtoService.getByChargeItemCodes(itemCode).stream()
                            .filter(
                                    chargeItemPriceDetailBaseDto ->
                                            "COMMON"
                                                    .equals(
                                                            chargeItemPriceDetailBaseDto
                                                                    .getPriceType()))
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemPriceDetailBaseDto::getChargeItemCode,
                                            Function.identity()));
            for (ChargeItemDto chargeItemDto : chargeItemDtoList) {
                chargeItemDto.setPrice(
                        Optional.ofNullable(
                                        chargeItemPriceDetailBaseDtoMap.get(
                                                chargeItemDto.getItemCode()))
                                .orElse(new ChargeItemPriceDetailBaseDto())
                                .getPrice());
            }
        }
        return chargeItemDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
