package com.pulse.billing_public_config.service.query.collector;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailBaseDto;
import com.pulse.billing_public_config.service.ChargeItemBaseDtoService;
import com.pulse.billing_public_config.service.ChargeItemPriceDetailBaseDtoService;
import com.pulse.billing_public_config.service.query.assembler.ChargeItemListDtoDataAssembler.ChargeItemListDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ChargeItemListDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "50859676-c953-35d0-b7e1-451e4b10300d")
public class ChargeItemListDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemBaseDtoService chargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemListDtoDataCollector chargeItemListDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailBaseDtoService chargeItemPriceDetailBaseDtoService;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "0f8f123e-73c2-3b85-a6db-8ed2d5f4bf6c")
    private void fillDataWhenNecessary(ChargeItemListDtoDataHolder dataHolder) {
        List<ChargeItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.chargeItemPriceDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ChargeItemBaseDto::getItemCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ChargeItemPriceDetailBaseDto> baseDtoList =
                    chargeItemPriceDetailBaseDtoService
                            .getByChargeItemCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ChargeItemPriceDetailBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<ChargeItemPriceDetailBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ChargeItemPriceDetailBaseDto::getChargeItemCode));
            dataHolder.chargeItemPriceDetailList =
                    rootDtoList.stream()
                            .map(ChargeItemBaseDto::getItemCode)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "671d8f65-f486-3226-b631-f2ca7ca6837d")
    public void collectDataDefault(ChargeItemListDtoDataHolder dataHolder) {
        chargeItemListDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
