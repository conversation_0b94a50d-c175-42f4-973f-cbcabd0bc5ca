package com.pulse.billing_public_config.service.query;

import com.pulse.billing_public_config.manager.converter.ChargeItemPriceDetailChargeItemExtDtoConverter;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailChargeItemExtDto;
import com.pulse.billing_public_config.persist.qto.SearchChargeItemPriceDetailQto;
import com.pulse.billing_public_config.service.ChargeItemPriceDetailBaseDtoService;
import com.pulse.billing_public_config.service.index.entity.SearchChargeItemPriceDetailQtoService;
import com.pulse.billing_public_config.service.query.assembler.ChargeItemPriceDetailChargeItemExtDtoDataAssembler;
import com.pulse.billing_public_config.service.query.assembler.ChargeItemPriceDetailChargeItemExtDtoDataAssembler.ChargeItemPriceDetailChargeItemExtDtoDataHolder;
import com.pulse.billing_public_config.service.query.collector.ChargeItemPriceDetailChargeItemExtDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** ChargeItemPriceDetailChargeItemExtDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "53c213aa-752b-3443-ad3e-141be7024340")
public class ChargeItemPriceDetailChargeItemExtDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailBaseDtoService chargeItemPriceDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailChargeItemExtDtoConverter
            chargeItemPriceDetailChargeItemExtDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailChargeItemExtDtoDataAssembler
            chargeItemPriceDetailChargeItemExtDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailChargeItemExtDtoDataCollector
            chargeItemPriceDetailChargeItemExtDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchChargeItemPriceDetailQtoService searchChargeItemPriceDetailQtoService;

    /** 根据SearchChargeItemPriceDetailQto查询ChargeItemPriceDetailChargeItemExtDto列表,分页 */
    @PublicInterface(id = "c235a088-f553-4199-ab3c-3f3bc3483b47", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "11eed939-b17f-3ba4-9017-6c93a3e1880c")
    public VSQueryResult<ChargeItemPriceDetailChargeItemExtDto> searchChargeItemPriceDetailPaged(
            @Valid @NotNull SearchChargeItemPriceDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchChargeItemPriceDetailQtoService.queryPaged(qto);
        ChargeItemPriceDetailChargeItemExtDtoDataHolder dataHolder =
                new ChargeItemPriceDetailChargeItemExtDtoDataHolder();
        List<ChargeItemPriceDetailChargeItemExtDto> dtoList = toDtoList(ids, dataHolder);
        chargeItemPriceDetailChargeItemExtDtoDataCollector.collectDataDefault(dataHolder);
        chargeItemPriceDetailChargeItemExtDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchChargeItemPriceDetailQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "492b782c-7753-3adc-9cd4-62726d53bd3c")
    private List<ChargeItemPriceDetailChargeItemExtDto> toDtoList(
            List<String> ids, ChargeItemPriceDetailChargeItemExtDtoDataHolder dataHolder) {
        List<ChargeItemPriceDetailBaseDto> baseDtoList =
                chargeItemPriceDetailBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, ChargeItemPriceDetailChargeItemExtDto> dtoMap =
                chargeItemPriceDetailChargeItemExtDtoConverter
                        .convertFromChargeItemPriceDetailBaseDtoToChargeItemPriceDetailChargeItemExtDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ChargeItemPriceDetailChargeItemExtDto::getId,
                                        Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
