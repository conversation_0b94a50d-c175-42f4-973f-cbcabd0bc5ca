package com.pulse.billing_public_config.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.ChargeItemPerformDepartmentBaseDtoManager;
import com.pulse.billing_public_config.manager.dto.ChargeItemPerformDepartmentBaseDto;
import com.pulse.billing_public_config.service.converter.ChargeItemPerformDepartmentBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "11abaad9-86c1-4a15-829c-c6f88cd6bc03|DTO|SERVICE")
public class ChargeItemPerformDepartmentBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemPerformDepartmentBaseDtoManager chargeItemPerformDepartmentBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemPerformDepartmentBaseDtoServiceConverter
            chargeItemPerformDepartmentBaseDtoServiceConverter;

    @PublicInterface(id = "4d87892a-b9ea-4fe1-b891-cf8ef028fae9", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "1de70484-aa65-33fd-96a7-bc2379bc4e7a")
    public List<ChargeItemPerformDepartmentBaseDto> getByDepartmentIds(
            @Valid @NotNull(message = "科室id不能为空") List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        departmentId = new ArrayList<>(new HashSet<>(departmentId));
        List<ChargeItemPerformDepartmentBaseDto> chargeItemPerformDepartmentBaseDtoList =
                chargeItemPerformDepartmentBaseDtoManager.getByDepartmentIds(departmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return chargeItemPerformDepartmentBaseDtoServiceConverter
                .ChargeItemPerformDepartmentBaseDtoConverter(
                        chargeItemPerformDepartmentBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "30c2d3a9-86a6-49b1-b2c2-a390c77bb8c9", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "367f7764-8d6d-383f-911d-de2c90e702d4")
    public List<ChargeItemPerformDepartmentBaseDto> getByDepartmentId(
            @NotNull(message = "科室id不能为空") String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDepartmentIds(Arrays.asList(departmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "3a069ff6-5b00-49e4-8b19-65cf5f220e58", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "8ca86cd8-6fc5-39a9-95e2-1c481cef8f59")
    public List<ChargeItemPerformDepartmentBaseDto> getByItemCode(
            @NotNull(message = "项目代码不能为空") String itemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByItemCodes(Arrays.asList(itemCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "039de401-685e-4206-9d2a-3e3003c55850", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "9f563bd8-bc02-34dc-a6ba-aa413e1141af")
    public List<ChargeItemPerformDepartmentBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ChargeItemPerformDepartmentBaseDto> chargeItemPerformDepartmentBaseDtoList =
                chargeItemPerformDepartmentBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return chargeItemPerformDepartmentBaseDtoServiceConverter
                .ChargeItemPerformDepartmentBaseDtoConverter(
                        chargeItemPerformDepartmentBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "5ec1758e-d1fe-40b0-bdfc-9943d183349e", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "d8dd0ec3-4d84-31f1-8c55-f13f4c2328a4")
    public ChargeItemPerformDepartmentBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargeItemPerformDepartmentBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "52399f56-c4d1-4c0c-84b4-49d7a1eb09ee", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "fdff4477-5a62-38a8-b3cb-a4b90b40ec67")
    public List<ChargeItemPerformDepartmentBaseDto> getByItemCodes(
            @Valid @NotNull(message = "项目代码不能为空") List<String> itemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        itemCode = new ArrayList<>(new HashSet<>(itemCode));
        List<ChargeItemPerformDepartmentBaseDto> chargeItemPerformDepartmentBaseDtoList =
                chargeItemPerformDepartmentBaseDtoManager.getByItemCodes(itemCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return chargeItemPerformDepartmentBaseDtoServiceConverter
                .ChargeItemPerformDepartmentBaseDtoConverter(
                        chargeItemPerformDepartmentBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
