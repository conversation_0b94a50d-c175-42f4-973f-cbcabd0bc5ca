package com.pulse.billing_public_config.service.converter;

import com.pulse.billing_public_config.manager.dto.ChargeItemAdjustPriceReceiptDetailChargeExtDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "08f0828e-2164-35c3-94b4-9d06dd1555c7")
public class ChargeItemAdjustPriceReceiptDetailChargeExtDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ChargeItemAdjustPriceReceiptDetailChargeExtDto>
            ChargeItemAdjustPriceReceiptDetailChargeExtDtoConverter(
                    List<ChargeItemAdjustPriceReceiptDetailChargeExtDto>
                            chargeItemAdjustPriceReceiptDetailChargeExtDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return chargeItemAdjustPriceReceiptDetailChargeExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
