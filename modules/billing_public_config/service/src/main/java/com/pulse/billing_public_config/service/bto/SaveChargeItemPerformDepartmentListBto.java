package com.pulse.billing_public_config.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ChargeItem
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "e4c6c434-b363-4119-ae21-ff70f7cd12b2|BTO|DEFINITION")
public class SaveChargeItemPerformDepartmentListBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "5a682736-ae74-4a58-b345-d2c2563097d3")
    private List<SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto>
            chargeItemPerformDepartmentBtoList;

    /** 项目代码 */
    @AutoGenerated(locked = true, uuid = "d7b9f184-0e81-4bc9-919e-d982e7c056e0")
    private String itemCode;

    @AutoGenerated(locked = true)
    public void setChargeItemPerformDepartmentBtoList(
            List<SaveChargeItemPerformDepartmentListBto.ChargeItemPerformDepartmentBto>
                    chargeItemPerformDepartmentBtoList) {
        this.__$validPropertySet.add("chargeItemPerformDepartmentBtoList");
        this.chargeItemPerformDepartmentBtoList = chargeItemPerformDepartmentBtoList;
    }

    @AutoGenerated(locked = true)
    public void setItemCode(String itemCode) {
        this.__$validPropertySet.add("itemCode");
        this.itemCode = itemCode;
    }

    /**
     * <b>[源自]</b> ChargeItemPerformDepartment
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ChargeItemPerformDepartmentBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "6c38e388-8c1c-4908-8295-fc03a1167521")
        private String id;

        /** 科室id */
        @AutoGenerated(locked = true, uuid = "3f37e540-5367-4fab-aad4-2bfdd5e10bc9")
        private String departmentId;

        /** 默认标志 */
        @AutoGenerated(locked = true, uuid = "7c480aef-d9d1-4a37-bb56-f0cd0540bcc2")
        private Boolean defaultFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setDepartmentId(String departmentId) {
            this.__$validPropertySet.add("departmentId");
            this.departmentId = departmentId;
        }

        @AutoGenerated(locked = true)
        public void setDefaultFlag(Boolean defaultFlag) {
            this.__$validPropertySet.add("defaultFlag");
            this.defaultFlag = defaultFlag;
        }
    }
}
