package com.pulse.billing_public_config.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ChargeItemAdjustPriceReceipt
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "affbea5c-f2a3-4c7f-bd4e-d934eeb0e9fd|BTO|DEFINITION")
public class UpdateChargeItemPriceReceiptDetailBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "2c4c568f-549c-4da0-bc6a-92f3a1ed9339")
    private List<UpdateChargeItemPriceReceiptDetailBto.ChargeItemAdjustPriceReceiptDetailBto>
            chargeItemAdjustPriceReceiptDetailBtoList;

    /** 调价单号 */
    @AutoGenerated(locked = true, uuid = "69cba44e-6822-4d15-9648-9f47037983c6")
    private String receiptNumber;

    @AutoGenerated(locked = true)
    public void setChargeItemAdjustPriceReceiptDetailBtoList(
            List<UpdateChargeItemPriceReceiptDetailBto.ChargeItemAdjustPriceReceiptDetailBto>
                    chargeItemAdjustPriceReceiptDetailBtoList) {
        this.__$validPropertySet.add("chargeItemAdjustPriceReceiptDetailBtoList");
        this.chargeItemAdjustPriceReceiptDetailBtoList = chargeItemAdjustPriceReceiptDetailBtoList;
    }

    @AutoGenerated(locked = true)
    public void setReceiptNumber(String receiptNumber) {
        this.__$validPropertySet.add("receiptNumber");
        this.receiptNumber = receiptNumber;
    }

    /**
     * <b>[源自]</b> ChargeItemAdjustPriceReceiptDetail
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ChargeItemAdjustPriceReceiptDetailBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "cacf4e96-2d60-4b3b-a258-7b90485206c9")
        private String id;

        /** 排序编号 */
        @AutoGenerated(locked = true, uuid = "7d05cf61-2476-43ef-9899-b5d5e041bc18")
        private Long sortNumber;

        /** 院区id */
        @AutoGenerated(locked = true, uuid = "a0b4fa30-31cb-4a37-be14-435127cd3c02")
        private String campusId;

        /** 项目代码 */
        @AutoGenerated(locked = true, uuid = "89c3555c-f9e6-421e-bcf7-8e2a4fb9095d")
        private String itemCode;

        /** 价格类型 */
        @AutoGenerated(locked = true, uuid = "dcf36a6d-31f5-4730-b962-0ad308ff999f")
        private String priceType;

        /** 原价 */
        @AutoGenerated(locked = true, uuid = "3f4028b9-542b-47c5-a881-1509c5d26a34")
        private BigDecimal originalPrice;

        /** 新价格 */
        @AutoGenerated(locked = true, uuid = "95a908b8-5fa5-445d-a295-ca3376da6629")
        private BigDecimal newPrice;

        /** 收费项目价格详情ID */
        @AutoGenerated(locked = true, uuid = "7a6efe2e-ff25-44e6-b2ba-d665b40799fd")
        private String chargeItemPriceDetailId;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "46b7631e-8273-4f28-a78a-6167c01c159c")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "05a914a4-c402-46ca-b8e7-20312c1775a9")
        private String createdBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setSortNumber(Long sortNumber) {
            this.__$validPropertySet.add("sortNumber");
            this.sortNumber = sortNumber;
        }

        @AutoGenerated(locked = true)
        public void setCampusId(String campusId) {
            this.__$validPropertySet.add("campusId");
            this.campusId = campusId;
        }

        @AutoGenerated(locked = true)
        public void setItemCode(String itemCode) {
            this.__$validPropertySet.add("itemCode");
            this.itemCode = itemCode;
        }

        @AutoGenerated(locked = true)
        public void setPriceType(String priceType) {
            this.__$validPropertySet.add("priceType");
            this.priceType = priceType;
        }

        @AutoGenerated(locked = true)
        public void setOriginalPrice(BigDecimal originalPrice) {
            this.__$validPropertySet.add("originalPrice");
            this.originalPrice = originalPrice;
        }

        @AutoGenerated(locked = true)
        public void setNewPrice(BigDecimal newPrice) {
            this.__$validPropertySet.add("newPrice");
            this.newPrice = newPrice;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemPriceDetailId(String chargeItemPriceDetailId) {
            this.__$validPropertySet.add("chargeItemPriceDetailId");
            this.chargeItemPriceDetailId = chargeItemPriceDetailId;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }
    }
}
