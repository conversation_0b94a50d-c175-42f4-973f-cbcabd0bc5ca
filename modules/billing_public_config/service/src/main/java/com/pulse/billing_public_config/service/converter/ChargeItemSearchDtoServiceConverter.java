package com.pulse.billing_public_config.service.converter;

import com.pulse.billing_public_config.manager.dto.ChargeItemSearchDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "8e409fc4-2286-3710-8ce9-0dd32d70b89d")
public class ChargeItemSearchDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ChargeItemSearchDto> ChargeItemSearchDtoConverter(
            List<ChargeItemSearchDto> chargeItemSearchDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return chargeItemSearchDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
