package com.pulse.billing_public_config.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.ChargeItemBaseDtoManager;
import com.pulse.billing_public_config.manager.ChargePackageDetailBaseDtoManager;
import com.pulse.billing_public_config.manager.ChargePackageDetailChargePackageExtDtoManager;
import com.pulse.billing_public_config.manager.converter.ChargePackageDetailBaseDtoConverter;
import com.pulse.billing_public_config.manager.converter.ChargePackageDetailChargePackageExtDtoConverter;
import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargePackageDetailBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargePackageDetailChargePackageExtDto;
import com.pulse.billing_public_config.persist.dos.ChargePackageDetail;
import com.pulse.billing_public_config.persist.mapper.ChargePackageDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "d052f22d-049a-4101-acbf-4edba3d249a4|DTO|BASE_MANAGER_IMPL")
public abstract class ChargePackageDetailChargePackageExtDtoManagerBaseImpl
        implements ChargePackageDetailChargePackageExtDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemBaseDtoManager chargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargePackageDetailBaseDtoConverter chargePackageDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargePackageDetailBaseDtoManager chargePackageDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargePackageDetailChargePackageExtDtoConverter
            chargePackageDetailChargePackageExtDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargePackageDetailDao chargePackageDetailDao;

    @AutoGenerated(locked = true, uuid = "20efcbf2-a3f1-3dbc-9a6c-88a1f6fce25a")
    @Override
    public List<ChargePackageDetailChargePackageExtDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<ChargePackageDetail> chargePackageDetailList = chargePackageDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(chargePackageDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, ChargePackageDetail> chargePackageDetailMap =
                chargePackageDetailList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        chargePackageDetailList =
                id.stream()
                        .map(i -> chargePackageDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromChargePackageDetailToChargePackageDetailChargePackageExtDto(
                chargePackageDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2c5f2abe-15e9-3cad-8095-a425f566f5e8")
    @Override
    public List<ChargePackageDetailChargePackageExtDto> getByChargeItemIds(
            List<String> chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemId)) {
            return Collections.emptyList();
        }

        List<ChargePackageDetail> chargePackageDetailList =
                chargePackageDetailDao.getByChargeItemIds(chargeItemId);
        if (CollectionUtil.isEmpty(chargePackageDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromChargePackageDetailToChargePackageDetailChargePackageExtDto(
                chargePackageDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2d6b2268-122a-3604-b382-45d1aa115b37")
    @Override
    public ChargePackageDetailChargePackageExtDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargePackageDetailChargePackageExtDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        ChargePackageDetailChargePackageExtDto chargePackageDetailChargePackageExtDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return chargePackageDetailChargePackageExtDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "553b55db-8418-317f-b358-99b4d4888409")
    @Override
    public List<ChargePackageDetailChargePackageExtDto> getByChargePackageIds(
            List<String> chargePackageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargePackageId)) {
            return Collections.emptyList();
        }

        List<ChargePackageDetail> chargePackageDetailList =
                chargePackageDetailDao.getByChargePackageIds(chargePackageId);
        if (CollectionUtil.isEmpty(chargePackageDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromChargePackageDetailToChargePackageDetailChargePackageExtDto(
                chargePackageDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "98daaeb8-8045-392d-9705-1bf4356945da")
    @Override
    public List<ChargePackageDetailChargePackageExtDto> getByChargePackageId(
            String chargePackageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargePackageDetailChargePackageExtDto> chargePackageDetailChargePackageExtDtoList =
                getByChargePackageIds(Arrays.asList(chargePackageId));
        return chargePackageDetailChargePackageExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cd7276d9-efeb-3391-8cce-433959467e12")
    @Override
    public List<ChargePackageDetailChargePackageExtDto> getByChargeItemId(String chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargePackageDetailChargePackageExtDto> chargePackageDetailChargePackageExtDtoList =
                getByChargeItemIds(Arrays.asList(chargeItemId));
        return chargePackageDetailChargePackageExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e73f8082-eac0-3806-98d1-ee0fed058dbc")
    public List<ChargePackageDetailChargePackageExtDto>
            doConvertFromChargePackageDetailToChargePackageDetailChargePackageExtDto(
                    List<ChargePackageDetail> chargePackageDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargePackageDetailList)) {
            return Collections.emptyList();
        }

        Map<String, String> chargePackageIdMap =
                chargePackageDetailList.stream()
                        .filter(i -> i.getChargePackageId() != null)
                        .collect(
                                Collectors.toMap(
                                        ChargePackageDetail::getId,
                                        ChargePackageDetail::getChargePackageId));
        List<ChargeItemBaseDto> chargePackageIdChargeItemBaseDtoList =
                chargeItemBaseDtoManager.getByItemCodes(
                        new ArrayList<>(new HashSet<>(chargePackageIdMap.values())));
        Map<String, ChargeItemBaseDto> chargePackageIdChargeItemBaseDtoMapRaw =
                chargePackageIdChargeItemBaseDtoList.stream()
                        .collect(Collectors.toMap(ChargeItemBaseDto::getItemCode, i -> i));
        Map<String, ChargeItemBaseDto> chargePackageIdChargeItemBaseDtoMap =
                chargePackageIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        chargePackageIdChargeItemBaseDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                chargePackageIdChargeItemBaseDtoMapRaw.get(
                                                        i.getValue())));

        List<ChargePackageDetailBaseDto> baseDtoList =
                chargePackageDetailBaseDtoConverter
                        .convertFromChargePackageDetailToChargePackageDetailBaseDto(
                                chargePackageDetailList);
        Map<String, ChargePackageDetailChargePackageExtDto> dtoMap =
                chargePackageDetailChargePackageExtDtoConverter
                        .convertFromChargePackageDetailBaseDtoToChargePackageDetailChargePackageExtDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ChargePackageDetailChargePackageExtDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<ChargePackageDetailChargePackageExtDto> chargePackageDetailChargePackageExtDtoList =
                new ArrayList<>();
        for (ChargePackageDetail i : chargePackageDetailList) {
            ChargePackageDetailChargePackageExtDto chargePackageDetailChargePackageExtDto =
                    dtoMap.get(i.getId());
            if (chargePackageDetailChargePackageExtDto == null) {
                continue;
            }

            if (null != i.getChargePackageId()) {
                chargePackageDetailChargePackageExtDto.setChargePackage(
                        chargePackageIdChargeItemBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            chargePackageDetailChargePackageExtDtoList.add(chargePackageDetailChargePackageExtDto);
        }
        return chargePackageDetailChargePackageExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
