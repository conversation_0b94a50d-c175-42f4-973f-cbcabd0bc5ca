package com.pulse.billing_public_config.manager.bo;

import com.pulse.billing_public_config.persist.dos.ChargeItemPerformDepartment;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "charge_item_perform_department")
@Entity
@AutoGenerated(locked = true, uuid = "f23f0837-9c71-4278-aece-9d6e36428318|BO|DEFINITION")
public class ChargeItemPerformDepartmentBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    @ManyToOne
    @JoinColumn(name = "item_code", referencedColumnName = "item_code")
    @AutoGenerated(locked = true)
    private ChargeItemBO chargeItemBO;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "751f210f-bb55-5f72-baa1-ead8002d60ec")
    private Date createdAt;

    /** 默认标志 */
    @Column(name = "default_flag")
    @AutoGenerated(locked = true, uuid = "25371d83-6d05-4280-8ab0-2d67f59edd14")
    private Boolean defaultFlag;

    /** 科室id */
    @Column(name = "department_id")
    @AutoGenerated(locked = true, uuid = "9814d3d4-894e-4344-ab06-4e678c360f9f")
    private String departmentId;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "15811eea-639b-4ee5-bf3e-e2d4d6aa5571")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "b21b0446-1513-5cac-9eee-8eaa6af520ae")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "bdbbadd1-6252-4b18-bf08-bfdda0515b73|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartment convertToChargeItemPerformDepartment() {
        ChargeItemPerformDepartment entity = new ChargeItemPerformDepartment();
        BoUtil.copyProperties(
                this, entity, "id", "departmentId", "defaultFlag", "createdAt", "updatedAt");
        ChargeItemBO chargeItemBO = this.getChargeItemBO();
        entity.setItemCode(chargeItemBO.getItemCode());
        return entity;
    }

    @AutoGenerated(locked = true)
    public ChargeItemBO getChargeItemBO() {
        return this.chargeItemBO;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Boolean getDefaultFlag() {
        return this.defaultFlag;
    }

    @AutoGenerated(locked = true)
    public String getDepartmentId() {
        return this.departmentId;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getItemCode() {
        return this.getChargeItemBO().getItemCode();
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartmentBO setChargeItemBO(ChargeItemBO chargeItemBO) {
        this.chargeItemBO = chargeItemBO;
        return (ChargeItemPerformDepartmentBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartmentBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (ChargeItemPerformDepartmentBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartmentBO setDefaultFlag(Boolean defaultFlag) {
        this.defaultFlag = defaultFlag;
        return (ChargeItemPerformDepartmentBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartmentBO setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
        return (ChargeItemPerformDepartmentBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartmentBO setId(String id) {
        this.id = id;
        return (ChargeItemPerformDepartmentBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemPerformDepartmentBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (ChargeItemPerformDepartmentBO) this;
    }
}
