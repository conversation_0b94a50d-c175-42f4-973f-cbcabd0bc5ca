package com.pulse.billing_public_config.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.billing_public_config.manager.ChargeItemBaseDtoManager;
import com.pulse.billing_public_config.manager.ChargePackageDetailBaseDtoManager;
import com.pulse.billing_public_config.manager.ChargePackageDetailDtoManager;
import com.pulse.billing_public_config.manager.converter.ChargeItemBaseDtoConverter;
import com.pulse.billing_public_config.manager.converter.ChargePackageDetailDtoConverter;
import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargePackageDetailBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargePackageDetailDto;
import com.pulse.billing_public_config.persist.dos.ChargeItem;
import com.pulse.billing_public_config.persist.mapper.ChargeItemDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "8d1626bf-f33a-4f8f-8d37-55d7fa135ace|DTO|BASE_MANAGER_IMPL")
public abstract class ChargePackageDetailDtoManagerBaseImpl
        implements ChargePackageDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemBaseDtoConverter chargeItemBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemBaseDtoManager chargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemDao chargeItemDao;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargePackageDetailBaseDtoManager chargePackageDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargePackageDetailDtoConverter chargePackageDetailDtoConverter;

    @AutoGenerated(locked = true, uuid = "22e23477-5674-3298-95b3-77b2905179e8")
    @Override
    public List<ChargePackageDetailDto> getByChargeItemCategorys(List<String> chargeItemCategory) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemCategory)) {
            return Collections.emptyList();
        }

        List<ChargeItem> chargeItemList =
                chargeItemDao.getByChargeItemCategorys(chargeItemCategory);
        if (CollectionUtil.isEmpty(chargeItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromChargeItemToChargePackageDetailDto(chargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2f1ad9f3-**************-c346771bc1d6")
    public List<ChargePackageDetailDto> doConvertFromChargeItemToChargePackageDetailDto(
            List<ChargeItem> chargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemList)) {
            return Collections.emptyList();
        }

        List<ChargePackageDetailBaseDto> chargePackageDetailBaseDtoList =
                chargePackageDetailBaseDtoManager.getByChargePackageIds(
                        chargeItemList.stream()
                                .map(i -> i.getItemCode())
                                .collect(Collectors.toList()));
        Map<String, List<ChargePackageDetailBaseDto>> itemCodeChargePackageDetailBaseDtoListMap =
                chargePackageDetailBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getChargePackageId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<ChargeItemBaseDto> baseDtoList =
                chargeItemBaseDtoConverter.convertFromChargeItemToChargeItemBaseDto(chargeItemList);
        Map<String, ChargePackageDetailDto> dtoMap =
                chargePackageDetailDtoConverter
                        .convertFromChargeItemBaseDtoToChargePackageDetailDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ChargePackageDetailDto::getItemCode,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<ChargePackageDetailDto> chargePackageDetailDtoList = new ArrayList<>();
        for (ChargeItem i : chargeItemList) {
            ChargePackageDetailDto chargePackageDetailDto = dtoMap.get(i.getItemCode());
            if (chargePackageDetailDto == null) {
                continue;
            }

            if (null != i.getItemCode()) {
                chargePackageDetailDto.setChargePackageDetailList(
                        itemCodeChargePackageDetailBaseDtoListMap.getOrDefault(
                                i.getItemCode(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            chargePackageDetailDtoList.add(chargePackageDetailDto);
        }
        return chargePackageDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "9cf1ecb0-bf2e-32c5-ac35-56eb1484ad96")
    @Override
    public List<ChargePackageDetailDto> getByItemCodes(List<String> itemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(itemCode)) {
            return Collections.emptyList();
        }

        List<ChargeItem> chargeItemList = chargeItemDao.getByItemCodes(itemCode);
        if (CollectionUtil.isEmpty(chargeItemList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, ChargeItem> chargeItemMap =
                chargeItemList.stream().collect(Collectors.toMap(i -> i.getItemCode(), i -> i));
        chargeItemList =
                itemCode.stream()
                        .map(i -> chargeItemMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromChargeItemToChargePackageDetailDto(chargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "acf5dabf-4947-3e54-a3eb-8f1401d67883")
    @Override
    public List<ChargePackageDetailDto> getByChargeItemCategory(String chargeItemCategory) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargePackageDetailDto> chargePackageDetailDtoList =
                getByChargeItemCategorys(Arrays.asList(chargeItemCategory));
        return chargePackageDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c90074aa-cf6a-392c-94f8-8233d1ee1e65")
    @Override
    public ChargePackageDetailDto getByItemCode(String itemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargePackageDetailDto> ret = getByItemCodes(Arrays.asList(itemCode));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        ChargePackageDetailDto chargePackageDetailDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return chargePackageDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
