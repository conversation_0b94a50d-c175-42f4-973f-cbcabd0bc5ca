package com.pulse.diagnosis.service;

import com.pulse.diagnosis.manager.bo.DiagnosisRecordBO;
import com.pulse.diagnosis.manager.dto.DiagnosisRecordBaseDto;
import com.pulse.diagnosis.persist.dos.DiagnosisRecord;
import com.pulse.diagnosis.persist.qto.SearchDiagnosisRecordQto;
import com.pulse.diagnosis.service.base.BaseDiagnosisRecordBOService;
import com.pulse.diagnosis.service.bto.DeleteDiagnosisRecordBto;
import com.pulse.diagnosis.service.bto.MergeDiagnosisRecordBto;
import com.pulse.diagnosis.service.index.entity.SearchDiagnosisRecordQtoService;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "1d0a5c41-7bff-4b0d-91a0-4d211a9906ad|BO|SERVICE")
public class DiagnosisRecordBOService extends BaseDiagnosisRecordBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DiagnosisRecordBaseDtoService diagnosisRecordBaseDtoService;

    @Resource private SearchDiagnosisRecordQtoService searchDiagnosisRecordQtoService;

    /** 删除诊断记录 */
    @PublicInterface(id = "43cfd715-a290-4254-9c59-935b6d4c46e0", module = "diagnosis")
    @Transactional
    @AutoGenerated(locked = false, uuid = "3db49e47-541a-446d-a768-a2d43fda9fb6")
    public String deleteDiagnosisRecord(
            @Valid @NotNull DeleteDiagnosisRecordBto deleteDiagnosisRecordBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DiagnosisRecordBaseDto diagnosisRecordBaseDto =
                diagnosisRecordBaseDtoService.getById(deleteDiagnosisRecordBto.getId());
        DeleteDiagnosisRecordBoResult boResult =
                super.deleteDiagnosisRecordBase(deleteDiagnosisRecordBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteDiagnosisRecordBto */
        {
            DeleteDiagnosisRecordBto bto =
                    boResult
                            .<DeleteDiagnosisRecordBto>getBtoOfType(DeleteDiagnosisRecordBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteDiagnosisRecordBto, DiagnosisRecord> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存诊断记录 */
    @PublicInterface(id = "ba15cc0d-bdcc-4e9a-a8e3-b0ec17f749d2", module = "diagnosis")
    @Transactional
    @AutoGenerated(locked = false, uuid = "7e8d1fad-64dd-4793-88fc-82681638dfce")
    public String mergeDiagnosisRecord(
            @Valid @NotNull MergeDiagnosisRecordBto mergeDiagnosisRecordBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DiagnosisRecordBaseDto diagnosisRecordBaseDto = null;
        if (mergeDiagnosisRecordBto.getId() != null) {
            diagnosisRecordBaseDto =
                    diagnosisRecordBaseDtoService.getById(mergeDiagnosisRecordBto.getId());
        }
        MergeDiagnosisRecordBoResult boResult =
                super.mergeDiagnosisRecordBase(mergeDiagnosisRecordBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeDiagnosisRecordBto */
        {
            MergeDiagnosisRecordBto bto =
                    boResult
                            .<MergeDiagnosisRecordBto>getBtoOfType(MergeDiagnosisRecordBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeDiagnosisRecordBto, DiagnosisRecord, DiagnosisRecordBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeDiagnosisRecordBto, DiagnosisRecordBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DiagnosisRecordBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DiagnosisRecord entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DiagnosisRecordBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /**
     * Merge-Full DiagnosisRecordList todo： 后续应该需要诊断更新的MO逻辑，例如：同步患者主诊断信息到其他表中
     *
     * @param mergeDiagnosisRecordBtoList
     * @return
     */
    @PublicInterface(
            id = "82f47e96-a3d1-49d7-80a7-17bda02d3bf4",
            module = "diagnosis",
            moduleId = "1c36726d-b659-4039-83f4-0ed2e4788ef6",
            version = "1748250497247",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "82f47e96-a3d1-49d7-80a7-17bda02d3bf4")
    public List<String> batchMergeDiagnosisRecord(
            @Valid List<MergeDiagnosisRecordBto> mergeDiagnosisRecordBtoList) {
        List<String> result = new ArrayList<>();
        String inpVisitId =
                mergeDiagnosisRecordBtoList.stream()
                        .findFirst()
                        .map(MergeDiagnosisRecordBto::getInpVisitId)
                        .orElse(null);
        String outpVisitId =
                mergeDiagnosisRecordBtoList.stream()
                        .findFirst()
                        .map(MergeDiagnosisRecordBto::getOutpVisitId)
                        .orElse(null);
        String patientId =
                mergeDiagnosisRecordBtoList.stream()
                        .findFirst()
                        .map(MergeDiagnosisRecordBto::getPatientId)
                        .orElse(null);
        // TODO:erpVisitId

        // 参数校验
        if (Strings.isEmpty(inpVisitId) || Strings.isEmpty(outpVisitId)) {
            log.warn("inpVisitId 或 outpVisitId 为空，跳过操作");

            throw new IgnoredException(ErrorCode.SYS_ERROR, "inpVisitId 或 outpVisitId 不可为空");
        }

        try {
            log.info("开始批量处理诊断记录");

            // 查询当前实体已有的属性值ID列表
            SearchDiagnosisRecordQto qto = new SearchDiagnosisRecordQto();
            qto.setInpVisitIdIs(inpVisitId);
            qto.setOutpVisitIdIs(outpVisitId);
            // 分类处理：删除、新增
            List<String> deleteIdList = searchDiagnosisRecordQtoService.query(qto);

            // 1. 删除住院/门诊/急诊诊断记录
            for (String id : deleteIdList) {
                DeleteDiagnosisRecordBto deleteBto = new DeleteDiagnosisRecordBto();
                deleteBto.setId(id);
                deleteDiagnosisRecord(deleteBto);
            }
            log.info("成功删除 {} 条诊断记录", deleteIdList.size());

            // 2. 创建新属性值
            for (MergeDiagnosisRecordBto createBto : mergeDiagnosisRecordBtoList) {
                result.add(mergeDiagnosisRecord(createBto));
            }
            log.info("成功创建 {} 条诊断记录", mergeDiagnosisRecordBtoList.size());

            return result;

        } catch (Exception e) {
            log.error("批量处理患者ID {} 的诊断记录发生异常", patientId, e);
            throw new RuntimeException("批量处理诊断记录失败", e);
        }
    }
}
