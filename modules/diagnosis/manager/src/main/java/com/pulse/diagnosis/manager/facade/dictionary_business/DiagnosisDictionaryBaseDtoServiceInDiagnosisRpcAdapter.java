package com.pulse.diagnosis.manager.facade.dictionary_business;

import com.pulse.diagnosis.manager.facade.dictionary_business.base.DiagnosisDictionaryBaseDtoServiceInDiagnosisBaseRpcAdapter;
import com.pulse.dictionary_business.manager.dto.DiagnosisDictionaryBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "1c36726d-b659-4039-83f4-0ed2e4788ef6")
@AutoGenerated(locked = false, uuid = "303f1a48-52b2-3fa1-b76e-027b8e8ad5ed")
public class DiagnosisDictionaryBaseDtoServiceInDiagnosisRpcAdapter
        extends DiagnosisDictionaryBaseDtoServiceInDiagnosisBaseRpcAdapter {

    @RpcRefer(id = "79b46998-bd63-4acd-91f3-fe618d3726d5", version = "1747811173369")
    @AutoGenerated(locked = false, uuid = "79b46998-bd63-4acd-91f3-fe618d3726d5|RPC|ADAPTER")
    public List<DiagnosisDictionaryBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }

    @RpcRefer(id = "f167f7ab-ae44-4d52-ad74-06656af78c9a", version = "1747811173365")
    @AutoGenerated(locked = false, uuid = "f167f7ab-ae44-4d52-ad74-06656af78c9a|RPC|ADAPTER")
    public DiagnosisDictionaryBaseDto getById(String id) {
        return super.getById(id);
    }
}
