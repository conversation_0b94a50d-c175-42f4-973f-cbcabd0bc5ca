package com.pulse.dictionary_business.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "c4d66b8e-5433-43ee-ba41-753b9b088ce4|ENUM|DEFINITION")
public enum UseScopeEnum {

    /** 门诊 */
    OUTP(),

    /** 住院 */
    INP(),

    /** 体检 */
    PHYSICAL_EXAM(),

    /** 院前 */
    PRE_HOSPITAL(),

    /** 留抢 */
    STAY_RESCUE();

    @AutoGenerated(locked = true)
    UseScopeEnum() {}
}
