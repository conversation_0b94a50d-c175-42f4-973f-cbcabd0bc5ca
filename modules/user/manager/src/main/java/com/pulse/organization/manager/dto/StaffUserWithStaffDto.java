package com.pulse.organization.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "bd717c88-4b36-488b-9fc5-ac8c89271da9|DTO|DEFINITION")
public class StaffUserWithStaffDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "31660040-611d-4155-b3eb-75138e0b50c8")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "182fec8e-9a21-4dc0-901e-8f7e135941e2")
    private String id;

    /** 员工ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "6a3a27c6-450f-45b9-85b4-5326b839444b")
    private StaffBaseDto staff;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "e240af90-f5b0-4f6c-b62b-25edc893ed53")
    private Date updatedAt;

    /** 用户ID */
    @AutoGenerated(locked = true, uuid = "1b8b644f-cb63-4926-a26d-c0c657a2a432")
    private String userId;
}
