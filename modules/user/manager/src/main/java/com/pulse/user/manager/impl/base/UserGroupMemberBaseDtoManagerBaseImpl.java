package com.pulse.user.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.manager.UserGroupMemberBaseDtoManager;
import com.pulse.user.manager.converter.UserGroupMemberBaseDtoConverter;
import com.pulse.user.manager.dto.UserGroupMemberBaseDto;
import com.pulse.user.persist.dos.UserGroupMember;
import com.pulse.user.persist.mapper.UserGroupMemberDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "cc9eaee9-73f4-4363-b645-b772ae8dfa07|DTO|BASE_MANAGER_IMPL")
public abstract class UserGroupMemberBaseDtoManagerBaseImpl
        implements UserGroupMemberBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private UserGroupMemberBaseDtoConverter userGroupMemberBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private UserGroupMemberDao userGroupMemberDao;

    @AutoGenerated(locked = true, uuid = "03e02f7b-65bd-3f95-b2c5-32653d7b58ea")
    @Override
    public List<UserGroupMemberBaseDto> getByUserIds(List<String> userId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(userId)) {
            return Collections.emptyList();
        }

        List<UserGroupMember> userGroupMemberList = userGroupMemberDao.getByUserIds(userId);
        if (CollectionUtil.isEmpty(userGroupMemberList)) {
            return Collections.emptyList();
        }

        return doConvertFromUserGroupMemberToUserGroupMemberBaseDto(userGroupMemberList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2254112b-b4ff-3eda-927b-f2a490faebf3")
    @Override
    public List<UserGroupMemberBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<UserGroupMember> userGroupMemberList = userGroupMemberDao.getByIds(id);
        if (CollectionUtil.isEmpty(userGroupMemberList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, UserGroupMember> userGroupMemberMap =
                userGroupMemberList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        userGroupMemberList =
                id.stream()
                        .map(i -> userGroupMemberMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromUserGroupMemberToUserGroupMemberBaseDto(userGroupMemberList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "36877f6e-d06f-3a4b-9cb4-a9cac2d0ce61")
    public List<UserGroupMemberBaseDto> doConvertFromUserGroupMemberToUserGroupMemberBaseDto(
            List<UserGroupMember> userGroupMemberList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(userGroupMemberList)) {
            return Collections.emptyList();
        }

        Map<String, UserGroupMemberBaseDto> dtoMap =
                userGroupMemberBaseDtoConverter
                        .convertFromUserGroupMemberToUserGroupMemberBaseDto(userGroupMemberList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        UserGroupMemberBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<UserGroupMemberBaseDto> userGroupMemberBaseDtoList = new ArrayList<>();
        for (UserGroupMember i : userGroupMemberList) {
            UserGroupMemberBaseDto userGroupMemberBaseDto = dtoMap.get(i.getId());
            if (userGroupMemberBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            userGroupMemberBaseDtoList.add(userGroupMemberBaseDto);
        }
        return userGroupMemberBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "38a3dff8-0c02-3384-b66e-93e2efb75a85")
    @Override
    public UserGroupMemberBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserGroupMemberBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        UserGroupMemberBaseDto userGroupMemberBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return userGroupMemberBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "74d83200-025a-3ad5-9f3a-f109861eb2a7")
    @Override
    public List<UserGroupMemberBaseDto> getByUserGroupId(String userGroupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserGroupMemberBaseDto> userGroupMemberBaseDtoList =
                getByUserGroupIds(Arrays.asList(userGroupId));
        return userGroupMemberBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "83505a74-3764-386c-a352-c59e06d51f13")
    @Override
    public List<UserGroupMemberBaseDto> getByUserGroupIds(List<String> userGroupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(userGroupId)) {
            return Collections.emptyList();
        }

        List<UserGroupMember> userGroupMemberList =
                userGroupMemberDao.getByUserGroupIds(userGroupId);
        if (CollectionUtil.isEmpty(userGroupMemberList)) {
            return Collections.emptyList();
        }

        return doConvertFromUserGroupMemberToUserGroupMemberBaseDto(userGroupMemberList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "febb2dcb-4010-37ab-a192-86ae9eaaaa13")
    @Override
    public List<UserGroupMemberBaseDto> getByUserId(String userId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserGroupMemberBaseDto> userGroupMemberBaseDtoList =
                getByUserIds(Arrays.asList(userId));
        return userGroupMemberBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
