package com.pulse.user.manager.facade.organization;

import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.user.manager.facade.organization.base.StaffBaseDtoServiceInUserBaseRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "11de64c6-24e2-4026-82a7-cb4fe1e0a1af")
@AutoGenerated(locked = false, uuid = "e1e24a6b-9fd1-3dca-8ab1-6994369b1e05")
public class StaffBaseDtoServiceInUserRpcAdapter extends StaffBaseDtoServiceInUserBaseRpcAdapter {

    @RpcRefer(id = "07572701-f307-4138-a136-f38c95315e0a", version = "1745393232339")
    @AutoGenerated(locked = false, uuid = "07572701-f307-4138-a136-f38c95315e0a|RPC|ADAPTER")
    public List<StaffBaseDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }
}
