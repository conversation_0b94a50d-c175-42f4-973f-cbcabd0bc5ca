package com.pulse.user.manager;

import com.pulse.user.manager.dto.UserInfoStaffDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "f0f22268-ee5a-416c-b901-974ae537f0e6|DTO|MANAGER")
public interface UserInfoStaffDtoManager {

    @AutoGenerated(locked = true, uuid = "6e92f419-0b8b-3119-b925-95205ac59499")
    List<UserInfoStaffDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "cb879e53-d966-3e89-bb78-904a9462af78")
    UserInfoStaffDto getById(String id);
}
