package com.pulse.user.entrance.web.query.assembler;

import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.pulse.user.entrance.web.vo.UserSimpleVo;
import com.pulse.user.manager.dto.UserBaseDto;
import com.pulse.user.service.UserBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** UserSimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "2f5f85fc-a9e8-3129-a283-047d7367e5a6")
public class UserSimpleVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private UserBaseDtoService userBaseDtoService;

    /** 批量自定义组装UserSimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "7fc6a883-4614-3e68-afc7-532cf4a2a50f")
    public void assembleDataCustomized(List<UserSimpleVo> dataList) {
        // 自定义数据组装

    }

    /** 组装staffUserWithStaff数据 */
    @AutoGenerated(locked = true, uuid = "8f55d9db-433b-3e90-b745-2444716c9a5b")
    private void assembleStaffUserWithStaffData(
            UserSimpleVoDataAssembler.UserSimpleVoDataHolder dataHolder) {
        Map<String, Pair<StaffBaseDto, UserSimpleVo.StaffBaseVo>> staffUserWithStaff2Staff =
                dataHolder.staffUserWithStaff2Staff.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto ->
                                                Pair.of(
                                                        dto,
                                                        dataHolder.staffUserWithStaff2Staff.get(
                                                                dto)),
                                        (o1, o2) -> o1));
        for (Map.Entry<StaffUserBaseDto, UserSimpleVo.StaffUserWithStaffVo> staffUserWithStaff :
                dataHolder.staffUserWithStaff.entrySet()) {
            StaffUserBaseDto baseDto = staffUserWithStaff.getKey();
            UserSimpleVo.StaffUserWithStaffVo vo = staffUserWithStaff.getValue();
            vo.setStaff(
                    Optional.ofNullable(staffUserWithStaff2Staff.get(baseDto.getStaffId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 组装UserSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "c85580d9-86e6-3d56-b653-06a3ba7ea2e6")
    public void assembleData(
            Map<String, UserSimpleVo> voMap,
            UserSimpleVoDataAssembler.UserSimpleVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<UserBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<StaffUserBaseDto, UserSimpleVo.StaffUserWithStaffVo>> staffUserWithStaff =
                dataHolder.staffUserWithStaff.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getUserId(),
                                        dto -> Pair.of(dto, dataHolder.staffUserWithStaff.get(dto)),
                                        (o1, o2) -> o1));

        for (UserBaseDto baseDto : baseDtoList) {
            UserSimpleVo vo = voMap.get(baseDto.getId());
            vo.setStaffUserWithStaff(
                    Optional.ofNullable(staffUserWithStaff.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleStaffUserWithStaffData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class UserSimpleVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<UserBaseDto> rootBaseDtoList;

        /** 持有字段staffUserWithStaff的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<StaffUserBaseDto, UserSimpleVo.StaffUserWithStaffVo> staffUserWithStaff;

        /** 持有字段staffUserWithStaff.staff的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<StaffBaseDto, UserSimpleVo.StaffBaseVo> staffUserWithStaff2Staff;
    }
}
