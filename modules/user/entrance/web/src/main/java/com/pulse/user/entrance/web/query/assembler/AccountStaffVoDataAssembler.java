package com.pulse.user.entrance.web.query.assembler;

import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.pulse.user.entrance.web.vo.AccountStaffVo;
import com.pulse.user.manager.dto.AccountBaseDto;
import com.pulse.user.manager.dto.UserBaseDto;
import com.pulse.user.service.AccountBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** AccountStaffVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "115dc0c1-fb9e-39a1-a2d1-f7c8cd14557d")
public class AccountStaffVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private AccountBaseDtoService accountBaseDtoService;

    /** 组装user2StaffUserStaffList数据 */
    @AutoGenerated(locked = true, uuid = "3547f417-e822-3fba-b24a-6c0f9586ef24")
    private void assembleUser2StaffUserStaffListData(
            AccountStaffVoDataAssembler.AccountStaffVoDataHolder dataHolder) {
        Map<String, Pair<StaffBaseDto, AccountStaffVo.StaffBaseVo>> user2StaffUserStaffList2Staff =
                dataHolder.user2StaffUserStaffList2Staff.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto ->
                                                Pair.of(
                                                        dto,
                                                        dataHolder.user2StaffUserStaffList2Staff
                                                                .get(dto)),
                                        (o1, o2) -> o1));
        for (Map.Entry<StaffUserBaseDto, AccountStaffVo.StaffUserWithStaffVo>
                user2StaffUserStaffList : dataHolder.user2StaffUserStaffList.entrySet()) {
            StaffUserBaseDto baseDto = user2StaffUserStaffList.getKey();
            AccountStaffVo.StaffUserWithStaffVo vo = user2StaffUserStaffList.getValue();
            vo.setStaff(
                    Optional.ofNullable(user2StaffUserStaffList2Staff.get(baseDto.getStaffId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 组装user数据 */
    @AutoGenerated(locked = true, uuid = "796ff213-430f-356a-a5c2-901e3f3e88ea")
    private void assembleUserData(AccountStaffVoDataAssembler.AccountStaffVoDataHolder dataHolder) {
        Map<String, Pair<StaffUserBaseDto, AccountStaffVo.StaffUserWithStaffVo>>
                user2StaffUserStaffList =
                        dataHolder.user2StaffUserStaffList.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getUserId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.user2StaffUserStaffList
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        for (Map.Entry<UserBaseDto, AccountStaffVo.UserInfoStaffVo> user :
                dataHolder.user.entrySet()) {
            UserBaseDto baseDto = user.getKey();
            AccountStaffVo.UserInfoStaffVo vo = user.getValue();
            vo.setStaffUserStaffList(
                    Optional.ofNullable(user2StaffUserStaffList.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
        assembleUser2StaffUserStaffListData(dataHolder);
    }

    /** 组装AccountStaffVo数据 */
    @AutoGenerated(locked = true, uuid = "9fd15eff-c1af-3ac5-8ac6-326d4da6f6bf")
    public void assembleData(
            Map<String, AccountStaffVo> voMap,
            AccountStaffVoDataAssembler.AccountStaffVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<AccountBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<UserBaseDto, AccountStaffVo.UserInfoStaffVo>> user =
                dataHolder.user.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.user.get(dto)),
                                        (o1, o2) -> o1));

        for (AccountBaseDto baseDto : baseDtoList) {
            AccountStaffVo vo = voMap.get(baseDto.getId());
            vo.setUser(
                    Optional.ofNullable(user.get(baseDto.getUserId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleUserData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装AccountStaffVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a78969ac-61c7-3ea0-9c48-0438b0128311")
    public void assembleDataCustomized(List<AccountStaffVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class AccountStaffVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<AccountBaseDto> rootBaseDtoList;

        /** 持有字段user的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<UserBaseDto, AccountStaffVo.UserInfoStaffVo> user;

        /** 持有字段user.staffUserStaffList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<StaffUserBaseDto, AccountStaffVo.StaffUserWithStaffVo> user2StaffUserStaffList;

        /** 持有字段user.staffUserStaffList.staff的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<StaffBaseDto, AccountStaffVo.StaffBaseVo> user2StaffUserStaffList2Staff;
    }
}
