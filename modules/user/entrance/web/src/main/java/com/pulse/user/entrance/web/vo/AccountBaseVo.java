package com.pulse.user.entrance.web.vo;

import com.pulse.user.common.enums.AccountStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9fbaf85d-c102-467c-a5f7-717e43efbf34|VO|DEFINITION")
public class AccountBaseVo {
    /** 访问令牌 */
    @AutoGenerated(locked = true, uuid = "********-3b8e-44bf-8dd8-42c0807e5f02")
    private String accessToken;

    /** 访问令牌有效期 */
    @AutoGenerated(locked = true, uuid = "7050d437-9c32-41a0-9e15-12747cb904a7")
    private Long accessTokenValidPeriod;

    /** 账户名称 */
    @AutoGenerated(locked = true, uuid = "85ce8815-7a7d-4091-b6fd-b8e8a68892ea")
    private String accountName;

    /** 认证功能数据 */
    @AutoGenerated(locked = true, uuid = "4609ac98-0cf5-4ec5-aea2-58abc693b8ae")
    private String authenticationFeatureData;

    /** 认证特征数据盐值 */
    @AutoGenerated(locked = true, uuid = "24eaa4c8-8d6b-4007-bd11-a944e8927c85")
    private String authenticationFeatureDataSalt;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "e2ae7958-443a-4108-80f7-209d50078bf8")
    private String cellphone;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "c8c80b15-7f13-47b3-9c34-9d36a45ec1e5")
    private Date createdAt;

    /** 加密密码 */
    @AutoGenerated(locked = true, uuid = "4e144151-0a17-4707-baef-8e0fdcebae83")
    private String encryptionPassword;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9fa7f7d5-f2e5-4bf7-a420-80f73913eb81")
    private String id;

    /** 最后登录失败时间 */
    @AutoGenerated(locked = true, uuid = "1d884d45-2d1c-4078-bc4c-7bb64ea0268d")
    private Date lastLoginFailureTime;

    /** 上次登录时间 */
    @AutoGenerated(locked = true, uuid = "d311fdae-0e38-4a19-ad68-22b49ae3806c")
    private Date lastLoginTime;

    /** 登录失败次数 */
    @AutoGenerated(locked = true, uuid = "de0cb306-60eb-4296-8edc-4e474e95b324")
    private Integer loginFailureCount;

    /** 登录类型 */
    @AutoGenerated(locked = true, uuid = "f55503e6-6cdb-4430-83b8-8b23749ce22f")
    private String loginType;

    /** 密码盐值 */
    @AutoGenerated(locked = true, uuid = "403700e3-ccad-49f5-a18f-ab7a369ba3df")
    private String passwordSalt;

    /** 刷新令牌 */
    @AutoGenerated(locked = true, uuid = "6be2e080-7d36-4293-be99-9051311e07dd")
    private String refreshToken;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "305cb9ee-f4ea-40cd-b0d3-38c88f4382f9")
    private AccountStatusEnum status;

    /** 第三方账户ID */
    @AutoGenerated(locked = true, uuid = "a75a4612-c470-4cbf-a293-954ebf40aa1d")
    private String thirdPartyAccountId;

    /** 第三方平台类型 */
    @AutoGenerated(locked = true, uuid = "a6c088aa-7f09-4b5c-b97e-d6ba62ac6f33")
    private String thirdPartyPlatformType;

    /** 令牌类型 */
    @AutoGenerated(locked = true, uuid = "941b8dd6-696d-4a25-9beb-f468adf9a82d")
    private String tokenType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b9b1847f-bf1f-4f1e-be2a-246e02fee550")
    private Date updatedAt;

    /** 用户ID */
    @AutoGenerated(locked = true, uuid = "9a2992b3-1046-41b4-a978-21bde6a8f1dc")
    private String userId;
}
