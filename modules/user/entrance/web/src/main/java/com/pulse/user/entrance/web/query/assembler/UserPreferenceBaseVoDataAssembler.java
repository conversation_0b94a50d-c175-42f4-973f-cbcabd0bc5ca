package com.pulse.user.entrance.web.query.assembler;

import com.pulse.user.entrance.web.vo.UserPreferenceBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** UserPreferenceBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "c216844a-164c-3a4a-a439-413cbac25178")
public class UserPreferenceBaseVoDataAssembler {

    /** 批量自定义组装UserPreferenceBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "52872f63-30b7-3b5e-9d88-ebe34a6f596c")
    public void assembleDataCustomized(List<UserPreferenceBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装UserPreferenceBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "ba1c60c0-1b3f-3245-bef0-2c6c532fdad1")
    public void assembleData(Map<String, UserPreferenceBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
