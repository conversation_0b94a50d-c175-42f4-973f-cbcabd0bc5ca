package com.pulse.user.entrance.web.vo;

import com.pulse.user.common.enums.SessionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "8f9deb15-fce0-4076-a515-0a930d713165|VO|DEFINITION")
public class SessionBaseVo {
    /** 客户端设备类型 */
    @AutoGenerated(locked = true, uuid = "c1899cc9-20c3-4c4f-b5fe-1796a5388d6f")
    private String clientDeviceType;

    /** 客户端IP */
    @AutoGenerated(locked = true, uuid = "140e4385-936d-4b79-8166-8039f1e2f4dd")
    private String clientIp;

    /** 客户端的用户代理信息 */
    @AutoGenerated(locked = true, uuid = "84c608ce-fcd7-45b3-8c6f-645a064210a8")
    private String clientUserProxyInfo;

    /** 创建日期 */
    @AutoGenerated(locked = true, uuid = "b3b10de7-7d85-4609-8037-c18e842be932")
    private Date createDate;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9a65d3c0-4f3b-4d64-b935-61f5cb530bb0")
    private Date createdAt;

    /** 会话过期时间 */
    @AutoGenerated(locked = true, uuid = "b4a97c7a-a930-4330-aeb0-9a66e23f329f")
    private Date expireTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9912f34b-df4c-438d-8bd2-a0f89736059a")
    private String id;

    /** 上次访问时间 */
    @AutoGenerated(locked = true, uuid = "4a0a52ac-c8a7-44a9-a6cb-83e1b33cce58")
    private Date lastAccessTime;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "662d6710-7699-4f19-89ce-9ee2a6d97760")
    private String name;

    /** 会话ID */
    @AutoGenerated(locked = true, uuid = "7a4f8ef6-a35c-4cb7-b9dd-644bbeb3c242")
    private String sessionId;

    /** 会话状态 */
    @AutoGenerated(locked = true, uuid = "f1c41a6f-7a96-42f2-aa3c-9626edb11301")
    private SessionStatusEnum status;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "29cc5d02-7e35-4dcc-ae9b-8889bbf35f34")
    private Date updatedAt;

    /** 关联用户ID */
    @AutoGenerated(locked = true, uuid = "f2d7b788-ea96-42ac-8e1e-d19d586c5b4d")
    private String userId;

    /** 用户最后操作时间 */
    @AutoGenerated(locked = true, uuid = "78cd99a0-8d5a-48d9-8b05-e0f86f8a4700")
    private Date userLastActionTime;
}
