package com.pulse.user.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> UserInfo
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "08490c52-2f05-47a7-8886-c51a8b64ea0d|BTO|DEFINITION")
public class DeletePreferenceBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "562815fc-2122-4c80-9276-11e4fa9fbd9c")
    private String id;

    @Valid
    @AutoGenerated(locked = true, uuid = "1dcc23cb-1ce3-41ef-8c96-9ede8631651b")
    private List<DeletePreferenceBto.UserPreferenceBto> userPreferenceBtoList;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setUserPreferenceBtoList(
            List<DeletePreferenceBto.UserPreferenceBto> userPreferenceBtoList) {
        this.__$validPropertySet.add("userPreferenceBtoList");
        this.userPreferenceBtoList = userPreferenceBtoList;
    }

    /**
     * <b>[源自]</b> UserPreference
     *
     * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class UserPreferenceBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "2a9f9d8b-6c6d-4d29-bbc5-983e6d7de239")
        private String id;

        /** 备注 */
        @AutoGenerated(locked = true, uuid = "1c0266a5-b7db-474d-bd95-7c4f2f39c949")
        private String remark;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setRemark(String remark) {
            this.__$validPropertySet.add("remark");
            this.remark = remark;
        }
    }
}
