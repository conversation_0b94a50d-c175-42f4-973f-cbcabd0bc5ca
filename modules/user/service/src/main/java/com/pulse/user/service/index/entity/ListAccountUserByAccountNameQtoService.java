package com.pulse.user.service.index.entity;

import com.pulse.user.persist.mapper.ListAccountUserByAccountNameQtoDao;
import com.pulse.user.persist.qto.ListAccountUserByAccountNameQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "4b32c4c2-4034-42fd-b767-03b971bc2aff|QTO|SERVICE")
public class ListAccountUserByAccountNameQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListAccountUserByAccountNameQtoDao listAccountUserByAccountNameMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "4b32c4c2-4034-42fd-b767-03b971bc2aff-query")
    public List<String> query(ListAccountUserByAccountNameQto qto) {
        return listAccountUserByAccountNameMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListAccountUserByAccountNameQto qto) {
        return listAccountUserByAccountNameMapper.count(qto);
    }
}
