package com.pulse.user.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.manager.LoginLogBaseDtoManager;
import com.pulse.user.manager.dto.LoginLogBaseDto;
import com.pulse.user.service.converter.LoginLogBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "6fb10e11-c671-4294-8185-0d9b21ebe15f|DTO|SERVICE")
public class LoginLogBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private LoginLogBaseDtoManager loginLogBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private LoginLogBaseDtoServiceConverter loginLogBaseDtoServiceConverter;

    @PublicInterface(id = "08304926-722c-4a8c-8eb3-1e6a10704029", module = "user")
    @AutoGenerated(locked = false, uuid = "249e4d8a-2723-39fe-afa6-f32a2fe4236b")
    public List<LoginLogBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<LoginLogBaseDto> loginLogBaseDtoList = loginLogBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return loginLogBaseDtoServiceConverter.LoginLogBaseDtoConverter(loginLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "1a8ff3b7-77c8-4201-9cce-b6852e070de3", module = "user")
    @AutoGenerated(locked = false, uuid = "4d58f185-f42c-3231-be22-0380d9aed78d")
    public List<LoginLogBaseDto> getBySourceBusinessId(
            @NotNull(message = "源业务ID不能为空") String sourceBusinessId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySourceBusinessIds(Arrays.asList(sourceBusinessId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "de98a88f-2c9f-4151-b28f-c21475aacdd1", module = "user")
    @AutoGenerated(locked = false, uuid = "6d1b3640-2704-390c-af95-b97f9c9a29ac")
    public List<LoginLogBaseDto> getBySourceBusinessIds(
            @Valid @NotNull(message = "源业务ID不能为空") List<String> sourceBusinessId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        sourceBusinessId = new ArrayList<>(new HashSet<>(sourceBusinessId));
        List<LoginLogBaseDto> loginLogBaseDtoList =
                loginLogBaseDtoManager.getBySourceBusinessIds(sourceBusinessId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return loginLogBaseDtoServiceConverter.LoginLogBaseDtoConverter(loginLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "773dea78-c2cd-40ce-8f5f-2fc9ee7f7e1c", module = "user")
    @AutoGenerated(locked = false, uuid = "7f3c50a2-554a-3bcf-93da-055b9735afcb")
    public LoginLogBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<LoginLogBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
