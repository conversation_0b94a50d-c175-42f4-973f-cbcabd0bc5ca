package com.pulse.user.service.converter;

import com.pulse.user.manager.dto.LoginLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "49d2fbc6-1d74-34b0-b628-4175feb3efd2")
public class LoginLogBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<LoginLogBaseDto> LoginLogBaseDtoConverter(
            List<LoginLogBaseDto> loginLogBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return loginLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
