package com.pulse.user.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.user.persist.mapper.ListPreferenceQtoDao;
import com.pulse.user.persist.qto.ListPreferenceQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "facd4135-473f-41b9-a7fb-b9e2ae046ff2|QTO|SERVICE")
public class ListPreferenceQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListPreferenceQtoDao listPreferenceMapper;

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListPreferenceQto qto) {
        return listPreferenceMapper.count(qto);
    }

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "facd4135-473f-41b9-a7fb-b9e2ae046ff2-query-paged")
    public List<String> queryPaged(ListPreferenceQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listPreferenceMapper.queryPaged(qto);
    }
}
