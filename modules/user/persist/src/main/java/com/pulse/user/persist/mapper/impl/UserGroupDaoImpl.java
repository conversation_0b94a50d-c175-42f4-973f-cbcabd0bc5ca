package com.pulse.user.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.user.persist.dos.UserGroup;
import com.pulse.user.persist.mapper.UserGroupDao;
import com.pulse.user.persist.mapper.mybatis.UserGroupMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "572cbd4e-e76f-4c29-9c71-4df7f7c06ee3|ENTITY|DAO")
public class UserGroupDaoImpl implements UserGroupDao {
    @AutoGenerated(locked = true)
    @Resource
    private UserGroupMapper userGroupMapper;

    @AutoGenerated(locked = true, uuid = "2f62cfb5-ef8a-3bd4-83ae-5d169cec11bc")
    @Override
    public List<UserGroup> getByIds(List<String> id) {
        QueryWrapper<UserGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return userGroupMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "72827ea5-573b-3e44-a5fb-9afbf5298919")
    @Override
    public UserGroup getById(String id) {
        QueryWrapper<UserGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return userGroupMapper.selectOne(queryWrapper);
    }
}
