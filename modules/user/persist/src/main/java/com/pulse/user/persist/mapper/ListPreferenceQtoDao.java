package com.pulse.user.persist.mapper;

import com.pulse.user.persist.qto.ListPreferenceQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "facd4135-473f-41b9-a7fb-b9e2ae046ff2|QTO|DAO")
public class ListPreferenceQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 通过用户和类型获取偏好 */
    @AutoGenerated(locked = false, uuid = "facd4135-473f-41b9-a7fb-b9e2ae046ff2-count")
    public Integer count(ListPreferenceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(user_preference.id) FROM user_preference WHERE"
                    + " user_preference.user_id = #userIdIs AND user_preference.favorite_type ="
                    + " #favoriteTypeIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getFavoriteTypeIs() == null) {
            conditionToRemove.add("#favoriteTypeIs");
        }
        if (qto.getUserIdIs() == null) {
            conditionToRemove.add("#userIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#favoriteTypeIs", "?").replace("#userIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#favoriteTypeIs")) {
                sqlParams.add(qto.getFavoriteTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#userIdIs")) {
                sqlParams.add(qto.getUserIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 通过用户和类型获取偏好 */
    @AutoGenerated(locked = false, uuid = "facd4135-473f-41b9-a7fb-b9e2ae046ff2-query-all")
    public List<String> query(ListPreferenceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT user_preference.id FROM user_preference WHERE user_preference.user_id ="
                    + " #userIdIs AND user_preference.favorite_type = #favoriteTypeIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getFavoriteTypeIs() == null) {
            conditionToRemove.add("#favoriteTypeIs");
        }
        if (qto.getUserIdIs() == null) {
            conditionToRemove.add("#userIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#favoriteTypeIs", "?").replace("#userIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#favoriteTypeIs")) {
                sqlParams.add(qto.getFavoriteTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#userIdIs")) {
                sqlParams.add(qto.getUserIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  user_preference.sort_number asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 通过用户和类型获取偏好 */
    @AutoGenerated(locked = false, uuid = "facd4135-473f-41b9-a7fb-b9e2ae046ff2-query-paginate")
    public List<String> queryPaged(ListPreferenceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT user_preference.id FROM user_preference WHERE user_preference.user_id ="
                    + " #userIdIs AND user_preference.favorite_type = #favoriteTypeIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getFavoriteTypeIs() == null) {
            conditionToRemove.add("#favoriteTypeIs");
        }
        if (qto.getUserIdIs() == null) {
            conditionToRemove.add("#userIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#favoriteTypeIs", "?").replace("#userIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#favoriteTypeIs")) {
                sqlParams.add(qto.getFavoriteTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#userIdIs")) {
                sqlParams.add(qto.getUserIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  user_preference.sort_number asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
