package com.pulse.pulse.common.context;

import com.vs.common.util.rpc.context.Endpoint;
import com.vs.common.util.rpc.context.LoginContext;
import com.vs.common.util.rpc.context.LoginContextParam;
// import com.vs.ox.common.utils.CookieUtils;
import com.vs.ox.common.utils.JsonUtils;
// import com.vs.user_account.common.enums.UserTypeEnum;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
@Slf4j
@Order(10)
public class AppContextFilter implements Filter {

    @Override
    public void doFilter(
            ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        try {
            String applicationIdStr = request.getHeader("application-id");
            try {
                if (StringUtils.isNotBlank(applicationIdStr)
                        && StringUtils.isNumeric(applicationIdStr)) {
                    String applicationId = applicationIdStr;
                    UserContext.setApplicationId(applicationId);
                    if (UserContext.getLoginContextParam() == null) {
                        UserContext.setLoginContextParam(new LoginContextParam());
                    }
                    // ExtInfo
                    if (UserContext.getLoginContextParam().getExtInfo() == null) {
                        UserContext.getLoginContextParam()
                                .setExtInfo(
                                        JsonUtils.toJson(
                                                new UserContext.ExtInfo(
                                                        applicationId, "", "", "", "", "", "")));
                    } else {
                        UserContext.ExtInfo extInfo =
                                JsonUtils.readObject(
                                        LoginContext.getLoginContextParam().getExtInfo(),
                                        UserContext.ExtInfo.class);
                        extInfo.setApplicationId(applicationId);
                        LoginContext.getLoginContextParam().setExtInfo(JsonUtils.toJson(extInfo));
                    }
                }
                // Endpoint
                LoginContext.setEndpoint(handleEndpoint(request));
                // UserContext.setClickpoint(handleClientPoint(request));

            } catch (Exception e) {
                log.error("Filter处理失败. ", e);
                throw new RuntimeException(e);
            }
            // mockContext(request); // FIXME mock，后面删除

            //            // 设置跨域，用于浏览器mock调用本地
            //
            // response.setHeader("Access-Control-Allow-Origin",request.getHeader("Origin"));
            //            response.setHeader("Access-Control-Allow-Credentials", "true");
            //            response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS,
            // DELETE");
            ////            response.setHeader("Access-Control-Allow-Headers",
            // "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,vs-projectVersion,vs-projectId,vs-moduleinfo,projectVersion,projectId,moduleinfo,MOCK-USER-ID,MOCK-USER-TYPE,APPLICATION-ID,vs-debug-id,requestId");
            //            response.setHeader("Access-Control-Allow-Headers",
            // "Content-Type,MOCK-USER-ID,MOCK-USER-TYPE,vs-debug-id,requestId,application-id");
            filterChain.doFilter(request, response);

        } finally {
            UserContext.clear();
        }
    }

    private static final String USER_AGENT = "USER_AGENT";
    private static final String OS = "OS";
    private static final String OS_VER = "OS_VER";
    private static final String APP_VER = "APP_VER";
    private static final String IP = "X-Forwarded-For";
    private static final String DEPARTMENT = "department";
    private static final String CAMPUS = "campus";
    private static final String WARD = "ward";
    private static final String USERID = "userid";
    private static final String STAFFID = "staffid";

    private Endpoint handleEndpoint(HttpServletRequest request) {
        Endpoint endpoint = new Endpoint();
        endpoint.setUserAgent(CookieUtils.getCookieValue(request, USER_AGENT, ""));
        String os = CookieUtils.getCookieValue(request, OS, "null");
        String osVer = CookieUtils.getCookieValue(request, OS_VER, "null");
        String appVer = CookieUtils.getCookieValue(request, APP_VER, "null");
        endpoint.setOsType(os);
        endpoint.setClientVersionStr(appVer);
        endpoint.setOsVer(osVer);
        String IP = request.getRemoteAddr();
        if (StringUtils.isNotBlank(IP)) {
            if (StringUtils.contains(IP, ",")) {
                endpoint.setIp(StringUtils.split(IP, ",")[0]);
            } else {
                endpoint.setIp(IP);
            }
        }
        return endpoint;
    }

    private ClientPoint handleClientPoint(HttpServletRequest request) {
        ClientPoint clientPoint = new ClientPoint();
        clientPoint.setUserAgent(CookieUtils.getCookieValue(request, USER_AGENT, ""));
        String os = CookieUtils.getCookieValue(request, OS, "null");
        String osVer = CookieUtils.getCookieValue(request, OS_VER, "null");
        String appVer = CookieUtils.getCookieValue(request, APP_VER, "null");
        String campus = CookieUtils.getCookieValue(request, CAMPUS, "null");
        String department = CookieUtils.getCookieValue(request, DEPARTMENT, "null");
        String ward = CookieUtils.getCookieValue(request, WARD, "null");
        String user = CookieUtils.getCookieValue(request, USERID, "null");
        String staff = CookieUtils.getCookieValue(request, STAFFID, "null");
        clientPoint.setOsType(os);
        clientPoint.setClientVersionStr(appVer);
        clientPoint.setOsVer(osVer);
        String IP = request.getRemoteAddr();
        if (StringUtils.isNotBlank(IP)) {
            if (StringUtils.contains(IP, ",")) {
                clientPoint.setIp(StringUtils.split(IP, ",")[0]);
            } else {
                clientPoint.setIp(IP);
            }
        }

        clientPoint.setOsVer(campus);
        clientPoint.setOsVer(department);
        clientPoint.setOsVer(ward);
        clientPoint.setOsVer(user);
        clientPoint.setOsVer(staff);
        return clientPoint;
    }

    private void mockContext(HttpServletRequest request) {
        if (UserContext.getApplicationId() == null) {
            UserContext.setApplicationId("201");
        }
        String mockUserId = request.getHeader("User-Id");
        if (StringUtils.isNotBlank(mockUserId) && StringUtils.isNumeric(mockUserId)) {
            String mockUserType = request.getHeader("Login-Type");
            /**
             * LoginContext.setCurrentUsers(Map.of("", new User(Long.valueOf(mockUserId),
             * StringUtils.isNotBlank(mockUserType) ? mockUserType : UserTypeEnum.STAFF.toString(),
             * 0L, "", System.currentTimeMillis())));
             */
        }
    }
}
