package com.pulse.pulse.common.idempotent;

import lombok.Builder;
import lombok.Data;

/**
 * 幂等处理配置类
 *
 * <p>用于配置幂等处理的相关参数，如键前缀、过期时间、锁超时时间等 通用配置类，可被整个项目的各个模块使用
 *
 * <AUTHOR>
 */
@Data
@Builder
public class IdempotentConfig {

    /** 幂等键前缀 */
    private String idempotentKeyPrefix;

    /** 分布式锁键前缀 */
    private String lockKeyPrefix;

    /** 幂等键过期时间（秒），默认24小时 */
    @Builder.Default private int idempotentExpireSeconds = 24 * 60 * 60;

    /** 分布式锁超时时间（秒），默认30秒 */
    @Builder.Default private int lockTimeoutSeconds = 30;

    /** 分布式锁等待时间（秒），默认1秒 */
    @Builder.Default private int lockWaitSeconds = 1;

    /**
     * 创建操作日志消费者的默认配置
     *
     * @return 操作日志消费者配置
     */
    public static IdempotentConfig forOperationLogConsumer() {
        return IdempotentConfig.builder()
                .idempotentKeyPrefix("operation_log_consumer:idempotent:")
                .lockKeyPrefix("operation_log_consumer:lock:")
                .idempotentExpireSeconds(24 * 60 * 60)
                .lockTimeoutSeconds(30)
                .lockWaitSeconds(1)
                .build();
    }

    /**
     * 创建药品库存消费者的默认配置
     *
     * @return 药品库存消费者配置
     */
    public static IdempotentConfig forDrugInventoryConsumer() {
        return IdempotentConfig.builder()
                .idempotentKeyPrefix("drug_inventory_consumer:idempotent:")
                .lockKeyPrefix("drug_inventory_consumer:lock:")
                .idempotentExpireSeconds(12 * 60 * 60) // 12小时
                .lockTimeoutSeconds(60) // 药品库存处理可能较慢
                .lockWaitSeconds(2)
                .build();
    }

    /**
     * 创建患者信息消费者的默认配置
     *
     * @return 患者信息消费者配置
     */
    public static IdempotentConfig forPatientInfoConsumer() {
        return IdempotentConfig.builder()
                .idempotentKeyPrefix("patient_info_consumer:idempotent:")
                .lockKeyPrefix("patient_info_consumer:lock:")
                .idempotentExpireSeconds(48 * 60 * 60) // 48小时，患者信息保留更久
                .lockTimeoutSeconds(20)
                .lockWaitSeconds(1)
                .build();
    }

    /**
     * 创建财务结算消费者的默认配置
     *
     * @return 财务结算消费者配置
     */
    public static IdempotentConfig forBillingConsumer() {
        return IdempotentConfig.builder()
                .idempotentKeyPrefix("billing_consumer:idempotent:")
                .lockKeyPrefix("billing_consumer:lock:")
                .idempotentExpireSeconds(72 * 60 * 60) // 72小时，财务数据保留更久
                .lockTimeoutSeconds(45) // 财务处理可能较慢
                .lockWaitSeconds(2)
                .build();
    }

    /**
     * 创建自定义配置
     *
     * @param keyPrefix 键前缀
     * @return 自定义配置
     */
    public static IdempotentConfig custom(String keyPrefix) {
        return IdempotentConfig.builder()
                .idempotentKeyPrefix(keyPrefix + ":idempotent:")
                .lockKeyPrefix(keyPrefix + ":lock:")
                .build();
    }

    /**
     * 创建自定义配置（带过期时间）
     *
     * @param keyPrefix 键前缀
     * @param expireHours 过期时间（小时）
     * @return 自定义配置
     */
    public static IdempotentConfig custom(String keyPrefix, int expireHours) {
        return IdempotentConfig.builder()
                .idempotentKeyPrefix(keyPrefix + ":idempotent:")
                .lockKeyPrefix(keyPrefix + ":lock:")
                .idempotentExpireSeconds(expireHours * 60 * 60)
                .build();
    }

    /**
     * 重写toString方法，提供更好的日志输出
     *
     * @return 配置信息的字符串表示
     */
    @Override
    public String toString() {
        return String.format(
                "IdempotentConfig{prefix=%s, expire=%ds, lockTimeout=%ds, lockWait=%ds}",
                getKeyPrefixForDisplay(),
                idempotentExpireSeconds,
                lockTimeoutSeconds,
                lockWaitSeconds);
    }

    /**
     * 获取用于显示的键前缀（去掉后缀）
     *
     * @return 简化的键前缀
     */
    private String getKeyPrefixForDisplay() {
        if (idempotentKeyPrefix == null) {
            return "null";
        }
        // 移除 ":idempotent:" 后缀以简化显示
        return idempotentKeyPrefix.replace(":idempotent:", "");
    }
}
