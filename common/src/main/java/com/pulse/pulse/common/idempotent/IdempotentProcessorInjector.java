package com.pulse.pulse.common.idempotent;

import cn.hutool.core.util.StrUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;

import redis.clients.jedis.JedisPool;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.annotation.Resource;

/**
 * 幂等处理器注入后处理器
 *
 * <p>自动扫描带有@EnableIdempotent注解的类，并为带有@InjectIdempotentProcessor注解的字段 注入对应配置的幂等处理器实例
 *
 * <p>注意：此类通过IdempotentAutoConfiguration中的@Bean注册，不使用@Component注解
 *
 * <AUTHOR>
 */
@Slf4j
public class IdempotentProcessorInjector implements BeanPostProcessor {

    /** 幂等处理器缓存，避免重复创建 */
    private final ConcurrentMap<String, IdempotentProcessor> processorCache =
            new ConcurrentHashMap<>();

    @Resource private JedisPool jedisPool;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName)
            throws BeansException {
        if (bean == null) {
            return bean;
        }

        Class<?> clazz = bean.getClass();

        // 检查类是否有@EnableIdempotent注解
        EnableIdempotent enableIdempotent = clazz.getAnnotation(EnableIdempotent.class);
        if (enableIdempotent == null) {
            return bean;
        }

        // 检查JedisPool是否可用
        if (jedisPool == null) {
            log.error("JedisPool未注入，无法创建幂等处理器，类: {}", clazz.getSimpleName());
            throw new IllegalStateException("JedisPool未注入，请检查Redis配置");
        }

        // 扫描字段，查找@InjectIdempotentProcessor注解
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            InjectIdempotentProcessor injectAnnotation =
                    field.getAnnotation(InjectIdempotentProcessor.class);
            if (injectAnnotation != null && field.getType() == IdempotentProcessor.class) {
                try {
                    // 注入幂等处理器
                    IdempotentProcessor processor =
                            createIdempotentProcessor(enableIdempotent, injectAnnotation, clazz);
                    field.setAccessible(true);
                    field.set(bean, processor);

                    log.debug("成功注入幂等处理器到 {}.{}", clazz.getSimpleName(), field.getName());
                } catch (Exception e) {
                    String errorMsg =
                            String.format(
                                    "注入幂等处理器失败: %s.%s, 原因: %s",
                                    clazz.getSimpleName(), field.getName(), e.getMessage());
                    log.error(errorMsg, e);
                    throw new RuntimeException(errorMsg, e);
                }
            }
        }

        return bean;
    }

    /**
     * 创建幂等处理器
     *
     * @param enableIdempotent 类级别的幂等配置注解
     * @param injectAnnotation 字段级别的注入注解
     * @param clazz 目标类
     * @return 幂等处理器实例
     */
    private IdempotentProcessor createIdempotentProcessor(
            EnableIdempotent enableIdempotent,
            InjectIdempotentProcessor injectAnnotation,
            Class<?> clazz) {

        // 生成缓存键
        String cacheKey = generateCacheKey(enableIdempotent, injectAnnotation, clazz);

        // 从缓存中获取或创建新的处理器
        return processorCache.computeIfAbsent(
                cacheKey,
                key -> {
                    IdempotentConfig config =
                            createIdempotentConfig(enableIdempotent, injectAnnotation, clazz);
                    log.debug("创建幂等处理器，缓存键: {}, 配置: {}", key, config);
                    return new IdempotentProcessor(jedisPool, config);
                });
    }

    /**
     * 创建幂等配置
     *
     * @param enableIdempotent 类级别的幂等配置注解
     * @param injectAnnotation 字段级别的注入注解
     * @param clazz 目标类
     * @return 幂等配置
     */
    private IdempotentConfig createIdempotentConfig(
            EnableIdempotent enableIdempotent,
            InjectIdempotentProcessor injectAnnotation,
            Class<?> clazz) {

        // 如果指定了自定义配置名称，优先使用
        if (StrUtil.isNotEmpty(injectAnnotation.configName())) {
            return IdempotentConfig.custom(injectAnnotation.configName());
        }

        // 如果指定了预定义类型，使用预定义配置
        if (enableIdempotent.preset() != EnableIdempotent.PresetType.NONE) {
            return createPresetConfig(enableIdempotent.preset());
        }

        // 使用注解中的自定义配置
        String keyPrefix =
                StrUtil.isNotEmpty(enableIdempotent.keyPrefix())
                        ? enableIdempotent.keyPrefix()
                        : generateDefaultKeyPrefix(clazz);

        return IdempotentConfig.builder()
                .idempotentKeyPrefix(keyPrefix + ":idempotent:")
                .lockKeyPrefix(keyPrefix + ":lock:")
                .idempotentExpireSeconds(enableIdempotent.expireHours() * 60 * 60)
                .lockTimeoutSeconds(enableIdempotent.lockTimeoutSeconds())
                .lockWaitSeconds(enableIdempotent.lockWaitSeconds())
                .build();
    }

    /**
     * 创建预定义配置
     *
     * @param presetType 预定义类型
     * @return 幂等配置
     */
    private IdempotentConfig createPresetConfig(EnableIdempotent.PresetType presetType) {
        switch (presetType) {
            case OPERATION_LOG:
                return IdempotentConfig.forOperationLogConsumer();
            case DRUG_INVENTORY:
                return IdempotentConfig.forDrugInventoryConsumer();
            case PATIENT_INFO:
                return IdempotentConfig.forPatientInfoConsumer();
            case BILLING:
                return IdempotentConfig.forBillingConsumer();
            default:
                throw new IllegalArgumentException("不支持的预定义类型: " + presetType);
        }
    }

    /**
     * 生成缓存键
     *
     * @param enableIdempotent 类级别的幂等配置注解
     * @param injectAnnotation 字段级别的注入注解
     * @param clazz 目标类
     * @return 缓存键
     */
    private String generateCacheKey(
            EnableIdempotent enableIdempotent,
            InjectIdempotentProcessor injectAnnotation,
            Class<?> clazz) {

        if (StrUtil.isNotEmpty(injectAnnotation.configName())) {
            return "custom_" + injectAnnotation.configName();
        }

        if (enableIdempotent.preset() != EnableIdempotent.PresetType.NONE) {
            return "preset_" + enableIdempotent.preset().name().toLowerCase();
        }

        String keyPrefix =
                StrUtil.isNotEmpty(enableIdempotent.keyPrefix())
                        ? enableIdempotent.keyPrefix()
                        : generateDefaultKeyPrefix(clazz);

        return String.format(
                "custom_%s_%dh_%ds_%ds",
                keyPrefix,
                enableIdempotent.expireHours(),
                enableIdempotent.lockTimeoutSeconds(),
                enableIdempotent.lockWaitSeconds());
    }

    /**
     * 生成默认的键前缀
     *
     * @param clazz 目标类
     * @return 默认键前缀
     */
    private String generateDefaultKeyPrefix(Class<?> clazz) {
        String className = clazz.getSimpleName();
        // 将类名转换为下划线分隔的小写形式
        return className.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /** 清除处理器缓存（用于测试） */
    public void clearCache() {
        log.info("清除幂等处理器缓存，数量: {}", processorCache.size());
        processorCache.clear();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return String.format(
                "幂等处理器缓存统计 - 总数: %d, 缓存键: %s", processorCache.size(), processorCache.keySet());
    }
}
